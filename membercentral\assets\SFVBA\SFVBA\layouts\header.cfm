<cfoutput>
	<cfset local.strMenus = application.objCMS.getPageMenus(event=event)>
	<cfset local.logo="" />
	<cfif application.objCMS.getZoneItemCount(zone='A' ,event=event)>
		<cfif arrayIsDefined(event.getValue( "mc_pageDefinition").pageZones['A'],1)>
			<cfset local.logo=trim(REReplace(event.getValue( "mc_pageDefinition").pageZones['A'][1].data, "<\/?p[^>]*>", "", "all"))>
		</cfif>
	</cfif>
	<cfset local.zoneB1Content="" />
	<cfif application.objCMS.getZoneItemCount(zone='B' ,event=event)>
		<cfif arrayIsDefined(event.getValue( "mc_pageDefinition").pageZones['B'],1)>
			<cfset local.zoneB1Content=trim(REReplace(event.getValue( "mc_pageDefinition").pageZones['B'][1].data, "<\/?p[^>]*>", "", "all"))>
		</cfif>
	</cfif>
	<cfset local.zoneC1Content="" />
	<cfif application.objCMS.getZoneItemCount(zone='C' ,event=event)>
		<cfif arrayIsDefined(event.getValue( "mc_pageDefinition").pageZones['C'],1)>
			<cfset local.zoneC1Content=trim(REReplace(event.getValue( "mc_pageDefinition").pageZones['C'][1].data, "<\/?p[^>]*>", "", "all"))>
		</cfif>
	</cfif>
	<!--Print Header-->
	<div class="printHeader">
		#local.logo#
		<p> https://sfvba.org/ </p>
	</div>
	<!--Print Header End-->
	<!--Header Start-->
	<header class="header outer-width">
		<div class="top-header">
			<div class="container containerCustom">
				<div class="top-header-wrapper d-flex-wrap">
					<div class="col-right">
						<div class="social-links">
							<cfif len(trim(local.zoneB1Content))>
								#local.zoneB1Content#
							</cfif>
						</div>
						<div class="top-btn-links d-flex-wrap">
							<ul class="headerLinks">
								<li class="searchBtnFn">
									<a href="javascript:void(0);">
									<i class="fa-solid fa-magnifying-glass">&nbsp;</i>
									</a>
									<ul>
										<li class="span12">
											<div class="formframe">
												<form method="post" name="searchform1" id="searchform1" action="/?pg=search">
													<input name="s_a" type="hidden" value="doSearch">
													<input name="s_frm" type="hidden" value="1">
													<input name="s_key_all" id="s_key_all"  onblur="if (this.value == '') {this.value = 'Search';}"
														onfocus="if (this.value == 'Search') {this.value = '';}" value="Search"
														type="text">
													<a href="javascript:void(0);" onclick="document.getElementById('searchform1').submit();" >
													<i class="fa-solid fa-magnifying-glass">&nbsp;</i>
													</a>
												</form>
											</div>
											<a href="##" class="close-search">
											<i class="fa-solid fa-xmark">&nbsp;</i>
											</a>
										</li>
									</ul>
								</li>
								#rereplace(rereplace(local.zoneC1Content,'(.*)(</ul>)(.*)','\1\3','one'),'(.*?)(<ul>)(.*)','\1\3','one')#
							</ul>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div id="navbar-example" class="navbar">
			<div class="navbar-inner">
				<div class="container containerCustom">
					<div class="row-fluid">
						<a class="btn btn-navbar collapsed" id="openMainMenu">
						<span class="icon-bar"></span>
						<span class="icon-bar"></span>
						<span class="icon-bar"></span>
						</a>
						<a href="/" class="navbar-brand">#local.logo#</a>
						<div class="nav-collapse collapse" id="mainNavMenus">
							<ul class="nav mainNav navHolder">
							<cfif structKeyExists(local.strMenus,"primaryNav")>
								#Replace(rereplace(rereplace(local.strMenus.primaryNav.menuHTML.rawcontent,'(.*)(</ul>)(.*)','\1\3','one'),'(.*?)(<ul>)(.*)','\1\3','one'), "&nbsp;", "", "all")#
							</cfif>
							</ul>
							<div class="mobMenus">
								<a class="" id="closeMainMenu">
								<i class="fa-solid fa-xmark">&nbsp;</i>
								</a>
								<ul class="nav mobNav navHolder">
								<cfif structKeyExists(local.strMenus,"primaryNav")>
                                    #Replace(rereplace(rereplace(local.strMenus.primaryNav.menuHTML.rawcontent,'(.*)(</ul>)(.*)','\1\3','one'),'(.*?)(<ul>)(.*)','\1\3','one'), "&nbsp;", "", "all")#
                                </cfif>
								</ul>
							</div>
							<div class="d-mobile-only">
								<div class="find-online-block">
									<!-- <p>Find SFVBA online</p> -->
									<div class="social-links">
										<cfif len(trim(local.zoneB1Content))>
											#local.zoneB1Content#
										</cfif>
									</div>
									<div class="col-left inline-icon-list">
										<div class="dropdown">
											<a class="dropdown-toggle" data-toggle="dropdown" href="##"><i class="fa-solid fa-table-list">&nbsp;</i> topmost</a>
											<ul class="dropdown-menu headerLinks" role="menu" aria-labelledby="dLabel">
												#rereplace(rereplace(local.zoneC1Content,'(.*)(</ul>)(.*)','\1\3','one'),'(.*?)(<ul>)(.*)','\1\3','one')#
											</ul>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!-- /navbar- -->
	</header>
	<div class="headerSpace"></div>
	<!--Header End-->
</cfoutput>