CREATE PROC dbo.tr_autoCloseMCPayEcheckBatches
@itemCount int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#mcBatchesToClose') IS NOT NULL 
		DROP TABLE #mcBatchesToClose;
	IF OBJECT_ID('tempdb..#tmpMCPayECheckMPProfiles') IS NOT NULL 
		DROP TABLE #tmpMCPayECheckMPProfiles;
	CREATE TABLE #mcBatchesToClose (batchID int, statusID int);
	CREATE TABLE #tmpMCPayECheckMPProfiles (profileID int);

	DECLARE @enteredByMemberID int, @dateToUse datetime, @orgID int, @closedStatusID int, @openStatusID int, @openForModificationStatusID int;
	SELECT @enteredByMemberID = dbo.fn_ams_getMCSystemMemberID();
	SET @dateToUse = DATEADD(dd, DATEDIFF(dd,0,getdate()), 0);
	SET @itemCount = 0;

	SELECT @closedStatusID = statusID FROM dbo.tr_batchStatuses WHERE [status] = 'closed';
	SELECT @openStatusID = statusID FROM dbo.tr_batchStatuses WHERE [status] = 'open';
	SELECT @openForModificationStatusID = statusID FROM dbo.tr_batchStatuses WHERE [status] = 'Open for Modification';

	SELECT @orgID = min(orgID) FROM dbo.organizations;
	WHILE @orgID IS NOT NULL BEGIN

		-- MCPayEcheck profiles
		INSERT INTO #tmpMCPayECheckMPProfiles (profileID)
		SELECT mp.profileID
		FROM dbo.mp_profiles AS mp
		INNER JOIN dbo.mp_gateways AS g ON g.gatewayID = mp.gatewayID
		INNER JOIN dbo.sites AS s ON s.siteID = mp.siteID
		WHERE s.orgID = @orgID
		AND mp.[status] IN ('A','I')
		AND g.gatewayType = 'MCPayEcheck'
		AND g.isActive = 1;

		-- get batches that need to be closed
		INSERT INTO #mcBatchesToClose (batchID, statusID)
		SELECT b.batchID, b.statusID
		FROM dbo.tr_batches AS b
		INNER JOIN #tmpMCPayECheckMPProfiles AS tmp ON tmp.profileID = b.payProfileID
		WHERE b.orgID = @orgID
		AND b.statusID IN (@openStatusID,@openForModificationStatusID)
		AND b.isSystemCreated = 1
		AND b.depositDate < @dateToUse;

		IF @@ROWCOUNT > 0 BEGIN
			SET @itemCount = @itemCount + 1;

			BEGIN TRAN;
				-- record status history
				INSERT INTO dbo.tr_batchStatusHistory (batchID, updateDate, statusID, oldStatusID, enteredByMemberID)
				SELECT batchID, getdate(), @closedStatusID, statusID, @enteredByMemberID
				FROM #mcBatchesToClose;
				
				-- close batch
				UPDATE b
				SET b.statusID = @closedStatusID
				FROM dbo.tr_batches as b
				INNER JOIN #mcBatchesToClose as tmp on tmp.batchID = b.batchID
				WHERE b.orgID = @orgID;
			COMMIT TRAN;
		END

		TRUNCATE TABLE #tmpMCPayECheckMPProfiles;
		TRUNCATE TABLE #mcBatchesToClose;

		SELECT @orgID = min(orgID) FROM dbo.organizations WHERE orgID > @orgID;
	END

	IF OBJECT_ID('tempdb..#mcBatchesToClose') IS NOT NULL 
		DROP TABLE #mcBatchesToClose;
	IF OBJECT_ID('tempdb..#tmpMCPayECheckMPProfiles') IS NOT NULL 
		DROP TABLE #tmpMCPayECheckMPProfiles;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
