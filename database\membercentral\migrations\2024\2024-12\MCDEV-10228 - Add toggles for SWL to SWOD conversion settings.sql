USE seminarweb;
GO

ALTER PROC dbo.swl_convertToSWOD
@seminarID INT,
@recordedByMemberID INT,
@incWebinarSettings BIT = 1,
@incCreditApprovals BIT = 1,
@seminarErrCode INT OUTPUT,
@newSeminarID INT OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @dateOrigPublished datetime, @seminarLength int, @seminarName varchar(250), @titleName varchar(250), @seminarSubTitle varchar(250),
		@programCode varchar(15), @seminarDesc varchar(max), @participantID int, @freeRateDisplay varchar(5),
		@orgID int, @siteID int, @crlf varchar(10), @msgjson varchar(max), @siteCode varchar(10), @fileName varchar(100), @fileTitle varchar(200),
		@hasReplayVideo bit, @videoFileTypeID int, @paperFileTypeID int, @s3CopyReadyStatusID int, @objectKey varchar(200),
		@oldObject<PERSON>ey varchar(200), @newObjectKey varchar(200), @fileID int, @speakerAuthorTypeID int, @moderatorAuthorTypeID int,
		@nowDate datetime = GETDATE(), @s3keyMod varchar(4), @seminarFeatureImageID int, @seminarImageConfigID int, @featureImageUsageID int,
		@newTitleID int, @isPriceBasedOnActual int, @creditStatusId int, @layoutID int, @resourceTypeID int, @siteResourceID int,
		@semWebCatalogSiteResourceID int, @seminarDocumentID int, @docFileName varchar(255), @docTitle varchar(255), @fileExt varchar(20),
		@documentVersionID int, @SWAdminSiteResourceID int, @swlUsageID int, @swodUsageID int, @minFieldID int, @newFieldID int;

	SET @newSeminarID = 0;
	SET @seminarErrCode = 0;
	SET @crlf = CHAR(13) + CHAR(10);

	IF OBJECT_ID('tempdb..#tmpLogMessages') IS NOT NULL
		DROP TABLE #tmpLogMessages;

	CREATE TABLE #tmpLogMessages (rowID INT IDENTITY(1,1), msg VARCHAR(MAX));

	-- check for valid swl seminar
	IF NOT EXISTS (select s.seminarID from dbo.tblSeminars as s inner join dbo.tblSeminarsSWLive as swl on swl.seminarID = s.seminarID where s.seminarID = @seminarID) BEGIN
		set @seminarErrCode = 1;
		GOTO on_done;
	END
	IF EXISTS (select 1 from dbo.tblSeminarsSWOD where convertedFromSeminarID = @seminarID) BEGIN
		set @seminarErrCode = 2;
		GOTO on_done;
	END
	
	SELECT @resourceTypeID = memberCentral.dbo.fn_getResourceTypeID('SWSeminar');
	
	EXEC memberCentral.dbo.getUniqueCode @uniqueCode=@programCode OUTPUT;

	SELECT @orgID = mcs.orgID, @siteID = mcs.siteID, @siteCode = mcs.siteCode, @participantID = p.participantID, 
		@seminarName = s.seminarName, @seminarSubTitle = s.seminarSubTitle, @seminarDesc = s.seminarDesc, 
		@isPriceBasedOnActual = s.isPriceBasedOnActual,
		@freeRateDisplay = s.freeRateDisplay, @dateOrigPublished = sswl.dateStart, 
		@seminarLength = dateDiff(minute, sswl.dateStart, sswl.dateEnd),
		@hasReplayVideo = CASE WHEN p.offerSWLReplays = 1 AND sswl.offerReplay = 1 AND sswl.isUploadedReplay = 1 THEN 1 ELSE 0 END
	FROM dbo.tblSeminars AS s
	INNER JOIN dbo.tblSeminarsSWLive AS sswl ON sswl.seminarID = s.seminarID
	INNER JOIN dbo.tblParticipants AS p ON p.participantID = s.participantID
	INNER JOIN membercentral.dbo.sites AS mcs ON mcs.siteCode = p.orgCode
	WHERE s.seminarID = @seminarID;
	
	SELECT @semWebCatalogSiteResourceID = ai.siteResourceID
	FROM memberCentral.dbo.cms_applicationInstances AS ai 
	INNER JOIN memberCentral.dbo.cms_applicationTypes AS apt ON apt.applicationTypeID = ai.applicationTypeID 
		and apt.applicationTypeName = 'SemWebCatalog' 
	INNER JOIN memberCentral.dbo.cms_siteResources as sr ON sr.siteID = @siteID
		AND sr.siteResourceID = ai.siteResourceID 
		AND sr.siteResourceStatusID = 1
	WHERE ai.siteID = @siteID;

	SELECT @layoutID = layoutID
	FROM dbo.tblSeminarsSWODLayouts
	WHERE layout = 'largeVideo';

	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='s3Copy', @queueStatus='readyToProcess', @queueStatusID=@s3CopyReadyStatusID OUTPUT;
	
	SELECT @speakerAuthorTypeID = authorTypeID
	FROM dbo.tblAuthorTypes
	WHERE authorType = 'Speaker';

	SELECT @moderatorAuthorTypeID = authorTypeID
	FROM dbo.tblAuthorTypes
	WHERE authorType = 'Moderator';
	
	BEGIN TRAN;
		EXEC memberCentral.dbo.cms_createSiteResource @resourceTypeID=@resourceTypeID, @siteResourceStatusID=1, @siteID=@siteID,  
			@isVisible=1, @parentSiteResourceID=@semWebCatalogSiteResourceID, @siteResourceID=@siteResourceID OUTPUT;
			
		-- seminar
		INSERT INTO dbo.tblSeminars (seminarName, seminarSubTitle, programCode, seminarDesc, isPublished, participantID, isDeleted, 
			dateCatalogStart, dateCatalogEnd, freeRateDisplay, isPriceBasedOnActual, siteResourceID)
		VALUES (@seminarName, @seminarSubTitle, @programCode, @seminarDesc, 0, @participantID, 0, @nowDate, dateadd(year,1,@nowDate), 
			@freeRateDisplay, @isPriceBasedOnActual, @siteResourceID);
			SET @newSeminarID = SCOPE_IDENTITY();

		UPDATE dbo.tblSeminars
		SET programCode = 'SWOD-' + CAST(@newSeminarID AS varchar(10))
		WHERE seminarID = @newSeminarID;

		INSERT INTO dbo.tblSeminarsSWOD (seminarID, dateOrigPublished, priceSyndication, allowSyndication, layoutID, 
			seminarLength, offerQA, convertedFromSeminarID, dateCreated, submittedByMemberID)
		VALUES (@newSeminarID, @dateOrigPublished, null, 0, @layoutID, @seminarLength, 0, @seminarID, @nowDate, @recordedByMemberID);

		-- auto opt in the publisher
		INSERT INTO dbo.tblSeminarsOptIn (seminarID, participantID, isPriceBasedOnActual, freeRateDisplay, dateAdded, isActive)
		VALUES (@newSeminarID, @participantID, @isPriceBasedOnActual, @freeRateDisplay, @nowDate, 1);

		SELECT @msgjson = 'SWOD- ' + CAST(@newSeminarID AS VARCHAR(20)) + ' has been created by converting from SWL-' + CAST(@seminarID AS VARCHAR(20)) + '.';

		-- auto add enrollments
		EXEC dbo.swod_autoAddEnrollments @newSeminarID;

		EXEC dbo.swod_addBillingLogForSeminarCreation @orgcode=@siteCode, @seminarID=@newSeminarID, @recordedByMemberID=@recordedByMemberID;

		-- swod speakers
		INSERT INTO dbo.tblSeminarsAndAuthors (seminarID, authorID, authorOrder)
		SELECT @newSeminarID, saa.authorID, saa.authorOrder
		FROM dbo.tblSeminarsAndAuthors AS saa
		INNER JOIN dbo.tblAuthors AS a ON a.authorID = saa.authorID
		WHERE saa.seminarID = @seminarID
		AND a.authorTypeID = @speakerAuthorTypeID;

		INSERT INTO #tmpLogMessages(msg)
		SELECT 'Speaker [' + a.firstName + ' ' + a.lastName + '] was added to SWOD-' + CAST(@newSeminarID AS VARCHAR(20)) + '.'
		FROM dbo.tblSeminarsAndAuthors AS saa
		INNER JOIN dbo.tblAuthors AS a ON a.authorID = saa.authorID
		WHERE saa.seminarID = @seminarID
		AND a.authorTypeID = @speakerAuthorTypeID;

		-- seminar subjects
		INSERT INTO dbo.tblSeminarsAndCategories (seminarID, categoryID)
		SELECT @newSeminarID, categoryID
		FROM dbo.tblSeminarsAndCategories
		WHERE seminarID = @seminarID;

		INSERT INTO #tmpLogMessages(msg)
		SELECT 'Subjects ' + STRING_AGG('[' + c.categoryName + ']',', ') WITHIN GROUP (ORDER BY c.categoryName ASC) + ' were added to SWOD-' + CAST(@newSeminarID AS VARCHAR(20)) + '.'
		FROM dbo.tblSeminarsAndCategories AS sc
		INNER JOIN dbo.tblCategories AS c ON c.categoryID = sc.categoryID
		WHERE sc.seminarID = @seminarID;

		-- seminar objectives
		INSERT INTO dbo.tblLearningObjectives (seminarID, objective, objectiveOrder)
		SELECT @newSeminarID, objective, objectiveOrder
		FROM dbo.tblLearningObjectives
		WHERE seminarID = @seminarID;

		INSERT INTO #tmpLogMessages(msg)
		SELECT 'New Learning Objective ' + STRING_AGG('[' + lo.objective + ']',', ') WITHIN GROUP (ORDER BY lo.objectiveOrder ASC) + ' has been added to SWOD-' + CAST(@newSeminarID AS VARCHAR(20)) + '.'
		FROM dbo.tblLearningObjectives as lo
		WHERE seminarID = @seminarID;

		-- create title
		SET @titleName = 'Replay of ' + @seminarName;
		EXEC dbo.sw_addTitle @orgCode=@siteCode, @seminarID=@newSeminarID, @titleName=@titleName, @recordedByMemberID=@recordedByMemberID, @titleID=@newTitleID OUTPUT;

		INSERT INTO #tmpLogMessages(msg)
		VALUES('SWTL-' + CAST(@newTitleID AS VARCHAR(20)) + ' [Replay of ' + @seminarName +'] has been created as part of the conversion from SWL-' + CAST(@seminarID AS VARCHAR(20)) + '.');

		-- credit sponsors/authorities
		SELECT @creditStatusId = statusID from dbo.tblCreditStatuses where status = 'Not Submitted';

		IF @incCreditApprovals = 1 BEGIN
			INSERT INTO dbo.tblSeminarsAndCredit (seminarID, CSALinkID, statusID, courseapproval, wddxcreditsAvailable, creditOfferedStartDate, creditOfferedEndDate, creditCompleteByDate, isCreditRequired, isIDRequired, isCreditDefaulted, hasForm1)
			SELECT @newSeminarID, CSALinkID, statusID, courseapproval, wddxcreditsAvailable, NULL, NULL, NULL, 0, isIDRequired, 0, 0
			FROM dbo.tblSeminarsAndCredit
			WHERE seminarID = @seminarID;
		END

		INSERT INTO #tmpLogMessages(msg)
		SELECT 'Credit Authorities ' + STRING_AGG('[' + ca.authorityName + ']',', ') WITHIN GROUP (ORDER BY ca.authorityName ASC) + ' have been added to SWOD-' + CAST(@newSeminarID AS VARCHAR(20)) + '.'
		FROM dbo.tblSeminarsAndCredit sc
		INNER JOIN dbo.tblCreditSponsorsAndAuthorities csa ON sc.csalinkid = csa.csalinkid
		INNER JOIN dbo.tblCreditAuthorities ca ON ca.authorityID = csa.authorityID
		WHERE sc.seminarID = @seminarID;
		
		-- Seminar Featured Images
		IF EXISTS (select 1 from memberCentral.dbo.cms_featuredImageUsages WHERE referenceID = @seminarID AND referenceType='swlProgram') BEGIN
			SELECT TOP 1 @seminarFeatureImageID = featureImageID, @seminarImageConfigID = featureImageConfigID 
			FROM memberCentral.dbo.cms_featuredImageUsages 
			WHERE referenceID = @seminarID 
			AND referenceType='swlProgram';
			
			EXEC memberCentral.dbo.cms_createFeaturedImageUsage @featureImageID=@seminarFeatureImageID, @featureImageConfigID=@seminarImageConfigID,
				@referenceType='swodProgram', @referenceID=@newSeminarID, @featureImageUsageID=@featureImageUsageID OUTPUT;
				
			INSERT INTO #tmpLogMessages(msg)
			SELECT 'Featured Image is added to SWOD-' + CAST(@newSeminarID AS VARCHAR(20)) + '.';
		END
		
		-- Copying Rates
		EXEC dbo.sw_copyRates @participantID=@participantID, @copyFromSeminarID=@seminarID, @copyToSeminarID=@newSeminarID, @recordedByMemberID=@recordedByMemberID;

		IF @hasReplayVideo = 1 BEGIN
			select @videoFileTypeID = filetypeID from dbo.tblFilesTypes where fileType = 'video';
			SET @objectkey = LOWER('swlreplay/' + @siteCode + '/'+ cast(@participantID as varchar(10)) +'/'+ cast(@seminarID as varchar(10)) +'.mp4');

			SET @fileName = LEFT(memberCentral.dbo.fn_RegExReplace('Replay of ' + @seminarName,'[^A-Za-z0-9]',''),100);
			SET @fileTitle = LEFT('Replay of ' + @seminarName,200);

			EXEC dbo.sw_addFile @orgCode=@siteCode, @titleID=@newTitleID, @fileTypeID=@videoFileTypeID, @fileName=@fileName, 
				@fileTitle=@fileTitle, @fileDesc=NULL, @recordedByMemberID=@recordedByMemberID, @addPVR=0, @fileID=@fileID OUTPUT;
			
			UPDATE dbo.tblFiles
			SET formatsAvailable = '<formats><format ext="mp4" accesstype="S" /></formats>'
			WHERE fileID = @fileID;

			IF (SELECT tier FROM memberCentral.dbo.fn_getServerSettings()) = 'production'
			BEGIN
				INSERT INTO platformQueue.dbo.queue_S3Copy (s3bucketName, objectkey, newS3bucketName, newObjectKey, dateAdded, dateUpdated, nextAttemptDate, statusID)
				VALUES ('seminarweb', @objectkey, 'seminarweb', LOWER('swod/' + @siteCode + '/'+ cast(@participantID as varchar(10)) +'/'+ cast(@fileID as varchar(10)) +'.mp4'),
					@nowDate, @nowDate, @nowDate, @s3CopyReadyStatusID);
			END
		END

		-- swl materials to title download files (ignore mp3/mp4)
		SELECT @paperFileTypeID = filetypeID from dbo.tblFilesTypes where fileType = 'paper';
		DECLARE @tblSWLFiles TABLE (seminarDocumentID int, documentVersionID int, docFileName varchar(255), docTitle varchar(255), fileExt varchar(20));
			
		-- tblFiles expects the fileName to not have the extension, so strip it here
		INSERT INTO @tblSWLFiles (seminarDocumentID, documentVersionID, docFileName, docTitle, fileExt)
		SELECT sd.seminarDocumentID, dv.documentVersionID, replace(dv.fileName,'.'+dv.fileExt,''), dl.docTitle, dv.fileExt
		FROM dbo.tblSeminarsAndDocuments as sd
		INNER JOIN membercentral.dbo.cms_documents as d ON sd.documentID = d.documentID
		INNER JOIN membercentral.dbo.cms_documentLanguages as dl ON d.documentID = dl.documentID
		INNER JOIN membercentral.dbo.cms_documentVersions as dv ON dl.documentLanguageID = dv.documentLanguageID 
			AND dv.isActive = 1
			AND dv.fileExt not in ('mp3','mp4')
		INNER JOIN membercentral.dbo.cms_siteResources as sr on sr.siteID = @siteID 
			AND sr.siteResourceStatusID = 1
			AND sr.siteResourceID = d.siteResourceID
		WHERE sd.seminarID = @seminarID;

		SELECT @seminarDocumentID = min(seminarDocumentID) from @tblSWLFiles;
		WHILE @seminarDocumentID is not null BEGIN
			SELECT @fileID = null, @docFileName = null, @docTitle = null, @fileExt = null, @documentVersionID = null;

			SELECT @documentVersionID = documentVersionID, @docFileName = docFileName, @docTitle = docTitle, @fileExt = fileExt
			FROM @tblSWLFiles
			WHERE seminarDocumentID = @seminarDocumentID;

			EXEC dbo.sw_addFile @orgCode=@siteCode, @titleID=@newTitleID, @fileTypeID=@paperFileTypeID, @fileName=@docFileName, 
				@fileTitle=@docTitle, @fileDesc=NULL, @recordedByMemberID=@recordedByMemberID, @addPVR=0, @fileID=@fileID OUTPUT;

			UPDATE dbo.tblFiles
			SET formatsAvailable = '<formats><format ext="' + @fileExt +'" accesstype="D" /></formats>'
			WHERE fileID = @fileID;

			IF (SELECT tier FROM memberCentral.dbo.fn_getServerSettings()) = 'production'
			BEGIN
				SET @s3keyMod = FORMAT(@documentVersionID % 1000, '0000');
				SET @oldObjectKey = LOWER('sitedocuments/' + membercentral.dbo.fn_getOrgCodeFromSiteCode(@siteCode) + '/' + @siteCode + '/' + @s3keyMod + '/' + cast(@documentVersionID as varchar(10)) + '.' + @fileExt);
				SET @newObjectkey = LOWER('swoddownloads/' + @siteCode + '/' + cast(@participantID as varchar(10)) + '/' + CAST(@fileID AS VARCHAR(10)) + '.' + @fileExt);

				INSERT INTO platformQueue.dbo.queue_S3Copy (s3bucketName, objectkey, newS3bucketName, newObjectKey, dateAdded, dateUpdated, nextAttemptDate, statusID)
				VALUES ('membercentralcdn', @oldObjectKey, 'seminarweb', @newObjectkey, @nowDate, @nowDate, @nowDate, @s3CopyReadyStatusID);
			END

			SELECT @seminarDocumentID = min(seminarDocumentID) from @tblSWLFiles where seminarDocumentID > @seminarDocumentID;
		END

		IF @incWebinarSettings = 1 BEGIN
			UPDATE dbo.tblSeminars
			SET offerCertificate = (
				SELECT offerCertificate
				FROM dbo.tblSeminars
				WHERE seminarID = @seminarID
			)
			WHERE seminarID = @newSeminarID;

			INSERT INTO dbo.tblSeminarsAndForms (seminarID, formID, loadPoint, orderBy, isRequired,
				maxMinutesAllowed, certifiedStatement, maxTriesPerQuestion, allowSkipBackward,
				includeQuestions, enforceQReqStatusCorrectMaxTries, showImmediateAnswer,
				overrideMessage, numResponsesPerEnrollment)
			SELECT @newSeminarID, formID, loadPoint, orderBy, isRequired, maxMinutesAllowed,
				certifiedStatement, maxTriesPerQuestion, allowSkipBackward, includeQuestions,
				enforceQReqStatusCorrectMaxTries, showImmediateAnswer, overrideMessage,
				numResponsesPerEnrollment
			FROM dbo.tblSeminarsAndForms
			WHERE seminarID = @seminarID
			AND loadPoint IN ('preTest', 'postTest', 'evaluation');
			
			SELECT @SWAdminSiteResourceID = siteResourceID
			FROM membercentral.dbo.cms_siteResources as csr
			INNER JOIN membercentral.dbo.cms_siteResourceTypes as csrt ON csrt.resourceTypeID = csr.resourceTypeID
			WHERE siteID = @siteID AND resourceType = 'SeminarWebAdmin' 

			SELECT @swlUsageID = membercentral.dbo.fn_cf_getUsageID('SemWebCatalog', 'SWLEnrollment', NULL);
			SELECT @swodUsageID = membercentral.dbo.fn_cf_getUsageID('SemWebCatalog', 'SWODEnrollment', NULL);

			IF @swlUsageID IS NOT NULL AND @swodUsageID IS NOT NULL BEGIN
				CREATE TABLE #FieldGroupingMapping (
					oldFieldGroupingID INT,
					newFieldGroupingID INT
				);

				CREATE TABLE #FieldMapping (
					oldFieldID INT,
					newFieldID INT
				);

				INSERT INTO membercentral.dbo.cf_fieldsGrouping (
					fieldControllingSiteResourceID, fieldUsageID, fieldDetailID,
					fieldGrouping, fieldGroupingDesc, fieldGroupingOrder
				)
				OUTPUT INSERTED.fieldGroupingID, INSERTED.fieldGroupingID INTO #FieldGroupingMapping
				SELECT
					@SWAdminSiteResourceID, @swodUsageID, @newSeminarID,
					fg.fieldGrouping, fg.fieldGroupingDesc, fg.fieldGroupingOrder
				FROM membercentral.dbo.cf_fieldsGrouping fg
				WHERE fg.fieldControllingSiteResourceID = @SWAdminSiteResourceID
					AND fg.fieldUsageID = @swlUsageID
					AND fg.fieldDetailID = @seminarID;

				UPDATE m
				SET m.oldFieldGroupingID = fg.fieldGroupingID
				FROM #FieldGroupingMapping as m
				INNER JOIN membercentral.dbo.cf_fieldsGrouping fg_new ON fg_new.fieldGroupingID = m.newFieldGroupingID
				INNER JOIN membercentral.dbo.cf_fieldsGrouping AS fg ON fg.fieldControllingSiteResourceID = @SWAdminSiteResourceID
					WHERE fg.fieldUsageID = @swlUsageID
					AND fg.fieldDetailID = @seminarID
					AND fg.fieldGrouping = fg_new.fieldGrouping;

				INSERT INTO membercentral.dbo.cf_fields (
					fieldGroupingID, controllingSiteResourceID, usageID, detailID, fieldTypeID,
					fieldText, fieldReference, isRequired, requiredMsg, adminOnly, displayOnly,
					autoFillRegistrant, amount, GLAccountID, inventory, [uid], fieldOrder
				)
				OUTPUT INSERTED.fieldID, INSERTED.fieldID INTO #FieldMapping
				SELECT
					COALESCE(fgm.newFieldGroupingID, f.fieldGroupingID), @SWAdminSiteResourceID, @swodUsageID, @newSeminarID,
					f.fieldTypeID, f.fieldText, f.fieldReference, f.isRequired, f.requiredMsg,
					f.adminOnly, f.displayOnly, f.autoFillRegistrant, f.amount, f.GLAccountID,
					f.inventory, newid(), f.fieldOrder
				FROM membercentral.dbo.cf_fields f
				LEFT JOIN #FieldGroupingMapping fgm ON fgm.oldFieldGroupingID = f.fieldGroupingID
				WHERE f.usageID = @swlUsageID
					AND f.detailID = @seminarID
					AND f.isActive = 1;

				UPDATE m
				SET m.oldFieldID = f.fieldID
				FROM #FieldMapping as m
				INNER JOIN membercentral.dbo.cf_fields f_new ON f_new.fieldID = m.newFieldID
				INNER JOIN membercentral.dbo.cf_fields AS f ON f.usageID = @swlUsageID
				WHERE f.detailID = @seminarID
					AND f.fieldText = f_new.fieldText
					AND f.fieldOrder = f_new.fieldOrder
					AND f.isActive = 1;

				INSERT INTO membercentral.dbo.cf_fieldValues (fieldID, valueString, amount, inventory, valueOrder,valueDecimal2,valueInteger,valueDate,valueBit,valueSiteResourceID,valueFeatureImageID)
				SELECT fm.newFieldID, fv.valueString, fv.amount, fv.inventory, fv.valueOrder,fv.valueDecimal2,fv.valueInteger,fv.valueDate,fv.valueBit,fv.valueSiteResourceID,fv.valueFeatureImageID
				FROM membercentral.dbo.cf_fieldValues fv
				INNER JOIN #FieldMapping fm ON fm.oldFieldID = fv.fieldID;

				INSERT INTO membercentral.dbo.cf_fieldValueConditions (fieldID, condFieldID, condExp, condValueID)
				SELECT fm1.newFieldID, fm2.newFieldID, fvc.condExp, fvc.condValueID
				FROM membercentral.dbo.cf_fieldValueConditions fvc
				INNER JOIN #FieldMapping fm1 ON fm1.oldFieldID = fvc.fieldID
				INNER JOIN #FieldMapping fm2 ON fm2.oldFieldID = fvc.condFieldID;

				-- Clean up temporary tables
				DROP TABLE #FieldGroupingMapping;
				DROP TABLE #FieldMapping;
			END
		END

		-- re-populate search text
		EXEC dbo.swod_populateSearchText @seminarID=@newSeminarID;
	COMMIT TRAN	

	IF EXISTS(SELECT 1 FROM #tmpLogMessages) BEGIN
		SET @msgjson = @msgjson + @crlf + 'The following actions have been performed:';

		SELECT @msgjson = COALESCE(@msgjson + @crlf, '') + memberCentral.dbo.fn_cleanInvalidXMLChars(msg)
		FROM #tmpLogMessages
		WHERE msg IS NOT NULL;
	END

	INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
	VALUES ('{ "c":"auditLog", "d": {
		"AUDITCODE":"SW",
		"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
		"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
		"ACTORMEMBERID":' + CAST(@recordedByMemberID AS VARCHAR(20)) + ',
		"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
		"MESSAGE":"' + REPLACE(@msgjson,'"','\"') + '" } }');

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO


