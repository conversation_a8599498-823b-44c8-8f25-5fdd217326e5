<cfcomponent extends="model.AppLoader" output="no">
	<cfset defaultEvent = "controller" />
	<cfset variables.applicationReservedURLParams = "ra,refaction,clientReferralID,refDateRange,applyPayment,saveClientBtn" />

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.dataStruct = StructNew();
			
			arguments.event.paramValue('ra','');
			local.objLocator = CreateObject("component","model.system.user.accountLocater");
			local.dataStruct.siteResourceID = this.siteResourceID;
			local.ajaxURL = '/?event=cms.showResource&resID=#local.dataStruct.siteResourceID#&mode=stream';
			arguments.event.paramValue('mainurl','/?#getBaseQueryString(false)#');
			local.dataStruct.mainurl = arguments.event.getValue('mainurl');
			local.frontEndUrl = '/?pg=clientreferrals';
			local.dataStruct.ajaxURL = local.ajaxURL;
			arguments.event.paramValue('referralID',0);
			local.thisAction = arguments.event.getValue('ra');
			local.dataStruct.siteID = arguments.event.getValue('mc_siteinfo.siteid');
			local.dataStruct.orgID = arguments.event.getValue('mc_siteInfo.orgID');
			local.dataStruct.orgCode = arguments.event.getValue('mc_siteInfo.orgcode');
			arguments.event.paramValue('siteID',local.dataStruct.siteID );
			local.dataStruct.objAdminReferrals	= CreateObject("component","model.admin.referrals.referrals");
			local.objReferrals	= CreateObject("component","model.referrals.referrals");
			local.objReferralXML = CreateObject("component","model.admin.referrals.referralsXML");
			local.dataStruct.qryStates = application.objCommon.getStates();
			local.objMemberFieldsets = CreateObject("component","model.system.platform.memberFieldsets");
			local.objResourceCustomFields = createObject("component","model.admin.common.modules.customFields.customFields");
			local.objSMSTemplate = createObject("component","model.admin.common.modules.smsTemplate.smsTemplate");
			// convert times from central (how stored in db) to default timezone of site
			local.objTZ = CreateObject("component","model.system.platform.tsTimeZone");
			local.regTimeZone = local.objTZ.getTZCodeFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));
			local.regTimeZoneName = local.objTZ.getTZFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));		
			local.referralDate = now();
			if (local.regTimeZone neq "US/Central") {
				local.referralDate = local.objTZ.convertTimeZone(dateToConvert=local.referralDate, fromTimeZone='US/Central', toTimeZone=local.regTimeZone);
			}		
			local.qryReferralSettings = getClientReferralSettings(local.dataStruct.siteID);
			local.dataStruct.referralID = val(local.qryReferralSettings.referralID);
			local.feCounselorID = local.qryReferralSettings.feCounselorID;
			local.clientFeeMemberID = local.qryReferralSettings.clientFeeMemberID;
			local.dataStruct.dspLegalDescription = val(local.qryReferralSettings.dspLegalDescription);
			local.dataStruct.feDspSurveyOption = val(local.qryReferralSettings.feDspSurveyOption);
			local.dataStruct.feSurveyOptionDefaultYes = val(local.qryReferralSettings.feSurveyOptionDefaultYes);
			local.dataStruct.feDspBlogOption = val(local.qryReferralSettings.feDspBlogOption);
			local.dataStruct.hideRepFieldsFE = val(local.qryReferralSettings.hideRepFieldsFE);
			local.dataStruct.feDspQuestionTree = val(local.qryReferralSettings.feDspQuestionTree);
			local.dataStruct.feFormInstructionsStep2Content = local.qryReferralSettings.feFormInstructionsStep2Content;
			local.dataStruct.feNoResultsInfoContent = local.qryReferralSettings.feNoResultsInfoContent;
			local.dataStruct.feSuccessfulResultsInstructionsContent = local.qryReferralSettings.feSuccessfulResultsInstructionsContent;
			local.dataStruct.clientPaymentSaleRecorded = 0;
			local.referralAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='Referrals', siteID=local.dataStruct.siteID);
			local.dataStruct.paymentRequired = 0;
			if ((application.objCMS.getTemplateSetting(arguments.event,"supportsBootstrap") eq "true") or (isdefined("session.enableMobile") and session.enableMobile)) {
				local.viewDirectory = arguments.event.getValue('viewDirectory', 'responsive');
			} else {
				local.viewDirectory = arguments.event.getValue('viewDirectory', 'default');
			}
			local.dataStruct.viewDirectory = local.viewDirectory;

			if(local.dataStruct.referralID eq 0)
				local.thisAction = "invalidConfig";
			
			local.ViewData = "";
			local.viewToUse = "echo";
		</cfscript>
		
		<cfoutput>
			<cfswitch expression="#local.thisAction#">
				<cfcase value="invalidConfig">
					<cfsavecontent variable="local.ViewData">
						<cfoutput>Referral Information Service is not configured.</cfoutput>
					</cfsavecontent>
				</cfcase>
				<cfcase value="checkDuplicate">
					<cfset local.canBeReferred = 1 />
					<cfset local.dataStruct.ccError = arguments.event.getValue("ccError","") />
					<cfif val(local.qryReferralSettings.feMaxMemberRefNum)>
						<cfset arguments.event.paramValue('topQueryNum',local.qryReferralSettings.feMaxMemberRefNum) />
					</cfif>
					<cfset arguments.event.setValue('siteResourceID',local.qryReferralSettings.siteResourceID) />
					<cfset arguments.event.setValue('referralID',local.qryReferralSettings.referralID)>
					<cfif arguments.event.getValue('subPanelID1',0) EQ 0>
						<cfset arguments.event.setValue('subPanelID1',"")>
					</cfif>
					<cfif val(local.qryReferralSettings.feDspLimitNumOfReferrals)>
						<cfset arguments.event.setValue('feMaxRefPerClient',local.qryReferralSettings.feMaxRefPerClient) />
						<cfset arguments.event.setValue('feFreqRefPerClient',local.qryReferralSettings.feFreqRefPerClient) />
						<cfset arguments.event.setValue('feIntakeFieldID',local.qryReferralSettings.feIntakeFieldID) />
						<cfset arguments.event.setValue('feDuplicateReferralTxt',local.qryReferralSettings.feDuplicateReferralTxt) />
						<cfset local.canBeReferred = checkClientCanBeReferred(event=arguments.event) />
					</cfif>
					<cfset local.dataStruct.canBeReferred = local.canBeReferred>
					<cfset local.dataStruct.feDuplicateReferralTxt = local.qryReferralSettings.feDuplicateReferralTxt>
					<cfset local.returnResponseStruct = StructNew()>
					<cfset local.returnResponseStruct['status'] = 'success'>
					<cfif not local.canBeReferred>
						<cfset local.returnResponseStruct['status'] = 'fail'>
						<cfset local.returnResponseStruct['referralhtml'] = getDuplicateMessageTemplate(local.dataStruct.feDuplicateReferralTxt,local.frontEndUrl)>
					</cfif>
					<cfreturn returnAppStruct(serializeJSON(local.returnResponseStruct),"echo")>
				</cfcase>
				<cfcase value="search">
					<cfif not arguments.event.valueExists('FIELDNAMES')>	
						<cfset application.objCommon.redirect('#local.dataStruct.mainurl#')>
					</cfif>
					<!--- reset strReferralSearch --->
					<cfset application.mcCacheManager.sessionSetValue(keyname='strReferralSearch', value={})>
					<cfsavecontent variable="local.ViewData">
						<cfset local.dataStruct.ccError = arguments.event.getValue("ccError","") />
						<cfif val(local.qryReferralSettings.feMaxMemberRefNum)>
							<cfset arguments.event.paramValue('topQueryNum',local.qryReferralSettings.feMaxMemberRefNum) />
						</cfif>
						<cfset arguments.event.setValue('searchFieldsetArea','clientreferralsearch') />
						<cfset arguments.event.setValue('resultFieldsetArea','clientreferralresult') />
						<cfset arguments.event.setValue('siteResourceID',local.qryReferralSettings.siteResourceID) />
						<cfset arguments.event.setValue('referralID',local.qryReferralSettings.referralID)>
						<cfif arguments.event.getValue('subPanelID1',0) EQ 0>
							<cfset arguments.event.setValue('subPanelID1',"")>
						</cfif>
						
						<!--- 	Check if client has called before	 --->
						<cfif NOT local.dataStruct.feDspQuestionTree>
							<cfset local.canBeReferred = 1 />
							<cfif val(local.qryReferralSettings.feDspLimitNumOfReferrals)>
								<cfset arguments.event.setValue('feMaxRefPerClient',local.qryReferralSettings.feMaxRefPerClient) />
								<cfset arguments.event.setValue('feFreqRefPerClient',local.qryReferralSettings.feFreqRefPerClient) />
								<cfset arguments.event.setValue('feIntakeFieldID',local.qryReferralSettings.feIntakeFieldID) />
								<cfset arguments.event.setValue('feDuplicateReferralTxt',local.qryReferralSettings.feDuplicateReferralTxt) />
								<cfset local.canBeReferred = checkClientCanBeReferred(event=arguments.event) />
							</cfif>
							<cfset local.dataStruct.canBeReferred = local.canBeReferred>
							<cfset local.dataStruct.feDuplicateReferralTxt = local.qryReferralSettings.feDuplicateReferralTxt>
							
							<cfif not local.canBeReferred>
								<cfset local.returnResponseStruct = StructNew()>
								<cfset local.returnResponseStruct['status'] = 'fail'>
								<cfset local.returnResponseStruct['referralhtml'] = getDuplicateMessageTemplate(local.dataStruct.feDuplicateReferralTxt,local.frontEndUrl)>
								<cfreturn returnAppStruct(local.returnResponseStruct.referralhtml,"echo")>
							</cfif>
						</cfif>
						
						<cfset local.dataStruct.qryGetSearchResults = local.dataStruct.objAdminReferrals.getSearchResults(event=arguments.event) />
						<cfset local.panelID = arguments.event.getValue("panelid1",0) />

						<cfset local.dataStruct.qryPanelData = local.dataStruct.objAdminReferrals.getPanelByID(panelID=local.panelID) />						
						<cfset local.dataStruct.clientReferralAmount = local.dataStruct.qryPanelData.clientReferralAmount />
						<cfset local.dataStruct.feAllowClientReferral = local.dataStruct.qryPanelData.feAllowClientReferral />

						<cfif local.dataStruct.qryGetSearchResults.recordCount>	
							<cfset application.mcCacheManager.sessionSetValue(keyname='strReferralSearch', value={ "qryGetSearchResults":local.dataStruct.qryGetSearchResults })>
							
							<cfif val(local.dataStruct.clientReferralAmount)>
								<!--- Processing Fees --->
								<cfset local.enableProcessingFee = 0>
								<cfset local.selProcessingFee = 0>
								<cfset local.processFeeLabel = "">
								<cfset local.processingFee = 0>
								<cfif val(local.qryReferralSettings.fePayProfileID)>
									<cfset local.useGLAcctID = val(local.dataStruct.qryPanelData.clientFeeGLAccountID) ? val(local.dataStruct.qryPanelData.clientFeeGLAccountID) : local.qryReferralSettings.feGLAccountID>
									<cfset local.qryMerchantProfile = getMerchantProfileDetails(profileID=local.qryReferralSettings.fePayProfileID, GLAccountID=local.useGLAcctID)>
									<cfset local.enableProcessingFee = local.qryMerchantProfile.enableProcessingFeeDonation AND val(local.qryMerchantProfile.processFeeDonationFeePercent) GT 0>
									<cfif local.enableProcessingFee>
										<cfset var strProcessingFees = application.objPayments.getPaymentProcessingFees(gl=local.qryMerchantProfile.processFeeDonationRenevueGLAccountID, amt=local.dataStruct.clientReferralAmount, 
												feepct=local.qryMerchantProfile.processFeeDonationFeePercent, stateIDForTax=val(arguments.event.getValue('state',0)), zipForTax=arguments.event.getTrimValue('postalCode',''))>
										<cfset local.processingFee = strProcessingFees.processingfees>
										<cfset local.selProcessingFee = val(local.qryMerchantProfile.processFeeDonationDefaultSelect) EQ 1 ? 1 : 0>
										<cfset local.processFeeLabel = replaceNoCase(local.qryMerchantProfile.processFeeOtherPaymentsFELabel,"{{AMOUNT}}",strProcessingFees.processingfeesDspLabel)>
									</cfif>
								</cfif>
								<cfset local.chargeInfo = { "amt":local.dataStruct.clientReferralAmount, "processingfees":local.processingFee }>
								<cfset local.strPaymentFeatures = application.objPayments.setDefaultPayFeaturesStruct()>
								<cfset local.strPaymentFeatures.processingFee = { 
									"enable":local.enableProcessingFee, 
									"select":local.selProcessingFee, 
									"label":local.processFeeLabel, 
									"denylabel":local.qryMerchantProfile.processFeeOtherPaymentsFEDenyLabel,
									"title":local.qryMerchantProfile.processFeeDonationFETitle,
									"msg":local.qryMerchantProfile.processFeeDonationFEMsg
								}>
								<cfset local.strPaymentFeatures.surcharge.enable = local.qryMerchantProfile.enableSurcharge>
								<cfset local.strPaymentFeatures.applePay.enable = local.qryMerchantProfile.enableApplePay>
								<cfset local.strPaymentFeatures.googlePay.enable = local.qryMerchantProfile.enableGooglePay>
								<cfif NOT local.dataStruct.feDspQuestionTree>
									<cfset local.dataStruct.profile_1 = structNew() />
									<cfset local.dataStruct.profile_1.profileID = local.qryReferralSettings.fePayProfileID />
									<cfset local.dataStruct.profile_1.profileCode = local.qryReferralSettings.fePayProfileCode />
									
									<cfset local.dataStruct.profile_1.strPaymentForm = application.objPayments.showGatewayInputForm(siteid=local.dataStruct.siteID, profilecode=local.dataStruct.profile_1.profileCode,
												pmid=local.clientFeeMemberID, showCOF=false, usePopup=false, usePopupDIVName='ccForm', autoShowForm=1, paymentFeatures=local.strPaymentFeatures, chargeInfo=local.chargeInfo)>

									<cfif len(local.dataStruct.profile_1.strPaymentForm.headCode)>
										<cfhtmlhead text="#application.objCommon.minText(local.dataStruct.profile_1.strPaymentForm.headCode)#">
									</cfif>
									<cfif val(local.qryReferralSettings.collectClientFeeFE)><!--- skip payment in front end --->	
										<cfset local.datastruct = processReferral(event=arguments.event,dataStruct=local.dataStruct,referralSettings=local.qryReferralSettings)>
										<cfset local.viewToUse = 'clientReferrals/#local.viewDirectory#/dsp_clientReferralResults'>	
										<cfset application.mcCacheManager.sessionDeleteValue(keyname='strReferralSearch')>
									<cfelse>
										<cfset local.viewToUse = 'clientReferrals/#local.viewDirectory#/frm_clientReferralPayment'>
									</cfif>
								</cfif>
								
								<cfif local.dataStruct.feDspQuestionTree>	
									<cfset local.dataStruct.subPanelIds = arguments.event.getValue("subPanelID1",0) />
									<cfset local.dataStruct.subPanelNames = "">
									<cfloop list="#local.dataStruct.subPanelIDs#" item="local.subPanelID">
										<cfset local.qrySubPanelData = local.dataStruct.objAdminReferrals.getPanelByID(panelID=local.subPanelID) />
										<cfset local.dataStruct.subPanelNames = ListAppend(local.dataStruct.subPanelNames,local.qrySubPanelData.name)>
									</cfloop>

									<cfset local.returnResponseStruct = StructNew()>
									<cfset local.returnResponseStruct["status"] = "attorneyfound">
									<cfsavecontent variable="local.returnResponseStruct.referralhtml">
										<cfoutput>
											<cfif len(trim(local.dataStruct.feFormInstructionsStep2Content))>
												<div class="row-fluid control-group stepInstructions">				
													#local.dataStruct.feFormInstructionsStep2Content#							
												</div>	
											</cfif>
											<div class="row-fluid">
												<div class="questionLRISWrapper">
													<fieldset tabindex="0" >
														<legend><cfif len(trim(local.qryReferralSettings.feReferralInforMatchTitle))>#trim(local.qryReferralSettings.feReferralInforMatchTitle)#<cfelse>Referral Information</cfif></legend>
														<div class="innerLRISContentWrapper">
															<p>Based on your criteria, #local.dataStruct.qryGetSearchResults.recordCount# record(s) match your criteria in the following area of law:</p>
															<br>
															<div class="form-group">
																<div class="well well-small" arial-label="Area of Law: #local.dataStruct.qryPanelData.name#. <cfif ListLen(local.dataStruct.subPanelNames) GT 0>Specialization(s): #local.dataStruct.subPanelNames#</cfif>">
																	Area of Law: #local.dataStruct.qryPanelData.name#
																	<cfif ListLen(local.dataStruct.subPanelNames) GT 0>
																	<br>Specialization(s): #local.dataStruct.subPanelNames#
																	</cfif>
																</div>
															</div>
															<p>There is a <b>#dollarFormat(local.dataStruct.clientReferralAmount)# referral fee</b> for this panel. 
																<cfif len(trim(local.dataStruct.feSuccessfulResultsInstructionsContent))>				
																	#local.dataStruct.feSuccessfulResultsInstructionsContent#	
																</cfif>
															</p>
															<br>
															<div class="control-group clearfix">
																
																<div class="span12 text-center">
																	<button class="btn btn-default btnGoToStep1" type="button">Previous </button>
																	<button class="btn btn-primary btnAccept" type="button">Accept and Continue</button>
																</div>
															</div>
														</div>
													</fieldset>
												</div>
											</div>
										</cfoutput>
									</cfsavecontent>
									<cfset local.returnResponseStruct["chargeinfo"] = { "amt":local.dataStruct.clientReferralAmount, "processingfees":local.processingFee }>
									<cfif local.processingFee GT 0>
										<cfset local.returnResponseStruct["fepayprofileid"] = local.qryReferralSettings.fePayProfileID>
										<cfset local.returnResponseStruct["processfeelabel"] = local.processFeeLabel>
										<cfset local.returnResponseStruct["chargeinfo"]["amtincprocessingfees"] = numberFormat(precisionEvaluate(local.dataStruct.clientReferralAmount + local.processingFee),"0.00")>
									</cfif>
									<cfreturn returnAppStruct(serializeJSON(local.returnResponseStruct),"echo")>
								</cfif>
							<cfelse>
							
								<cfif local.dataStruct.feDspQuestionTree>
									<cfset local.dataStruct.subPanelIds = arguments.event.getValue("subPanelID1",0) />
									<cfset local.dataStruct.subPanelNames = "">
									<cfloop list="#local.dataStruct.subPanelIDs#" item="local.subPanelID">
										<cfset local.qrySubPanelData = local.dataStruct.objAdminReferrals.getPanelByID(panelID=local.subPanelID) />
										<cfset local.dataStruct.subPanelNames = ListAppend(local.dataStruct.subPanelNames,local.qrySubPanelData.name)>
									</cfloop>
								

									<cfset local.returnResponseStruct = StructNew()>
									<cfset local.returnResponseStruct["status"] = "attorneyfound">
									<cfsavecontent variable="local.returnResponseStruct.referralhtml">
										<cfoutput>
											<div class="row-fluid">
												<div class="questionLRISWrapper">
													<fieldset tabindex="0" aria-label="<cfif len(trim(local.qryReferralSettings.feReferralInforMatchTitle))>#trim(local.qryReferralSettings.feReferralInforMatchTitle)#<cfelse>Referral Information</cfif>">
														<legend><cfif len(trim(local.qryReferralSettings.feReferralInforMatchTitle))>#trim(local.qryReferralSettings.feReferralInforMatchTitle)#<cfelse>Referral Information</cfif></legend>
														<div class="innerLRISContentWrapper">
															<p>Based on your criteria, #local.dataStruct.qryGetSearchResults.recordCount# record(s) match your criteria in the following area of law:</p>
															<br>
															<div class="well well-small" arial-label="Area of Law: #local.dataStruct.qryPanelData.name#. <cfif ListLen(local.dataStruct.subPanelNames) GT 0>Specialization(s): #local.dataStruct.subPanelNames#</cfif>">
																Area of Law: #local.dataStruct.qryPanelData.name#
																<cfif ListLen(local.dataStruct.subPanelNames) GT 0>
																	<br>Specialization(s): #local.dataStruct.subPanelNames#
																</cfif>
															</div>
															<p>
																<cfif len(trim(local.dataStruct.feSuccessfulResultsInstructionsContent))>				
																	#local.dataStruct.feSuccessfulResultsInstructionsContent#	
																</cfif>
															</p>
															<br>
															<div class="control-group clearfix">
																
																<div class="span12 text-center">
																	<button class="btn btn-default btnGoToStep1" type="button">Previous </button>
																	<button class="btn btn-primary btnAccept" type="button">Accept and Continue</button>
																</div>
															</div>
														</div>
													</fieldset>
												</div>
											</div>
										</cfoutput>
									</cfsavecontent>
									<cfreturn returnAppStruct(serializeJSON(local.returnResponseStruct),"echo")>
								<cfelse>
									<!--- create referral record --->		
									<cfset local.datastruct = processReferral(event=arguments.event,dataStruct=local.dataStruct,referralSettings=local.qryReferralSettings)>
									<cfset local.viewToUse = 'clientReferrals/#local.viewDirectory#/dsp_clientReferralResults'>	
									<cfset application.mcCacheManager.sessionDeleteValue(keyname='strReferralSearch')>
								</cfif>
							</cfif>
						<cfelse>
							<!--- reset strReferralSearch --->
							<cfset application.mcCacheManager.sessionSetValue(keyname='strReferralSearch', value={})>
							<cfif local.dataStruct.feDspQuestionTree>
								<cfset local.returnResponse = StructNew()>
								<cfset local.returnResponse["status"]="noattorneysfound">
								<cfset application.mcCacheManager.sessionSetValue(keyname='noAttorneysFound', value=true)>
								<cfreturn returnAppStruct(serializeJSON(local.returnResponse),"echo")>
							<cfelse>
								<cfsavecontent variable="local.css">
									<style type="text/css">
									ul.groupImage { width:100%; text-align:left; float:left; padding:0; padding-left:0; }
									ul.groupImage li.groupImage { float:left; padding:0; padding-left:0; list-style-type:none; list-style-position:inside; }
									div##divclientReferralAmount { margin-left:40px; font-size: 110%; }
									</style>
									<script language="JavaScript">
									$(document).ready(function(){
										$('.goBack').click(function(){
											var errorMsg = "";
											$("##frmClient").attr('action', '<cfoutput>#local.dataStruct.mainurl#</cfoutput>');
											$('##frmClient').submit();
										});	
									});						
									</script>	
								</cfsavecontent>
								<cfhtmlhead text="#local.css#">	
			
								<cfform name="frmClient" id="frmClient" method="POST">
									<cfloop collection="#form#" item="local.thisField">
										<cfinput type="hidden" name="#local.thisField#"  id="#local.thisField#" value="#form[local.thisField]#" />
									</cfloop>		
									<cfoutput>
									<cfif local.viewDirectory eq "responsive">
										<div>
											<b>Sorry, there are no results based on your search criteria.</b>
											<div style="margin:20px 0;">#local.dataStruct.feNoResultsInfoContent#</div>
										</div>
										<div><a href="##" class="goBack"><button type="button" class="btn btn-default">Go back</button></a> to previous page.</div>
									<cfelse>
										<div class="tsAppBodyText">
											<b>Sorry, there are no results based on your search criteria. #local.dataStruct.feNoResultsInfoContent#</b>
											<div style="margin:20px 0;">#local.dataStruct.feNoResultsInfoContent#</div>
										</div>
										<div><button type="button"><a href="##" class="goBack">Go back</a></button> to previous page.</div><br/>
									</cfif>		
									</cfoutput>
								</cfform>
							</cfif>					
																				
						</cfif>	
					</cfsavecontent>		
				</cfcase>

				<cfcase value="checkout">
					<cfsetting requesttimeout="600">
					<cfif not arguments.event.valueExists('FIELDNAMES')>	
						<cfset application.objCommon.redirect('#local.dataStruct.mainurl#')>
					</cfif>
					<cfset local.checkOutFailed = false>
					<cfset local.panelID = arguments.event.getValue("panelid1",0)>
					<cfset local.dataStruct.qryPanelData = local.dataStruct.objAdminReferrals.getPanelByID(panelID=local.panelID)>
					<cfset local.dataStruct.clientReferralAmount = local.dataStruct.qryPanelData.clientReferralAmount>
					
					<cfif val(local.dataStruct.clientReferralAmount)>
						<cfset local.dataStruct.profile_1 = structNew() />
						<cfset local.dataStruct.profile_1.profileID = local.qryReferralSettings.fePayProfileID />
						<cfset local.dataStruct.profile_1.profileCode = local.qryReferralSettings.fePayProfileCode />
						<cfset local.ccRevenueGLAccountCode = local.qryReferralSettings.faAccountCode />
						<cfif val(local.dataStruct.qryPanelData.clientFeeGLAccountID)>
							<cfset local.tmpStrAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=val(local.dataStruct.qryPanelData.clientFeeGLAccountID), orgID=arguments.event.getValue('mc_siteInfo.orgID')) />
							<cfset local.ccRevenueGLAccountCode = local.tmpStrAccount.qryAccount.AccountCode />
						</cfif>
						<cfset local.useGLAcctID = val(local.dataStruct.qryPanelData.clientFeeGLAccountID) ? val(local.dataStruct.qryPanelData.clientFeeGLAccountID) : local.qryReferralSettings.feGLAccountID>	

						<!--- ---------------------- --->
						<!--- Payment and accounting --->
						<!--- ---------------------- --->
						<cfif local.qryReferralSettings.collectClientFeeFE><!---skip payment in front end--->
							
							<cfset local.datastruct = processReferral(event=arguments.event,dataStruct=local.dataStruct,referralSettings=local.qryReferralSettings)>
							
						<cfelse>

							<!--- invalid session --->
							<cfset local.strRefSearch = application.mcCacheManager.sessionGetValue(keyname='strReferralSearch', defaultValue={})>
							<cfif NOT (local.strRefSearch.count() AND structKeyExists(local.strRefSearch,"qryGetSearchResults"))>
								<cfreturn returnAppStruct("There was an error with your submission. Please try again.","echo")>
							</cfif>

							<cfset local.qryMerchantProfile = getMerchantProfileDetails(profileID=val(local.qryReferralSettings.fePayProfileID), GLAccountID=local.useGLAcctID)>
							<cfset local.strAccTemp = { totalPaymentAmount=local.dataStruct.clientReferralAmount, 
														assignedToMemberID=local.clientFeeMemberID , 
														recordedByMemberID=local.feCounselorID, 
														rc=arguments.event.getCollection() } />
							<cfset local.strAccTemp.payment = { detail="#arguments.event.getValue('mc_siteInfo.ORGShortName')# - Client Referral - #arguments.event.getTrimValue('firstName')# - #arguments.event.getTrimValue('lastName')#",
																amount=local.strAccTemp.totalPaymentAmount, 
																profileID=local.dataStruct.profile_1.profileID, 
																profileCode=local.dataStruct.profile_1.profileCode,
																supportPaymentFees=1, stopOnError=1, qryMerchantProfile=local.qryMerchantProfile,
																taxInfo: { stateIDForTax=val(arguments.event.getTrimValue('state',0)), zipForTax=arguments.event.getTrimValue('postalCode','') } }>
							<cfset local.strAccTemp.revenue  = [
								{
									revenueGLAccountCode=local.ccRevenueGLAccountCode, 
									detail="FE - Client Referral Fee | #arguments.event.getTrimValue('firstName')# #arguments.event.getTrimValue('lastName')# | #local.dataStruct.qryPanelData.name#", 
									amount=local.strAccTemp.totalPaymentAmount
								}
							]>
							
							<!--- Processing Fees --->
							<cfset local.offeredPaymentFee = val(local.qryMerchantProfile.enableProcessingFeeDonation) AND val(local.qryMerchantProfile.processFeeDonationFeePercent) GT 0>
							<cfset local.payProcessFee = local.offeredPaymentFee AND arguments.event.getValue('processFeeDonation#local.qryReferralSettings.fePayProfileID#',0) EQ 1>
							
							<cfif listFindNoCase("AuthorizeCCCIM",local.qryMerchantProfile.gatewayType)>
								<cfset local.strAccTemp.payment["qryLevel3Data"] = QueryNew("name,desc,itemPriceExcDiscount,itemPriceIncDiscount,discount,qty,total","varchar,varchar,decimal,decimal,decimal,decimal,decimal")>
								<cfset QueryAddRow(local.strAccTemp.payment["qryLevel3Data"], {
									"name": "FE - Client Referral Fee",
									"desc": "#arguments.event.getValue('mc_siteInfo.ORGShortName')# - Client Referral - #arguments.event.getTrimValue('firstName')# - #arguments.event.getTrimValue('lastName')#",
									"itemPriceExcDiscount": local.strAccTemp.totalPaymentAmount,
									"itemPriceIncDiscount": local.strAccTemp.totalPaymentAmount,
									"discount": 0,
									"qty": 1,
									"total": local.strAccTemp.totalPaymentAmount
								})>
							</cfif>
							<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>
							<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>

							<cfif local.strACCResponse.paymentResponse.responseCode eq 1 and local.strACCResponse.paymentResponse.mc_transactionID gt 0>					
								<cfif val(local.strACCResponse.revenueResponse[1].transactionID)>
									<cfset arguments.event.setValue("tID",local.strACCResponse.revenueResponse[1].transactionID)>
									<cfset local.datastruct = processReferral(event=arguments.event,dataStruct=local.dataStruct,referralSettings=local.qryReferralSettings)>
								<cfelse>
									
									<cfset local.checkOutFailed = true>

									<cfsavecontent variable="local.selectFormJS">
										<cfoutput>
											<script language="JavaScript">
												$(document).ready(function(){
													<cfif  local.dataStruct.feDspQuestionTree>
														$("##frmClient").attr('action', '#local.dataStruct.mainurl#');			
														$("##frmClient").submit();
													<cfelse>
														$("##frmClient").attr('action', '#local.dataStruct.mainurl#&ra=search&ccError=1');			
														$("##frmClient").submit();
													</cfif>
												});
											</script>
										</cfoutput>
									</cfsavecontent>
									<cfhtmlhead text="#application.objCommon.minText(local.selectFormJS)#" />

									<cfsavecontent variable="local.ViewData">
										<cfoutput>
										<cfform name="frmClient" id="frmClient" method="POST">
											<cfloop collection="#form#" item="local.thisField">
												<cfinput type="hidden" name="#local.thisField#"  id="#local.thisField#" value="#form[local.thisField]#" />
											</cfloop>
											<cfif local.dataStruct.feDspQuestionTree>
												<cfinput type="hidden" name="ccError" value="1">
											</cfif>
										</cfform>
										</cfoutput>
									</cfsavecontent>
													
								</cfif>							
							<cfelse>
								<cfsavecontent variable="local.selectFormJS">
									<cfoutput>
										<script language="JavaScript">
											$(document).ready(function(){	
												<cfif  local.dataStruct.feDspQuestionTree>
													$("##frmClient").attr('action', '#local.dataStruct.mainurl#');			
													$("##frmClient").submit();
												<cfelse>
													$("##frmClient").attr('action', '#local.dataStruct.mainurl#&ra=search&ccError=1');			
													$("##frmClient").submit();
												</cfif>
											});
										</script>
									</cfoutput>
								</cfsavecontent>
								<cfhtmlhead text="#application.objCommon.minText(local.selectFormJS)#" />

								<cfsavecontent variable="local.ViewData">
									<cfoutput>
									<cfform name="frmClient" id="frmClient" method="POST">
										<cfloop collection="#form#" item="local.thisField">
											<cfinput type="hidden" name="#local.thisField#" id="#local.thisField#" value="#form[local.thisField]#" />
										</cfloop>
										<cfif local.dataStruct.feDspQuestionTree>
											<cfinput type="hidden" name="ccError" value="1">
										</cfif>
									</cfform>
									</cfoutput>
								</cfsavecontent>

								<cfset local.checkOutFailed = true>

							</cfif>
						</cfif>
					<cfelse>
						<cfset local.datastruct = processReferral(event=arguments.event,dataStruct=local.dataStruct,referralSettings=local.qryReferralSettings)>
					</cfif>

					<!--- reset strReferralSearch --->
					<cfif NOT local.checkOutFailed>
						<cfset application.mcCacheManager.sessionSetValue(keyname='strReferralSearch', value={})>
						<cfset application.mcCacheManager.sessionSetValue(keyname='refResults', value=local.datastruct)>
						<cflocation url="#local.dataStruct.mainurl#&ra=results" addtoken="false">
					</cfif>
				</cfcase>

				<cfcase value="results">
					<cfset local.refResults = application.mcCacheManager.sessionGetValue(keyname='refResults', defaultValue={})>
					<cfif local.refResults.count()>
						<cfset local.dataStruct.append(local.refResults)>
						<cfset local.viewToUse = 'clientReferrals/#local.viewDirectory#/dsp_clientReferralResults'>
						<cfset application.mcCacheManager.sessionSetValue(keyname='refResults', value={})>
					<cfelse>
						<cflocation url="#local.dataStruct.mainurl#" addtoken="false">
					</cfif>
				</cfcase>

				<cfcase value="refer">
					<cfset local.datastruct = processReferral(event=arguments.event,dataStruct=local.dataStruct,referralSettings=local.qryReferralSettings)>
					<cfset local.viewToUse = 'clientReferrals/#local.viewDirectory#/dsp_clientReferralResults'>				
				</cfcase>

				<cfcase value="submitcontact">
					<cfsetting requesttimeout="600">

					<cfset local.noAttorneysFound = application.mcCacheManager.sessionGetValue(keyname='noAttorneysFound', defaultValue=false)>
					<cfif not local.noAttorneysFound>
						<cfset application.objCommon.redirect('#local.dataStruct.mainurl#')>
					</cfif>
					<cfif arguments.event.valueExists('isCellPhone')>
						<cfset arguments.event.setValue('cellPhone',arguments.event.getValue('homePhone',''))>
						<cfset arguments.event.setValue('homePhone','')>
					</cfif>
					<cfset local.datastruct = processReferral(event=arguments.event,dataStruct=local.dataStruct,referralSettings=local.qryReferralSettings,noMemberFound=1)>
					<cfset local.viewToUse = 'clientReferrals/#local.viewDirectory#/dsp_clientReferralResults'>
					<cfset application.mcCacheManager.sessionDeleteValue(keyname='noAttorneysFound')>
					<cfset application.mcCacheManager.sessionSetValue(keyname='strReferralSearch', value={})>
				</cfcase>
				
				<cfcase value="subPanelData">
					<cfreturn returnAppStruct(local.dataStruct.objAdminReferrals.getSubPanelsByID(panelid=arguments.event.getValue('panelid',0), isActive=arguments.event.getValue('isActive',0)),"echo") />
				</cfcase>

				<cfdefaultcase>
					<cfset local.qryClientSearchFieldSet = local.dataStruct.objAdminReferrals.getLocatorFieldsetID(siteResourceID=local.qryReferralSettings.siteResourceID, area='clientreferralsearch') />
					<cfset local.dataStruct.additionalFiltersFieldSet = local.qryClientSearchFieldSet.fieldsetID>
					<cfset local.dataStruct.qryActiveSMSTemplate = local.objSMSTemplate.getSMSTemplateDetails(local.dataStruct.siteID,'REFCLIENTS','REFERRAL_SENT')>
					
					<cfset local.dataStruct.xmlFields = local.objMemberFieldsets.getMemberFieldsXML(fieldsetid=val(local.qryClientSearchFieldSet.fieldsetID), usage="memberReferralSearch") />
					<!--- Initialize local variables and append custom field names to field names list --->
					<cfif isDefined("local.dataStruct.xmlFields.xmlRoot.xmlChildren") and arrayLen(local.dataStruct.xmlFields.xmlRoot.xmlChildren)>	
						<cfloop array="#local.dataStruct.xmlFields.xmlRoot.xmlChildren#" index="local.thisfield">
							<cfset "local.dataStruct.#local.thisfield.xmlattributes.fieldCode#" = "" />
							<cfif not arguments.event.valueExists('copy')>
								<cfset "local.dataStruct.#local.thisfield.xmlattributes.fieldCode#" = arguments.event.getValue('#local.thisfield.xmlattributes.fieldCode#','') />
							</cfif>
							<cfif ReFindNoCase('mat?_[0-9]+_postalcode',local.thisfield.xmlattributes.fieldCode) or findNoCase('_proximity',local.thisfield.xmlattributes.dbField)>
								<cfset "local.dataStruct.#local.thisfield.xmlattributes.fieldCode#_radius" = "" />
								<cfif not arguments.event.valueExists('copy')>
									<cfset "local.dataStruct.#local.thisfield.xmlattributes.fieldCode#_radius" = arguments.event.getValue('#local.thisfield.xmlattributes.fieldCode#_radius','') />
								</cfif>
							</cfif>
						</cfloop>
					</cfif>									
					<cfset local.dataStruct.qryGetClassifications = local.dataStruct.objAdminReferrals.getClassifications(local.dataStruct.referralID) />						
					<!--- Initialize local variables and append classification fields to field names list --->
					<cfloop query="local.dataStruct.qryGetClassifications">
						<cfif val(local.dataStruct.qryGetClassifications.allowSearch)>
							<cfset "local.dataStruct.mg_gid_#local.dataStruct.qryGetClassifications.groupSetID#" = "" />
							<cfif not arguments.event.valueExists('copy')>
								<cfset "local.dataStruct.mg_gid_#local.dataStruct.qryGetClassifications.groupSetID#" = arguments.event.getValue('mg_gid_#local.dataStruct.qryGetClassifications.groupSetID#','') />
							</cfif>
						</cfif>
					</cfloop>	

					<cfscript>
						local.dataStruct.fieldsetInfo = structNew();
						local.dataStruct.fieldsetInfo.fieldsetName = local.qryClientSearchFieldSet.fieldsetName;
						local.dataStruct.fieldsetInfo.nameFormat = local.qryClientSearchFieldSet.nameFormat;
						local.dataStruct.fieldsetInfo.showHelp = local.qryClientSearchFieldSet.showHelp;		
						local.dataStruct.showReqFlag = false;						
						local.dataStruct.msgText = "";
						local.isThisRefRecord = 1;
						local.isThisCaseRecord = 0;
						local.dataStruct.clientReferralID = arguments.event.getValue('clientReferralID','0');
						local.dataStruct.qryGetLanguages 	= local.dataStruct.objAdminReferrals.getReferralLanguages(local.dataStruct.referralID);
						arguments.event.setValue('referralID',local.dataStruct.referralID);
						local.dataStruct.qryGetPanelsFilter = local.dataStruct.objAdminReferrals.getPanels(referralID=local.dataStruct.referralID, statusName="Active");
						local.dataStruct.panelid1 = arguments.event.getValue('panelid1',0);
						if (val(local.dataStruct.panelid1))
							local.dataStruct.qryGetSubPanelsFilter1 = local.dataStruct.objAdminReferrals.getPanelChildrenByID(panelid=local.dataStruct.panelid1, isActive=1);
						local.dataStruct.subpanelid1 = arguments.event.getValue('subpanelid1','');
						local.dataStruct.fePanelInfoContent = local.qryReferralSettings.fePanelInfoContent;
						local.dataStruct.feNoResultsInfoContent = local.qryReferralSettings.feNoResultsInfoContent;
						local.dataStruct.feLegalDescInstructContent = local.qryReferralSettings.feLegalDescInstructContent;
						local.dataStruct.feFormInstructionsContent = local.qryReferralSettings.feFormInstructionsContent;

						local.dataStruct.feFormInstructionsStep1Content = local.qryReferralSettings.feFormInstructionsStep1Content;
						local.dataStruct.feFormInstructionsStep2Content = local.qryReferralSettings.feFormInstructionsStep2Content;
						local.dataStruct.feFormInstructionsStep3Content = local.qryReferralSettings.feFormInstructionsStep3Content;
						local.dataStruct.feFormInstructionsStep4Content = local.qryReferralSettings.feFormInstructionsStep4Content;
						local.dataStruct.feSuccessfulResultsInstructionsContent = local.qryReferralSettings.feSuccessfulResultsInstructionsContent;
						
						local.qryCountries = application.objCommon.getCountries();
						local.countryCode = QueryFilter(local.qryCountries, function(thisRow) { return arguments.thisRow.countryID EQ application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).defaultCountryID; }).countryCode;

						/* Client Data */
						local.dataStruct.countryCode = local.countryCode;
						local.dataStruct.firstName = arguments.event.getValue('firstName','');
						local.dataStruct.middleName = arguments.event.getValue('middleName','');
						local.dataStruct.lastName = arguments.event.getValue('lastName','');
						local.dataStruct.businessName = arguments.event.getValue('businessName','');
						local.dataStruct.address1 = arguments.event.getValue('address1','');
						local.dataStruct.address2 = arguments.event.getValue('address2','');
						local.dataStruct.address3 = arguments.event.getValue('address3','');
						local.dataStruct.city = arguments.event.getValue('city','');
						if(not len(local.dataStruct.city))
							local.dataStruct.city = local.qryReferralSettings.defaultCity; 						
						local.dataStruct.state = arguments.event.getValue('state',0);
						if(not val(local.dataStruct.state))
							local.dataStruct.state = local.qryReferralSettings.defaultStateID; 
						local.dataStruct.postalCode = arguments.event.getValue('postalCode','');
						local.countryID = arguments.event.getValue('countryID','');
						local.dataStruct.email = trim(arguments.event.getValue('email',''));
						local.dataStruct.homePhone = arguments.event.getValue('homePhone','');
						local.dataStruct.cellPhone = arguments.event.getValue('cellPhone','');
						local.dataStruct.alternatePhone = arguments.event.getValue('alternatePhone','');

						local.dataStruct.homePhoneE164 = arguments.event.getValue('homePhoneE164','');
						local.dataStruct.homePhoneNational = arguments.event.getValue('homePhoneNational','');
						local.dataStruct.cellPhoneE164 = arguments.event.getValue('cellPhoneE164','');
						local.dataStruct.cellPhoneNational = arguments.event.getValue('cellPhoneNational','');
						local.dataStruct.alternatePhoneE164 = arguments.event.getValue('alternatePhoneE164','');
						local.dataStruct.alternatePhoneNational = arguments.event.getValue('alternatePhoneNational','');

						local.dataStruct.clientParentID = arguments.event.getValue('clientParentID',0);	
						/* Rep Data */
						local.dataStruct.isRep = arguments.event.getValue('isRep',0);
						local.dataStruct.repID = arguments.event.getValue('repID',0);
						local.dataStruct.repFirstName = arguments.event.getValue('repFirstName','');
						local.dataStruct.repLastName = arguments.event.getValue('repLastName','');
						local.dataStruct.relationToClient = arguments.event.getValue('relationToClient','');
						local.dataStruct.repAddress1 = arguments.event.getValue('repAddress1','');
						local.dataStruct.repAddress2 = arguments.event.getValue('repAddress2','');
						local.dataStruct.repCity = arguments.event.getValue('repCity','');
						local.dataStruct.repState = arguments.event.getValue('repState',0);
						local.dataStruct.repPostalCode = arguments.event.getValue('repPostalCode','');
						local.dataStruct.repEmail = trim(arguments.event.getValue('repEmail',''));
						local.dataStruct.repHomePhone = arguments.event.getValue('repHomePhone','');
						local.dataStruct.repCellPhone = arguments.event.getValue('repCellPhone','');
						local.dataStruct.repAlternatePhone = arguments.event.getValue('repAlternatePhone','');

						local.dataStruct.repHomePhoneE164 = arguments.event.getValue('repHomePhoneE164','');
						local.dataStruct.repHomePhoneNational = arguments.event.getValue('repHomePhoneNational','');
						local.dataStruct.repCellPhoneE164 = arguments.event.getValue('repCellPhoneE164','');
						local.dataStruct.repCellPhoneNational = arguments.event.getValue('repCellPhoneNational','');
						local.dataStruct.repAlternatePhoneE164 = arguments.event.getValue('repAlternatePhoneE164','');
						local.dataStruct.repAlternatePhoneNational = arguments.event.getValue('repAlternatePhoneNational','');

						local.dataStruct.repParentID = arguments.event.getValue('repParentID',0);	
						/* Referral Data */
						local.dataStruct.clientReferralID = arguments.event.getValue('clientReferralID',0);					
						local.dataStruct.communicateLanguageID = arguments.event.getValue('communicateLanguageID','');
						local.dataStruct.issueDesc = arguments.event.getValue('issueDesc','');
						local.dataStruct.sendSurvey = arguments.event.getValue('sendSurvey',0);
						local.dataStruct.sendNewsBlog = arguments.event.getValue('sendNewsBlog',0);
						local.dataStruct.referralCanEditClient = 1;
						local.dataStruct.referralPageTitle = "Client Referral";	
						local.dataStruct.labelTDwidth = "18%";
						local.dataStruct.responsiveFieldwidth = "min-width: 15em; width: 60%;";						
						local.dataStruct.extraInformation = local.objResourceCustomFields.renderResourceFields(siteID=local.dataStruct.siteID,	viewMode=local.viewDirectory, 
						resourceType='ClientReferrals', areaName='ClientReferrals', csrid=local.referralAdminSiteResourceID, detailID=0, 
						hideAdminOnly=1, itemType='ClientRefCustom', itemID=0, trItemType='', trApplicationType='');
						if( local.dataStruct.feDspQuestionTree){
							local.dataStruct.referralSettingsQry = local.dataStruct.objAdminReferrals.getReferralSettings(arguments.event.getValue('mc_siteInfo.siteID'));
							local.clientFeeMemberID = local.qryReferralSettings.clientFeeMemberID;
							local.dataStruct.strRefQuestionAnswers = getReferralQuestionAnswers(mcproxy_siteID=local.dataStruct.siteID, questionID=0, answerID=0, currentQAMode="init");
						}
						local.qryGetIntakeFormFieldDetails = local.dataStruct.objAdminReferrals.getIntakeFormFieldDetails(referralID=local.dataStruct.referralID);
						local.dataStruct.saleError = arguments.event.getValue('saleError',0);
						local.dataStruct.collectClientFeeFE = local.qryReferralSettings.collectClientFeeFE;
						local.dataStruct.feLegalDescLimitWords = local.qryReferralSettings.feLegalDescLimitWords;
						local.dataStruct.feLegalDescLimitWordCount = local.qryReferralSettings.feLegalDescLimitWordCount;
						local.dataStruct.feLegalDescLimitExceedMsg = local.qryReferralSettings.feLegalDescLimitExceedMsg;
					</cfscript>
					
					<cfloop query="local.qryGetIntakeFormFieldDetails">
						<cfset local.dataStruct.isFrontEndDisplay['#local.qryGetIntakeFormFieldDetails.referralFormFieldName#'] = local.qryGetIntakeFormFieldDetails.isFrontEndDisplay>
						<cfset local.dataStruct.isRequired['#local.qryGetIntakeFormFieldDetails.referralFormFieldName#'] = local.qryGetIntakeFormFieldDetails.isRequired>
					</cfloop>
					
					<cfif local.dataStruct.feDspQuestionTree>
						<cfset local.dataStruct.ccError = 0>
						<cfif arguments.event.getValue('ccError',0)>
							<cfset local.dataStruct.ccError = 1>
							<cfloop collection="#form#" item="local.thisField">
								<cfset local.dataStruct[local.thisField] = form[local.thisField]>
							</cfloop>
						</cfif>

						<cfset local.dataStruct.profile_1 = structNew() />
						<cfset local.dataStruct.profile_1.profileID = local.qryReferralSettings.fePayProfileID />
						<cfset local.dataStruct.profile_1.profileCode = local.qryReferralSettings.fePayProfileCode />
						<cfset local.qryMerchantProfile = getMerchantProfileDetails(profileID=local.qryReferralSettings.fePayProfileID, GLAccountID=local.qryReferralSettings.feGLAccountID)>
						<cfset local.enableProcessingFee = local.qryMerchantProfile.enableProcessingFeeDonation AND val(local.qryMerchantProfile.processFeeDonationFeePercent) GT 0>
						<cfif local.enableProcessingFee>
							<cfset local.selProcessingFee = val(local.qryMerchantProfile.processFeeDonationDefaultSelect) EQ 1 ? 1 : 0>
							<cfset local.processFeeLabel = replaceNoCase(local.qryMerchantProfile.processFeeOtherPaymentsFELabel,"{{AMOUNT}}","#local.qryMerchantProfile.processFeeDonationFeePercent#%")>
						<cfelse>
							<cfset local.selProcessingFee = 0>
							<cfset local.processFeeLabel = "">
						</cfif>
						<cfset local.strPaymentFeatures = application.objPayments.setDefaultPayFeaturesStruct()>
						<cfset local.strPaymentFeatures.processingFee = { 
							"enable":local.enableProcessingFee, 
							"select":local.selProcessingFee, 
							"label":local.processFeeLabel, 
							"denylabel":local.qryMerchantProfile.processFeeOtherPaymentsFEDenyLabel,
							"title":local.qryMerchantProfile.processFeeDonationFETitle,
							"msg":local.qryMerchantProfile.processFeeDonationFEMsg
						}>
						<cfset local.strPaymentFeatures.surcharge.enable = local.qryMerchantProfile.enableSurcharge>
						<cfset local.strPaymentFeatures.applePay.enable = local.qryMerchantProfile.enableApplePay>
						<cfset local.strPaymentFeatures.googlePay.enable = local.qryMerchantProfile.enableGooglePay>
						<cfset local.dataStruct.profile_1.strPaymentForm = application.objPayments.showGatewayInputForm(siteid=local.dataStruct.siteID, profilecode=local.dataStruct.profile_1.profileCode,
									pmid=local.clientFeeMemberID, showCOF=false, usePopup=false, usePopupDIVName='ccForm', autoShowForm=1, paymentFeatures=local.strPaymentFeatures)>

						<cfset local.dataStruct.showContactFormFirst = local.qryReferralSettings.feDspClientInfoFormFirst>		
						<cfset local.viewToUse = 'clientReferrals/#local.viewDirectory#/frm_clientReferralQuestions'>
					<cfelse>					
						<cfset local.viewToUse = 'clientReferrals/#local.viewDirectory#/frm_clientReferral'>		
					</cfif>
				</cfdefaultcase>
			</cfswitch>
		</cfoutput>

		<!--- record app hit --->
		<cfset application.objPlatformStats.recordAppHit(appname="clientreferrals",appsection="") />

		<!--- return the app struct --->
		<cfif local.viewToUse EQ 'echo'>
			<cfreturn returnAppStruct(local.viewData,"echo")>
		<cfelse>
			<cfreturn returnAppStruct(local.dataStruct,local.viewToUse)>	
		</cfif>
	</cffunction>

	<cffunction name="getClientReferralSettings" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true" />
		
		<cfset var local = structNew()>
		
		<cfquery name="local.qryReferralInfo" datasource="#application.dsn.membercentral.dsn#">
			select r.referralID, r.defaultStateID, r.defaultCity, r.fePanelInfoContentID, fePanelInfoContent.rawContent as fePanelInfoContent,
				r.feNoResultsInfoContentID, feNoResultsInfoContent.rawContent as feNoResultsInfoContent, r.feCounselorID,
				r.feMaxMemberRefNum, r.feApplicationInstanceID, r.feGLAccountID, gla.AccountCode as faAccountCode,
				r.fePendingStatusID, r.feReferredStatusID, r.clientFeeMemberID, r.clientMailTopTxt, r.clientMailBottomTxt,
				r.memberMailTopTxt, r.memberMailBottomTxt, r.emailRecipient, r.dspLegalDescription,		
				r.feDspSurveyOption, r.feSurveyOptionDefaultYes, r.feDspBlogOption, r.hideRepFieldsFE,
				r.feDspQuestionTree, r.feDspClientInfoFormFirst, r.feLegalIssueDescTitle, r.feAdditionalFiltersTitle,
				r.feReferralInforMatchTitle, r.feAdditionalInfoTitle, r.dspEmailPanelList, r.feFormInstructionsContentID,
				r.collectClientFeeFE, r.collectClientFeeFEOverrideTxt,
				feFormInstructionsContent.rawContent as feFormInstructionsContent,
				r.feFormInstructionsStep1ContentID,
				feFormInstructionsStep1Content.rawContent as feFormInstructionsStep1Content,
				r.feFormInstructionsStep2ContentID,
				feFormInstructionsStep2Content.rawContent as feFormInstructionsStep2Content,
				r.feFormInstructionsStep3ContentID,
				feFormInstructionsStep3Content.rawContent as feFormInstructionsStep3Content,
				r.feFormInstructionsStep4ContentID,
				feFormInstructionsStep4Content.rawContent as feFormInstructionsStep4Content,
				r.feSuccessfulResultsInstructionsContentID,
				feSuccessfulResultsInstructionsContent.rawContent as feSuccessfulResultsInstructionsContent,
				r.feLegalDescInstructContentID,
				feLegalDescInstructContent.rawContent as feLegalDescInstructContent,
				r.feLegalDescLimitWords, r.feLegalDescLimitWordCount, r.feLegalDescLimitExceedMsg,
				feDspLimitNumOfReferrals,
				feMaxRefPerClient,
				feFreqRefPerClient,
				feIntakeFieldID,
				feDuplicateReferralTxt,
				r.feeStructureTypeID,
				mp.merchantProfileID as fePayProfileID,
				pr.profileCode as fePayProfileCode, 
				m.firstName + ' ' + m.lastName + ' (' + m.memberNumber + ') ' as clientFeeMemberName,
				m2.firstName + ' ' + m2.lastName + ' (' + m2.memberNumber + ') ' as feCounselorName,
				ai.siteID,
				ai.siteResourceID, 
				ai.applicationInstanceName, 
				ai.applicationInstanceDesc, 
				ai.dateCreated, 
				ai.settingsXML, 
				at.applicationTypeID, 
				at.resourceTypeID, 
				at.applicationTypeName, 
				at.applicationTypeDesc,
				at.suggestedPageName, 
				at.settingsXML 
			from dbo.ref_referrals r
			inner join 	dbo.cms_applicationInstances ai on
				ai.applicationInstanceID = r.feApplicationInstanceID		
				and ai.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#" />
			inner join dbo.cms_applicationTypes at on 
				at.applicationTypeID = ai.applicationTypeID
			inner join sites s on
				s.siteID =  ai.siteID
			cross apply dbo.fn_getContent(r.fePanelInfoContentID,1) as fePanelInfoContent
			cross apply dbo.fn_getContent(r.feNoResultsInfoContentID,1) as feNoResultsInfoContent
			cross apply dbo.fn_getContent(r.feLegalDescInstructContentID,1) as feLegalDescInstructContent
			cross apply dbo.fn_getContent(r.feFormInstructionsContentID,1) as feFormInstructionsContent
			cross apply dbo.fn_getContent(r.feFormInstructionsStep1ContentID,1) as feFormInstructionsStep1Content
			cross apply dbo.fn_getContent(r.feFormInstructionsStep2ContentID,1) as feFormInstructionsStep2Content
			cross apply dbo.fn_getContent(r.feFormInstructionsStep3ContentID,1) as feFormInstructionsStep3Content
			cross apply dbo.fn_getContent(r.feFormInstructionsStep4ContentID,1) as feFormInstructionsStep4Content
			cross apply dbo.fn_getContent(r.feSuccessfulResultsInstructionsContentID,1) as feSuccessfulResultsInstructionsContent
			left outer join dbo.ref_merchantProfiles mp 
				inner join  dbo.mp_profiles as pr on
					pr.profileID = mp.merchantProfileID
				on mp.applicationInstanceID = r.feApplicationInstanceID
			left outer join tr_GLAccounts gla on
				gla.GLAccountID = r.feGLAccountID					
			left outer join ams_members m on
				m.memberID =  r.clientFeeMemberID
				and m.memberID = m.activeMemberID
				and m.orgID = s.orgID	
			left outer join ams_members m2 on
				m2.memberID =  r.feCounselorID
				and m2.memberID = m2.activeMemberID
				and m2.orgID = s.orgID													
		</cfquery>

		<cfreturn local.qryReferralInfo />
	</cffunction>

	<cffunction name="doSendContactEmailtoStaff" access="private" output="false" returntype="void">
		<cfargument name="Event" type="any" />
		<cfargument name="emailStr" type="struct" required="true" />
		<cfscript>
			var local = structNew();
			local.emailStr = StructNew();
			local.emailStr = arguments.emailStr;
			local.dataStruct = local.emailStr.dataStruct;
			local.sendTo = len(local.emailStr.qryReferralSettings.emailRecipient) 
							? local.emailStr.qryReferralSettings.emailRecipient
							: arguments.event.getValue('mc_siteInfo.supportProviderEmail');
			local.thiMemberID = (structKeyExists(local.emailStr.qryReferralSettings, "clientFeeMemberID") and val(local.emailStr.qryReferralSettings.clientFeeMemberID)) ? val(local.emailStr.qryReferralSettings.clientFeeMemberID) : arguments.event.getValue('mc_siteInfo.sysmemberid');
			local.questionAnswerPath = local.dataStruct.objAdminReferrals.getSearchXMLByClientID(local.emailStr.qryGetClientData.clientID);
			local.objAppBaseLink = CreateObject('component', 'model.apploader');
			local.clientMailTopTxt = local.emailStr.qryReferralSettings.clientMailTopTxt;
			local.dataStruct.feDspBlogOption = val(local.emailStr.qryReferralSettings.feDspBlogOption);	
			local.dataStruct.feDspQuestionTree = val(local.emailStr.qryReferralSettings.feDspQuestionTree);	
			local.clientMailBottomTxt = local.emailStr.qryReferralSettings.clientMailBottomTxt;
			local.memberMailTopTxt = local.emailStr.qryReferralSettings.memberMailTopTxt;
			local.memberMailBottomTxt = local.emailStr.qryReferralSettings.memberMailBottomTxt;	
			local.appBaseLink = local.objAppBaseLink.getAppBaseLink(applicationInstanceID=local.emailStr.qryReferralSettings.feApplicationInstanceID,siteID=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).siteID);
			local.memberLocalLink = "/?" & local.appBaseLink;
			local.memberExternalLink = "#arguments.event.getValue('mc_siteInfo.scheme')#://" & arguments.event.getValue('mc_siteInfo.mainhostname') & local.memberLocalLink;
			local.clientSubscriptionLocalLink = "/?pg=clientRefSubscribe";
			local.clientSubscriptionExternalLink = "#arguments.event.getValue('mc_siteInfo.scheme')#://" & arguments.event.getValue('mc_siteInfo.mainhostname') & local.clientSubscriptionLocalLink;
			local.referralAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='Referrals', siteID=arguments.event.getValue('mc_siteInfo.siteID'));
			
			local.pageStyle = "width:550px;padding:1px 1px 1px 1px;font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##666;";
			local.tdStyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##666;";
		</cfscript>
		<cftry>
			<cfsavecontent variable="local.emailContent">
				<cfoutput>				
					<div style="#local.pageStyle#">					
						<p style="font-size:13px;color:##333333;">#local.emailStr.qryGetClientData.firstName# #local.emailStr.qryGetClientData.lastName# has requested a lawyer referral.</p> 
						<p style="font-size:13px;color:##333333;">Please see the details below:</p>
						<p style="font-size:13px;color:##333333; margin-top:3px;">Referral ##: #local.dataStruct.clientReferralID#.</p>
						<p style="font-size:13px;color:##333333; margin-top:3px;"><b>Contact Details:</b></p>
						<p style="font-size:13px;color:##333333; margin-top:3px;">Name: #local.emailStr.qryGetClientData.firstName# #local.emailStr.qryGetClientData.lastName#</p>
						<p style="font-size:13px;color:##333333; margin-top:3px;">E-mail: #local.emailStr.qryGetClientData.email#</p>
						<cfif len(local.emailStr.qryGetClientData.cellPhone)>
							<p style="font-size:13px;color:##333333; margin-top:3px;">Cell Phone: #local.emailStr.qryGetClientData.cellPhone#</p>
						<cfelseif len(local.emailStr.qryGetClientData.homePhone)>
							<p style="font-size:13px;color:##333333; margin-top:3px;">Home Phone: #local.emailStr.qryGetClientData.homePhone#</p>
						</cfif>
						<cfif arguments.event.valueExists('bestCallTime')>
							<p style="font-size:13px;color:##333333; margin-top:3px;">Best time to call: #arguments.event.getValue('bestCallTime')#</p>
						</cfif>
						<cfif len(trim(replace(local.questionAnswerPath,"/","")))>
							<p style="font-size:13px;color:##333333; margin-top:3px;"><b>Question/Answer Path</b></p>
							<p style="font-size:13px;color:##333333; margin-top:3px;">#local.questionAnswerPath#</p>
						<cfelse>
							<cfset local.panelID = arguments.event.getValue('panelID1','')>
							<cfset local.subPanelIDs = arguments.event.getValue('subPanelID1','')>
							<cfset local.qryPanelData = local.dataStruct.objAdminReferrals.getPanelByID(panelID=local.panelID) />
							<cfset local.subPanelNames = "">
							<cfloop list="#local.subPanelIDs#" item="local.subPanelID">
								<cfset local.qrySubPanelData = local.dataStruct.objAdminReferrals.getPanelByID(panelID=local.subPanelID) />
								<cfset local.subPanelNames = ListAppend(local.subPanelNames,local.qrySubPanelData.name)>
							</cfloop>
							<p style="font-size:13px;color:##333333; margin-top:3px;">Area of Law: #local.qryPanelData.name#</p>
							<cfif ListLen(local.subPanelNames) GT 0>
								<p style="font-size:13px;color:##333333; margin-top:3px;">Specialization(s): #local.subPanelNames# </p>
							</cfif>
						</cfif>
					</div>																
					<div style="clear:both"></div>
					</cfoutput>
				</cfsavecontent>

				<cfset local.arrEmailTo = []>
				<cfset local.toEmailArr = listToArray(local.sendTo,';')>
				<cfloop array="#local.toEmailArr#" item="local.thisEmail">
					<cfset ArrayAppend( local.arrEmailTo, { name:"", email:local.thisEmail })>
				</cfloop>

				<cfset local.emailTitle = "#arguments.event.getValue('mc_siteInfo.siteName')# Attorney Referral Request">

				<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
					emailfrom={ name=arguments.event.getValue('mc_siteInfo.orgname'), email=arguments.event.getValue('mc_siteInfo.networkEmailFrom')},
					emailto=local.arrEmailTo,
					emailreplyto=arguments.event.getValue('mc_siteInfo.networkEmailFrom'),
					emailsubject=local.emailTitle,
					emailtitle=local.emailTitle,
					emailhtmlcontent=local.emailContent,
					emailAttachments=[],
					siteID=arguments.event.getValue('mc_siteInfo.siteID'),
					memberID=local.thiMemberID,
					messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="REFERRALCONEMST"),
					sendingSiteResourceID=local.referralAdminSiteResourceID)/>

			<cfcatch type="any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local) />
			</cfcatch>
		</cftry>
	</cffunction>

	<cffunction name="doSendReferraltoClient" access="private" output="false" returntype="void">
		<cfargument name="Event" type="any" />
		<cfargument name="emailStr" type="struct" required="true" />

		<cfset var local = structNew()>
		
		<cfset local.emailStr = StructNew()>
			
		<cfset local.emailStr = arguments.emailStr>
		<cfset local.dataStruct = local.emailStr.dataStruct>
		<cfscript>
		
		local.objAppBaseLink = CreateObject('component', 'model.apploader');
		local.clientMailTopTxt = local.emailStr.qryReferralSettings.clientMailTopTxt;
		local.dataStruct.feDspBlogOption = val(local.emailStr.qryReferralSettings.feDspBlogOption);	
		local.dataStruct.feDspQuestionTree = val(local.emailStr.qryReferralSettings.feDspQuestionTree);	
		local.clientMailBottomTxt = local.emailStr.qryReferralSettings.clientMailBottomTxt;
		local.memberMailTopTxt = local.emailStr.qryReferralSettings.memberMailTopTxt;
		local.memberMailBottomTxt = local.emailStr.qryReferralSettings.memberMailBottomTxt;	
		local.sendFrom = len(local.emailStr.qryReferralSettings.emailRecipient) 
						? listFirst(local.emailStr.qryReferralSettings.emailRecipient,";")
						: arguments.event.getValue('mc_siteInfo.supportProviderEmail');
		local.thiMemberID = (structKeyExists(local.emailStr.qryReferralSettings, "clientFeeMemberID") and val(local.emailStr.qryReferralSettings.clientFeeMemberID)) ? val(local.emailStr.qryReferralSettings.clientFeeMemberID) : arguments.event.getValue('mc_siteInfo.sysmemberid');
		local.appBaseLink = local.objAppBaseLink.getAppBaseLink(applicationInstanceID=local.emailStr.qryReferralSettings.feApplicationInstanceID,siteID=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).siteID);
		local.memberLocalLink = "/?" & local.appBaseLink;
		local.memberExternalLink = "#arguments.event.getValue('mc_siteInfo.scheme')#://" & arguments.event.getValue('mc_siteInfo.mainhostname') & local.memberLocalLink;
		local.clientSubscriptionLocalLink = "/?pg=clientRefSubscribe";
		local.clientSubscriptionExternalLink = "#arguments.event.getValue('mc_siteInfo.scheme')#://" & arguments.event.getValue('mc_siteInfo.mainhostname') & local.clientSubscriptionLocalLink;	
		local.dataStruct.email = arguments.event.getValue("email","");
		local.dataStruct.repEmail = arguments.event.getValue("repEmail","");
		</cfscript>
			
		<!--- Send referral e-mail to client --->
		<cfif len(trim(local.sendFrom)) and (len(trim(local.dataStruct.email)) OR len(trim(local.dataStruct.repEmail)))>
			<cfscript>
			local.pageStyle = "width:550px;padding:1px 1px 1px 1px;font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##666;";
			local.tdStyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##666;";		
			
			local.toEmailList = "";
			if (len(trim(local.dataStruct.email)))
				local.toEmailList = local.dataStruct.email;

			if (len(trim(local.dataStruct.repEmail)))
				local.toEmailList = iif(len(trim(local.toEmailList)),de("#local.toEmailList#,"),de("")) & local.dataStruct.repEmail;

			if (len(local.sendFrom))
				local.toEmailList = listAppend(local.toEmailList, local.sendFrom);
			</cfscript>

			<!--- attorney found cases --->
			<cfif structKeyExists(local.dataStruct,"qryGetSearchResults") AND local.dataStruct.qryGetSearchResults.recordCount>
				<cfset local.xmlResultFields = xmlParse(local.dataStruct.qryGetSearchResults.mc_outputFieldsXML[1])>
				<cfset local.RecordTypeInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_recordtypeid']")>
				<cfset local.memberTypeInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_membertypeid']")>
				<cfset local.LastLoginDateInFS = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,17)='ml_datelastlogin_']")>
				<cfset local.memberStatusInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_status']")>

				<cfset local.mc_combinedAddresses = structNew()>
				<cfset local.strOrgAddressTypes = structNew()>
				<cfloop query="local.dataStruct.qryGetAddressTypes">
					<cfif local.dataStruct.qryGetAddressTypes.isTag is 1>
						<cfset local.strOrgAddressTypes["t#local.dataStruct.qryGetAddressTypes.addressTypeID#"] = local.dataStruct.qryGetAddressTypes.addressType>
					<cfelse>
						<cfset local.strOrgAddressTypes[local.dataStruct.qryGetAddressTypes.addressTypeID] = local.dataStruct.qryGetAddressTypes.addressType>
					</cfif>
				</cfloop>

				<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,3)='ma_' or substring(@fieldCode,1,3)='mp_' or substring(@fieldCode,1,4)='mat_' or substring(@fieldCode,1,4)='mpt_']")>
				<cfloop array="#local.tmp#" index="local.thisField">
					<cfset local.thisATID = listGetAt(local.thisField.xmlattributes.fieldcode,2,"_")>

					<cfif listFindNoCase("mat,mpt",listGetAt(local.thisField.xmlattributes.fieldcode,1,"_"))>
						<cfset local.strKey = "t#local.thisATID#">
					<cfelse>
						<cfset local.strKey = local.thisATID>
					</cfif>
					<cfif NOT StructKeyExists(local.mc_combinedAddresses,local.strKey)>
						<cfset local.mc_combinedAddresses[local.strKey] = { addr='', type=local.strOrgAddressTypes[local.strKey], id=local.thisATID } >
					</cfif>
				</cfloop>
			</cfif>
			
			<cftry>
				<cfsavecontent variable="local.emailContent">
				<cfoutput>
					<div style="#local.pageStyle#">
						<cfif NOT val(local.dataStruct.qryPanelData.feAllowClientReferral) OR local.dataStruct.noMemberFound>
							<div style="#local.pageStyle#">
								<p style="font-size:13px;color:##333333;">Dear #local.emailStr.qryGetClientData.firstName# #local.emailStr.qryGetClientData.lastName#:</p> 
								<cfif local.dataStruct.noMemberFound>
									#local.dataStruct.feReviewSubmissionContent#
								<cfelse>
									#local.dataStruct.feConfirmReferralContent#
								</cfif>
							</div>
						<cfelse>
							<p style="font-size:13px;color:##333333;">Dear #local.emailStr.qryGetClientData.firstName#:</p> 
							<div style="font-size:13px;color:##333333; margin-botom:0;">#local.clientMailTopTxt#</div>
								<cfloop query="local.dataStruct.qryGetSearchResults">	
									<cfset local.thisCurrentRow = local.dataStruct.qryGetSearchResults.currentRow>
									<cfset local.stFullName = local.dataStruct.qryGetSearchResults['Extended Name'][local.thisCurrentRow]>
							
									<!--- combine address fields if there are any --->
									<cfset local.dataStruct.arrMemberAddresses = arrayNew(1) />
									<cfset local.thisMember = local.dataStruct.objAdminReferrals.getMember(referralID=local.dataStruct.referralID, memberid=local.dataStruct.qryGetSearchResults.memberid, orgID=arguments.event.getValue('mc_siteInfo.orgID')) />
									<cfset local.thisMem_mc_combinedAddresses = duplicate(local.mc_combinedAddresses)>
									<cfloop collection="#local.thisMem_mc_combinedAddresses#" item="local.thisATID">
										<cfset local.MAfcPrefix = "ma_#local.thisATID#_" />
										<cfset local.MPfcPrefix = "mp_#local.thisATID#_" />				
										<cfif left(local.thisATID,1) eq "t">
											<cfset local.MAfcPrefix = "mat_#right(local.thisATID,len(local.thisATID)-1)#_" />
											<cfset local.MPfcPrefix = "mpt_#right(local.thisATID,len(local.thisATID)-1)#_" />
										</cfif>
										<cfsavecontent variable="local.dataStruct.thisATFull">
											<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address1']")>				
											<cfif arrayLen(local.tmp) is 1 
												and listFindNoCase(local.dataStruct.qryGetSearchResults.columnList,local.tmp[1].xmlAttributes.fieldLabel) 
												and len(local.dataStruct.qryGetSearchResults[local.tmp[1].xmlAttributes.fieldLabel][local.thisCurrentRow])>#local.dataStruct.qryGetSearchResults[local.tmp[1].xmlAttributes.fieldLabel][local.thisCurrentRow]#<br/></cfif>

						 					<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address2']")>
											<cfif arrayLen(local.tmp) is 1 
												and listFindNoCase(local.dataStruct.qryGetSearchResults.columnList,local.tmp[1].xmlAttributes.fieldLabel) 
												and len(local.dataStruct.qryGetSearchResults[local.tmp[1].xmlAttributes.fieldLabel][local.thisCurrentRow])>#local.dataStruct.qryGetSearchResults[local.tmp[1].xmlAttributes.fieldLabel][local.thisCurrentRow]#<br/></cfif>
											
											<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address3']")>
											<cfif arrayLen(local.tmp) is 1 
												and listFindNoCase(local.dataStruct.qryGetSearchResults.columnList,local.tmp[1].xmlAttributes.fieldLabel) 
												and len(local.dataStruct.qryGetSearchResults[local.tmp[1].xmlAttributes.fieldLabel][local.thisCurrentRow])>#local.dataStruct.qryGetSearchResults[local.tmp[1].xmlAttributes.fieldLabel][local.thisCurrentRow]#<br/></cfif>
											
											<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#city']")>
											<cfif arrayLen(local.tmp) is 1 
												and listFindNoCase(local.dataStruct.qryGetSearchResults.columnList,local.tmp[1].xmlAttributes.fieldLabel) 
												and len(local.dataStruct.qryGetSearchResults[local.tmp[1].xmlAttributes.fieldLabel][local.thisCurrentRow])>#local.dataStruct.qryGetSearchResults[local.tmp[1].xmlAttributes.fieldLabel][local.thisCurrentRow]#</cfif>
											
											<cfset local.tmp2 = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#stateprov']")>
											<cfif arrayLen(local.tmp2) is 1
												and listFindNoCase(local.dataStruct.qryGetSearchResults.columnList,local.tmp2[1].xmlAttributes.fieldLabel)
												and len(local.dataStruct.qryGetSearchResults[local.tmp2[1].xmlAttributes.fieldLabel][local.thisCurrentRow])>
												<cfif arrayLen(local.tmp) is 1 
													and listFindNoCase(local.dataStruct.qryGetSearchResults.columnList,local.tmp[1].xmlAttributes.fieldLabel) 
													and len(local.dataStruct.qryGetSearchResults[local.tmp[1].xmlAttributes.fieldLabel][local.thisCurrentRow])>, </cfif> #local.dataStruct.qryGetSearchResults[local.tmp2[1].xmlAttributes.fieldLabel][local.thisCurrentRow]# </cfif>
											
											<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#postalcode']")>
											<cfif arrayLen(local.tmp) is 1
												and listFindNoCase(local.dataStruct.qryGetSearchResults.columnList,local.tmp[1].xmlAttributes.fieldLabel)
												and len(local.dataStruct.qryGetSearchResults[local.tmp[1].xmlAttributes.fieldLabel][local.thisCurrentRow])> #local.dataStruct.qryGetSearchResults[local.tmp[1].xmlAttributes.fieldLabel][local.thisCurrentRow]# </cfif>
											
											<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#county']")>
											<cfif arrayLen(local.tmp) is 1
												and listFindNoCase(local.dataStruct.qryGetSearchResults.columnList,local.tmp[1].xmlAttributes.fieldLabel)
												and len(local.dataStruct.qryGetSearchResults[local.tmp[1].xmlAttributes.fieldLabel][local.thisCurrentRow])><br/>#local.dataStruct.qryGetSearchResults[local.tmp[1].xmlAttributes.fieldLabel][local.thisCurrentRow]# County</cfif>

											<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#country']")>
											<cfif arrayLen(local.tmp) is 1
												and listFindNoCase(local.dataStruct.qryGetSearchResults.columnList,local.tmp[1].xmlAttributes.fieldLabel) 
												and len(local.dataStruct.qryGetSearchResults[local.tmp[1].xmlAttributes.fieldLabel][local.thisCurrentRow])><br/>#local.dataStruct.qryGetSearchResults[local.tmp[1].xmlAttributes.fieldLabel][local.thisCurrentRow]#</cfif>
										</cfsavecontent>
										<cfset local.dataStruct.thisATfull = trim(replace(replace(local.dataStruct.thisATFull,'  ',' ','ALL'),' ,',',','ALL'))>
										<cfif left(local.dataStruct.thisATfull,2) eq ", ">
											<cfset local.dataStruct.thisATfull = right(local.dataStruct.thisATfull,len(local.dataStruct.thisATfull)-2)>
										</cfif>
										<cfif len(local.dataStruct.thisATfull)>
											<cfset local.thisMem_mc_combinedAddresses[local.thisATID]['addr'] = local.dataStruct.thisATfull>
										<cfelse>
											<cfset structDelete(local.thisMem_mc_combinedAddresses,local.thisATID,false)>
										</cfif>

										<cfif len(trim(local.dataStruct.thisATfull))>
											<cfset local.tmpStr = structNew()/>
											<cfset local.tmpStr.addressTypeID = local.thisMem_mc_combinedAddresses[local.thisATID].id />
											<cfset local.tmpStr.addressType = local.thisMem_mc_combinedAddresses[local.thisATID].type />
											<cfset local.tmpStr.stAddress = local.dataStruct.thisATfull />
												
											<cfsavecontent variable="local.dataStruct.stAddressMaps">
												<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address1']")>				
												<cfif arrayLen(local.tmp) is 1 
													and listFindNoCase(local.dataStruct.qryGetSearchResults.columnList,local.tmp[1].xmlAttributes.fieldLabel) 
													and len(local.dataStruct.qryGetSearchResults[local.tmp[1].xmlAttributes.fieldLabel][local.thisCurrentRow])>#local.dataStruct.qryGetSearchResults[local.tmp[1].xmlAttributes.fieldLabel][local.thisCurrentRow]#</cfif>

							 					<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address2']")>
												<cfif arrayLen(local.tmp) is 1 
													and listFindNoCase(local.dataStruct.qryGetSearchResults.columnList,local.tmp[1].xmlAttributes.fieldLabel) 
													and len(local.dataStruct.qryGetSearchResults[local.tmp[1].xmlAttributes.fieldLabel][local.thisCurrentRow])>, #local.dataStruct.qryGetSearchResults[local.tmp[1].xmlAttributes.fieldLabel][local.thisCurrentRow]#<br/></cfif>
												
												<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address3']")>
												<cfif arrayLen(local.tmp) is 1 
													and listFindNoCase(local.dataStruct.qryGetSearchResults.columnList,local.tmp[1].xmlAttributes.fieldLabel) 
													and len(local.dataStruct.qryGetSearchResults[local.tmp[1].xmlAttributes.fieldLabel][local.thisCurrentRow])>, #local.dataStruct.qryGetSearchResults[local.tmp[1].xmlAttributes.fieldLabel][local.thisCurrentRow]#</cfif>
												
												<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#city']")>
												<cfif arrayLen(local.tmp) is 1 
													and listFindNoCase(local.dataStruct.qryGetSearchResults.columnList,local.tmp[1].xmlAttributes.fieldLabel) 
													and len(local.dataStruct.qryGetSearchResults[local.tmp[1].xmlAttributes.fieldLabel][local.thisCurrentRow])>, #local.dataStruct.qryGetSearchResults[local.tmp[1].xmlAttributes.fieldLabel][local.thisCurrentRow]#</cfif>
												
												<cfset local.tmp2 = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#stateprov']")>
												<cfif arrayLen(local.tmp2) is 1
													and listFindNoCase(local.dataStruct.qryGetSearchResults.columnList,local.tmp2[1].xmlAttributes.fieldLabel)
													and len(local.dataStruct.qryGetSearchResults[local.tmp2[1].xmlAttributes.fieldLabel][local.thisCurrentRow])>
													<cfif arrayLen(local.tmp) is 1 
														and listFindNoCase(local.dataStruct.qryGetSearchResults.columnList,local.tmp[1].xmlAttributes.fieldLabel) 
														and len(local.dataStruct.qryGetSearchResults[local.tmp[1].xmlAttributes.fieldLabel][local.thisCurrentRow])>, </cfif> #local.dataStruct.qryGetSearchResults[local.tmp2[1].xmlAttributes.fieldLabel][local.thisCurrentRow]# </cfif>
												
												<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#postalcode']")>
												<cfif arrayLen(local.tmp) is 1
													and listFindNoCase(local.dataStruct.qryGetSearchResults.columnList,local.tmp[1].xmlAttributes.fieldLabel)
													and len(local.dataStruct.qryGetSearchResults[local.tmp[1].xmlAttributes.fieldLabel][local.thisCurrentRow])> #local.dataStruct.qryGetSearchResults[local.tmp[1].xmlAttributes.fieldLabel][local.thisCurrentRow]# </cfif>	
											</cfsavecontent>
							
											<!--- Google Maps --->
											<cfset local.tmpStr.stAddressMaps = replace(local.dataStruct.stAddressMaps,' ,',',','ALL') />
											<cfset local.tmpStr.stGoogleMap = "" />
											<cfif len(local.tmpStr.stAddressMaps)>
												<cfset local.tmpStr.stGoogleMap = "http://maps.google.com/maps?f=q&source=s_q&hl=en&geocode=&q=#URLEncodedFormat(local.tmpStr.stAddressMaps)#&z=16" />
											</cfif>
							
											<!--- phones --->
											<cfset local.arrPhoneIndex = 1 />
											<cfset local.tmpStr.arrPhone =  arrayNew(1) />
											<cfloop array="#local.xmlResultFields.xmlRoot.xmlChildren#" index="local.thisField">
												<cfif ReFindNoCase('#local.MPfcPrefix#[0-9]+',local.thisField.xmlattributes.fieldcode) and len(local.dataStruct.qryGetSearchResults[local.thisField.xmlattributes.fieldLabel][local.thisCurrentRow])>
													<cfset local.tmpStr.arrPhone[local.arrPhoneIndex] = structNew() />
													<cfset local.tmpStr.arrPhone[local.arrPhoneIndex].fieldLabel =  htmlEditFormat(local.thisField.xmlattributes.fieldLabel) />
													<cfset local.tmpStr.arrPhone[local.arrPhoneIndex].phone =  local.dataStruct.qryGetSearchResults[local.thisField.xmlattributes.fieldLabel][local.thisCurrentRow] />
													<cfset local.arrPhoneIndex ++ />
												</cfif>
											</cfloop> 
											<cfset arrayAppend(local.dataStruct.arrMemberAddresses, local.tmpStr) />
										</cfif>
										<cfset structDelete(local, "tmpStr")/>
									</cfloop> 
									<br><p style="font-size:13px;color:##333333; margin-top:3px;">Referral ##: #local.dataStruct.clientReferralID#.</p><br />
									<table cellpadding="4" cellspacing="0" border="1" width="550">
									<tr valign="top" style="#local.tdStyle#">
										<td width="5%" style="#local.tdStyle#">
											<cfset local.tempMemberNumber = "" />
											<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_membernumber']") />
											<cfif arrayLen(local.tmp) eq 1 and listFindNoCase(local.dataStruct.qryGetSearchResults.columnList,local.tmp[1].xmlAttributes.fieldLabel) and arrayLen(local.tmp) eq 1>
												<cfset local.tempMemberNumber  = local.dataStruct.qryGetSearchResults[local.tmp[1].xmlAttributes.fieldLabel][local.thisCurrentRow] />
											</cfif>
											<cfset local.imgToUse = '<img src="/assets/common/images/directory/default.jpg">'>
											<cfif local.thisMember.qryMember.hasMemberPhotoThumb is 1>
												<cfset local.imgToUse = '<img class="tsAppDirPhoto" src="/memberphotosth/#LCASE(local.tempMemberNumber)#.jpg">'>
											</cfif>												
											#local.imgToUse#
										</td>					
										<td style="#local.tdStyle#  border-left-style:dotted;border-left-color:##ccc;" width="35%" valign="top">
											<cfif len(local.stFullName)><b>#local.stFullName#</b><br/></cfif>
											<cfif len(trim(local.dataStruct.qryGetSearchResults["company"][local.thisCurrentRow]))>#local.dataStruct.qryGetSearchResults["company"][local.thisCurrentRow]#<br/></cfif>
											<br/>
											<cfloop array="#local.dataStruct.arrMemberAddresses#"  index="local.thisItem">
												<b style="font-size:95%; margin-right:15px;">#local.thisItem.addressType#</b> 
												<cfif len(local.thisItem.stAddressMaps)><a href="#local.thisItem.stGoogleMap#" target="_blank" title="[View Map at Google]"><img src="/assets/common/images/map.png" alt="[View Map at Google]" width="16" height="16" border="0" align="absmiddle"></a></cfif><br/>
												#local.thisItem.stAddress#<br/>
												<cfif isArray(local.thisItem.arrPhone) and arrayLen(local.thisItem.arrPhone)>
													<cfloop array="#local.thisItem.arrPhone#"  index="local.thisPhoneItem">
														<cfif structKeyExists(local.thisPhoneItem, "fieldLabel") and structKeyExists(local.thisPhoneItem, "phone")>
															#local.thisPhoneItem.fieldLabel#: #local.thisPhoneItem.phone#<br/>
														</cfif>
													</cfloop>
												</cfif>
												<br/>
											</cfloop>
							
											<!--- emails --->
											<cfloop array="#local.xmlResultFields.xmlRoot.xmlChildren#" index="local.thisField">
												<cfif (ReFindNoCase('me_[0-9]+_email',local.thisField.xmlattributes.fieldcode) or ReFindNoCase('met_[0-9]+_email',local.thisField.xmlattributes.fieldcode)) and len(local.dataStruct.qryGetSearchResults[local.thisField.xmlAttributes.fieldLabel][local.thisCurrentRow])>
													#htmlEditFormat(local.thisField.xmlattributes.fieldLabel)#: <a href="mailto:#local.dataStruct.qryGetSearchResults[local.thisField.xmlAttributes.fieldLabel][local.thisCurrentRow]#">#local.dataStruct.qryGetSearchResults[local.thisField.xmlAttributes.fieldLabel][local.thisCurrentRow]#</a><br/>
												</cfif>
											</cfloop>
							
											<!--- websites --->
											<cfloop array="#local.xmlResultFields.xmlRoot.xmlChildren#" index="local.thisField">
												<cfif ReFindNoCase('mw_[0-9]+_website',local.thisField.xmlattributes.fieldcode) and len(local.dataStruct.qryGetSearchResults[local.thisField.xmlAttributes.fieldLabel][local.thisCurrentRow])>
													#htmlEditFormat(local.thisField.xmlattributes.fieldLabel)#: <a href="#local.dataStruct.qryGetSearchResults[local.thisField.xmlAttributes.fieldLabel][local.thisCurrentRow]#" target="_blank">#local.dataStruct.qryGetSearchResults[local.thisField.xmlAttributes.fieldLabel][local.thisCurrentRow]#</a><br/>
												</cfif>
											</cfloop>
							
											<!--- professional licenses --->
											<cfloop array="#local.xmlResultFields.xmlRoot.xmlChildren#" index="local.thisField">
												<cfif ReFindNoCase('mpl_[0-9]+_[a-z]+',local.thisField.xmlattributes.fieldcode) and len(local.dataStruct.qryGetSearchResults[local.thisField.xmlAttributes.fieldLabel][local.thisCurrentRow])>
													<cfif listFindNoCase(local.thisField.xmlattributes.fieldcode,"activeDate","_")>
														#htmlEditFormat(local.thisField.xmlattributes.fieldLabel)#: #dateFormat(local.dataStruct.qryGetSearchResults[local.thisField.xmlAttributes.fieldLabel][local.thisCurrentRow], "mm/dd/yyyy")#<br/>
													<cfelse>
														#htmlEditFormat(local.thisField.xmlattributes.fieldLabel)#: #local.dataStruct.qryGetSearchResults[local.thisField.xmlAttributes.fieldLabel][local.thisCurrentRow]#<br/>
													</cfif>
												</cfif>
											</cfloop>
											
											<!--- memberdata --->
											<cfloop array="#local.xmlResultFields.xmlRoot.xmlChildren#" index="local.thisField">
												<cfif ReFindNoCase('md_[0-9]+',local.thisField.xmlattributes.fieldcode) and len(local.dataStruct.qryGetSearchResults[local.thisField.xmlAttributes.fieldLabel][local.thisCurrentRow])>
													#htmlEditFormat(local.thisField.xmlattributes.fieldLabel)#: 
														<cfif local.thisField.xmlAttributes.allowMultiple is 1>
															#ReplaceNoCase(local.dataStruct.qryGetSearchResults[local.thisField.xmlAttributes.fieldLabel][local.thisCurrentRow],"|",", ","ALL")#<br/>
														<cfelse>
															#local.dataStruct.qryGetSearchResults[local.thisField.xmlAttributes.fieldLabel][local.thisCurrentRow]#<br/>
														</cfif>
												</cfif>
											</cfloop>
											
											<!--- get recordtype if available --->
											<cfif arrayLen(local.RecordTypeInFS) is 1 and len(local.dataStruct.qryGetSearchResults[local.RecordTypeInFS[1].xmlAttributes.FieldLabel][local.thisCurrentRow])>
												#htmlEditFormat(local.RecordTypeInFS[1].xmlAttributes.FieldLabel)#: #local.dataStruct.qryGetSearchResults[local.RecordTypeInFS[1].xmlAttributes.FieldLabel][local.thisCurrentRow]#<br/>
											</cfif>
											
											<!--- get membertypeid if available --->
											<cfif arrayLen(local.memberTypeInFS) is 1 and len(local.dataStruct.qryGetSearchResults[local.memberTypeInFS[1].xmlAttributes.FieldLabel][local.thisCurrentRow])>
												#htmlEditFormat(local.memberTypeInFS[1].xmlAttributes.FieldLabel)#: #local.dataStruct.qryGetSearchResults[local.memberTypeInFS[1].xmlAttributes.FieldLabel][local.thisCurrentRow]#<br/>
											</cfif>
											
											<!--- get status if available --->
											<cfif arrayLen(local.memberStatusInFS) is 1 and len(local.dataStruct.qryGetSearchResults[local.memberStatusInFS[1].xmlAttributes.FieldLabel][local.thisCurrentRow])>
												#htmlEditFormat(local.memberStatusInFS[1].xmlAttributes.FieldLabel)#: #local.dataStruct.qryGetSearchResults[local.memberStatusInFS[1].xmlAttributes.FieldLabel][local.thisCurrentRow]#<br/>
											</cfif>					
										</td>
										<td style="#local.tdStyle# border-left-style:dotted;border-left-color:##ccc;" width="60%" valign="top">
											<cfif local.dataStruct.feDspQuestionTree><b style="font-size:95%;">Areas of Law</b><cfelse><b style="font-size:95%;">Panels</b></cfif><br/>
											<cfloop query="local.thisMember.qryMemberPanels">
												<cfif len(trim(local.thisMember.qryMemberPanels.panelParentID))>&nbsp;&nbsp;&nbsp;&nbsp;</cfif>
												- #local.thisMember.qryMemberPanels.name#<cfif val(local.thisMember.qryMemberPanels.isCertified)>, <b>CERTIFIED</b></cfif><br/>
											</cfloop>
											<br/>		
											<cfif structKeyExists(local.thisMember,"classifications") and isArray(local.thisMember.classifications) and not arrayIsEmpty(local.thisMember.classifications)>
												<cfloop from="1" to="#arrayLen(local.thisMember.classifications)#" index="local.currentClass">				
													<cfset local.qryClass = local.thisMember.classifications[local.currentClass].qryClass />	
													<cfset local.name = local.thisMember.classifications[local.currentClass].name />				
													<cfif local.qryClass.recordCount and local.qryClass.showInSearchResults is 1>					
														<b style="font-size:95%;">#replace(local.name,'_',' ','ALL')#</b><br/>
														<cfloop query="local.qryClass">
															- #local.qryClass.groupName#<br/>
														</cfloop>
														<br/>
													</cfif>
												</cfloop>
											</cfif>				
										</td>
									</tr>
									</table>
									<br />								
								</cfloop>
							<cfif val(local.dataStruct.feDspBlogOption)>
							<p style="font-size:13px;color:##333333;">Would you like to receive Newsletters? <a href="#local.clientSubscriptionExternalLink#&add=1&cid=#local.clientID#">Click here</a> to sign up.</p>
							</cfif>
							<div style="font-size:13px;color:##333333;">#local.clientMailBottomTxt#</div>
						</cfif>					
					</div>																
					<div style="clear:both"></div>
				</cfoutput>
				</cfsavecontent>
				
				<cfset local.referralAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='Referrals', siteID=arguments.event.getValue('mc_siteInfo.siteID'))>

				<cfset local.arrEmailTo = []>
				<cfset local.toEmailArr = listToArray(local.toEmailList,',')>
				<cfloop array="#local.toEmailArr#" item="local.thisEmail">
					<cfset ArrayAppend( local.arrEmailTo, { name:"", email:local.thisEmail })>
				</cfloop>

				<cfset local.emailTitle = "#arguments.event.getValue('mc_siteinfo.sitename')# Attorney Referral Confirmation">

				<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
					emailfrom={ name=arguments.event.getValue('mc_siteInfo.orgname'), email=local.sendFrom },
					emailto=local.arrEmailTo,
					emailreplyto=local.sendFrom,
					emailsubject=local.emailTitle,
					emailtitle=local.emailTitle,
					emailhtmlcontent=local.emailContent,
					emailAttachments=[],
					siteID=arguments.event.getValue('mc_siteInfo.siteID'),
					memberID=local.thiMemberID,
					messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="REFERRALCNFCLNT"),
					sendingSiteResourceID=local.referralAdminSiteResourceID)/>

			<cfcatch type="any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local) />
			</cfcatch>
			</cftry>
		</cfif>
	</cffunction>
	
	<cffunction name="processReferral" access="public" output="true" returntype="struct">
		<cfargument name="Event" type="any">
		<cfargument name="dataStruct" type="struct" required="yes">
		<cfargument name="referralSettings" type="query"  required="yes">
		<cfargument name="noMemberFound" type="numeric" default="false"  required="yes">
		
		<cfset var local = structNew()>
		<cfset arguments.dataStruct.noMemberFound = arguments.noMemberFound>
		<cfset arguments.dataStruct.feFormInstructionsStep4Content = arguments.referralSettings.feFormInstructionsStep4Content>
		<cfset arguments.dataStruct.feSuccessfulResultsInstructionsContent = arguments.referralSettings.feSuccessfulResultsInstructionsContent>
		<cfset arguments.dataStruct.panelListToShow =''>
		<cfset arguments.dataStruct.dspEmailPanelList = arguments.referralSettings.dspEmailPanelList>
		<cfif not arguments.event.valueExists('FIELDNAMES')>	
			<cfset application.objCommon.redirect('#arguments.dataStruct.mainurl#')>
		</cfif>
		<cfset local.frontEndUrl = '/?pg=clientreferrals'>
		
		
		<!--- create referral record --->
		<cfset local.objMemberFieldsets = CreateObject("component","model.system.platform.memberFieldsets")>
		<cfset arguments.event.setValue('referralID',arguments.dataStruct.referralID) />
		<cfif val(arguments.referralSettings.feMaxMemberRefNum)>
			<cfset arguments.event.paramValue('topQueryNum',arguments.referralSettings.feMaxMemberRefNum) />
		</cfif>
		<cfset arguments.event.paramValue('searchFieldsetArea','clientreferralsearch') />
		<cfset arguments.event.paramValue('resultFieldsetArea','clientreferralresult') />
		<cfset arguments.event.paramValue('siteResourceID',arguments.referralSettings.siteResourceID) />
		<cfset local.sessionError = false>
		<cfif NOT arguments.noMemberFound>
			<cfset local.strReferralSearch = application.mcCacheManager.sessionGetValue(keyname='strReferralSearch', defaultValue={})>
			<cfif local.strReferralSearch.count() AND structKeyExists(local.strReferralSearch,"qryGetSearchResults")>
				<cfset arguments.dataStruct.qryGetSearchResults = local.strReferralSearch.qryGetSearchResults />
			<cfelse>
				<cfset local.sessionError = true>
			</cfif>
		<cfelse>
			<cfset arguments.event.setValue('firstName',arguments.event.getValue('firstNameSecondary',''))>
			<cfset arguments.event.setValue('lastName',arguments.event.getValue('lastNameSecondary',''))>
			<cfset arguments.event.setValue('email',trim(arguments.event.getValue('emailSecondary','')))>
			<cfset arguments.event.setValue('homePhone',arguments.event.getValue('homePhoneSecondary',''))>
		</cfif>
		<cfset arguments.dataStruct.referralProcessResponse = StructNew()>
		<cfif NOT local.sessionError>
			<cfset local.panelID = arguments.event.getValue("panelid1",0) />
			<cfif not structKeyExists(arguments.dataStruct, "qryPanelData")>
				<cfset arguments.dataStruct.qryPanelData = arguments.dataStruct.objAdminReferrals.getPanelByID(panelID=local.panelID) />
			</cfif>
			<cfset arguments.dataStruct.clientReferralAmount = arguments.dataStruct.qryPanelData.clientReferralAmount />
			<cfset arguments.dataStruct.feConfirmReferralContent = arguments.dataStruct.qryPanelData.feConfirmReferralContent />
			<cfset arguments.dataStruct.feReviewSubmissionContent = arguments.dataStruct.qryPanelData.feReviewSubmissionContent />
			<cfset local.qryClientResultsFieldSet = arguments.dataStruct.objAdminReferrals.getLocatorFieldsetID(siteResourceID=arguments.referralSettings.siteResourceID, area='clientreferralresult') />
			<cfset arguments.dataStruct.xmlResultFields = local.objMemberFieldsets.getMemberFieldsXML(fieldsetid=val(local.qryClientResultsFieldSet.fieldsetID), usage="memberreferralresult") />
			<cfset arguments.dataStruct.qryGetAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID=arguments.event.getValue('mc_siteinfo.orgid'), includeTags=1) />
			<cfset local.sourceID = arguments.dataStruct.objAdminReferrals.getSources(referralID=arguments.dataStruct.referralID, isFrontEndDefault=1).clientReferralSourceID />
			<cfset arguments.event.setValue('sourceID',local.sourceID) />						
			<cfset local.typeID = arguments.dataStruct.objAdminReferrals.getReferralTypes(event=arguments.event,isReferral=1).clientReferralTypeID />
			<cfset arguments.event.setValue('typeID',local.typeID) />
			<cfset arguments.event.setValue('memberLoggedInID',arguments.referralSettings.feCounselorID) />
			<cfset arguments.event.setValue('clientReferralID',0) />
			<cfset arguments.event.setValue('caseID',0) />
			<cfset arguments.event.setValue('clientParentID',0) />
			<cfset arguments.event.setValue('filterFieldNames',"PANELID1,SUBPANELID1") />
			<cfif arguments.referralSettings.feDspQuestionTree>
				<cfset arguments.event.setValue('feDspQuestionTree',1) />
			<cfelse>
				<cfset arguments.event.setValue('feDspQuestionTree',0) />
			</cfif>
			<!--- 	Check if client has called before	 --->
			<cfset local.canBeReferred = 1 />
			<cfif val(arguments.referralSettings.feDspLimitNumOfReferrals)>
				<cfset arguments.event.setValue('feMaxRefPerClient',arguments.referralSettings.feMaxRefPerClient) />
				<cfset arguments.event.setValue('feFreqRefPerClient',arguments.referralSettings.feFreqRefPerClient) />
				<cfset arguments.event.setValue('feIntakeFieldID',arguments.referralSettings.feIntakeFieldID) />
				<cfset arguments.event.setValue('feDuplicateReferralTxt',arguments.referralSettings.feDuplicateReferralTxt) />
				<cfset local.canBeReferred = checkClientCanBeReferred(event=arguments.event) />
			</cfif>
			<cfset arguments.dataStruct.canBeReferred = local.canBeReferred>
			<cfset arguments.dataStruct.feDuplicateReferralTxt = arguments.referralSettings.feDuplicateReferralTxt>
			<cfset arguments.dataStruct.referralProcessResponse = StructNew()>
			
			<cfif local.canBeReferred>
				<!--- validate required fields --->
				<cfif NOT arguments.noMemberFound>
					<cfset local.qryIntakeFormFieldDetails = arguments.dataStruct.objAdminReferrals.getIntakeFormFieldDetails(referralID=arguments.dataStruct.referralID)>
					<cfset local.strIntakeFormFieldDetails = { 
						"First Name": "firstName", "Middle Name": "middleName", "Last Name": "lastName", "Business": "businessName", 
						"Address 1": "address1", "Address 2": "address2", "City": "city", "State": "state", "Zip Code": "postalCode", 
						"Email": "email", "Home Phone ##": "homePhone", "Cell Phone ##": "cellPhone", "Alternate Phone ##": "alternatePhone" 
					}>
					<cfloop query="local.qryIntakeFormFieldDetails">
						<cfif local.qryIntakeFormFieldDetails.isFrontEndDisplay 
							AND local.qryIntakeFormFieldDetails.isRequired 
							AND local.strIntakeFormFieldDetails.keyExists(local.qryIntakeFormFieldDetails.referralFormFieldName)
							AND (
								NOT arguments.event.valueExists(local.strIntakeFormFieldDetails[local.qryIntakeFormFieldDetails.referralFormFieldName])
								OR NOT isSimpleValue(arguments.event.getValue(local.strIntakeFormFieldDetails[local.qryIntakeFormFieldDetails.referralFormFieldName]))
								OR NOT len(arguments.event.getTrimValue(local.strIntakeFormFieldDetails[local.qryIntakeFormFieldDetails.referralFormFieldName]))
							)>
							<cfset application.objCommon.redirect('#arguments.dataStruct.mainurl#')>
						</cfif>
					</cfloop>
				</cfif>
				
				<cfif val(arguments.dataStruct.qryPanelData.feAllowClientReferral) AND NOT arguments.noMemberFound>
					<cfset local.statusID = arguments.referralSettings.feReferredStatusID />	
					<cfset arguments.event.setValue('statusID',local.statusID) />
					<cfset arguments.event.setValue('selectedMemberIDs',valueList(arguments.dataStruct.qryGetSearchResults.memberID))/>	
					<cfset local.referralsAdminToolTypeID = createObject("component","model.admin.admin").getToolIDByName("ReferralsAdmin")>
					<cfset arguments.event.getCollection()['mc_adminNav']['adminHome'] = ''>
					<cfset arguments.event.setValue('mca_s','0')>
					<cfset arguments.event.setValue('mca_a','0')> 
					<cfset arguments.event.getCollection()['mc_admintoolInfo']['toolType']['tooltypeID'] = local.referralsAdminToolTypeID> 
					<cfset arguments.event.setValue('mca_tt',local.referralsAdminToolTypeID)>
					<cfset arguments.event.setValue('mca_ta','completeReferral')>
					<cfset arguments.event.setValue('isFrontEnd',1)>
					<cfset local.refStr = CreateObject("component","model.admin.referrals.ReferralsAdmin").runDefaultEvent(arguments.event,this.appInstanceID,arguments.dataStruct.siteResourceID) />							
					<cfset arguments.dataStruct.clientReferralID = local.refStr.clientReferralID />					
					<cfset arguments.dataStruct.qryClientDetails = arguments.dataStruct.objAdminReferrals.getReferralData(orgID=arguments.event.getValue('mc_siteinfo.orgid'),clientReferralID=arguments.dataStruct.clientReferralID) />
					
				
					<cfset arguments.dataStruct.clientID = arguments.dataStruct.qryClientDetails['clientID'] />	
					<cfset arguments.dataStruct.communicateLanguageID = arguments.dataStruct.qryClientDetails['communicateLanguageID'] />	
					<cfset arguments.dataStruct.smsClientNumbers = arguments.event.getValue('smsClientNumbers','')>
					<cfset arguments.dataStruct.usageTypeCode = 'REFCLIENTS' />
					<cfset arguments.dataStruct.refType = 'REFCLIENT' />
					<cfset insertReferralSMSParticipants(arguments.dataStruct)>
					
					
					<cfif len(arguments.dataStruct.qryClientDetails['repID']) >
						<cfset arguments.dataStruct.clientID = arguments.dataStruct.qryClientDetails['repID'] />	
						<cfset arguments.dataStruct.smsClientNumbers = arguments.event.getValue('smsRepNumbers','')>
						<cfset arguments.dataStruct.usageTypeCode = 'REFCLIENTS' />
						<cfset arguments.dataStruct.refType = 'REFREPRESENTATIVE' />
					</cfif>

					<cfset insertReferralSMSParticipants(arguments.dataStruct)>

				<cfelse>
					<cfset local.statusID = arguments.referralSettings.fePendingStatusID />
					<cfset arguments.event.setValue('statusID',local.statusID) />
					<cfif arguments.event.getValue('subPanelID1',0) EQ 0>
						<cfset arguments.event.setValue('subPanelID1',"")>
					</cfif>
					<cfset arguments.dataStruct.clientReferralID = arguments.dataStruct.objAdminReferrals.insertClientReferral(event=arguments.event) />
					<cfset arguments.dataStruct.qryClientDetails = arguments.dataStruct.objAdminReferrals.getReferralData(orgID=arguments.event.getValue('mc_siteinfo.orgid'),clientReferralID=arguments.dataStruct.clientReferralID) />
					
					<cfset arguments.dataStruct.clientID = arguments.dataStruct.qryClientDetails['clientID'] />	
					<cfset arguments.dataStruct.communicateLanguageID = arguments.dataStruct.qryClientDetails['communicateLanguageID'] />	
					<cfset arguments.dataStruct.smsClientNumbers = arguments.event.getValue('smsClientNumbers','')>
					<cfset arguments.dataStruct.usageTypeCode = 'REFCLIENTS' />
					<cfset arguments.dataStruct.refType = 'REFCLIENT' />
					<cfset insertReferralSMSParticipants(arguments.dataStruct)>

					<cfif len(arguments.dataStruct.qryClientDetails['repID']) >
						<cfset arguments.dataStruct.clientID = arguments.dataStruct.qryClientDetails['repID'] />	
						<cfset arguments.dataStruct.smsClientNumbers = arguments.event.getValue('smsRepNumbers','')>
						<cfset arguments.dataStruct.usageTypeCode = 'REFCLIENTS' />
						<cfset arguments.dataStruct.refType = 'REFREPRESENTATIVE' />
					</cfif>

					<cfset insertReferralSMSParticipants(arguments.dataStruct)>

					<cfset arguments.dataStruct.objAdminReferrals.reprocessConditions(orgID=arguments.event.getValue('mc_siteinfo.orgid'), clientReferralID=arguments.dataStruct.clientReferralID) />
				</cfif>

				<cfif val(arguments.dataStruct.clientReferralID)>
					<cfset arguments.dataStruct.panelListToShow = ListAppend(arguments.dataStruct.panelListToShow,arguments.event.getValue('subPanelID1',0))>
					<cfset arguments.dataStruct.panelListToShow = ListAppend(arguments.dataStruct.panelListToShow,arguments.event.getValue("panelid1",0))>
					<cfset local.qryGetClientData = arguments.dataStruct.objAdminReferrals.getReferralData(orgID=arguments.event.getValue('mc_siteinfo.orgid'),clientReferralID=arguments.dataStruct.clientReferralID) />
					<cfif arguments.event.valueExists('tID') and val(arguments.event.getValue('tID'))>
						<cfset local.clientID = local.qryGetClientData.clientID />
						<cfset arguments.event.setValue('clientID',local.clientID)>
						<cfset arguments.event.setValue('clientReferralID',arguments.dataStruct.clientReferralID) />
						<cfset arguments.event.setValue('callUID',local.qryGetClientData.callUID) />
						<cfquery name="local.qryGetAppType" datasource="#application.dsn.membercentral.dsn#">		
							select applicationTypeID, resourceTypeID
							from dbo.cms_applicationTypes
							where applicationTypeName = 'ClientReferrals'
						</cfquery>							
						<cfset arguments.dataStruct.objAdminReferrals.insertApplication(orgID=arguments.event.getValue('mc_siteinfo.orgid'), applicationTypeID=local.qryGetAppType.applicationTypeID,
							transactionID=arguments.event.getValue('tID'), itemType="ClientReferralFee", itemID=local.clientID, status="A")>
						<cfset local.sendReceiptEmail = arguments.dataStruct.qryPanelData.sendReceiptEmail />
						<cfif val(local.sendReceiptEmail)>
							<cfset arguments.dataStruct.objAdminReferrals.doEmailClientReceipt(event=arguments.event) />
						</cfif>											
					</cfif>
					<cfif not val(arguments.dataStruct.qryPanelData.feAllowClientReferral) OR arguments.dataStruct.noMemberFound>
						<cfset local.emailStr = StructNew()>
						<cfset local.emailStr.qryReferralSettings = arguments.referralSettings>
						<cfset local.emailStr.qryGetClientData = local.qryGetClientData>
						<cfset local.emailStr.dataStruct = arguments.dataStruct>
			
						<cfset doSendReferraltoClient(event=arguments.event, emailStr=local.emailStr)>
						<cfif arguments.dataStruct.noMemberFound>
							<cfset doSendContactEmailtoStaff(event=arguments.event, emailStr=local.emailStr)>
						</cfif>
					</cfif>
					<cfset arguments.dataStruct.referralProcessResponse.status = 'success'>
				<cfelse>
					<cfset arguments.dataStruct.referralProcessResponse.status = 'fail'>
					<cfset arguments.dataStruct.referralProcessResponse.errorMessage = getDuplicateMessageTemplate('There was an error with your submission. Please try again.',local.frontEndUrl)>
				</cfif>
			<cfelse>
				<cfset arguments.dataStruct.referralProcessResponse.status = 'fail'>
				<cfset arguments.dataStruct.referralProcessResponse.errorMessage = getDuplicateMessageTemplate(arguments.dataStruct.feDuplicateReferralTxt,local.frontEndUrl)>
			</cfif>
		<cfelse>
			<cfset arguments.dataStruct.referralProcessResponse.status = 'fail'>
			<cfset arguments.dataStruct.referralProcessResponse.errorMessage = getDuplicateMessageTemplate('There was an error with your submission. Please try again.',local.frontEndUrl)>
		</cfif>

		<cfreturn arguments.dataStruct>
	</cffunction>

	<cffunction name="checkClientCanBeReferred" access="public" output="false" returntype="boolean">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>			
		
		<cfset local.canBeReferred = 1>
		<cfset local.qryGetIntakeFormFieldDetails = CreateObject("component","model.admin.referrals.referrals").getIntakeFormFieldDetailsByID(referralID=arguments.event.getValue('referralID'), fieldID=arguments.event.getValue('feIntakeFieldID'))>
		<cfset local.referralFieldsList = valueList(local.qryGetIntakeFormFieldDetails.referralFormFieldName)>
		<cfset local.fieldToSearch = "">
		<cfset local.searchHomePhone = "">
		<cfset local.searchCellPhone = "">
		<cfset local.searchAlternatePhone = "">

		<!--- Check if 'Email' is present --->
		<cfif findNoCase("Email", local.referralFieldsList)>
			<cfset local.fieldToSearch = "email">
		</cfif>

		<!--- Check if 'Home Phone #' is present --->
		<cfif findNoCase("Home Phone ##", local.referralFieldsList)>
			<cfif len(local.fieldToSearch) gt 0>
				<cfset local.fieldToSearch = local.fieldToSearch & "AndHome">
			<cfelse>
				<cfset local.fieldToSearch = "home">
			</cfif>
			<cfset local.searchHomePhone = arguments.event.getValue('homePhone','')>
			<cfset local.searchHomePhone = ReReplaceNoCase(local.searchHomePhone,"[^0-9]","","ALL")>
		</cfif>

		<!--- Check if 'Cell Phone #' is present --->
		<cfif findNoCase("Cell Phone ##", local.referralFieldsList)>
			<cfif len(local.fieldToSearch) gt 0>
				<cfset local.fieldToSearch = local.fieldToSearch & "AndCell">
			<cfelse>
				<cfset local.fieldToSearch = "cell">
			</cfif>
			<cfset local.searchCellPhone = arguments.event.getValue('cellPhone','')>
			<cfset local.searchCellPhone = ReReplaceNoCase(local.searchCellPhone,"[^0-9]","","ALL")>
		</cfif>

		<!--- Check if 'Alternate Phone #' is present --->
		<cfif findNoCase("Alternate Phone ##", local.referralFieldsList)>
			<cfif len(local.fieldToSearch) gt 0>
				<cfset local.fieldToSearch = local.fieldToSearch & "AndAlternate">
			<cfelse>
				<cfset local.fieldToSearch = "alternate">
			</cfif>
			<cfset local.searchAlternatePhone = arguments.event.getValue('alternatePhone','')>
			<cfset local.searchAlternatePhone = ReReplaceNoCase(local.searchAlternatePhone,"[^0-9]","","ALL")>
		</cfif>

		<cfif arguments.event.getValue('feFreqRefPerClient',0) eq "D">
			<cfset local.numOfDays = 1>
		<cfelseif arguments.event.getValue('feFreqRefPerClient',0) eq "W">
			<cfset local.numOfDays = 7>
		<cfelse>
			<cfset local.numOfDays = 30>
		</cfif>

		<cfquery name="local.qryCheckExists" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;	

			declare @nowDate datetime = getdate(), @startDate datetime, @clientID int, @clientCount int,
				@feMaxRefPerClient int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('feMaxRefPerClient')#" />,
				@freqDays int = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.numOfDays#" />,
				@email varchar(255) = <cfqueryparam cfsqltype="cf_sql_varchar" value="%#arguments.event.getValue('email','')#%" />, 
				@searchHomePhone varchar(125) = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.searchHomePhone#%" />,
				@searchCellPhone varchar(125) = <cfqueryparam cfsqltype="cf_sql_varchar" value="%#local.searchCellPhone#%" />,
				@searchAlternatePhone varchar(125) = <cfqueryparam cfsqltype="cf_sql_varchar" value="%#local.searchAlternatePhone#" />;
			declare @clientTbl as table(clientID int);

			select @startDate = dateadd(d,-@freqDays,DATEADD(dd, DATEDIFF(dd, 0, @nowDate), 0));			

			insert into @clientTbl
			select clientID
			from dbo.ref_clients
			where referralID = <cfqueryparam value="#arguments.event.getValue('referralID')#" cfsqltype="cf_sql_integer" />	
				and clientParentID is null 
			<cfif local.fieldToSearch eq "emailAndHomeAndCellAndAlternate">
				and (email like @email
					or (phoneForSearch like @searchHomePhone or SUBSTRING(phoneForSearch, 12, 10) like @searchCellPhone or phoneForSearch like @searchAlternatePhone))
			<cfelseif local.fieldToSearch eq "emailAndHomeAndCell">
				and (email like @email
					or (phoneForSearch like @searchHomePhone or SUBSTRING(phoneForSearch, 12, 10) like @searchCellPhone))
			<cfelseif local.fieldToSearch eq "emailAndHomeAndAlternate">
				and (email like @email
					or (phoneForSearch like @searchHomePhone or phoneForSearch like @searchAlternatePhone))
			<cfelseif local.fieldToSearch eq "emailAndCellAndAlternate">
				and (email like @email
					or (SUBSTRING(phoneForSearch, 12, 10) like @searchCellPhone or phoneForSearch like @searchAlternatePhone))
			<cfelseif local.fieldToSearch eq "homeAndCellAndAlternate">
				and (phoneForSearch like @searchHomePhone or SUBSTRING(phoneForSearch, 12, 10) like @searchCellPhone or phoneForSearch like @searchAlternatePhone)
			<cfelseif local.fieldToSearch eq "emailAndHome">
				and (email like @email
					or (phoneForSearch like @searchHomePhone ))
			<cfelseif local.fieldToSearch eq "emailAndCell">
				and (email like @email
					or (SUBSTRING(phoneForSearch, 12, 10) like @searchCellPhone))
			<cfelseif local.fieldToSearch eq "emailAndAlternate">
				and (email like @email
					or (phoneForSearch like @searchAlternatePhone))
			<cfelseif local.fieldToSearch eq "homeAndCell">
				and (phoneForSearch like @searchHomePhone or SUBSTRING(phoneForSearch, 12, 10) like @searchCellPhone)
			<cfelseif local.fieldToSearch eq "homeAndAlternate">
				and (phoneForSearch like @searchHomePhone or phoneForSearch like @searchAlternatePhone)
			<cfelseif local.fieldToSearch eq "cellAndAlternate">
				and (SUBSTRING(phoneForSearch, 12, 10) like @searchCellPhone or phoneForSearch like @searchAlternatePhone)
			<cfelseif local.fieldToSearch eq "email">
				and email like @email
			<cfelseif local.fieldToSearch eq "home">
				and phoneForSearch like @searchHomePhone
			<cfelseif local.fieldToSearch eq "cell">
				and SUBSTRING(phoneForSearch, 12, 10) like @searchCellPhone
			<cfelseif local.fieldToSearch eq "alternate">
				and phoneForSearch like @searchAlternatePhone
			</cfif>
			and dateCreated between @startDate and @nowDate;

			select top 1  @clientID = clientID from @clientTbl;

			select @clientCount = count(clientID)  from @clientTbl;

			if @clientCount >= @feMaxRefPerClient
				insert into dbo.ref_clientIntakeLog(clientID, dateCreated)
				values(@clientID,@nowDate);

			select @clientCount as clientCount;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;	
		</cfquery>
	
		<cfif local.qryCheckExists.clientCount gte arguments.event.getValue('feMaxRefPerClient')>
			<cfset local.canBeReferred = 0>
		</cfif>
		<cfreturn local.canBeReferred>
	</cffunction>	

	<cffunction name="getReferralQuestionAnswers" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="questionID" type="numeric" required="true">
		<cfargument name="answerID" type="numeric" required="true">
		<cfargument name="currentQAMode" type="string" required="true">

		<cfset var local= StructNew()>
		<cfset local.strQA = {
			"parentquestionid":arguments.questionID,
			"selectedanswerid":arguments.answerID,
			"questionid":0,
			"currentqamode":arguments.currentQAMode,
			"hasvalidanswers":false,
			"arranswers":[],
			"arrpanels":[],
			"qamode":"",
			"success":true
		}>

		<cfif arguments.questionID EQ 0 AND arguments.answerID EQ 0>
			<cfset local.strQA.qamode = "getrootquestions">
		<cfelseif arguments.questionID EQ 0 AND arguments.answerID GT 0>
			<cfset local.strQA.qamode = "getrootquestionanswers">
		<cfelseif arguments.questionID GT 0 AND arguments.answerID GT 0>
			<cfset local.strQA.qamode = "getnextquestionandanswers">
		</cfif>

		<cfswitch expression="#local.strQA.qamode#">
			<cfcase value="getrootquestions">
				<cfstoredproc procedure="ref_getRootQuestionAndAnswers" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
					<cfprocresult name="local.qryQuestionAnswers" resultset="1">
				</cfstoredproc>
			</cfcase>
			<cfcase value="getrootquestionanswers">
				<cfset local.rootQuestionID = arguments.answerID>
				
				
				<cfset local.defaultControllingSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='Referrals',siteID=arguments.mcproxy_siteID)>

				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryQuestionAnswers">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">,
						@fieldID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rootQuestionID#">,
						@controllingSiteResourceID int, @usageID int, @fieldIDList varchar(max);

					SELECT @usageID = dbo.fn_cf_getUsageID('ClientReferrals','ReferralPanelChooser',NULL);

					<cfswitch expression="#arguments.currentQAMode#">
						<cfcase value="getpanelfields">
							SELECT @controllingSiteResourceID = controllingSiteResourceID
							FROM dbo.cf_fields
							WHERE fieldID = @fieldID
							AND usageID = @usageID
							AND isActive = 1
							AND adminOnly = 0;
						</cfcase>
						<cfdefaultcase>
							set @controllingSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.defaultControllingSiteResourceID#">;
						</cfdefaultcase>
					</cfswitch>

					SELECT @fieldIDList = COALESCE(@fieldIDList + ',', '') + cast(f.fieldID as varchar(10))
					FROM dbo.cf_fields as f 
					LEFT OUTER JOIN dbo.cf_fieldValueConditions as fvc on fvc.fieldID = f.fieldID
					WHERE f.fieldID = @fieldID
					and f.controllingSiteResourceID = @controllingSiteResourceID
					AND f.usageID = @usageID
					AND f.isActive = 1
					AND f.adminOnly = 0
					AND fvc.conditionID is null;

					-- return valid answers
					EXEC dbo.ref_getValidAnswersFromFieldIDList @siteID=@siteID, @fieldIDList=@fieldIDList;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
			</cfcase>
			<cfcase value="getnextquestionandanswers">
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.strQA.arranswers" returntype="array">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					IF OBJECT_ID('tempdb..##tmpFinalQuestionAndAnswers') IS NOT NULL 
						DROP TABLE ##tmpFinalQuestionAndAnswers;
					CREATE TABLE ##tmpFinalQuestionAndAnswers (fieldID int, fieldText varchar(max), valueID int, valueString varchar(max), 
						fieldOrder int, valueOrder int);

					DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">,
						@condFieldID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.questionID#">,
						@condValueID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.answerID#">,
						@usageID int, @fieldIDList varchar(max);

					SELECT @usageID = dbo.fn_cf_getUsageID('ClientReferrals','ReferralPanelChooser',NULL);

					SELECT @fieldIDList = COALESCE(@fieldIDList + ',', '') + cast(f.fieldID as varchar(10))
					FROM dbo.cf_fields as f 
					INNER JOIN dbo.cf_fieldValueConditions as fvc on fvc.fieldID = f.fieldID
					WHERE fvc.condFieldID = @condFieldID
					AND fvc.condValueID = @condValueID
					AND f.usageID = @usageID
					AND f.isActive = 1
					AND f.adminOnly = 0;

					-- get valid answers
					INSERT INTO ##tmpFinalQuestionAndAnswers (fieldID, fieldText, valueID, valueString, fieldOrder, valueOrder)
					EXEC dbo.ref_getValidAnswersFromFieldIDList @siteID=@siteID, @fieldIDList=@fieldIDList;

					SELECT fieldID as fieldid, fieldText as fieldtext, valueID as valueid, valueString as valuestring
					FROM ##tmpFinalQuestionAndAnswers
					ORDER BY fieldOrder, valueOrder;

					IF OBJECT_ID('tempdb..##tmpFinalQuestionAndAnswers') IS NOT NULL 
						DROP TABLE ##tmpFinalQuestionAndAnswers;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>

				<cfif NOT arrayLen(local.strQA.arranswers)>
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryGetAnswerPanels">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						IF OBJECT_ID('tempdb..##tmpFinalQuestionAndAnswers') IS NOT NULL 
							DROP TABLE ##tmpFinalQuestionAndAnswers;
						CREATE TABLE ##tmpFinalQuestionAndAnswers (fieldID int, fieldText varchar(max), valueID int, valueString varchar(max), 
							fieldOrder int, valueOrder int);

						DECLARE @fieldID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.questionID#">,
							@valueID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.answerID#">,
							@siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">,
							@panelID int, @referralID int, @panelFieldsCount int, @panelSRID int, @usageID int,
							@fieldIDList varchar(max);

						SELECT @usageID = dbo.fn_cf_getUsageID('ClientReferrals','ReferralPanelChooser',NULL);

						SELECT @panelID = p.panelID, @referralID = p.referralID, @panelSRID = p.siteResourceID, 
							@panelFieldsCount = COUNT(rfv.fieldID)
						FROM dbo.cf_fieldValues AS fv
						INNER JOIN dbo.cf_fieldValueSiteResources AS fvs ON fvs.valueID = fv.valueID
						INNER JOIN dbo.ref_panels AS p ON p.siteResourceID = fvs.siteResourceID
						INNER JOIN dbo.ref_panelStatus AS ps ON ps.panelStatusID = p.statusID
							AND ps.statusName in ('Active','Inactive')
						LEFT OUTER JOIN dbo.ref_fieldValue_fields AS rfv ON rfv.valueID = fv.valueID
							AND rfv.referralID = p.referralID
						WHERE fv.fieldID = @fieldID
						AND fv.valueID = @valueID
						AND p.feDspClientReferral = 1
						GROUP BY p.panelID, p.referralID, p.siteResourceID;

						IF @panelFieldsCount > 0 BEGIN
							SELECT @fieldIDList = COALESCE(@fieldIDList + ',', '') + cast(f.fieldID as varchar(10))
							FROM dbo.cf_fields AS f
							INNER JOIN dbo.ref_fieldValue_fields AS ff on ff.fieldID = f.fieldID
								AND ff.valueID = @valueID
								AND ff.referralID = @referralID
							LEFT OUTER JOIN dbo.cf_fieldValueConditions AS fvc on f.fieldID = fvc.fieldID
							WHERE f.controllingSiteResourceID = @panelSRID
							AND f.usageID = @usageID
							AND fvc.fieldID IS NULL
							AND f.isActive = 1;

							INSERT INTO ##tmpFinalQuestionAndAnswers (fieldID, fieldText, valueID, valueString, fieldOrder, valueOrder)
							EXEC dbo.ref_getValidAnswersFromFieldIDList @siteID=@siteID, @fieldIDList=@fieldIDList;

							SELECT @panelFieldsCount = COUNT(DISTINCT fieldID) FROM ##tmpFinalQuestionAndAnswers;

							IF @panelFieldsCount > 0
								SELECT fieldID, fieldText, valueID, valueString, @panelFieldsCount as panelFieldsCount, 'getpanelfields' as qamode
								FROM ##tmpFinalQuestionAndAnswers
								ORDER BY fieldOrder, valueOrder;
							ELSE
								SELECT panelID as panelid, name, isnull(panelParentID,0) as panelparentid, 'getpanels' as qamode
								FROM dbo.ref_panels
								WHERE panelID = @panelID;
						END
						ELSE
							SELECT rp.panelID as panelid, rp.name, isnull(rp.panelParentID,0) as panelparentid, 'getpanels' as qamode
							FROM dbo.fn_getRecursiveReferralPanels(@referralID,@panelID) AS rp
							INNER JOIN dbo.ref_panels AS p ON p.panelID = rp.panelID
							WHERE p.feDspClientReferral = 1
							ORDER BY rp.row;

						IF OBJECT_ID('tempdb..##tmpFinalQuestionAndAnswers') IS NOT NULL 
							DROP TABLE ##tmpFinalQuestionAndAnswers;

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>

					<cfif local.qryGetAnswerPanels.recordCount>
						<cfset local.strQA.qamode = local.qryGetAnswerPanels.qamode>

						<cfswitch expression="#local.strQA.qamode#">
							<cfcase value="getpanelfields">
								<cfif local.qryGetAnswerPanels.panelFieldsCount GT 1>
									<cfquery name="local.strQA.arranswers" dbtype="query" returntype="array">
										select distinct 0 as fieldid, 'Please choose one from below ' as fieldtext, fieldid as valueid, fieldtext as valuestring
										from [local].qryGetAnswerPanels
									</cfquery>
								<cfelse>
									<cfquery name="local.strQA.arranswers" dbtype="query" returntype="array">
										select fieldid, fieldtext, valueid, valuestring
										from [local].qryGetAnswerPanels
									</cfquery>
								</cfif>
							</cfcase>
							<cfcase value="getpanels">
								<cfquery name="local.strQA.arrpanels" dbtype="query" returntype="array">
									select panelid, name, panelparentid
									from [local].qryGetAnswerPanels
								</cfquery>
							</cfcase>
						</cfswitch>
					</cfif>
				</cfif>
			</cfcase>
		</cfswitch>

		<cfswitch expression="#local.strQA.qamode#">
			<cfcase value="getrootquestions,getrootquestionanswers">
				<cfset local.strQA.arranswers = new Query(
						sql="select fieldid, fieldtext, valueid, valuestring from qryQA", 
						dbtype="query", 
						qryQA=local.qryQuestionAnswers,
						returntype="array"
					).execute().getResult()>

				<cfif arrayLen(local.strQA.arranswers)>
					<cfswitch expression="#local.strQA.qamode#">
						<cfcase value="getrootquestions">
							<cfset local.strQA.questionid = local.strQA.arranswers[1].fieldid GT 0 ? local.strQA.arranswers[1].fieldid : "x">
						</cfcase>
						<cfcase value="getrootquestionanswers">
							<cfset local.strQA.questionid = local.strQA.arranswers[1].fieldid>
							<cfif arguments.currentQAMode NEQ 'getpanelfields'>
								<cfset local.strQA.parentquestionid = "x">
							</cfif>
						</cfcase>
					</cfswitch>
					<cfset local.strQA.hasvalidanswers = true>
				</cfif>
			</cfcase>
			<cfdefaultcase>
				<cfif arrayLen(local.strQA.arranswers)>
					<cfset local.strQA.hasvalidanswers = true>
					<cfset local.strQA.questionid = local.strQA.arranswers[1].fieldid>
				</cfif>
			</cfdefaultcase>
		</cfswitch>

		<cfreturn local.strQA>
	</cffunction>

	<cffunction name="getDuplicateMessageTemplate"  access="public" output="false" returntype="string">
		<cfargument name="messageText" type="string" required="true">
		<cfargument name="backUrl" type="string" required="true">
		<cfsavecontent variable="local.duplicateMessageTemplate">
			<cfoutput>
			<div class="row-fluid" id="MC_referralDuplicateMsgWrapper">
				<div class="questionLRISWrapper">
					<div class="well well-small" id="MC_referralDuplicateMsg">			
						#arguments.messageText#					
					</div>
					<div class="control-group clearfix">																
						<div class="span12 text-left">
							<button class="btn btn-default" type="button" onclick="window.location.href='#arguments.backUrl#';">Go Back</button>
						</div>
					</div>
				</div>
			</div>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.duplicateMessageTemplate>
	</cffunction>

	<cffunction name="getMerchantProfileDetails" access="private" output="false" returntype="query">
		<cfargument name="profileID" type="numeric" required="true">
		<cfargument name="GLAccountID" type="numeric" required="true">

		<cfset var qryMerchantProfile = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryMerchantProfile">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @procFeeSupportedGatewayIDs varchar(10) = '10';	-- AuthorizeCCCIM
			DECLARE @tmpInvoiceProfileProcFeeOverrides TABLE (gatewayID int, enableProcessingFeeDonation bit, 
				processFeeDonationDefaultSelect bit, processFeeDonationFETitle varchar(100), processFeeDonationFEMsg varchar(800));

			INSERT INTO @tmpInvoiceProfileProcFeeOverrides (gatewayID, enableProcessingFeeDonation, processFeeDonationDefaultSelect, processFeeDonationFETitle, processFeeDonationFEMsg)
			SELECT tmp.listitem, ip.enableProcessingFeeDonation, ip.processFeeDonationDefaultSelect, pfm.title, pfm.message
			FROM dbo.tr_GLAccounts AS gl
			INNER JOIN dbo.tr_invoiceProfiles AS ip ON ip.profileID = gl.invoiceProfileID
			LEFT OUTER JOIN dbo.tr_solicitationMessages AS pfm ON pfm.messageID = ip.solicitationMessageID
			CROSS APPLY dbo.fn_intListToTableInline(@procFeeSupportedGatewayIDs,',') AS tmp
			WHERE gl.GLAccountID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.GLAccountID#">;
			
			SELECT mp.profileID, mp.profileCode, mp.profileName, mp.tabTitle, 
				CASE WHEN mp.enableProcessingFeeDonation = 1 AND ISNULL(tmp.enableProcessingFeeDonation,1) = 1 THEN 1 ELSE 0 END AS enableProcessingFeeDonation,
				mp.processFeeDonationFeePercent, ISNULL(tmp.processFeeDonationFETitle,pfm.title) AS processFeeDonationFETitle,
				ISNULL(tmp.processFeeDonationFEMsg,pfm.message) AS processFeeDonationFEMsg,
				ISNULL(tmp.processFeeDonationDefaultSelect,mp.processFeeDonationDefaultSelect) AS processFeeDonationDefaultSelect, 
				mp.processFeeDonationRenevueGLAccountID, mp.processFeeDonationRevTransDesc, mp.processFeeOtherPaymentsFELabel, mp.processFeeOtherPaymentsFEDenyLabel, 
				g.gatewayID, g.gatewayType, mp.enableApplePay, mp.enableGooglePay, mp.enableSurcharge, mp.surchargePercent, mp.surchargeRevenueGLAccountID,
				mp.processingFeeLabel
			FROM dbo.mp_profiles AS mp
			INNER JOIN dbo.mp_gateways AS g ON mp.gatewayID = g.gatewayID
			LEFT OUTER JOIN dbo.tr_solicitationMessages AS pfm ON pfm.messageID = mp.solicitationMessageID
			LEFT OUTER JOIN @tmpInvoiceProfileProcFeeOverrides AS tmp ON tmp.gatewayID = mp.gatewayID
			WHERE mp.profileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.profileID#">
			AND mp.status = 'A'
			ORDER BY mp.frontEndOrderBy;
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryMerchantProfile>
	</cffunction>

	<cffunction name="insertReferralSMSParticipants" access="private" output="false" returntype="string">
		<cfargument name="dataStruct" type="struct" required="true" />

		<cfset local.objSMSTemplate = createObject("component","model.admin.common.modules.smsTemplate.smsTemplate")>
		<cfif arguments.dataStruct.qryClientDetails.recordcount gt 0>

			<cfloop list="#arguments.dataStruct.smsClientNumbers#" item="local.smsNumbers">
				<cfset local.returnObj = local.objSMSTemplate.saveSMSUsageParticipants(
					arguments.dataStruct.SITEID,
					arguments.dataStruct.usageTypeCode,
					local.smsNumbers,
					arguments.dataStruct.communicateLanguageID,
					local.objSMSTemplate.getMessagingServiceRecipientStatusIDByName('optinViaWeb'),
					arguments.dataStruct.refType,
					arguments.dataStruct.clientID
				)>
			</cfloop>		
			
			<cfreturn 'true'>
		</cfif>
			
		<cfreturn 'false'>		
	</cffunction>

</cfcomponent>