html {
    scroll-behavior: smooth;
}
*:not(select) { 
    -moz-box-sizing: border-box; 
    -ms-box-sizing: border-box; 
    -o-box-sizing: border-box; 
    -webkit-box-sizing: border-box; 
    box-sizing: border-box;  
}
.innerPage-content input {
    -moz-box-sizing: unset!important; 
    -ms-box-sizing: unset!important; 
    -o-box-sizing: unset!important; 
    -webkit-box-sizing: unset!important; 
    box-sizing: unset!important; 
}
:before, :after {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
body, html {
    overflow-x: hidden;
    font-size: 18px;
    background: #fff;
    font-family: ubuntu, sans-serif;
}

@media (max-width: 767px) {
    body {
        padding: 0;
    }
}
body, html, input, textarea, select {
    color: #333;
    line-height: 1.5em;
    font-size: 18px;
    font-family: ubuntu, sans-serif;
    font-weight: 400;
}
@media (max-width: 1349px) {
    body, html, input, textarea, select {
        font-size: 16px;
    }
}
@media (max-width: 1199px) {
    body, html, input, textarea, select {
        font-size: 15px;
    }
}
.clearfix:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
}
.text-highlight {
    color: #ffb612
}
.text-grey {
    color: #626262;
}
.text-blue {
    color: #003145
}
.font-highlight {
    font-family: ubuntu, sans-serif
}
.font21 {
    font-size: 1.105rem;
    line-height: 1.52em;
}
.font23 {
    font-size: 1.27778rem;
    line-height: 1.69em;
}
.text-capitalize {
    text-transform: capitalize;
}
.text-uppercase {
    text-transform: uppercase;
}
.pt-1 {
    padding-top: 1rem;
}
.pt-2 {
    padding-top: 2rem;
}
.pt-3 {
    padding-top: 3rem;
}
.pt-4 {
    padding-top: 4rem;
}
.pt-5 {
    padding-top: 5rem;
}
.flex {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-direction: row;
    -moz-flex-direction: row;
    -ms-flex-direction: row;
    flex-direction: row;
}
.flex.reverse {
    -webkit-flex-direction: row-reverse;
    -moz-flex-direction: row-reverse;
    -ms-flex-direction: row-reverse;
    flex-direction: row-reverse;
}
.flex.justify-content-end {
    justify-content: flex-end;
}
.flex.justify-content-center {
    justify-content: center;
}
.flex.justify-content-start {
    justify-content: flex-start;
}
blockquote {
    font-style: italic;
    font-weight: 500;
    font-size: 1.11112rem;
    line-height: 1.54em;
    color: #333;
    border-left: solid 4px #15783c;
    padding-left: 1.1em;
    margin-bottom: 1.4em;
    padding-top: 0.4em;
    margin-top: 2.4rem;
}
blockquote p {
    font-style: italic;
    font-weight: 500;
    font-size: 1.11112rem;
    line-height: 1.54em;
    color: #333;
}
 p, ul, li {
    font-size: 1rem;
    margin-top: 0;
    margin-bottom: 1.5em;
}
 ul {
    padding-left: 0;
}
 li {
    margin-bottom: 0;
}
 a {
    outline: medium none;
    text-decoration: none;
    transition: 0.5s;
    -moz-transition: 0.5s;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    color: #0065bd;
}
.contentBx #zoneMain  a:hover{    
    color: #3db7e4 !important
}
 a:hover {
    color: #ffb612;
    text-decoration: underline;
    text-decoration-thickness: 1px;
}
 b, strong {
    font-weight: 700;
}
 u {
    text-decoration-line: underline;
    text-decoration-thickness: 1px;
}
h1, .h1, h2, .h2, h3, .h3, h4, .h4, h5, .h5 {
    font-family: ubuntu, sans-serif;
    font-weight: 700;
    margin: 0;
    padding: 0;
    line-height: 1em
}
h1, .h1, h2, .h2, h3, .h3, h4, .h4, h5, .h5 {
    font-weight: 700;
    line-height: 1em;
    padding-bottom: 0.45em;
    font-family: ubuntu, sans-serif
}
 h2, .h2 {
    font-size: 1.7778rem;
    line-height: 1.28em;
}

.container {
    width: 100%;
    max-width: 1236px;
    margin: 0 auto
}

@media (max-width: 479px) {
    h2, .h2 {
        font-size: 1.5rem;
    }
}
 h3, .h3 {
    font-size: 1.333rem;
    line-height: 1.166667em;
}
@media (max-width: 479px) {
    h3, .h3 {
        font-size: 1.133rem;
    }
}
 h4, .h4 {
    font-size: 1.16667rem;
    line-height: 1.142em;
}
@media (max-width: 479px) {
    h4, .h4 {
        font-size: 1.1rem;
    }
}
img {
    max-width: 100%;
    height: auto;
}

.button {
    background: #333;
    color: #fff;
    font-weight: 700;
    font-family: ubuntu, sans-serif;
    height: 56px;
    line-height: 51px;
    padding: 0 1.15rem;
    display: inline-block;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    font-size: 1.15rem;
    -webkit-transition: all .25s;
    -moz-transition: all .25s;
    -ms-transition: all .25s;
    -o-transition: all .25s;
    transition: all .25s;
    -webkit-transform: scale(1, 1);
    -moz-transform: scale(1, 1);
    -ms-transform: scale(1, 1);
    -o-transform: scale(1, 1);
    transform: scale(1, 1);
    border: 2px solid #333;
    text-align: center
}

@media(max-width:767px) {
    .button {
        height: 40px;
        line-height: 35px
    }
}

.button:hover,
.button:focus {
    text-decoration: none;
    outline: none;
    color: #fff;
    -webkit-transform: scale(1.045, 1.045);
    -moz-transform: scale(1.045, 1.045);
    -ms-transform: scale(1.045, 1.045);
    -o-transform: scale(1.045, 1.045);
    transform: scale(1.045, 1.045)
}

.button.buttonOutline {
    background: 0 0;
    color: #333
}

.button.buttonOutlineWhite {
    background: 0 0;
    color: #fff;
    border-color: #fff
}

.button.inactive {
    opacity: .5;
    cursor: default;
}

.button.inactive:hover,
.button.inactive:focus {
    text-decoration: none;
    outline: none;
    color: #fff;
    -webkit-transform: scale(1, 1);
    -moz-transform: scale(1, 1);
    -ms-transform: scale(1, 1);
    -o-transform: scale(1, 1);
    transform: scale(1, 1)
}

.button {
    background: #ffb612;
    font-family: ubuntu, sans-serif;
    padding: 0 1.01em;
    border: none;
    height: 56px;
    line-height: 56px;
    font-weight: 600;
    font-size: 1.11112rem;
    text-align: center;
    font-weight: 700;
    font-size: 1.333rem;
    color: #003145;
    text-transform: uppercase;
}
.button.tiny {
    height: 32.5px;
    line-height: 32.5px;
    font-size: 0.8889rem;
    padding: 0 0.96em;
}
.button.sml {
    height: 39px;
    line-height: 39px;
    font-size: 0.8333rem;
}
.button.btn-outline-light {
    border: 1px solid #666;
    background: none;
    color: #666;
}
.button.btn-outline-light.sml {
    font-size: 1.0556rem;
}
.button.btn-outline-blue {
    border: 1px solid #003145;
    background: 0 0;
    color: #003145
}
.button.btn-outline-blue.sml {
    font-size: 1.0556rem;
}
.button.btn-outline-dark {
    border: 1px solid #333;
    box-sizing: border-box;
    background: none;
    color: #333;
}
.button.btn-outline-dark.sml {
    font-size: 1.0556rem;
}
.button.btn-outline-white {
    border: 1px solid #fff;
    background: none;
    color: #fff;
}
.button.btn-outline-white.sml {
    font-size: 1.0556rem;
}
.button.grey {
    background: #d6d6d6;
    color: #003145
}
.button.join {
    background: #0065bd;
    color: #fff;
}
.button.signin {
    background: #e0e0e0;
    color: #555;
}
@media (max-width: 991px) {
   .button {
        height: 40px;
        line-height: 40px;
        font-size: 1rem;
    }
   .button.btn-outline-highlight, .button.btn-outline-white {
        border-width: 1px;
        line-height: 38px
    }
}
@media (max-width: 575px) {
   .button {
        padding: 0 0.75rem;
        font-size: 0.95rem;
    }
}

.container {
    padding-left: 20px;
    padding-right: 20px;
    position: relative;
    z-index: 9;
}
 section {
    padding: 3.65rem 0 3.1rem;
    position: relative;
    overflow: hidden;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    -webkit-background-size: cover;
    font-size: 1rem;
}
@media (min-width: 768px) {
    section {
        padding: 3.65rem 0 3.1rem;
    }
}
@media (min-width: 992px) {
    section {
        padding: 3.65rem 0 3.1rem;
    }
}
@media (min-width: 1200px) {
    section {
        padding: 3.65rem 0 3.1rem;
    }
}
 section .container .row-fluid .button {
    margin-top: 0.15rem;
}
 section .container .row-fluid p {
    line-height: 1.6667em;
}
.sectionTitle {
    font-weight: 700;
    font-size: 2.6111rem;
    line-height: 1.085em;
    padding-bottom: 0.4em;
}
@media (max-width: 979px) {
   .sectionTitle {
        font-size: 2.2rem;
    }
}
@media (max-width: 575px) {
   .sectionTitle {
        font-size: 1.8rem;
    }
}
.sectionTagline {
    font-family: ubuntu, sans-serif;
    font-weight: bold;
    font-size: 1.684rem;
    line-height: 1em;
    padding-bottom: 0.62em;
    padding-top: 0.35em;
}
.container {
    position: relative;
    z-index: 1;
}
.container .row {
    padding: 0;
}
.container .text-default {
    color: #333
}
.social-share {
    list-style: none;
    margin: 0;
    padding: 0;
}
.social-share li {
    display: inline-block;
    font-size: 1.526rem;
    line-height: 0.83em;
    text-align: center;
    color: #fff;
    margin-left: 0.3em;
}
@media (max-width: 575px) {
   .social-share li {
        font-size: 1.2rem;
    }
}
.social-share li a {
    color: #fff;
}
.social-share li a:hover {
    color: #ffb612;
}
.social-share li.facebook .button {
    background: #3b5998;
    border-color: #3b5998;
}
.social-share li.twitter .button {
    background: #00acee;
    border-color: #00acee;
}
.social-profiles {
    margin: 0;
    padding: 0;
    list-style: none;
}
.social-profiles li {
    display: inline-block;
    vertical-align: middle;
    font-size: 1.3333rem;
    line-height: 1.16667em;
    letter-spacing: 0.1em;
    color: #fff;
    margin-left: 0.32em;
}
.social-profiles li a {
    display: block;
    color: #fff;
}
 header {
    padding: 1.05rem 0.75rem 0.75rem 1.3rem;
    position: absolute;
    width: 100%;
    left: 0;
    top: 0;
    border-top: solid 7px #ffb612;
    z-index: 9;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: rgba(0, 39, 59, 0.9);
    min-height: 129px;
}
@media (max-width: 767px) {
    header {
        position: relative;
        background: #003145;
        padding: 15px;
    }
}
@media (max-width: 479px) {
    header {
        flex-direction: column;
    }
}
 header #headerLogo {
    position: relative;
}
@media (max-width: 1199px) {
    header #headerLogo {
        width: 170px;
    }
}
@media (max-width: 575px) {
    header #headerLogo {
        width: 110px;
    }
}
 header #headerLogo img {
    width: 100%;
    height: auto;
}
 header #headerContent {
    display: flex;
    flex-direction: column;
}
@media (max-width: 479px) {
    header #headerContent {
        flex-direction: row;
        width: 100%;
        align-items: center;
        padding-top: 10px;
        justify-content: space-between;
    }
}
 header #headerTop {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}
 header #headerBtns {
    display: flex;
    align-items: center;
}
 header #headerBtns a.button {
    margin-right: 7px;
}
#headerMobileContent #headerBtns a.button, #headerMobileContent #headerBtns a.headerLink {
    display: none;
}
@media (max-width: 850px) {
    #headerContent #headerBtns a.button {
        display: block;
        font-size: 13px;
        margin-left: 0px;
        padding: 0px 8px;
        height: 30px;
        line-height: 31px;
        margin-right: 7px;
    }
    header #headerSearch .headerSearchForm {
        width: 105px !important;
    }
    header #headerSearch .headerSearchForm input[type="text"] {
        height: 31px !important;
        margin-top: 1px;
    }
    .headerHelpLink {
        display: inline-block;
        margin-top: 18px;
    }
    header #headerContent {
        display: block;
    }
    header nav.menuHolder {
        float: right;
    }
    #headerContent #headerBtns a.headerLink {
        display: none;
    }
}
@media (max-width: 650px) {
    #headerContent #headerBtns a.button {
        display: block;
        font-size: 10px;
        margin-left: 0px;
        padding: 0px 6px;
        height: 25px;
        line-height: 27px;
        margin-right: 5px;
    }
    header #headerSearch .headerSearchForm input[type="text"] {
        height: 26px !important;
        margin-top: 3px;
    }
}
@media (max-width: 479px) {
    header #headerTop {
        justify-content: center;
    }
    header nav.menuHolder {
        margin-top: 8px;
    }
    .headerHelpLink {
        margin-top: 12px;
    }
}
header #headerBtns a.headerLink {
    font-weight: 500;
    font-size: 0.88889rem;
    line-height: 30px;
    color: #fff;
    padding: 0 0.75em;
    margin-right: 0.8rem;
}
#headerContent .headerHelpLink a.headerLink{
    font-weight: 500;
    font-size: 0.88889rem;
    line-height: 30px;
    color: #fff;
    margin-right: 0.8rem;
}
 header #headerSocial {
    display: none;
}
 header #headerSearch {
    position: relative;
    padding-right: 0.8rem;
}
@media (max-width: 767px) {
    header #headerSearch {
        padding-right: 0;
    }
}
 header #headerSearch .headerSearchForm {
    position: relative;
    height: 32.5px;
    width: 154px;
}
 header #headerSearch .headerSearchForm input[type="text"] {
    background: 0 0;
    border: 1px solid #fff;
    border-radius: 3px;
    height: 32.5px;
    line-height: 30px;
    font-size: 0.8889rem;
    color: #fff;
    width: 100%;
    padding: 0 0.6em 0 1.9em;
}
 header #headerSearch .headerSearchForm i {
    position: absolute;
    font-size: 0.8889rem;
    color: #fff;
    line-height: 1em;
    left: 0.6em;
    top: 50%;
    transform: translateY(-50%);
}
 header nav {
    position: relative;
    display: inline-block;
    padding-top: 0.9rem;
}
@media (max-width: 979px) {
    header nav {
        display: flex;
        justify-content: flex-end;
    }
}
@media (max-width: 479px) {
    header nav {
        padding-top: 0;
    }
}
 header nav #toggle-menu {
    display: none;
    font-weight: 700;
    font-size: 1rem;
    line-height: 1.1667em;
    color: #fff;
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    -ms-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s;
    height: 2rem;
    line-height: 2rem;
    border-radius: 3px;
    text-transform: uppercase;
    padding: 0 10px;
}
 header nav #toggle-menu:hover, header nav #toggle-menu:focus {
    background: #333;
    color: #fff;
    text-decoration: none;
}
@media (max-width: 979px) {
    header nav #toggle-menu {
        display: inline-block;
    }
}
 header nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}
@media (max-width: 979px) {
    header nav ul {
        display: none;
        position: absolute;
        background: #f3f3f3;
        width: 200px;
        z-index: 99;
        right: 0;
        top: 100%;
    }
}
 header nav ul li {
    display: inline-block;
    font-weight: 700;
    font-size: 1rem;
    line-height: 1.1667em;
    color: #fff;
    margin-left: 0.3em;
}
 header nav ul li.home-menu-item span {
    display: none;
}
@media (max-width: 979px) {
    header nav ul li.home-menu-item span {
        display: inline-block;
        margin-left: 0.4em;
    }
}
 header nav ul li:last-child {
    margin-right: -0.4em;
}
@media (max-width: 1199px) {
    header nav ul li {
        font-size: 0.95rem;
    }
}
@media (max-width: 979px) {
    header nav ul li {
        width: 100%;
        margin: 0;
        padding: 0;
        border-bottom: solid 1px rgba(0, 0, 0, 0.2);
    }
}
 header nav ul li a {
    display: block;
    color: #fff;
    padding: 0.556em 0.568em;
}
@media (max-width: 979px) {
    header nav ul li a {
        display: block;
        padding: 10px;
        color: #003145;
        text-align: right;
    }
    header nav ul li a:hover, header nav ul li a:focus {
        background: #dadada;
    }
}
 #banner {
    position: relative;
    margin-top: 129px!important;
    min-height: 190px;
}
 #banner:before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    background: linear-gradient(328.29deg, rgba(0, 0, 0, 0.2) -2.3%, rgba(0, 0, 0, 0) 28.85%), linear-gradient(90deg, #fff 50.9%, rgba(255, 255, 255, 0) 70%);
    z-index: 1;
}
@media (max-width: 767px) {
    #banner {
        margin-top: 0!important;
    }
    #banner:before {
        display: none;
    }
}
 #banner .banner-image {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    width: 54%;
    z-index: 0;
	background-position: 63% !important;
	background-size: cover!important;
}
 #banner .banner-image img {
    object-fit: cover;
    object-position: center center;
    width: 100%;
    height: 100%;
}
.single-default .col.span7.fadeInLeft, .groupSectionPage .col.span7.fadeInLeft{
    padding-bottom: 30px;
}
@media (max-width: 767px) {
    #banner .banner-image {
        position: relative;
        width: 100%;
        height: 40vw;
    }
    #banner .banner-image:before {
        background: linear-gradient(360deg, #fff 0%, transparent 35%);
        width: 100%;
        content: '';
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
    }
}
 #banner video {
    object-fit: cover;
    width: 100vw;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;
}
 #banner .container {
    z-index: 9;
    position: relative;
}
 #banner h1, #banner h2 {
    font-weight: 700;
    font-size: 2.3rem;
    line-height: 1.000em;
    color: #003145;
    padding-bottom: 0.6em;
    padding-top: 0.6em;
}
@media (max-width: 979px) {
    #banner h1, #banner h2 {
        font-size: 2.2rem;
    }
}
@media (max-width: 767px) {
    #banner h1, #banner h2 {
        font-size: 1.85rem;
    }
}
@media (max-width: 479px) {
    #banner h1, #banner h2 {
        font-size: 1.57rem;
    }
}
 #banner .bannerTxt {
    font-weight: 400;
    font-size: 1.444rem;
    line-height: 1.384em;
    color: #666;
}
@media (max-width: 979px) {
    #banner .bannerTxt {
        font-size: 1.22rem;
    }
}
@media (max-width: 479px) {
    #banner .bannerTxt {
        font-size: 1.05rem;
    }
}
 form.row {
    margin: 0 -6px;
}
 form .col {
    padding-left: 6px;
    padding-right: 6px;
    padding-bottom: 12px;
    padding-top: 0;
}
 form .col label {
    padding: 0;
    margin: 0;
    font-weight: bold;
    font-size: 1.105rem;
    color: #666;
}
@media (max-width: 991px) {
    form .col label {
        line-height: 40px;
    }
}
 form .col.toggle-label label {
    font-size: 0.736rem;
}
 form .col input, form .col textarea, form .col select {
    background: #fff;
    border: none;
    font-weight: bold;
    font-size: 1.105rem;
    color: #333;
}
form .col input:hover, form .col textarea:hover, form .col select:hover, form .col input:focus, form .col textarea:focus, form .col select:focus {
    border: none;
}
form .col input::placeholder, form .col textarea::placeholder, form .col select::placeholder {
    color: #666;
}
@media (max-width: 991px) {
    form .col input, form .col textarea, form .col select {
        height: 40px;
        line-height: 40px;
    }
}
form .col .button {
    width: auto;
    padding: 0 1rem;
}
 form .col .checkbox_group {
    padding: 0.75rem 0.65rem 3px 0.65rem;
    border: 2px solid #fff;
}
 form .col .checkbox_group h4 {
    font-family: ubuntu, sans-serif;
    font-style: normal;
    font-weight: normal;
    font-size: 1.2rem;
    line-height: 1em;
    color: #fff;
    padding-bottom: 0.45em;
}
 form .col .checkbox_group .row {
    margin: 0 -10px;
}
 form .col .checkbox_group .row .col {
    padding-left: 10px;
    padding-right: 10px;
    padding-bottom: 16px !important;
}
 form .col .checkbox_group .row .col label {
    line-height: 1.2em;
    margin-right: 1.1rem;
    color: #fff;
}
form .col .checkbox_group .row .col input[type="checkbox"]:checked + label::before {
    color: #fff;
}
 #signup {
    padding: 2rem 0;
    background: #262626;
}
 #form {
    background: #f3f3f3;
}
 #form .container {
    max-width: 1000px;
}
 body.single-default main {
    padding-bottom: 3.24rem;
}
 body.single-default .contentBx {
    padding-bottom: 3px;
}
 body.single-default .contentBx h2 {
    padding-bottom: 0.3em;
}
 body.single-default .contentBx h3 {
    padding-top: 0.1em;
}
 body.single-group-section #banner {
    padding: 9.9rem 0 3.37rem;
}
@media (max-width: 767px) {
    body.single-group-section #banner {
        padding-top: 0;
    }
}
.post-meta {
    font-weight: 700;
    font-size: 1.333rem;
    line-height: 1.16667em;
    color: #626262;
    padding-top: 0.6em;
}
@media (max-width: 979px) {
   .post-meta {
        font-size: 1.133rem;
    }
}
.post-meta a {
    color: #626262;
}
 #breadcrumbs {
    font-weight: 700;
    font-size: 1.333rem;
    line-height: 1.16667em;
    color: #b6b6b6;
    padding-bottom: 0.6em;
}
@media (max-width: 979px) {
    #breadcrumbs {
        font-size: 1.133rem;
    }
}
 #breadcrumbs a {
    color: #626262;
}
.bannerLinks {
    padding-top: 1.15rem;
    display: flex;
    align-items: center;
}
@media (max-width: 479px) {
   .bannerLinks {
        flex-direction: column;
    }
}
.bannerLinks .button {
    margin-right: 1.3rem;
}
@media (max-width: 479px) {
   .bannerLinks .button {
        margin-right: 0;
        margin-bottom: 10px;
    }
}
.bannerLinks .rowLink {
    font-weight: 700;
    font-size: 1.2778rem;
    line-height: 1.565em;
    color: #666;
}
@media (max-width: 479px) {
   .bannerLinks .rowLink {
        font-size: 1.1rem;
    }
}
 main {
    padding: 2.5rem 0 3.7rem;
    position: relative;
    background: #f3f3f3;
}
main .button {
    margin-top: 0.7rem;
}
 #container-area {
    display: flex;
    flex-direction: row-reverse;
}
@media (max-width: 767px) {
    #container-area {
        display: block;
    }
}
 #container-area #content {
    width: 50%;
    flex-grow: 1;
    padding-left: 1.28rem;
}
@media (max-width: 767px) {
    #container-area #content {
        width: 100%;
        padding-left: 0;
    }
}
 #container-area #sidebar {
    width: 32%;
}
@media (max-width: 767px) {
    #container-area #content {
        width: 100% !important;
    }
    #container-area #sidebar {
        width: 100%;
    }
}
.contentBx {
    background: #fff;
    border-radius: 3px;
    padding: 1.3rem 2rem;
}
@media (max-width: 479px) {
   .contentBx {
        padding: 20px;
    }
}
.contentBx h3 {
    padding-top: 0.5em;
    padding-bottom: 0.6em;
}
.contentBx h4 {
    padding-bottom: 0.6em;
}
.post-img {
    margin: 0.65rem 0 1.55rem;
    position: relative;
}
.post-img .img-caption {
    font-style: italic;
    font-weight: 400;
    font-size: 0.7778rem;
    line-height: 1.5em;
    color: #333;
    padding-top: 1.24em;
    padding-left: 0.65em;
    padding-right: 0.65em;
}
.sidebar-widget {
    margin-bottom: 1.55rem;
    position: relative;
}
.sidebar-nav {
    margin: 0;
    padding: 1rem 0 0 0;
    list-style: none;
}
.sidebar-nav li {
    display: block;
    border-bottom: 1px solid #ddd;
    font-weight: 700;
    font-size: 1.16667rem;
    line-height: 1.1em;
    color: #626262;
    position: relative;
    padding: 0.82em 1.2em 0.82em 1.7em;
}
.sidebar-nav li:last-child {
    border-bottom: none;
}
.sidebar-nav li:before {
    content: "›";
    left: 0.5em;
    position: absolute;
    font-size: 1.238em;
    color: #ffb612;
}
.sidebar-nav li a {
    color: #626262;
}
.sidebar-alert {
    background: #fff;
    border-radius: 3px;
    padding: 0.9rem 1.2rem 1.8rem;
}
.sidebar-ad {
    padding: 2.3rem 2.2rem 0.9rem;
    text-align: center;
    background: #fff;
    border-radius: 3px;
}
@media (max-width: 979px) {
   .sidebar-ad {
        padding: 10px;
    }
}
.sidebar-ad .advertisementTag {
    font-weight: 400;
    font-size: 0.8889rem;
    line-height: 1.5em;
    color: #666;
    padding-top: 1em;
}
.widget-title {
    font-weight: 700;
    font-size: 1.2778rem;
    line-height: 1.2em;
    color: #003145;
    padding-bottom: 0.7em;
}
.group-title {
    font-weight: 700;
    font-size: 1.5556rem;
    line-height: 1.2em;
}
.alert-text {
    font-size: 0.94444rem;
    line-height: 1.5294em;
}
.alert-btn {
    padding-top: 0.4rem;
}
 #bottom-posts {
    position: relative;
    background: #003145;
}
 #bottom-posts.group-posts #latest-news {
    width: 100%;
    padding-right: 0;
    padding-top: 2.45rem;
    padding-bottom: 2.15rem;
}
@media (max-width: 979px) {
    #bottom-posts.group-posts #latest-news {
        padding-left: 20px;
        padding-right: 20px;
    }
}
 #bottom-posts.group-posts #latest-news .group-head {
    max-width: 550px;
    padding-bottom: 1.7rem;
}
 #bottom-posts.group-posts #latest-news #news-posts {
    display: flex;
}
@media (max-width: 767px) {
    #bottom-posts.group-posts #latest-news #news-posts {
        display: block;
    }
}
 #bottom-posts.group-posts #latest-news #news-posts .span6 {
    margin-bottom: 1.2rem;
}
 #bottom-posts.group-posts #latest-news #news-posts .span6 .post-list-bx {
    height: 100%;
    align-items: center;
    padding: 1.05rem 0;
}
@media (max-width: 479px) {
    #bottom-posts.group-posts #latest-news #news-posts .span6 .post-list-bx.withImg {
        padding-top: 20px;
    }
}
@media (max-width: 767px) {
    #bottom-posts.group-posts #latest-news #news-posts .span6 .post-list-bx {
        align-items: initial;
    }
}
 #bottom-posts.group-posts #latest-news #news-posts .span6 .post-list-bx .post-list-img {
    width: 24.7%;
    height: 100%;
}
@media (max-width: 767px) {
    #bottom-posts.group-posts #latest-news #news-posts .span6 .post-list-bx .post-list-img {
        height: auto;
    }
}
@media (max-width: 479px) {
    #bottom-posts.group-posts #latest-news #news-posts .span6 .post-list-bx {
        height: 250px !important;
    }
    #bottom-posts.group-posts #latest-news #news-posts .span6 .post-list-bx .post-list-img {
        width: 100%;
    }
}
 #bottom-posts.group-posts #latest-news #news-posts .span6 .post-list-bx .post-list-content {
    padding: 0 1.4rem;
}
 #bottom-posts .container {
    display: flex;
}
@media (max-width: 979px) {
    #bottom-posts .container {
        display: block;
        padding: 0;
    }
}
 #bottom-posts #upcoming-events {
    width: 30%;
    flex-grow: 1;
    padding-top: 1.9rem;
    padding-left: 20px;
    background: #fff;
    border-radius: 0px 0px 0px 3px;
    position: relative;
}
@media (max-width: 979px) {
    #bottom-posts #upcoming-events {
        width: 100%;
        padding: 2rem 20px;
    }
}
 #bottom-posts #upcoming-events:before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 100%;
    background: #fff;
}
@media (max-width: 979px) {
    #bottom-posts #upcoming-events:before {
        display: none;
    }
}
 #bottom-posts #latest-news {
    width: 58.7%;
    color: #fff;
    padding-top: 1.9rem;
    padding-right: 20px;
}
@media (max-width: 979px) {
    #bottom-posts #latest-news {
        width: 100%;
        padding: 2rem 20px;
    }
}
 #bottom-posts #latest-news .post-list-bx:last-child {
    margin-bottom: 0;
}
 #upcoming-events.sidebar-events {
    padding-top: 6px;
}
 #upcoming-events.sidebar-events .group-head {
    padding-bottom: 0.9rem;
}
 #upcoming-events.sidebar-events #event-posts .event-list-bx {
    padding: 0.8rem 0;
    border-bottom: none;
}
 #upcoming-events.sidebar-events #event-posts .event-list-bx h3 {
    padding-bottom: 0.3em;
}
 #upcoming-events .group-head {
    border-bottom: 4px solid #1e4f63;
}
.group-head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 1.1rem;
}
.group-foot {
    display: block;
    justify-content: space-between;
    align-items: center;
    padding-top: 1.1rem;
    text-align:right;
}
#upcoming-events.upcomingEvents{margin-bottom: 58.5px;}
#upcoming-events .group-foot{
    display: block;
    width: 100%;
    position: absolute;
    bottom: -58px;
    right: 20px;
}
#upcoming-events .group-foot .group-head-btn{text-align:right;}
@media (max-width: 479px) {
   .group-head {
        flex-direction: column;
    }
   .group-head .group-head-btn {
        padding-top: 12px;
    }
}
.group-head h3 {
    padding-bottom: 0;
}
.post-list-bx {
    position: relative;
    display: flex;
    width: 100%;
    border-radius: 3px;
    background: #fff;
    margin-bottom: 0.85rem;
    padding: 0.9rem 0;
    height: 188px;
}
@media (max-width: 479px) {
   .post-list-bx {
        height: 230px;
    }
    .post-list-bx .post-list-img {
        width: 35% !important;
    }
}
.post-list-bx.withImg .post-list-content {

    padding-left: 1.2rem;
}
.post-list-bx .post-list-content {
    width: 50%;
    flex-grow: 1;
    padding: 0 0.9rem;
}
@media (max-width: 479px) {
   .post-list-bx .post-list-content {
        width: 100%;
    }
}
.post-list-bx .post-list-content h3 {
    font-weight: 700;
    font-size: 1.1667rem;
    line-height: 1.142em;
    color: #626262;
    padding-bottom: 0.35em;
}
.post-list-bx .post-list-content h3 a {
    color: #626262;
}
.post-list-bx .post-list-content .post-summary {
    font-size: 0.8333rem;
    line-height: 1.1333em;
    color: #333;
}
@media (max-width: 1199px) {
   .post-list-bx .post-list-content .post-summary {
        font-size: 0.9rem;
    }
}
.post-list-bx .post-list-content .button {
    margin-top: 0.5rem;
}
.post-list-bx .post-list-img {
    width: 21.6%;
    border-radius: 0 3px 3px 0;
    overflow: hidden;
}
.post-list-bx .post-list-img img {
    object-position: center center;
    margin-left: 4px;
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;
}
 #bottom-ad {
    padding: 2.85rem 0 2.95rem;
    background: #003145;
}
@media (max-width: 767px) {
    #bottom-ad {
        padding: 10px;
    }
}
 #bottom-ad-bx {
    max-width: 728px;
    margin: 0 auto;
    color: #fff;
    text-align: center;
}
 #event-posts {
    position: relative;
}
 #event-posts .event-list-bx {
    padding: 1.55rem 0;
    border-bottom: 1px solid #ddd;
}
 #event-posts .event-list-bx:last-child {
    border-bottom: 0;
}
 #event-posts .event-list-bx h3 {
    font-weight: 700;
    font-size: 1.16667rem;
    line-height: 1.142em;
    color: #333;
    padding-bottom: 0.45em;
}
 #event-posts .event-list-bx h3 a {
    color: #333;
}
 #event-posts .event-list-bx .event-date {
    font-weight: 400;
    font-size: 0.8333rem;
    line-height: 1.1333em;
    color: #333;
}
 #event-posts .event-list-bx .event-date a {
    color: #333;
}
 #event-posts .event-list-bx .advertising_tag {
    font-weight: 400;
    font-size: 0.8333rem;
    line-height: 1.1333em;
    color: #333;
    padding-bottom: 0.55em;
}
.sponsor-logos {
    padding-top: 0;
}
.sponsor-logos .owl-item {
    float: left;
}
.sponsor-logos .item {
    height: 100px;
    align-items: center;
    padding: 0 40px;
}
.sponsor-logos .item img {
    max-height: 100%;
    max-width: 275px;
    width: 100%;
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%);
}
 #sponsors {
    position: relative;
    padding: 2.85rem 0;
    text-align: center;
    background: #f3f3f3;
}
.sponsors-label {
    font-style: italic;
    font-weight: 500;
    font-size: 1.333rem;
    line-height: 1.2em;
    color: #808080;
}
.sponsors-ad {
    padding-top: 1.8rem;
    text-align: center;
}
 #latest-section-leadership {
    padding-top: 2.2rem;
}
 #latest-section-leadership .group-head {
    padding: 0 1.8rem 10px;
}
 #latest-section-leadership .group-head h2 {
    color: #1a3d4f;
    padding-bottom: 0;
}
 #section-leadership-posts {
    display: flex;
    flex-wrap: wrap;
}
 #section-leadership-posts .leadership-post {
    width: 50%;
    padding-bottom: 25px;
}
 #section-leadership-posts .leadership-post:nth-child(odd) {
    padding-right: 10px;
}
 #section-leadership-posts .leadership-post:nth-child(even) {
    padding-left: 10px;
}
 #section-leadership-posts .leadership-post .leadership-bx {
    padding: 1.5rem 1.7rem;
    background: #fff;
    border-radius: 3px;
    display: flex;
    align-items: center;
}
@media (max-width: 979px) {
    #section-leadership-posts .leadership-post .leadership-bx {
        flex-direction: column;
    }
}
 #section-leadership-posts .leadership-post .leadership-bx .leadership-img {
    width: 106px;
    height: 106px;
}
#section-leadership-posts .leadership-post .leadership-bx .leadership-img img {
    object-fit: cover;
    object-position: center center;
    width: 100%;
    height: 100%;
}
 #section-leadership-posts .leadership-post .leadership-bx .leadership-content {
    width: 50%;
    flex-grow: 1;
    padding-left: 1.05rem;
}
@media (max-width: 979px) {
    #section-leadership-posts .leadership-post .leadership-bx .leadership-content {
        width: 100%;
        padding: 1rem 0 0 0;
        text-align: center;
    }
}
 #section-leadership-posts .leadership-post .leadership-bx .leadership-content h4 {
    font-weight: 700;
    font-size: 1.444rem;
    line-height: 1.153em;
    color: #626262;
    padding-bottom: 0.15em;
}
 #section-leadership-posts .leadership-post .leadership-bx .leadership-content .leaderDesig {
    font-weight: 400;
    font-size: 1.05556rem;
    line-height: 1.368em;
    color: #333;
    padding-bottom: 0.35em;
}
 #section-leadership-posts .leadership-post .leadership-bx .leadership-content .leaderSocial ul {
    margin: 0;
    list-style: none;
}
 #section-leadership-posts .leadership-post .leadership-bx .leadership-content .leaderSocial ul li {
    display: inline-block;
    margin-right: 0.4em;
    font-size: 1.33rem;
    line-height: 1.166em;
    color: #666;
}
 #section-leadership-posts .leadership-post .leadership-bx .leadership-content .leaderSocial ul li a {
    color: #666;
}
 footer {
    padding: 2.95rem 0 1.25rem;
    color: #fff;
    background: #003145;
}
@media (max-width: 767px) {
    footer {
        text-align: center;
    }
}
 footer .footerRight {
    max-width: 258px;
    margin-left: auto;
}
@media (max-width: 767px) {
    footer .footerRight {
        margin: 0 auto;
    }
}
 footer #footerLogo {
    max-width: 228px;
    margin: 0 auto;
    padding-bottom: 0.95rem;
}
 footer #footerBtns .button {
    display: block;
    width: 100%;
    margin-bottom: 0.7rem;
    height: 42px;
    line-height: 42px;
    font-size: 0.8889rem;
}
 footer #footerSocial {
    padding-bottom: 0.7rem;
    padding-left: 18px;
    padding-right: 13px;
}
 footer #footerSocial ul {
    display: flex;
    justify-content: space-between;
}
 footer #footerSocial ul li {
    font-size: 1.7222rem;
    margin: 0;
}
 footer #footerSocial ul li a {
    color: #fff;
}
 footer .footer-nav-head {
    font-weight: 700;
    font-size: 1.333rem;
    line-height: 1.4em;
    color: #ffb612;
    padding-bottom: 0.4em;
}
 footer .footer-nav {
    margin-bottom: 1.55rem;
}
 footer .footer-nav ul {
    margin: 0;
    padding: 0;
    list-style: none;
}
 footer .footer-nav ul li {
    font-weight: 400;
    font-size: 1rem;
    line-height: 1.111em;
    color: #fff;
    margin-bottom: 0.72em;
}
@media (max-width: 979px) {
    footer .footer-nav ul li {
        font-size: 0.95rem;
    }
}
 footer .footer-nav ul li a {
    color: #fff;
}
 #copyright-area {
    text-align: center;
    padding: 2.6rem 0 4.3rem;
    color: #fff;
    font-weight: 400;
    font-size: 1rem;
    line-height: 1.1667em;
    background: #001d31;
}
@media (max-width: 979px) {
    #copyright-area {
        font-size: 0.95rem;
    }
}
 #copyright-area a {
    color: #fff;
}
 #copyright-area #copyright-nav span {
    display: inline-block;
    margin: 0 0.3em;
}
 #copyright-area #copyright {
    padding-top: 1.1em;
    line-height: 1.142em;
    font-size: 0.7778rem;
}
.mfp-bg {
    background: #333;
}
 #inquire {
    background: #fff;
    padding: 5%;
}
 #inquire .button {
    margin-top: 1rem;
}
 #home #banner {
    background: #fff;
}
 #home #banner:before {
    display: none;
}
 #home #banner-slides {
    position: relative;
}
 #banner-slides .banner-image {
    width: 54%;
}
 #banner-slides .banner-image img {
    object-position: left center;
}
@media (max-width: 767px) {
     #banner-slides .banner-image {
        width: 100%;
        height: 55vw;
        position: relative;
    }
    #banner-slides .banner-image:before {
        background: linear-gradient(360deg, #fff 0%, transparent 35%);
        width: 100%;
        content: '';
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
    }
}
 #home #banner-slides ul {
    margin: 0;
    padding: 0;
    list-style: none;
}
 #home #banner-slides ol.flex-control-paging {
    margin: 0;
    padding: 0 20px;
    list-style: none;
    position: absolute;
    z-index: 99;
    bottom: 20px;
    width: 1236px;
    max-width: 100%;
    left: 50%;
    transform: translateX(-50%);
}
@media (max-width: 479px) {
    #home #banner-slides ol.flex-control-paging {
        text-align: center;
    }
}
 #home #banner-slides ol.flex-control-paging li {
    display: inline-block;
    margin-right: 0.5rem;
}
@media (max-width: 479px) {
    #home #banner-slides ol.flex-control-paging li {
        margin: 0 0.25em;
    }
}
 #home #banner-slides ol.flex-control-paging li a {
    display: block;
    text-indent: -9999px;
    width: 55px;
    height: 10px;
    background: #cfcfcf;
    border-radius: 30px;
}
 #home #banner-slides ol.flex-control-paging li a.flex-active {
    background: #ffb612;
}
@media (max-width: 479px) {
    #home #banner-slides ol.flex-control-paging li a {
        width: 40px;
    }
}
 #home #banner-slides .banner-slide {
    position: relative;
    height: 450px;
}
@media (max-width: 767px) {
    #home #banner-slides .banner-slide {
        padding: 0 0 3rem;
        height:unset;
    }
}
 #home #banner-slides .banner-slide:before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    background: linear-gradient(90deg, #ffff 46%, #a28e8e52 81%, rgb(226 208 208 / 0%) 100%);
    z-index: 1;
    height: 450px;
}
@media (max-width: 767px) {
    #home #banner-slides .banner-slide:before {
        display: none;
    }
    #home #banner-slides .banner-slide .bannerTxtBx{
        padding-top: 0px; 
    }
}
 #home #banner-slides .banner-slide .bannerTxtBx {
    padding-top: 45px;
}
 #home #section-1 {
    color: #fff;
    background: #003145;
}
 #home #section-1:after {
    content: "";
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    position: absolute;
    background: linear-gradient(270deg, #003145 46.46%, rgba(0, 49, 69, 0) 87.71%);
}
@media (max-width: 767px) {
    #home #section-1 {
        padding-top: 0;
    }
    #home #section-1:after {
        display: none;
    }
}
 #home #section-1 .bg-image {
    width: 58.68%;
    height: 100%;
    top: 0;
    left: 0;
}
 #home #section-1 .bg-image:before {
    background: none;
}
@media (max-width: 767px) {
    #home #section-1 .bg-image {
        height: 55vw;
        width: 100%;
    }
    #home #section-1 .bg-image:before {
        background: linear-gradient(360deg, #003145 0%, rgba(0, 0, 0, 0) 35%);
    }
}
 #home #section-2 {
    color: #333;
    background: #f3f3f3;
    padding-bottom: 3.55rem;
}
 #home #section-2:after {
    content: "";
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    position: absolute;
    background: linear-gradient(90deg, #fff 48.4%, rgba(255, 255, 255, 0) 80.35%);
}
@media (max-width: 767px) {
    #home #section-2 {
        padding-top: 0;
    }
    #home #section-2:after {
        display: none;
    }
}
 #home #section-2 .bg-image {
    width: 60%;
    height: 100%;
    top: 0;
    left: 50%;
    position: absolute;
}
 #home #section-2 .bg-image:before {
    background: none;
}
@media (max-width: 767px) {
    #home #section-2 .bg-image {
        position: relative;
        width: 100%;
        height: 55vw;
        left: 0;
    }
    #home #section-2 .bg-image:before {
        background: linear-gradient(360deg, #f3f3f3 0%, rgba(0, 0, 0, 0) 35%);
        left: 0;
    }
}
 #home #section-3 {
    color: #333;
    background: #f3f3f3;
    padding-bottom: 1.7rem;
    padding-top: 0;
}
 #home #section-3 .bg-image {
    width: 100%;
    height: 40vw;
    max-height: 460px;
    top: 0;
    left: 0;
    position: relative;
}
 #home #section-3 .bg-image:before {
    background: linear-gradient(0deg, #f3f3f3 0%, rgba(255, 255, 255, 0) 65.19%);
}
@media (max-width: 767px) {
    #home #section-3 .bg-image {
        position: relative;
        width: 100%;
        height: 55vw;
        left: 0;
    }
    #home #section-3 .bg-image:before {
        background: linear-gradient(360deg, #f3f3f3 0%, rgba(0, 0, 0, 0) 35%);
        left: 0;
    }
}
@media (max-width: 767px) {
    #home #section-3 .row-fluid.flex {
        display: block;
    }
}
 #home #home-bottom .container .span12 {
    display: flex;
}
@media (max-width: 979px) {
    #home #home-bottom .container .span12 {
        display: block;
    }
}
 #home #home-bottom .container #home-publications {
    width: 49.2%;
    padding-right: 3rem;
}
@media (max-width: 979px) {
    #home #home-bottom .container #home-publications {
        width: 100%;
        padding-right: 0;
    }
}
 #home #home-bottom .container #social-tabs-bx {
    width: 50%;
    flex-grow: 1;
    background: #f3f3f3;
    border-radius: 10px;
    padding: 1.95rem 2.5rem;
}
@media (max-width: 979px) {
    #home #home-bottom .container #social-tabs-bx {
        width: 100%;
        margin-top: 3rem;
    }
}
@media (max-width: 767px) {
    #home #home-bottom .container #social-tabs-bx {
        padding: 20px;
    }
}
 #home #home-bottom .container #social-tabs-bx .tab-content {
    max-height: 422.98px;
    background: #fff;
    padding: 1.5rem;
}
 #home #home-bottom .container #social-tabs-bx .tab-menu {
    margin: 0 -8px;
    padding: 0 0 1.55rem;
    list-style: none;
    display: flex;
}
 #home #home-bottom .container #social-tabs-bx .tab-menu li {
    width: 50%;
    padding: 0 8px;
}
 #home #home-bottom .container #social-tabs-bx .tab-menu li a {
    display: block;
    width: 100%;
    height: 40px;
    line-height: 40px;
    padding: 0 10px;
    border: 1px solid #003145;
    box-sizing: border-box;
    border-radius: 3px;
    font-weight: 700;
    font-size: 1.05556rem;
    text-decoration: none;
    text-align: center;
    color: #003145;
}
 #home #home-bottom .container #social-tabs-bx .tab-menu li a i {
    margin-right: 0.2em;
}
@media (max-width: 479px) {
    #home #home-bottom .container #social-tabs-bx .tab-menu li a {
        height: 34px;
        line-height: 32px;
        font-size: 0.9rem;
    }
}
 #home #home-bottom .container #social-tabs-bx .tab-menu li.active a {
    background: #ffb612;
    border-color: #ffb612;
}
 #home .standard-home {
    padding: 20px 0;
}
@media (max-width: 575px) {
    #home .standard-home img {
        margin-bottom: 20px;
    }
}
 #publication-posts .publication-post {
    display: flex;
    align-items: center;
    padding: 10px 6px;
    border-bottom: 1px solid #ccc;
}
 #publication-posts .publication-post .publication-meta {
    display: flex;
    align-items: center;
    width: 50%;
    flex-grow: 1;
    padding-left: 1.5rem;
    padding-right: 1rem;
    justify-content: space-between;
}
@media (max-width: 479px) {
    #publication-posts .publication-post .publication-meta {
        display: block;
        padding-right: 0;
        padding-left: 20px;
    }
}
 #publication-posts .publication-post .publication-meta h4 {
    padding-bottom: 0;
}
 #publication-posts .publication-post .publication-img {
    width: 66px;
    height: 86px;
}
 #publication-posts .publication-post .publication-img img {
    object-fit: cover;
    object-position: center center;
    width: 100%;
    height: 100%;
}
.all-publications-btn {
    padding-top: 0.8rem;
}

.bg-image {
    position: absolute;
    display: block;
    width: 50%;
    height: 100%;
    top: 0;
    left: 0;
    overflow: hidden
}

.bg-image:before {
    content: '';
    width: 100%;
    height: 100%;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    background: linear-gradient(270deg, #FFFFFF 0%, rgba(255, 255, 255, 0) 30%);
    z-index: 9
}

.bg-image img {
    object-fit: cover;
    object-position: center center;
    width: 100%;
    height: 100%
}

@media(max-width:767px) {
    .bg-image {
        width: 100%;
        height: 350px;
        position: relative
    }

    .bg-image:before {
        background: linear-gradient(360deg, #FFFFFF 0%, rgba(255, 255, 255, 0) 35%)
    }
}

.owl-carousel,
.owl-carousel .owl-item {
    -webkit-tap-highlight-color: transparent;
    position: relative
}

.owl-carousel {
    display: none;
    width: 100%;
    z-index: 1
}

.owl-carousel .owl-stage {
    position: relative;
    -ms-touch-action: pan-Y;
    touch-action: manipulation;
    -moz-backface-visibility: hidden
}

.owl-carousel .owl-stage:after {
    content: ".";
    display: block;
    clear: both;
    visibility: hidden;
    line-height: 0;
    height: 0
}

.owl-carousel .owl-stage-outer {
    position: relative;
    overflow: hidden;
    -webkit-transform: translate3d(0, 0, 0)
}

.owl-carousel .owl-item,
.owl-carousel .owl-wrapper {
    -webkit-backface-visibility: hidden;
    -moz-backface-visibility: hidden;
    -ms-backface-visibility: hidden;
    -webkit-transform: translate3d(0, 0, 0);
    -moz-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0)
}

.owl-carousel .owl-item {
    min-height: 1px;
    float: left;
    -webkit-backface-visibility: hidden;
    -webkit-touch-callout: none
}

.owl-carousel .owl-item img {
    display: block;
    width: 100%
}

.owl-carousel .owl-dots.disabled,
.owl-carousel .owl-nav.disabled {
    display: none
}

.no-js .owl-carousel,
.owl-carousel.owl-loaded {
    display: block
}

.owl-carousel .owl-dot,
.owl-carousel .owl-nav .owl-next,
.owl-carousel .owl-nav .owl-prev {
    cursor: pointer;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.owl-carousel .owl-nav button.owl-next,
.owl-carousel .owl-nav button.owl-prev,
.owl-carousel button.owl-dot {
    background: 0 0;
    color: inherit;
    border: none;
    padding: 0 !important;
    font: inherit
}

.owl-carousel.owl-loading {
    opacity: 0;
    display: block
}

.owl-carousel.owl-hidden {
    opacity: 0
}

.owl-carousel.owl-refresh .owl-item {
    visibility: hidden
}

.owl-carousel.owl-drag .owl-item {
    -ms-touch-action: pan-y;
    touch-action: pan-y;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.owl-carousel.owl-grab {
    cursor: move;
    cursor: grab
}

.owl-carousel.owl-rtl {
    direction: rtl
}

.owl-carousel.owl-rtl .owl-item {
    float: right
}

.owl-carousel .animated {
    animation-duration: 1s;
    animation-fill-mode: both
}

.owl-carousel .owl-animated-in {
    z-index: 0
}

.owl-carousel .owl-animated-out {
    z-index: 1
}

.owl-carousel .fadeOut {
    animation-name: fadeOut
}

@keyframes fadeOut {
    0% {
        opacity: 1
    }

    100% {
        opacity: 0
    }
}

.owl-height {
    transition: height .5s ease-in-out
}

.owl-carousel .owl-item .owl-lazy {
    opacity: 0;
    transition: opacity .4s ease
}

.owl-carousel .owl-item .owl-lazy:not([src]),
.owl-carousel .owl-item .owl-lazy[src^=""] {
    max-height: 0
}

.owl-carousel .owl-item img.owl-lazy {
    transform-style: preserve-3d
}

.owl-carousel .owl-video-wrapper {
    position: relative;
    height: 100%;
    background: #000
}

.owl-carousel .owl-video-play-icon {
    position: absolute;
    height: 80px;
    width: 80px;
    left: 50%;
    top: 50%;
    margin-left: -40px;
    margin-top: -40px;
    background: url(owl.video.play.png) no-repeat;
    cursor: pointer;
    z-index: 1;
    -webkit-backface-visibility: hidden;
    transition: transform .1s ease
}

.owl-carousel .owl-video-play-icon:hover {
    -ms-transform: scale(1.3, 1.3);
    transform: scale(1.3, 1.3)
}

.owl-carousel .owl-video-playing .owl-video-play-icon,
.owl-carousel .owl-video-playing .owl-video-tn {
    display: none
}

.owl-carousel .owl-video-tn {
    opacity: 0;
    height: 100%;
    background-position: 50%;
    background-repeat: no-repeat;
    background-size: contain;
    transition: opacity .4s ease
}

.owl-carousel .owl-video-frame {
    position: relative;
    z-index: 1;
    height: 100%;
    width: 100%
}

/* style.css */

.sidebarAlertImg > img {
    width: 425px;
    height: 280px;
}
#headerLogo img{
    max-height:76px;
    max-width: 256px;
}
.hrSection{
    background: #123146;
    border: 5px solid #123146;
    margin: 0;
}
.helloUser{
    padding-right: 10px;
    cursor: unset;
}
.helloUser:hover,.helloUser:focus,.helloUser:active{
    padding-right: 10px;
    cursor: unset;
    text-decoration: none !important;
    color: #ffb612 !important;
}
a.smlA{
    display:none;
}
.pb-20{padding-bottom:20px;}
.visible-desktop-inline{display:inline!important;}
@media (max-width: 767px) {
.visible-desktop-inline{display:none!important;}
}
@media (max-width: 996px) {
    a.lgsA{
        display:none !important;
    }
    a.smlA{
        display:block !important;
    }
}
#home-linksToResource{
    padding: 0 !important;
}
#home-linksToResource .container{
    max-width: unset !important;
    padding: 0 !important;
}
#home-linksToResource .container .span6{
    width: 50% !important;
    margin: 0 !important;
    text-align: center;
}
#home-linksToResource .container .span12{
    margin: 0 !important;
    text-align: center;
}
#home-linksToResource .container .span12 p{
    margin-bottom: 0 !important;
}
#home-linksToResource .container .span6 p{
    margin-bottom: 0 !important;
}
#home-linksToResource .container .span6 img{
    width:100%;
}
#home-linksToResource .container .span12 img{
    width:100%;
}
@media (max-width: 996px) {
    #home-linksToResource .container .span6{
        width: 100% !important;
        margin: 0 !important;
    }
    #home-linksToResource .container .span6 img{
    }
}