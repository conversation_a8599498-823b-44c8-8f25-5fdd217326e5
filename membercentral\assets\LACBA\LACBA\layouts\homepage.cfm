<cfoutput>
<cfset local.zoneHContent = ""/>
<cfif application.objCMS.getZoneItemCount(zone='H',event=event)>
	<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['H'],1)>
		<cfset local.zoneHContent =  trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones['H'][1].data,"<p>",""),"</p>",""))/>
	</cfif>
</cfif>
<cfset local.zoneIContent = ""/>
<cfif application.objCMS.getZoneItemCount(zone='I',event=event)>
	<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['I'],1)>
		<cfset local.zoneIContent =  trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones['I'][1].data,"<p>",""),"</p>",""))/>
	</cfif>
</cfif>
<cfset local.zoneNContent = ""/>
<cfif application.objCMS.getZoneItemCount(zone='N',event=event)>
	<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['N'],1)>
		<cfset local.zoneNContent =  trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones['N'][1].data,"<p>",""),"</p>",""))/>
	</cfif>
</cfif>

<cfset local.zoneMContent = ""/>
<cfif application.objCMS.getZoneItemCount(zone='M',event=event)>
	<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['M'],1)>
		<cfset local.zoneMContent =  trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones['M'][1].data,"<p>",""),"</p>",""))/>
	</cfif>
</cfif>

<cfset local.zoneOlength = 0>
<cfset local.zoneOwidth = 'span12'>
<cfset local.zoneOCnt = ''>
<cfif application.objCMS.getZoneItemCount(zone='O',event=event)>
	<cfset local.zoneOContent = event.getValue("mc_pageDefinition").pageZones['O']>
	
	<cfloop from="1" to="#arrayLen(local.zoneOContent)#" index="local.thisItem" >
		<cfif lcase(trim(local.zoneOContent[local.thisItem].view)) EQ 'echo' AND len(trim(local.zoneOContent[local.thisItem].data))>
			<cfif application.objCMS.getZoneItemCount(zone='O',event=event) gt 1>
				<cfset local.zoneOwidth = 'span6'>
			</cfif>
			<cfset local.zoneOCnt &= '<div class="#local.zoneOwidth#">'& #local.zoneOContent[local.thisItem].data# &' </div>'>
			<cfset local.zoneOlength = local.zoneOlength + 1>	
			<cfif local.zoneOlength eq 2>
				<cfbreak>
			</cfif>
		</cfif>
	</cfloop>
</cfif>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
	<head>
		<cfinclude template="head.cfm">
	</head>
	<cfif event.getValue('mc_pageDefinition.layoutMode','normal') neq "direct">
		<body id="home">
			<cfinclude template="header.cfm">
			<span class="slideWrapper hide">
				<cfif application.objCMS.getZoneItemCount(zone='Main',event=event)>
					<cfset local.mainContent = event.getValue("mc_pageDefinition").pageZones['Main']>
					<cfloop from="1" to="#arrayLen(local.mainContent)#" index="local.thisItem" >
						<cfif lcase(trim(local.mainContent[local.thisItem].view)) EQ 'echo' AND len(trim(local.mainContent[local.thisItem].data))>
							#local.mainContent[local.thisItem].data#
						</cfif>
					</cfloop>
				</cfif>
			</span>
			<div id="banner">
				<div id="banner-slides" class="flexslider">
					<ul class="slides clearfix slideHolder">
					</ul>
				</div>
			</div>

			<cfif local.zoneOlength gt 0>
				<div id="home-linksToResource">
					<div class="container">
						<div class="row-fluid">
							#local.zoneOCnt#
						</div>
					</div>
				</div>
			</cfif>



			<cfif len(trim(local.zoneDContent)) OR len(trim(local.zoneUpcommingContent))>
				<div id="bottom-posts">
					<div class="container <cfif NOT len(trim(local.zoneFContent))>pb-20</cfif>">
						<div id="latest-news">
							#local.zoneDContent#
						</div> 
						<div id="upcoming-events" class="upcomingEvents">
							<cfif len(trim(local.zoneUpcommingContent))>
								<span class="eventHolder"></span>
							</cfif>
						</div>
					</div>
				</div>
			</cfif>
			<cfif len(trim(local.zoneFContent))>
				<div id="bottom-ad">
					<div id="bottom-ad-bx">
						#local.zoneFContent#
					</div>
				</div>
			</cfif>
			<span class="sponsorHolder"></span>
			<div id="home-sections" class="hide">
				<cfif len(trim(local.zoneHContent))>
					<span class="zoneHWrapper hide">#local.zoneHContent#</span>
					<section id="section-1" class="justify-content-end">
						<div class="bg-image aos-item">
						</div>
						<div class="container">
							<div class="row-fluid flex justify-content-end">
								<div class="span6 section1Content">
								</div>
							</div>
						</div>
					</section>
				</cfif>
				<cfif len(trim(local.zoneIContent))>
					<span class="zoneIWrapper hide">#local.zoneIContent#</span>
					<section id="section-2" class="justify-content-start">
						<div class="bg-image aos-item">
						</div>
						<div class="container">
							<div class="row-fluid flex justify-content-start">
								<div class="span6 section2Content">
								</div>
							</div>
						</div>
					</section>
				</cfif>
				<cfif application.objCMS.getZoneItemCount(zone='J',event=event)>
					<span class="zoneJWrapper hide">						
						<cfset local.zoneJContent = event.getValue("mc_pageDefinition").pageZones['J']>
						<cfloop from="1" to="#arrayLen(local.zoneJContent)#" index="local.thisItem" >
							<cfif lcase(trim(local.zoneJContent[local.thisItem].view)) EQ 'echo' AND len(trim(local.zoneJContent[local.thisItem].data))>
								#local.zoneJContent[local.thisItem].data#
							</cfif>
						</cfloop>						
					</span>
					<cfif len(trim(local.zoneJContent[1].data))>
						<hr class="hrSection">
						<section id="section-3" class="justify-content-start">
							<div class="bg-image aos-item">
							</div>
							<div class="container">
								<div class="row-fluid flex justify-content-start">
									<div id="section3Content" class="span12">
										<div class="row-fluid flex justify-content-center pt-2">
											<div class="span8 text-center subSection1Content">
											</div>
										</div>
										<div class="row-fluid flex pt-5 subSection2Content">
										</div>
									</div>
								</div>
							</div>
						</section>
					</cfif>
				</cfif>
			</div>
			<section id="home-bottom">
				<div class="container">
					<div class="row-fluid">
						<div class="span12">
							<div id="home-publications">
								#local.zoneMContent#
							</div>
							<span class="zoneNWrapper hide">#local.zoneNContent#</span>
							<div id="social-tabs-bx">
								<div id="social-tabs">
								</div>
							</div>
						</div>
					</div>
				</div>
			</section>
			<cfinclude template="footer.cfm">
		</body>
	<cfelse>
		<body class="innerPage-content">
			<cfif application.objCMS.getZoneItemCount(zone='Main',event=event)>
				#application.objCMS.renderZone(zone='Main',event=event)#
			</cfif>
		</body>
	</cfif>
	<cfinclude template="foot.cfm">
	<cfinclude template="toolBar.cfm">
</html>
</cfoutput>