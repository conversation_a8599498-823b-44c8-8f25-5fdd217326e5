<cfcomponent extends="model.admin.admin" output="no">
	<cfset variables.defaultEvent = 'controller'>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = buildRightAssignments(this.siteResourceID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'));

		// build quick links -------------------------------------------------------------------------- ::
		this.link.message = buildCurrentLink(arguments.event,"message");
		this.link.list = buildCurrentLink(arguments.event,"list");
		this.link.add = buildCurrentLink(arguments.event,"add");
		this.link.edit = buildCurrentLink(arguments.event,"edit");
		this.link.save = buildCurrentLink(arguments.event,"save");
		this.link.cardsOnFile = buildCurrentLink(arguments.event,"cardsOnFile");
		this.link.viewPaymentProfileUsage = buildCurrentLink(arguments.event,"viewPaymentProfileUsage") & "&mode=direct";
		this.link.viewSavingsCalculator = buildCurrentLink(arguments.event,"viewSavingsCalculator") & "&mode=direct";

		// Run Assigned Method ---------------------------------------------------------------------- ::
		local.methodToRun = this[arguments.event.getValue('mca_ta')];

		// pass the argument collection to the current method and execute it. ----------------------- ::
		return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="cardsOnFile" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.profileID = arguments.event.getValue('profileID',0)>

		<cfquery name="local.merchantProfile" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT mp.profileID, mp.profileName, g.gatewayType, g.tokenStore
			FROM dbo.mp_profiles AS mp
			INNER JOIN dbo.mp_gateways AS g ON g.gatewayID = mp.gatewayID
			WHERE mp.profileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.profileID#">
			AND mp.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">
			AND mp.status IN ('A','I')
			AND g.isActive = 1;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED; 
		</cfquery>
		<cfif NOT local.merchantProfile.recordcount>
			<cflocation url="#this.link.message#&ec=INVMP&mode=direct" addtoken="no">
		</cfif>

		<cfif local.merchantProfile.tokenStore EQ 'bankdraft'>
			<cfset local.COFType = "Bank Account">
			<cfset local.COFTypePlural = "Bank Accounts">
			<cfset local.COFTypeDesc = "Bank Accounts">

			<cfset local.sharedBankDraftProfileMessage = CreateObject("component","model.system.platform.gateways.BankDraft").generateSharedProfileMessage(orgID=arguments.event.getValue('mc_siteinfo.orgID'))>
		<cfelse>
			<cfset local.COFType = "Card">
			<cfset local.COFTypePlural = "Cards">
			<cfset local.COFTypeDesc = "Cards on File">
		</cfif>

		<cfset local.cardsOnFileLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=merchantProfilesJSON&meth=cardsOnFile&profileID=#local.profileID#&mode=stream">
		<cfset local.exportCOFLink = buildCurrentLink(arguments.event,"exportProfileCardsOnFile") & "&profileID=#local.profileID#&mode=stream">

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_payment_cardsOnFile.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="viewPaymentProfileUsage" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.profileID = arguments.event.getValue('profileID',0)>
		<cfset local.selectedTab = arguments.event.getTrimValue("tab","groupUsage")>
		<cfset local.lockTab = arguments.event.getTrimValue("lockTab","false") ? local.selectedTab : "">
		<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		
		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_getPaymentProfilesUsage">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.profileID#">
			<cfprocresult name="local.qryPaymentProfileUsage">
		</cfstoredproc>

		<cfif local.qryPaymentProfileUsage.recordcount>	
			<cfset local.profileUsageList = valueList(local.qryPaymentProfileUsage.profileUsage)>
		</cfif>
		
		<cfquery name="local.qryMerchantProfile" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT mp.profileID, mp.profileName, g.tokenStore
			FROM dbo.mp_profiles AS mp
			INNER JOIN dbo.mp_gateways AS g ON g.gatewayID = mp.gatewayID
			WHERE mp.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">
			AND mp.profileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.profileID#">
			AND mp.status = 'A'
			AND g.isActive = 1;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfquery name="local.qrySiteMerchantProfiles" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT mp.profileID, mp.profileName, g.tokenStore, ROW_NUMBER() OVER (ORDER BY profileName)
			FROM dbo.mp_profiles AS mp
			INNER JOIN dbo.mp_gateways AS g ON g.gatewayID = mp.gatewayID
			WHERE mp.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">
			AND mp.status = 'A'
			AND g.isActive = 1
			AND mp.profileID <> <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.profileID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_payment_profiles_usage.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="viewSavingsCalculator" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>

		<cfset local.reportMode = "oneprofile">
		<cfif arguments.event.getValue('mc_siteInfo.siteCode') eq "MC" AND arguments.event.getValue('mpid',0) EQ -1>
			<cfset local.reportMode = "allprofiles">
		</cfif>

		<!--- if not on MC, verify profile belongs to site --->
		<cfif local.reportMode eq "oneprofile">
			<cfquery name="local.merchantProfile" datasource="#application.dsn.memberCentral.dsn#">
				SELECT mp.profileID, mp.profileName, mp.profileCode, s.siteCode, s.siteName, mp.enableProcessingFeeDonation, mp.enableSurcharge
				FROM dbo.mp_profiles as mp
				INNER JOIN dbo.sites as s on s.siteID = mp.siteID
				WHERE mp.profileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mpid',0)#">
				AND mp.[status] IN ('A','I')
				<cfif arguments.event.getValue('mc_siteInfo.siteCode') neq "MC">
					AND mp.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">
				</cfif>
				AND mp.enableMCPay = 1
				AND (mp.enableProcessingFeeDonation = 1 OR mp.enableSurcharge = 1)
			</cfquery>
			<cfif NOT local.merchantProfile.recordcount>
				<cflocation url="#this.link.message#&ec=INVMP" addtoken="no">
			</cfif>
		</cfif>

		<cfset local.firstOfCurrentMonth = createDate(year(now()), month(now()), 1)>
		<cfset local.startDate = dateAdd("m", -1, local.firstOfCurrentMonth)>
		<cfset local.endDate = createDate(year(local.startDate), month(local.startDate), daysInMonth(local.startDate))>
		<cfset local.estCardfeePct = 3.25>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_SavingsCalculator.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="recalculateSavings" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="profileID" type="numeric" required="true">
		<cfargument name="fRangeFrom" type="date" required="true">
		<cfargument name="fRangeTo" type="date" required="true">
		<cfargument name="fEstCardfeePct" type="numeric" required="true">
		
		<cfset var local = structNew()>
		<cfset local.result = { "success": false }>

		<cftry>
			<cfset local.reportMode = "oneprofile">
			<cfif arguments.mcproxy_siteCode eq "MC" AND arguments.profileID EQ -1>
				<cfset local.reportMode = "allprofiles">
			</cfif>

			<!--- if not on MC, verify profile belongs to site --->
			<cfif local.reportMode eq "oneprofile">
				<cfquery name="local.merchantProfile" datasource="#application.dsn.memberCentral.dsn#">
					SELECT profileID
					FROM dbo.mp_profiles
					WHERE profileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.profileID#">
					AND [status] IN ('A','I')
					<cfif arguments.mcproxy_siteCode neq "MC">
						AND siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
					</cfif>
					AND enableMCPay = 1
					AND (enableProcessingFeeDonation = 1 OR enableSurcharge = 1)
				</cfquery>
				<cfif NOT local.merchantProfile.recordcount>
					<cfthrow message="Invalid payment profile.">
				</cfif>
			</cfif>

			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="mp_getProcessingFeeSavingsCalculator">
				<cfif local.reportMode eq "oneprofile">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.merchantProfile.profileID#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
				</cfif>
				<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.fRangeFrom#">
				<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.fRangeTo# 23:59:59.997">
				<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" value="#arguments.fEstCardfeePct#">
				<cfprocresult name="local.qryStats">
			</cfstoredproc>

			<cfif local.reportMode eq "oneprofile">
				<cfset local.result = {
					"success": true,
					"data": {
						"feetype":val(local.qryStats.enableProcessingFeeDonation) eq 1 ? 1 : 2,
						"grosspaymentcount":val(local.qryStats.GrossPaymentCount),
						"grosspaymentamount":val(local.qryStats.GrossPaymentAmount),
						"estimatedcardfees":val(local.qryStats.EstimatedCardFees)
					}
				}>
				<cfif val(local.qryStats.enableProcessingFeeDonation) eq 1>
					<cfset structInsert(local.result.data,"countpfdoffered",val(local.qryStats.CountPFDOffered))>
					<cfset structInsert(local.result.data,"pctpfdoffered",val(local.qryStats.PctPFDOffered))>
					<cfset structInsert(local.result.data,"countpfdaccepted",val(local.qryStats.CountPFDAccepted))>
					<cfset structInsert(local.result.data,"pctpfdaccepted",val(local.qryStats.PctPFDAccepted))>
					<cfset structInsert(local.result.data,"sumpfdrevenue",val(local.qryStats.SumPFDRevenue))>
					<cfset structInsert(local.result.data,"pctpfdshareofcardfeescoveredestimate",val(local.qryStats.PctPFDShareOfCardFeesCoveredEstimate))>
				<cfelse>
					<cfset structInsert(local.result.data,"countsurcharges",val(local.qryStats.CountSurcharges))>
					<cfset structInsert(local.result.data,"sumsurchargerevenue",val(local.qryStats.SumSurchargeRevenue))>
					<cfset structInsert(local.result.data,"pctsurchargeshareofcardfeescoveredestimate",val(local.qryStats.PctSurchargeShareOfCardFeesCoveredEstimate))>
				</cfif>
			<cfelse>
				<cfset local.qryStats.deleteColumn("profileID")>
				<cfset local.qryStats.setColumnNames([
						"SiteCode","PaymentProfile","PFDEnabled","SurchargeEnabled","GrossPayCount","GrossPayAmount","EstCardFees", 
						"currentPFDPct","CountPFDOffered","PctPFDOffered","CountPFDAccepted","PctPFDAccepted","SumPFDRevenue","PctPFDShareOfCardFeesCoveredEst",
						"currentSurchargePct","CountSurcharges","SumSurchargeRevenue","PctSurchargeShareOfCardFeesCoveredEst"
				])>
				<cfset local.totalsRowNum = local.qryStats.recordCount + 3>

				<cfset local.strFolder = application.objDocDownload.createHoldingFolder()>
				<cfset local.reportFileNamexls = "SavingsCalculator-#dateformat(arguments.fRangeFrom,"yyyymmdd")#-#dateformat(arguments.fRangeTo,"yyyymmdd")#.xls">
				<cfset local.spreadsheet = CreateObject("component","modules.spreadsheet-cfml.spreadsheet").init()>
				<cfset local.spreadsheet.newChainable("xls")
					.addRows(data=local.qryStats, includeQueryColumnNames=true, autoSizeColumns=true)
					.addRow(data="")
					.addRow(data="")
					.addRow(data="")
					.formatColumns({font="Aptos Narrow", fontsize="11"}, "1-")
					.formatRows({bold="true"}, "1", false)
					.setColumnWidth(1, 9)
					.setColumnWidth(2, 36)
					.setColumnWidth(3, 12)
					.setColumnWidth(4, 17)
					.setColumnWidth(5, 14)
					.setColumnWidth(6, 16)
					.setColumnWidth(7, 14)
					.setCellValue("Totals",local.totalsRowNum,2)
					.setCellFormula("SUM(E2:E#local.totalsRowNum-2#)",local.totalsRowNum,5)
					.setCellFormula("SUM(F2:F#local.totalsRowNum-2#)",local.totalsRowNum,6)
					.setCellFormula("SUM(G2:G#local.totalsRowNum-2#)",local.totalsRowNum,7)
					.setCellValue("at #arguments.fEstCardfeePct#%",local.totalsRowNum+1,7)
					.setCellFormula("SUM(I2:I#local.totalsRowNum-2#)",local.totalsRowNum,9)
					.setCellFormula("(I#local.totalsRowNum#/E#local.totalsRowNum#)",local.totalsRowNum,10)
					.setCellFormula("SUM(K2:K#local.totalsRowNum-2#)",local.totalsRowNum,11)
					.setCellFormula("(K#local.totalsRowNum#/I#local.totalsRowNum#)",local.totalsRowNum,12)
					.setCellFormula("SUM(M2:M#local.totalsRowNum-2#)",local.totalsRowNum,13)
					.setCellFormula("(M#local.totalsRowNum#/G#local.totalsRowNum#)",local.totalsRowNum,14)
					.setCellValue("at #arguments.fEstCardfeePct#%",local.totalsRowNum+1,14)
					.setCellFormula("SUM(P2:P#local.totalsRowNum-2#)",local.totalsRowNum,16)
					.setCellFormula("SUM(Q2:Q#local.totalsRowNum-2#)",local.totalsRowNum,17)
					.setCellFormula("(Q#local.totalsRowNum#/G#local.totalsRowNum#)",local.totalsRowNum,18)
					.setCellValue("at #arguments.fEstCardfeePct#%",local.totalsRowNum+1,18)
					.formatCell({dataformat="0%"}, local.totalsRowNum, 10, false)
					.formatCell({dataformat="0%"}, local.totalsRowNum, 12, false)
					.formatCell({dataformat="0%"}, local.totalsRowNum, 14, false)
					.formatCell({dataformat="0%"}, local.totalsRowNum, 18, false)
					.formatRow({bold="true"}, local.totalsRowNum, false)
					.formatRow({alignment="RIGHT"}, local.totalsRowNum+1, false)
					.formatCellRange({alignment="RIGHT"}, 1, 1, 5, 18, false)
					.formatColumns({fgcolor="218,242,208"}, "8-14", false)
					.formatColumns({leftborder="MEDIUM"}, "8", false)
					.formatColumns({fgcolor="202,237,251"}, "15-18", false)
					.formatColumns({leftborder="MEDIUM"}, "15", false)
					.formatColumns({dataformat="$##,####0.00_);[Red]($##,####0.00)"}, "6,7,13,17", false)
					.renameSheet("Savings",1)
					.write("#local.strFolder.folderPath#/#local.reportFileNamexls#")>
				
				<cfset local.stDownloadURL = application.objDocDownload.createDownloadURL(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileNamexls#", displayName=local.reportFileNamexls, deleteSourceFile=1)>
				<cfset local.result = {
					"success": true,
					"data": {
						"durl":local.stDownloadURL
					}
				}>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>
		
		<cfreturn local.result>
	</cffunction>

	<cffunction name="removeFilteredCardsOnFile" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="profileID" type="numeric" required="true">
		<cfargument name="fSuccessPayDateFrom" type="string" required="true">
		<cfargument name="fSuccessPayDateTo" type="string" required="true">
		<cfargument name="fExpirationDateFrom" type="string" required="true">
		<cfargument name="fExpirationDateTo" type="string" required="true">
		<cfargument name="fCardStatus" type="string" required="true">
		<cfargument name="fCardAssocTo" type="string" required="true">

		<!--- force fCardAssocTo to be NotAssoc --->
		<cfset arguments.fCardAssocTo = "NotAssoc">

		<cfset var removeResult = manageFilteredCardsOnFile(siteID=arguments.mcproxy_siteID, orgID=arguments.mcproxy_orgID, profileID=arguments.profileID, 
								fSuccessPayDateFrom=arguments.fSuccessPayDateFrom, fSuccessPayDateTo=arguments.fSuccessPayDateTo,
								fExpirationDateFrom=arguments.fExpirationDateFrom, fExpirationDateTo=arguments.fExpirationDateTo, 
								fCardStatus=arguments.fCardStatus, fCardAssocTo=arguments.fCardAssocTo, mode="queueDelete")>

		<cfreturn { "success":removeResult.success EQ 1 }>
	</cffunction>

	<cffunction name="copyPaymentProfileUsage" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="copyFromProfileID" type="numeric" required="true">
		<cfargument name="copyToProfileID" type="numeric" required="true">
		<cfargument name="incEv" type="numeric" required="true">
		<cfargument name="invVGC" type="numeric" required="true">
		<cfargument name="incInv" type="numeric" required="true">
		<cfargument name="incInvProf" type="numeric" required="true">
		<cfargument name="incJB" type="numeric" required="true">
		<cfargument name="incMemberFields" type="numeric" required="true">
		<cfargument name="incReferrals" type="numeric" required="true">
		<cfargument name="incSW" type="numeric" required="true">
		<cfargument name="incStore" type="numeric" required="true">
		<cfargument name="incSubs" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfset local.siteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='MerchantProfilesAdmin', siteID=arguments.mcproxy_siteID)>
			<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>
			<cfif  local.tmpRights.mpManage is not 1>
				<cfthrow message="invalid request">
 			</cfif>

			<cfstoredproc procedure="ams_copyPaymentProfileUsage" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.copyFromProfileID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.copyToProfileID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.incEv#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.invVGC#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.incInv#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.incInvProf#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.incJB#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.incMemberFields#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.incReferrals#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.incSW#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.incStore#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.incSubs#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset local.data.success = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="removePaymentProfileUsage" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="profileID" type="numeric" required="true">
		<cfargument name="rmvEv" type="numeric" required="true">
		<cfargument name="rmvVGC" type="numeric" required="true">
		<cfargument name="rmvInv" type="numeric" required="true">
		<cfargument name="rmvInvProf" type="numeric" required="true">
		<cfargument name="rmvJB" type="numeric" required="true">
		<cfargument name="rmvMemberFields" type="numeric" required="true">
		<cfargument name="rmvReferrals" type="numeric" required="true">
		<cfargument name="rmvSW" type="numeric" required="true">
		<cfargument name="rmvStore" type="numeric" required="true">
		<cfargument name="rmvSubs" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfset local.siteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='MerchantProfilesAdmin', siteID=arguments.mcproxy_siteID)>
			<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>
			<cfif  local.tmpRights.mpManage is not 1>
				<cfthrow message="invalid request">
 			</cfif>

			<cfquery name="local.qryRemovePayProfileUsages" datasource="#application.dsn.membercentral.dsn#">
				EXEC dbo.ams_removePaymentProfileUsage
					@siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">,
					@profileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.profileID#">,
					@rmvEv = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.rmvEv#">,
					@rmvVGC = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.rmvVGC#">,
					@rmvInv = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.rmvInv#">,
					@rmvInvProf = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.rmvInvProf#">,
					@rmvJB = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.rmvJB#">,
					@rmvMemberFields = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.rmvMemberFields#">,
					@rmvReferrals = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.rmvReferrals#">,
					@rmvSW = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.rmvSW#">,
					@rmvStore = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.rmvStore#">,
					@rmvSubs = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.rmvSubs#">;
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset local.data.success = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="exportProfileCardsOnFile" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>

		<cfquery name="local.qryMP" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT g.tokenStore
			FROM dbo.mp_profiles AS mp
			INNER JOIN dbo.mp_gateways AS g ON g.gatewayID = mp.gatewayID
			WHERE mp.profileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('profileID',0)#">
			AND mp.status IN ('A','I');
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		<cfif local.qryMP.tokenStore EQ 'bankdraft'>
			<cfset local.reportFileName = "BankAccountsOnFile.csv">
		<cfelse>
			<cfset local.reportFileName = "CardsOnFile.csv">
		</cfif>

		<cfset local.qryExportResult = CreateObject("component","merchantProfilesAdmin").manageFilteredCardsOnFile(siteID=arguments.event.getValue('mc_siteinfo.siteid'),
										orgID=arguments.event.getValue('mc_siteinfo.orgID'), profileID=arguments.event.getValue('profileID',0), 
										fSuccessPayDateFrom=arguments.event.getValue('fSuccessPayDateFrom',''), fSuccessPayDateTo=arguments.event.getValue('fSuccessPayDateTo',''), 
										fExpirationDateFrom=arguments.event.getValue('fExpirationDateFrom',''), fExpirationDateTo=arguments.event.getValue('fExpirationDateTo',''), 
										fCardStatus=arguments.event.getValue('fCardStatus',''), fCardAssocTo=arguments.event.getValue('fCardAssocTo',''), mode="export", 
										reportFileName="#local.strFolder.folderPathUNC#\#local.reportFileName#")>

		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
		<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>
		<cfif not local.docResult>
			<cfreturn returnAppStruct("An error occurred while producing the export.","echo")>
		</cfif>
	</cffunction>

	<cffunction name="manageFilteredCardsOnFile" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="profileID" type="numeric" required="true">
		<cfargument name="fSuccessPayDateFrom" type="string" required="true">
		<cfargument name="fSuccessPayDateTo" type="string" required="true">
		<cfargument name="fExpirationDateFrom" type="string" required="true">
		<cfargument name="fExpirationDateTo" type="string" required="true">
		<cfargument name="fCardStatus" type="string" required="true">
		<cfargument name="fCardAssocTo" type="string" required="true">
		<cfargument name="mode" type="string" required="true">
		<cfargument name="start" type="numeric" required="false" default="0">
		<cfargument name="count" type="numeric" required="false" default="0">
		<cfargument name="reportFileName" type="string" required="false">

		<cfset var qryCOF = "">
		<cfset var qryMP = "">

		<cfquery name="qryMP" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT g.tokenStore
			FROM dbo.mp_profiles AS mp
			INNER JOIN dbo.mp_gateways AS g ON g.gatewayID = mp.gatewayID
			WHERE mp.profileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.profileID#">
			AND mp.status IN ('A','I');
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfquery name="qryCOF" datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tmpMPPList') IS NOT NULL
					DROP TABLE ##tmpMPPList;
				CREATE TABLE ##tmpMPPList (payProfileID int, profileID int, memberID int, customerProfileID varchar(50), paymentProfileID varchar(50), 
					cardType varchar(30), cardDetail varchar(50), expDate date, acctType varchar(8), dateAdded datetime, lastSuccessfulPayDate datetime, 
					cardStatus varchar(10), surchargeEligible bit, firstname varchar(75), lastname varchar(75), memberNumber varchar(50), company varchar(200), 
					cardAssocToContrib int, cardAssocToSubs int, cardAssocToInv int, row int);

				DECLARE @orgID int, @siteID int, @profileID int, @totalCount int, @posStart int, @posStartAndCount int;
				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
				SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;
				SET @profileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.profileID#">;
				SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.start#">;
				SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.count#">;

				<cfif qryMP.tokenStore EQ 'bankdraft'>
					INSERT INTO ##tmpMPPList (payProfileID, profileID, memberID, acctType, cardDetail, dateAdded, lastSuccessfulPayDate, cardStatus, 
						surchargeEligible, firstname, lastname, memberNumber, company, cardAssocToContrib, cardAssocToSubs, cardAssocToInv, row)
					SELECT mpp.payProfileID, mpp.profileID, m.memberID, b.acctType, mpp.detail, mpp.dateAdded, MAX(tph.datePaid), 
						CASE WHEN mpp.failedCount IS NOT NULL THEN 'Failed' ELSE 'Good' END AS cardStatus,
						0 AS surchargeEligible, m.firstName, m.lastName, m.memberNumber, m.company, 
						COUNT(cpp.payProfileID), COUNT(ss.payProfileID), COUNT(tri.payProfileID),
						ROW_NUMBER() OVER (ORDER BY m.lastName, m.firstName, m.memberNumber, mpp.detail) AS row
					FROM dbo.tr_bankAccounts AS b
					INNER JOIN dbo.ams_memberPaymentProfiles AS mpp ON mpp.payProfileID = b.MPPPayProfileID
						AND mpp.status = 'A'
						<cfif arguments.fCardStatus EQ 1>
							AND mpp.failedCount IS NULL 
						<cfelseif arguments.fCardStatus EQ 0>
							AND mpp.failedCount > 0 
						</cfif>
					INNER JOIN dbo.mp_profiles AS mp ON mp.profileID = mpp.profileID
					INNER JOIN dbo.ams_members as m ON m.orgID = @orgID
						AND m.status in ('A','I')
						AND m.memberID = mpp.memberID
					<cfif len(arguments.fSuccessPayDateFrom) OR len(arguments.fSuccessPayDateTo)>INNER<cfelse>LEFT OUTER</cfif> JOIN dbo.tr_paymentHistory as tph ON tph.orgID = @orgID
						AND tph.memberPaymentProfileID = mpp.payProfileID
						AND tph.isSuccess = 1
						<cfif len(arguments.fSuccessPayDateFrom)>
							AND tph.datePaid > <cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.fSuccessPayDateFrom#">
						</cfif>
						<cfif len(arguments.fSuccessPayDateTo)>
							AND tph.datePaid < <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.fSuccessPayDateTo# 23:59:59.997">
						</cfif>
					LEFT OUTER JOIN dbo.cp_contributionPayProfiles as cpp ON cpp.payProfileID = mpp.payProfileID
					LEFT OUTER JOIN dbo.sub_subscribers as ss ON ss.orgID = @orgID 
						AND ss.payProfileID = mpp.payProfileID
					LEFT OUTER JOIN dbo.tr_invoices as tri ON tri.orgID = @orgID
						AND tri.payProfileID = mpp.payProfileID
					LEFT OUTER JOIN dbo.mp_cardTypes AS ct ON ct.cardTypeID = mpp.cardTypeID
					WHERE b.orgID = @orgID
					GROUP BY mpp.payProfileID, mpp.profileID, m.memberID, b.acctType, mpp.detail,
						mpp.dateAdded, mpp.failedCount, m.firstName, m.lastName, m.memberNumber, m.company
					<cfif listLen(arguments.fCardAssocTo)>
						HAVING
							<cfif ListFindNoCase(arguments.fCardAssocTo,"NotAssoc")>
								COUNT(tri.payProfileID) + COUNT(ss.payProfileID) + COUNT(cpp.payProfileID) = 0
							<cfelse>
								<cfset local.fCardAssocToCounter = listLen(arguments.fCardAssocTo)>
								<cfif ListFindNoCase(arguments.fCardAssocTo,"Contributions")>
									COUNT(cpp.payProfileID) > 0
									<cfset local.fCardAssocToCounter-->
									<cfif local.fCardAssocToCounter NEQ 0>OR</cfif>
								</cfif>
								<cfif ListFindNoCase(arguments.fCardAssocTo,"Subscriptions")>
									COUNT(ss.payProfileID) > 0
									<cfset local.fCardAssocToCounter-->
									<cfif local.fCardAssocToCounter NEQ 0>OR</cfif>
								</cfif>
								<cfif ListFindNoCase(arguments.fCardAssocTo,"Invoices")>
									COUNT(tri.payProfileID) > 0
								</cfif>
							</cfif>
					</cfif>;
				<cfelse>
					INSERT INTO ##tmpMPPList (payProfileID, profileID, memberID, customerProfileID, paymentProfileID, cardType, cardDetail, expDate, 
						dateAdded, lastSuccessfulPayDate, cardStatus, surchargeEligible, firstname, lastname, memberNumber, company, 
						cardAssocToContrib, cardAssocToSubs, cardAssocToInv, row)
					SELECT mpp.payProfileID, mpp.profileID, m.memberID, mpp.customerProfileID, mpp.paymentProfileID, ct.cardType, mpp.detail, mpp.expiration, 
						mpp.dateAdded, MAX(tph.datePaid), CASE WHEN mpp.failedCount IS NOT NULL THEN 'Failed' ELSE 'Good' END AS cardStatus,
						CASE WHEN mp.enableMCPay = 1 AND mp.enableSurcharge = 1 AND mpp.surchargeEligible = 1 THEN 1 ELSE 0 END AS surchargeEligible,
						m.firstName, m.lastName, m.memberNumber, m.company, COUNT(cpp.payProfileID), COUNT(ss.payProfileID), COUNT(tri.payProfileID),
						ROW_NUMBER() OVER (ORDER BY m.lastName, m.firstName, m.memberNumber, mpp.detail) AS row
					FROM dbo.ams_memberPaymentProfiles AS mpp
					INNER JOIN dbo.mp_profiles AS mp ON mp.siteID = @siteID
						AND mp.profileID = mpp.profileID
					INNER JOIN dbo.ams_members as m ON m.orgID = @orgID
						AND m.status in ('A','I')
						AND m.memberID = mpp.memberID
					<cfif len(arguments.fSuccessPayDateFrom) OR len(arguments.fSuccessPayDateTo)>INNER<cfelse>LEFT OUTER</cfif> JOIN dbo.tr_paymentHistory as tph ON tph.orgID = @orgID
						AND tph.memberPaymentProfileID = mpp.payProfileID
						AND tph.isSuccess = 1
						<cfif len(arguments.fSuccessPayDateFrom)>
							AND tph.datePaid > <cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.fSuccessPayDateFrom#">
						</cfif>
						<cfif len(arguments.fSuccessPayDateTo)>
							AND tph.datePaid < <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.fSuccessPayDateTo# 23:59:59.997">
						</cfif>
					LEFT OUTER JOIN dbo.cp_contributionPayProfiles as cpp ON cpp.payProfileID = mpp.payProfileID
					LEFT OUTER JOIN dbo.sub_subscribers as ss ON ss.orgID = @orgID 
						AND ss.payProfileID = mpp.payProfileID
					LEFT OUTER JOIN dbo.tr_invoices as tri ON tri.orgID = @orgID
						AND tri.payProfileID = mpp.payProfileID
					LEFT OUTER JOIN dbo.mp_cardTypes AS ct ON ct.cardTypeID = mpp.cardTypeID
					WHERE mpp.profileID = @profileID
					AND mpp.status = 'A'
					<cfif arguments.fCardStatus EQ 1>
						AND mpp.failedCount IS NULL 
					<cfelseif arguments.fCardStatus EQ 0>
						AND mpp.failedCount > 0 
					</cfif>
					<cfif len(arguments.fExpirationDateFrom)>
						AND mpp.expiration >= <cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.fExpirationDateFrom#">
					</cfif>
					<cfif len(arguments.fExpirationDateTo)>
						AND mpp.expiration <= <cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.fExpirationDateTo#">
					</cfif>
					GROUP BY mpp.payProfileID, mpp.profileID, m.memberID, mpp.customerProfileID, mpp.paymentProfileID, ct.cardType, mpp.detail, mpp.expiration, 
						mpp.dateAdded, mpp.failedCount, mp.enableMCPay, mp.enableSurcharge, mpp.surchargeEligible, m.firstName, m.lastName, 
						m.memberNumber, m.company
					<cfif listLen(arguments.fCardAssocTo)>
						HAVING
							<cfif ListFindNoCase(arguments.fCardAssocTo,"NotAssoc")>
								COUNT(tri.payProfileID) + COUNT(ss.payProfileID) + COUNT(cpp.payProfileID) = 0
							<cfelse>
								<cfset local.fCardAssocToCounter = listLen(arguments.fCardAssocTo)>
								<cfif ListFindNoCase(arguments.fCardAssocTo,"Contributions")>
									COUNT(cpp.payProfileID) > 0
									<cfset local.fCardAssocToCounter-->
									<cfif local.fCardAssocToCounter NEQ 0>OR</cfif>
								</cfif>
								<cfif ListFindNoCase(arguments.fCardAssocTo,"Subscriptions")>
									COUNT(ss.payProfileID) > 0
									<cfset local.fCardAssocToCounter-->
									<cfif local.fCardAssocToCounter NEQ 0>OR</cfif>
								</cfif>
								<cfif ListFindNoCase(arguments.fCardAssocTo,"Invoices")>
									COUNT(tri.payProfileID) > 0
								</cfif>
							</cfif>
					</cfif>;
				</cfif>

				SELECT @totalCount = COUNT(row) FROM ##tmpMPPList;

				<cfif arguments.mode EQ "queueDelete">
					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

					IF @totalCount > 0 BEGIN
						DECLARE @queueStatusID int, @recordedByMemberID int, @itemGroupUID uniqueidentifier = NEWID();
						SET @recordedByMemberID = <cfqueryparam value="#session.cfcuser.memberdata.memberID#" cfsqltype="cf_sql_integer">;
						EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='deleteCardsOnFile', @queueStatus='readyToProcess', @queueStatusID=@queueStatusID OUTPUT;

						INSERT INTO platformQueue.dbo.queue_deleteCardsOnFile (itemGroupUID, payProfileID, profileID, memberID, customerProfileID, 
							paymentProfileID, recordedByMemberID, statusID, dateAdded, dateUpdated)
						SELECT @itemGroupUID, payProfileID, profileID, memberID, customerProfileID, paymentProfileID, @recordedByMemberID, @queueStatusID, getdate(), getdate()
						FROM ##tmpMPPList;

						EXEC dbo.sched_resumeTask @name='Delete Cards on File', @engine='MCLuceeLinux';

						SELECT 1 AS success;
					END
				<cfelseif arguments.mode EQ "grid">
					SELECT payProfileID, profileID, memberID, customerProfileID, paymentProfileID, cardType, cardDetail, expDate, acctType, 
						dateAdded, lastSuccessfulPayDate, cardStatus, surchargeEligible, firstname, lastname, memberNumber, company, 
						cardAssocToContrib, cardAssocToSubs, cardAssocToInv, @totalCount AS totalCount
					FROM ##tmpMPPList
					WHERE row > @posStart 
					AND row <= @posStartAndCount
					ORDER BY row;
				<cfelseif arguments.mode EQ "export">
					DECLARE @selectsql VARCHAR(MAX);

					IF OBJECT_ID('tempdb..##tmpExportCOF') IS NOT NULL 
						DROP TABLE ##tmpExportCOF;
					
					<cfif qryMP.tokenStore EQ 'bankdraft'>
						CREATE TABLE ##tmpExportCOF (FirstName varchar(75), LastName varchar(75), MemberNumber varchar(50),
							Company varchar(200), [Bank Account Type] varchar(30), [Bank Account Details] varchar(50), 
							[Bank Account Date Added] date, [Bank Account Last Successful Payment Date] date, [Bank Account Status] varchar(10), 
							[Bank Account Associated with Invoices] varchar(3), [Bank Account Associated with Subscriptions] varchar(3),
							[Bank Account Associated with Contributions] varchar(3), row int);

						INSERT INTO ##tmpExportCOF (FirstName, LastName, MemberNumber, Company, [Bank Account Type], [Bank Account Details], 
							[Bank Account Date Added], [Bank Account Last Successful Payment Date], [Bank Account Status], [Bank Account Associated with Invoices],
							[Bank Account Associated with Subscriptions], [Bank Account Associated with Contributions], row)
						SELECT firstname, lastname, memberNumber, company, acctType + ' Checking', cardDetail, dateAdded,
							lastSuccessfulPayDate, cardStatus, CASE WHEN cardAssocToInv > 0 THEN 'Yes' ELSE 'No' END,
							CASE WHEN cardAssocToSubs > 0 THEN 'Yes' ELSE 'No' END, CASE WHEN cardAssocToContrib > 0 THEN 'Yes' ELSE 'No' END,
							row
						FROM ##tmpMPPList;

						SET @selectsql = 'SELECT FirstName, LastName, MemberNumber, Company, [Bank Account Type], [Bank Account Details],
							[Bank Account Date Added], [Bank Account Last Successful Payment Date], [Bank Account Status], [Bank Account Associated with Invoices],
							[Bank Account Associated with Subscriptions], [Bank Account Associated with Contributions], ROW_NUMBER() OVER(ORDER BY row) AS mcCSVorder
							*FROM* ##tmpExportCOF';
					<cfelse>
						CREATE TABLE ##tmpExportCOF (FirstName varchar(75), LastName varchar(75), MemberNumber varchar(50),
							Company varchar(200), [Card Type] varchar(30), [Card Details] varchar(50), Expiration date,
							[Card Date Added] date, [Card Last Successful Payment Date] date, [Card Status] varchar(10), 
							[Card Associated with Invoices] varchar(3), [Card Associated with Subscriptions] varchar(3),
							[Card Associated with Contributions] varchar(3), [Surcharge Eligible] varchar(3), row int);
							
						INSERT INTO ##tmpExportCOF (FirstName, LastName, MemberNumber, Company, [Card Type], [Card Details], Expiration,
							[Card Date Added], [Card Last Successful Payment Date], [Card Status], [Card Associated with Invoices],
							[Card Associated with Subscriptions], [Card Associated with Contributions], [Surcharge Eligible], row)
						SELECT firstname, lastname, memberNumber, company, cardType, cardDetail, expDate, dateAdded,
							lastSuccessfulPayDate, cardStatus, CASE WHEN cardAssocToInv > 0 THEN 'Yes' ELSE 'No' END,
							CASE WHEN cardAssocToSubs > 0 THEN 'Yes' ELSE 'No' END, CASE WHEN cardAssocToContrib > 0 THEN 'Yes' ELSE 'No' END,
							CASE WHEN surchargeEligible = 1 THEN 'Yes' ELSE 'No' END, row
						FROM ##tmpMPPList;
						
						SET @selectsql = 'SELECT FirstName, LastName, MemberNumber, Company, [Card Type], [Card Details], Expiration,
							[Card Date Added], [Card Last Successful Payment Date], [Card Status], [Card Associated with Invoices],
							[Card Associated with Subscriptions], [Card Associated with Contributions], [Surcharge Eligible], 
							ROW_NUMBER() OVER(ORDER BY row) AS mcCSVorder
							*FROM* ##tmpExportCOF';
					</cfif>
					
					EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#arguments.reportFileName#', @returnColumns=1;

					IF OBJECT_ID('tempdb..##tmpExportCOF') IS NOT NULL 
						DROP TABLE ##tmpExportCOF;
				<cfelse>
					SELECT payProfileID, profileID, memberID, customerProfileID, paymentProfileID, cardType, cardDetail, expDate, 
						acctType, dateAdded, lastSuccessfulPayDate, cardStatus, surchargeEligible, firstname, lastname, memberNumber, 
						company, cardAssocToContrib, cardAssocToSubs, cardAssocToInv, @totalCount AS totalCount
					FROM ##tmpMPPList;
				</cfif>

				IF OBJECT_ID('tempdb..##tmpMPPList') IS NOT NULL
					DROP TABLE ##tmpMPPList;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn qryCOF>
	</cffunction>	
		
	<cffunction name="list" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.listPaymentProfilesLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=merchantProfilesJSON&meth=getPaymentProfiles&mode=stream";
			local.sampleBankDraftImportTemplate = buildCurrentLink(arguments.event,"sampleBankDraftImportTemplate") & "&mode=stream";			
			local.sampleAuthorizeCIMImportTemplate = buildCurrentLink(arguments.event,"sampleAuthorizeCIMImportTemplate") & "&mode=stream";
			local.sampleAffiniPayImportTemplate = buildCurrentLink(arguments.event,"sampleAffiniPayImportTemplate") & "&mode=stream";
			local.exportCOFLink = buildCurrentLink(arguments.event,"exportCOF") & "&mode=stream";
			local.payProfilesAuditLogLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=merchantProfilesJSON&meth=getPaymentProfilesAuditLog&mode=stream";
		</cfscript>

		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.mpManage') is not 1>
			<cflocation url="#this.link.message#&ec=LNP" addtoken="no">
		</cfif>

		<cfset local.showImpTemplate = true>
		<cfif arguments.event.getValue('tab','') eq 'import' and len(arguments.event.getValue('importFileName1',''))>
			<cfset local.impData = processBankDraftImport(event=arguments.event)>
			<cfset local.showImpTemplate = false>
		<cfelseif arguments.event.getValue('tab','') eq 'import' and len(arguments.event.getValue('importFileName2',''))>
			<cfset local.impData = processAuthorizeCIMImport(event=arguments.event)>
			<cfset local.showImpTemplate = false>
		</cfif>

		<cfquery name="local.qryImportBankDraft" datasource="#application.dsn.memberCentral.dsn#">
			SELECT count(*) AS bankDraftCount
			FROM dbo.mp_profiles AS mp
			INNER JOIN dbo.mp_gateways g ON mp.gatewayID = g.gatewayID
			WHERE mp.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
			AND mp.status = 'A'
			AND g.tokenStore = 'BankDraft'
		</cfquery>
		
		<cfquery name="local.qryImportAuthorizeCIM" datasource="#application.dsn.memberCentral.dsn#">
			SELECT count(*) AS autCimCount
			FROM dbo.mp_profiles AS mp
			INNER JOIN dbo.mp_gateways g ON mp.gatewayID = g.gatewayID
			WHERE mp.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
			AND mp.status= 'A'
			AND g.gatewayType = 'AuthorizeCCCIM'
		</cfquery>

		<cfquery name="local.qryImportAffiniPayCIM" datasource="#application.dsn.memberCentral.dsn#">
			SELECT TOP 1 mp.gatewayID, mp.profileID, mp.gatewayUsername
			FROM dbo.mp_profiles AS mp
			INNER JOIN dbo.mp_gateways as g ON mp.gatewayID = g.gatewayID
			where mp.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
			AND mp.status= 'A'
			AND g.gatewayType = 'AffiniPayCC'
		</cfquery>

		<cfquery name="local.qryExportCOF" datasource="#application.dsn.memberCentral.dsn#">
			SELECT mp.profileID, mp.profileName
			FROM dbo.mp_profiles AS mp
			INNER JOIN dbo.mp_gateways g ON mp.gatewayID = g.gatewayID
			where mp.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
			AND mp.status= 'A'
			AND g.gatewayType in ('BankDraft','AuthorizeCCCIM','SageCCCIM','AffiniPayCC','MCPayEcheck')
			order by mp.profileName
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_payment_profiles.cfm">
		</cfsavecontent>
	
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

	<cffunction name="toggleProfileStatus" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="profileID" type="numeric" required="true">
		<cfargument name="status" type="string" required="true">

		<cfset var local = structNew()>

		<cfquery name="local.qryToggleStatus" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @siteID int, @orgID int, @profileID int;
			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">;
			SET @profileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.profileID#">;

			UPDATE dbo.mp_profiles
			SET status = <cfif arguments.status is 'I'>'A'<cfelse>'I'</cfif>
			WHERE siteID = @siteID
			AND profileID = @profileID
			AND status = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.status#">;

			SELECT @orgID = orgID FROM dbo.sites WHERE siteID = @siteID;
			
			INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
			SELECT '{ "c":"auditLog", "d": {
				"AUDITCODE":"PP",
				"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
				"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
				"ACTORMEMBERID":' + CAST(#session.cfcuser.memberdata.memberID# AS VARCHAR(20)) + ',
				"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
				"MESSAGE":"' + REPLACE(memberCentral.dbo.fn_cleanInvalidXMLChars(profileName + ' payment profile has been ' +  <cfif arguments.status IS 'I'> 'Activated' <cfelse> 'Inactivated' </cfif> ) ,'"','\"') + '" } }'
			FROM dbo.mp_profiles
			WHERE profileID = @profileID;
		</cfquery>

		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="deleteProfile" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="profileID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfset local.siteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='MerchantProfilesAdmin', siteID=arguments.mcproxy_siteID)>
			<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>
			<cfif  local.tmpRights.mpManage is not 1>
				<cfthrow message="invalid request">
 			</cfif>

			<cfstoredproc procedure="mp_deleteProfile" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.profileID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset local.data.success = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="testPayProfile" access="public" returntype="struct" output="false">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="profileID" type="numeric" required="true">
		<cfargument name="gatewayID" type="numeric" required="true">
		<cfargument name="status" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.success = false>

		<cfquery name="local.qryGatewayType" datasource="#application.dsn.memberCentral.dsn#">
			select gatewayType
			from dbo.mp_gateways 
			where gatewayID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.gatewayID#">
		</cfquery>

		<cfswitch expression="#local.qryGatewayType.gatewayType#">
			<cfcase value="AuthorizeCCCIM">
				<cfset local.apiResponse = CreateObject("component","model.system.platform.gateways.AuthorizeCCCIM").testAPI(profileID=val(arguments.profileID))>
				<cfset local.success = local.apiResponse.success>
				
				<cfif NOT local.success and len(local.apiResponse.errMessage)>
					<cfsavecontent variable="local.emailContent">
						<cfoutput>
						<div>#local.apiResponse.extraInfo.siteName# has an Authorize.Net payment profile with connection issues.</div><br>
						<div>SiteCode: #local.apiResponse.extraInfo.siteCode#</div>
						<div>Pay Profile: #local.apiResponse.extraInfo.profileName# (#local.apiResponse.extraInfo.profileCode#)</div>
						<br/>
						<div><b>#local.apiResponse.errMessage#</b></div>
						</cfoutput>
					</cfsavecontent>
			
					<cfset local.mc_siteinfo = application.objSiteInfo.getSiteInfo('MC')>
					<cfset local.siteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='ScheduledTasks', siteID=local.mc_siteinfo.siteID)>
					<cfset application.objEmailWrapper.sendMailESQ(
						emailfrom={ name='MemberCentral', email='<EMAIL>' },
						emailto=[{name:'', email:"<EMAIL>"}],
						emailreplyto="",
						emailsubject="[#ucFirst(application.MCEnvironment)#] Issue with #local.apiResponse.extraInfo.siteCode# Authorize Pay Profile #local.apiResponse.extraInfo.profileCode#",
						emailtitle="Authorize Pay Profile Tests Failed",
						emailhtmlcontent=local.emailContent,
						emailAttachments=[],
						siteID=local.mc_siteinfo.siteID,
						memberID=local.mc_siteinfo.sysmemberid,
						messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SCHEDTASK"),
						sendingSiteResourceID=local.siteResourceID
					)>
				</cfif>
			</cfcase>
			<cfcase value="AffiniPayCC">
				<cfset local.success = CreateObject("component","model.system.platform.gateways.AffiniPayCC").testAPI(profileID=val(arguments.profileID))>
			</cfcase>
			<cfcase value="SageCCCIM">
				<cfset local.success = CreateObject("component","model.system.platform.gateways.SageCCCIM").testAPI(profileID=val(arguments.profileID))>
			</cfcase>
		</cfswitch>

		<cfset local.updatedStatus = ''>
		<cfif local.success AND arguments.status EQ "I">
			<cfset toggleProfileStatus(mcproxy_siteID=arguments.mcproxy_siteID, status=arguments.status, profileID=val(arguments.profileID))>
			<cfset local.updatedStatus = 'A'>
		<cfelseif NOT local.success AND arguments.status EQ "A">
			<cfset toggleProfileStatus(mcproxy_siteID=arguments.mcproxy_siteID, status=arguments.status, profileID=val(arguments.profileID))>
			<cfset local.updatedStatus = 'I'>
		</cfif>
		
		<cfreturn { "success":local.success, "updatedstatus":local.updatedStatus }>
	</cffunction>

	<cffunction name="edit" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		arguments.event.paramValue('profileID',0);

		// Build breadCrumb Trail ------------------------------------------------------------------- ::
		if (arguments.event.getValue('profileID')) { local.pageTitle = 'Edit Payment Profile'; }
		else { local.pageTitle = 'Add Payment Profile'; }
		appendBreadCrumbs(arguments.event,{ link='', text=local.pageTitle });

		// get the SRID and permissions of Site to grab the ManageAdvancedSettings permission
		local.SiteSRID = arguments.event.getValue('mc_siteInfo.siteSiteResourceID');
		local.myRightsSite = buildRightAssignments(local.SiteSRID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'));
		</cfscript>

		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.mpManage') is not 1>
			<cflocation url="#this.link.message#&ec=LNP" addtoken="no">
		</cfif>

		<cfquery name="local.merchantProfile" datasource="#application.dsn.memberCentral.dsn#">
			SELECT mp.profileID, mp.gatewayID, g.gatewayType, mp.allowPayments, mp.allowRefunds, mp.refundImageExt, mp.profileName, mp.tabTitle, mp.profileCode, mp.gatewayUsername, 
				mp.gatewayPassword, mp.gatewayAccountID, mp.GLAccountID, '' as GLAccountPath, mp.allowRefundsFromAnyProfile, mp.allowPayInvoicesOnline, mp.uid,
				mp.gatewayMerchantId, s.orgID, mp.paymentInstructionsContentID, mpContent.rawContent as paymentInstructionsContent,
				mp.bankAccountName, mp.bankCompanyName, mp.bankTRN, mp.bankCompanyNumber, mp.bankImmediateOrigin, mp.bankImmediateOriginName, mp.bankCompanyEnterDesc,
				hasCardsLinked = case when exists (select payProfileID from dbo.ams_memberPaymentProfiles where profileID = mp.profileID and status = 'A') then 1 else 0 end,
				isnull(mp.daysBetweenAutoAttempts,0) as daysBetweenAutoAttempts, isnull(mp.maxFailedAutoAttempts,0) as maxFailedAutoAttempts, 
				isnull(mp.minDaysFailedCleanup,14) as minDaysFailedCleanup, mp.transactionLabel,  
				mp.activateProcessingFeeSettings, mp.enableProcessingFeeDonation, mp.processFeeDonationFeePercent, 
				mp.processingFeeLabel, mp.processFeeContributionsFELabel, mp.processFeeContributionsFEDenyLabel,
				mp.processFeeSubscriptionsFELabel, mp.processFeeSubscriptionsFEDenyLabel, mp.processFeeOtherPaymentsFELabel, mp.processFeeOtherPaymentsFEDenyLabel,
				mp.processFeeDonationDefaultSelect, mp.processFeeDonationRenevueGLAccountID, mp.solicitationMessageID,
				'' as processFeeDonationRevGLAcctPath, mp.processFeeDonationRevTransDesc, g.gatewayClass,  
				mp.enableApplePay, mp.enableGooglePay, mp.enableMCPay,
				'' as applePayMerchantID, mp.googlePayMerchantID, mp.enableSurcharge, mp.surchargePercent, mp.surchargeRevenueGLAccountID, '' as surchargeRevGLAcctPath, mp.orgIdentityID,
				mp.indivLimit, mp.dayLimit, mp.monthLimit
			FROM dbo.mp_profiles as mp
			INNER JOIN dbo.mp_gateways g ON mp.gatewayID = g.gatewayID
			INNER JOIN dbo.sites as s on s.siteID = mp.siteID
			OUTER APPLY dbo.fn_getContent(mp.paymentInstructionsContentID,1) as mpContent
			WHERE mp.profileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getTrimValue('profileID')#">
			AND mp.[status] IN ('A','I')
		</cfquery>

		<cfif local.merchantProfile.recordcount>
			<cfset local.objGL = CreateObject("component","model.admin.GLAccounts.GLAccounts")>

			<cfset local.tmpStrAccount = local.objGL.getGLAccount(GLAccountID=val(local.merchantProfile.GLAccountID), orgID=val(local.merchantProfile.OrgID))>
			<cfset QuerySetCell(local.merchantProfile,'GLAccountPath',local.tmpStrAccount.qryAccount.thePathExpanded)>

			<cfif local.merchantProfile.gatewayType EQ 'AuthorizeCCCIM'>
				<cfif val(local.merchantProfile.processFeeDonationRenevueGLAccountID)>
					<cfset local.tmpStrAccount = local.objGL.getGLAccount(GLAccountID=val(local.merchantProfile.processFeeDonationRenevueGLAccountID), orgID=val(local.merchantProfile.OrgID))>
					<cfset QuerySetCell(local.merchantProfile,'processFeeDonationRevGLAcctPath',local.tmpStrAccount.qryAccount.thePathExpanded)>
				<cfelse>
					<cfset QuerySetCell(local.merchantProfile,'processFeeDonationRevGLAcctPath',"")>
				</cfif>

				<cfif val(local.merchantProfile.surchargeRevenueGLAccountID)>
					<cfset local.tmpStrAccount = local.objGL.getGLAccount(GLAccountID=val(local.merchantProfile.surchargeRevenueGLAccountID), orgID=val(local.merchantProfile.OrgID))>
					<cfset QuerySetCell(local.merchantProfile,'surchargeRevGLAcctPath',local.tmpStrAccount.qryAccount.thePathExpanded)>
				<cfelse>
					<cfset QuerySetCell(local.merchantProfile,'surchargeRevGLAcctPath',"")>
				</cfif>

				<cfset local.strMerchantInfo = createObject("component","model.system.platform.gateways.AuthorizeCCCIM").getMerchantDetails(gatewayUsername=local.merchantProfile.gatewayUsername, gatewayPassword=local.merchantProfile.gatewayPassword)>
				<cfif local.strMerchantInfo.success>
					<cfquery name="local.qryUpdateAuthPayProfile" datasource="#application.dsn.membercentral.dsn#">
						UPDATE dbo.mp_profiles
						SET merchantDetailsJSON = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.strMerchantInfo.rawdata#">
						WHERE profileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.merchantProfile.profileID#">;
					</cfquery>
				<cfelse>
					<cfset local.authCIMMerchantError = arrayLen(local.strMerchantInfo.errors) and structKeyExists(local.strMerchantInfo.errors[1],"text") ? local.strMerchantInfo.errors[1].text : 'Unable to get Merchant Details'>
				</cfif>

				<!--- get derived apple pay merchant id --->
				<cfif local.merchantProfile.enableApplePay EQ 1>
					<cfset local.applePayUtils = new model.system.platform.gateways.applePayUtils(PAYMENTUTILITIESURL=application.paths.paymentUtilities.url)>
					<cfset QuerySetCell(local.merchantProfile,'applePayMerchantID',local.applePayUtils.getDerivedApplePayMerchantIdentifier(profileID=local.merchantProfile.profileID, siteID=arguments.event.getValue('mc_siteInfo.siteid')))>
				</cfif>
			</cfif>
		</cfif>

		<cfquery name="local.creditCards" datasource="#application.dsn.memberCentral.dsn#">
			SELECT pct.cardTypeID, pct.GLAccountID, rgl.thePathExpanded as GLAccountPath, ct.cardType
			FROM dbo.mp_profileCardTypes pct
			INNER JOIN dbo.mp_cardTypes ct ON pct.cardTypeID = ct.cardTypeID
			LEFT OUTER JOIN dbo.tr_GLAccounts as gl 
				INNER JOIN dbo.fn_getRecursiveGLAccounts(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(local.merchantProfile.OrgID)#">) as rgl on rgl.GLAccountID = gl.GLAccountID
				on gl.GLAccountID = pct.GLAccountID
			WHERE pct.profileID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getTrimValue('profileID')#">
		</cfquery>

		<cfquery name="local.seccodes" datasource="#application.dsn.memberCentral.dsn#">
			SELECT pct.seccodeID
			FROM dbo.mp_profilesecCodes pct
			WHERE pct.profileID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getTrimValue('profileID')#">
		</cfquery>

		<cfquery name="local.fields" datasource="#application.dsn.memberCentral.dsn#">
			SELECT f.fieldID, pf.isRequired
			FROM dbo.mp_profileFields pf
			INNER JOIN dbo.mp_fields f ON pf.fieldID = f.fieldID
			INNER JOIN dbo.mp_fieldTypes ft ON f.fieldTypeID = ft.fieldTypeID
			WHERE pf.profileID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getTrimValue('profileID')#">
		</cfquery>

		<cfquery name="local.requiredFields" dbtype="query">
			SELECT fieldID
			FROM [local].fields
			WHERE isRequired = 1
		</cfquery>

		<cfquery name="local.qryAllFields" datasource="#application.dsn.memberCentral.dsn#">
			SELECT f.fieldID,f.FieldName,ft.fieldTypeID,ft.fieldType,ft.fieldTypeDesc,ft.fieldTypeClass
			FROM dbo.mp_fields as f
			INNER JOIN dbo.mp_fieldTypes as ft ON f.fieldTypeID = ft.fieldTypeID
			WHERE EXISTS (
				select gf.fieldID 
				from dbo.mp_gatewayFields as gf
				inner join dbo.mp_gateways as g on g.gatewayID = gf.gatewayID
				where g.isActive = 1
				and gf.fieldID = f.fieldID
				union all
				select pf.fieldID 
				from dbo.mp_profileFields as pf
				inner join dbo.mp_profiles as p on p.profileID = pf.profileID
				where p.status = 'A'
				and pf.fieldID = f.fieldID
			)
			ORDER BY ft.fieldTypeClass,f.fieldID
		</cfquery>
		<cfquery name="local.qryAllCreditCards" datasource="#application.dsn.memberCentral.dsn#">
			SELECT ct.cardTypeID,ct.cardType
			FROM dbo.mp_cardTypes as ct
			ORDER BY cardTypeID
		</cfquery>
		<cfquery name="local.qryAllSECCodes" datasource="#application.dsn.memberCentral.dsn#">
			SELECT secCodeID, secCode, notes
			FROM dbo.mp_secCodes
			ORDER BY secCodeID
		</cfquery>

		<cfset local.excludeGatewayIDs = "">
		<cfif local.merchantProfile.recordcount is 0>
			<!--- OfflineCC, AuthorizeCC, and SageCC --->
			<cfset local.excludeGatewayIDs = "1,3,9">
		</cfif>

		<cfquery name="local.qryAllGateways" datasource="#application.dsn.memberCentral.dsn#">
			SELECT g.gatewayID, g.gatewayType, g.gatewayTypeDesc, g.gatewayClass, 
				replace((select fieldid as [data()] from dbo.mp_gatewayfields where gatewayID = g.gatewayID FOR XML PATH ('')),' ', ',') as fieldList
			FROM dbo.mp_gateways as g
			WHERE g.isActive = 1
			<cfif len(local.excludeGatewayIDs)>
				and g.gatewayID not in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#local.excludeGatewayIDs#">)
			</cfif>
			ORDER BY g.gatewayTypeDesc
		</cfquery>

		<!--- GL Selectors --->
		<cfset local.objGLAcctWidget = createObject("component","model.admin.common.modules.glAccountWidget.glAccountWidget")>
		<cfset local.strCashGLAcctWidgetData = { label="Cash GL Account", btnTxt="Choose GL Account", glatid=1, widgetMode='GLSelector', 
			idFldName="GLAccountID", idFldValue=val(local.merchantProfile.GLAccountID), 
			pathFldValue=local.merchantProfile.GLAccountPath, pathNoneTxt="(no account selected)" }>
		<cfset local.strCashGLAcctWidget = local.objGLAcctWidget.renderWidget(strWidgetData=local.strCashGLAcctWidgetData)>

		<cfset local.strPFDGLAcctWidgetData = { label="Revenue GL Account for Voluntary Processing Fee Donation", btnTxt="Choose GL Account", glatid=3, widgetMode='GLSelector', 
			idFldName="processFeeDonationRevGLAcctID", idFldValue=val(local.merchantProfile.processFeeDonationRenevueGLAccountID), 
			pathFldValue=local.merchantProfile.processFeeDonationRevGLAcctPath, pathNoneTxt="(no account selected)" }>
		<cfset local.strPFDGLAcctWidget = local.objGLAcctWidget.renderWidget(strWidgetData=local.strPFDGLAcctWidgetData)>

		<cfset local.strSurchargeGLAcctWidgetData = { label="Revenue GL Account for Surcharge", btnTxt="Choose GL Account", glatid=3, widgetMode='GLSelector', 
			idFldName="surchargeRevGLAcctID", idFldValue=val(local.merchantProfile.surchargeRevenueGLAccountID), 
			pathFldValue=local.merchantProfile.surchargeRevGLAcctPath, pathNoneTxt="(no account selected)" }>
		<cfset local.strSurchargeGLAcctWidget = local.objGLAcctWidget.renderWidget(strWidgetData=local.strSurchargeGLAcctWidgetData)>

		<!--- Credit Card Type GL Selectors --->
		<cfset local.arrCardTypeGLWidgets = []>
		<cfloop query="local.qryAllCreditCards">
			<cfquery name="local.getCCAcct" dbtype="query">
				select GLAccountID, GLAccountPath
				from [local].creditcards
				where cardTypeID = #local.qryAllCreditCards.cardTypeID#
			</cfquery>
			<cfset local.cardTypeGLWidgetData = {
				label="Cash GL Account Override for #local.qryAllCreditCards.cardType#",
				btnTxt="Choose GL Account",
				glatid=1,
				widgetMode='GLSelector',
				idFldName="GLAccountID_#local.qryAllCreditCards.cardTypeID#",
				idFldValue=val(local.getCCAcct.GLAccountID),
				pathFldValue=local.getCCAcct.GLAccountPath,
				pathNoneTxt="(No account selected; uses profile's designated GL Account.)",
				clearBtnTxt: "Clear Selected GL Account"
			}>
			<cfset local.cardTypeGLWidget = local.objGLAcctWidget.renderWidget(strWidgetData=local.cardTypeGLWidgetData)>
			<cfset arrayAppend(local.arrCardTypeGLWidgets, {
				cardTypeID=local.qryAllCreditCards.cardTypeID,
				cardType=local.qryAllCreditCards.cardType,
				widget=local.cardTypeGLWidget
			})>
		</cfloop>
		<cfset local.strOrgIdentitySelector = createObject("component","model.admin.common.modules.orgIdentitySelector.orgIdentitySelector").getOrgIdentitySelector(
				orgID=arguments.event.getValue('mc_siteinfo.orgID'),
				selectorID="orgIdentityID",
				selectedValueID=val(local.merchantProfile.orgIdentityID),
				allowBlankOption=false
			)>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_merchantProfile.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>	
	
	<cffunction name="checkProfileCodeExists" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="profileCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryProfileCode">
			SELECT top 1 profileID
			FROM dbo.mp_profiles
			WHERE siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_siteID#">
			AND profileCode = <cfqueryparam cfsqltype="cf_sql_varchar" value="#trim(arguments.profileCode)#">
			AND [status] <> 'D'
		</cfquery>

		<cfset local.data.success = local.qryProfileCode.recordCount eq 0>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="save" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">
		<cfargument name="noRedirect" type="boolean" required="false" default="false">
		
		<cfscript>
			var local = structNew();
			local.gatewayID = listFirst(arguments.event.getValue('gatewayID'),'_');

			local.profileID = int(val(arguments.event.getValue('profileID',0)));
			local.daysBetweenAutoAttempts = 0;
			local.maxFailedAutoAttempts = 0;
			local.minDaysFailedCleanup = '';
			local.transactionLabel = '';
			local.cashGLAccountID = int(val(arguments.event.getValue('GLAccountID',0)));
			local.processingFeeSettingsActivated = false;

			// get the SRID and permissions of Site to grab the ManageAdvancedSettings permission
			local.SiteSRID = arguments.event.getValue('mc_siteInfo.siteSiteResourceID');
			local.myRightsSite = buildRightAssignments(local.SiteSRID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'));

			if (listFind("10,12,17",local.gatewayID)) {
				if (arguments.event.getValue('daysBetweenAutoAttempts',0))
					local.daysBetweenAutoAttempts = arguments.event.getValue('daysBetweenAutoAttempts',0);
				if (arguments.event.getValue('maxFailedAutoAttempts',0))
					local.maxFailedAutoAttempts = arguments.event.getValue('maxFailedAutoAttempts',0);
			
				local.minDaysFailedCleanup = val(arguments.event.getValue('minDaysFailedCleanup',0));
				if (local.minDaysFailedCleanup GT 99) local.minDaysFailedCleanup = 99; 
				else if (local.minDaysFailedCleanup LT 1) local.minDaysFailedCleanup = 14;

				if (local.gatewayID is 10) {
					local.gatewayAccountID = '';
					local.enableSurcharge = 0;
					local.enableGooglePay = 0;
					local.enableMCPay = 0;
					local.googlePayTESTMerchantID = '12345678901234567890';

					// use sandbox creds
					if (application.MCEnvironment NEQ 'production') {
						arguments.event.setValue('gatewayUsername',application.strPlatformAPIKeys.authorize.name);
						arguments.event.setValue('gatewayPassword',application.strPlatformAPIKeys.authorize.transactionkey);
					}
					
					if (local.profileID GT 0) {
						var qryAuthPayProfile = queryExecute("
							SET NOCOUNT ON;
							SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

							SELECT gatewayUsername, gatewayAccountID, enableMCPay, activateProcessingFeeSettings, enableGooglePay, merchantDetailsJSON
							FROM dbo.mp_profiles
							WHERE profileID = :profileID;

							SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
							", 
							{ 
								profileID = { value=local.profileID, cfsqltype="CF_SQL_INTEGER" }
							},
							{ datasource="#application.dsn.membercentral.dsn#" }
						);

						local.gatewayAccountID = qryAuthPayProfile.gatewayUsername EQ arguments.event.getTrimValue('gatewayUsername') ? qryAuthPayProfile.gatewayAccountID : '';
						local.enableMCPay = qryAuthPayProfile.enableMCPay;
						local.processingFeeSettingsActivated = qryAuthPayProfile.activateProcessingFeeSettings EQ 1;
						local.enableGooglePay = qryAuthPayProfile.enableGooglePay;
					}

					if (application.objUser.isSuperUser(cfcuser=session.cfcuser)) {
						local.enableMCPay = arguments.event.getValue('enableMCPay',0) EQ 1 ? 1 : 0;
						local.processingFeeSettingsActivated = arguments.event.getValue('activateProcessingFeeSettings');
						local.enableGooglePay = arguments.event.getValue('enableGooglePay',0) EQ 1 ? 1 : 0;
						local.enableSurcharge = arguments.event.getValue('enableSurcharge',0) EQ 1 ? 1 : 0;
						local.surchargePercent = val(arguments.event.getValue('surchargePercent',0));
						local.surchargeRevenueGLAccountID = val(arguments.event.getValue('surchargeRevGLAcctID',0));
					}

					if (NOT len(local.gatewayAccountID)) {
						local.strMerchantInfo = createObject("component","model.system.platform.gateways.AuthorizeCCCIM").getMerchantDetails(gatewayUsername=arguments.event.getTrimValue('gatewayUsername'), gatewayPassword=arguments.event.getTrimValue('gatewayPassword'));
						if (NOT local.strMerchantInfo.success) {
							if (arrayLen(local.strMerchantInfo.errors) AND local.strMerchantInfo.errors[1].code EQ 'E00007') {
								location("#this.link.message#&ec=AUTHINVAPIKEY", "false");
							} else {
								throw(message=arrayLen(local.strMerchantInfo.errors) ? local.strMerchantInfo.errors[1].text : 'Unable to get Merchant Details');
							}
						}
						
						local.gatewayAccountID = local.strMerchantInfo.data.gatewayId;
					}
				} else if (local.gatewayID is 17) {
					if (application.MCEnvironment NEQ 'production') {
						// use sandbox creds
						arguments.event.setValue('gatewayUsername',application.strPlatformAPIKeys.affinipaycc.public);
						arguments.event.setValue('gatewayPassword',application.strPlatformAPIKeys.affinipaycc.secret);
						arguments.event.setValue('gatewayMerchantId',application.strPlatformAPIKeys.affinipaycc.merchantid);
	
						if (local.profileID EQ 0) {
							arguments.event.setValue('allowRefundsFromAnyProfile',1); // will set allowRefunds = 1, allowRefundsFromAnyProfile = 0
							arguments.event.setValue('allowPayInvoicesOnline',1);
							arguments.event.setValue('allowPayments',1);
							arguments.event.setValue('fieldID_all_11',1); // Billing Postal Code
							arguments.event.setValue('isRequired_all_11',1);
							arguments.event.setValue('fieldID_all_12',1); // Billing Address
							arguments.event.setValue('isRequired_all_12',1);
						}
					}
				}

				// processing fee donation settings
				if (local.processingFeeSettingsActivated) {
					local.enableProcessingFeeDonation = val(arguments.event.getValue('enableProcessingFeeDonation',0));
					local.processFeeDonationFeePercent = val(arguments.event.getValue('processFeeDonationFeePercent',0));
					local.strSanitizeOptions = { "allowedTags": [], "allowedAttributes": {} };
					local.processingFeeLabel = application.objCommon.sanitizeHTML(dirtyHTML=arguments.event.getTrimValue('processingFeeLabel',''), sanitizeOptions=local.strSanitizeOptions).content;
					local.solicitationMessageID = val(arguments.event.getValue('solicitationMessageID',0));
					local.processFeeContributionsFELabel = arguments.event.getValue('mc_siteInfo.sf_contributions') EQ 1 ? arguments.event.getTrimValue('processFeeContributionsFELabel','') : "";
					local.processFeeContributionsFEDenyLabel = arguments.event.getValue('mc_siteInfo.sf_contributions') EQ 1 ? arguments.event.getTrimValue('processFeeContributionsFEDenyLabel','') : "";
					local.processFeeSubscriptionsFELabel = arguments.event.getValue('mc_siteInfo.sf_subscriptions') EQ 1 ? arguments.event.getTrimValue('processFeeSubscriptionsFELabel','') : "";
					local.processFeeSubscriptionsFEDenyLabel = arguments.event.getValue('mc_siteInfo.sf_subscriptions') EQ 1 ? arguments.event.getTrimValue('processFeeSubscriptionsFEDenyLabel','') : "";
					local.processFeeOtherPaymentsFELabel = arguments.event.getTrimValue('processFeeOtherPaymentsFELabel','');
					local.processFeeOtherPaymentsFEDenyLabel = arguments.event.getTrimValue('processFeeOtherPaymentsFEDenyLabel','');
					local.processFeeDonationDefaultSelect = val(arguments.event.getValue('processFeeDonationDefaultSelect',0));
					local.processFeeDonationRenevueGLAccountID = val(arguments.event.getValue('processFeeDonationRevGLAcctID',0));
					local.processFeeDonationRevTransDesc = arguments.event.getTrimValue('processFeeDonationRevTransDesc','');
				}

			} else if (local.gatewayID is 2) {
				if (len(arguments.event.getTrimValue('transactionLabel','')))
					local.transactionLabel = arguments.event.getTrimValue('transactionLabel','');
			} else if (local.gatewayID is 16) {
				local.bankTRN = '';
				for (local.i=1; local.i LTE 9; local.i=local.i+1) {
					local.bankTRN = local.bankTRN & left(arguments.event.getValue('bankTRN_#local.i#') & " ",1);			// DO NOT TRIM HERE. PRESERVE SPACES.
				}
				local.bankCN = '';
				for (local.i=1; local.i LTE 10; local.i=local.i+1) {
					local.bankCN = local.bankCN & left(arguments.event.getValue('bankCompanyNumber_#local.i#') & " ",1);	// DO NOT TRIM HERE. PRESERVE SPACES.
				}
				local.bankIO = '';
				for (local.i=1; local.i LTE 10; local.i=local.i+1) {
					local.bankIO = local.bankIO & left(arguments.event.getValue('bankImmediateOrigin_#local.i#') & " ",1);	// DO NOT TRIM HERE. PRESERVE SPACES.
				}
				local.minDaysFailedCleanup = val(arguments.event.getValue('minDaysFailedCleanup',0));
				if (local.minDaysFailedCleanup GT 99) local.minDaysFailedCleanup = 99; 
				else if (local.minDaysFailedCleanup LT 1) local.minDaysFailedCleanup = 14;
			} else if (local.gatewayID EQ 19) {
				if (application.MCEnvironment NEQ 'production') {
					// use sandbox creds
					arguments.event.setValue('gatewayUsername',application.strPlatformAPIKeys.mcpayecheck.debit.login);
					arguments.event.setValue('gatewayPassword',application.strPlatformAPIKeys.mcpayecheck.debit.pwd);
					arguments.event.setValue('gatewayMerchantId',application.strPlatformAPIKeys.mcpayecheck.debit.merchantid);
				}

				if (arguments.event.getValue('daysBetweenAutoAttempts',0))
					local.daysBetweenAutoAttempts = arguments.event.getValue('daysBetweenAutoAttempts',0);
				if (arguments.event.getValue('maxFailedAutoAttempts',0))
					local.maxFailedAutoAttempts = arguments.event.getValue('maxFailedAutoAttempts',0);
			
				local.minDaysFailedCleanup = val(arguments.event.getValue('minDaysFailedCleanup',0));
				if (local.minDaysFailedCleanup GT 99) local.minDaysFailedCleanup = 99; 
				else if (local.minDaysFailedCleanup LT 1) local.minDaysFailedCleanup = 14;

				local.indivLimit = val(ReReplace(arguments.event.getValue('mp_indivLimit',0),'[^0-9\.]','','ALL'));
				local.dayLimit = val(ReReplace(arguments.event.getValue('mp_dayLimit',0),'[^0-9\.]','','ALL'));
				local.monthLimit = val(ReReplace(arguments.event.getValue('mp_monthLimit',0),'[^0-9\.]','','ALL'));
			}
		</cfscript>
		
		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.mpManage') is not 1>
			<cflocation url="#this.link.message#&ec=LNP" addtoken="no">
		</cfif>

		<!--- Are we enabling or disabling Apple Pay? Used to register/unregister merchant in apple pay later --->
		<cfset local.enablingApplePayThisSave = false>
		<cfset local.disablingApplePayThisSave = false>
		<cfif local.gatewayID EQ 10 AND application.objUser.isSuperUser(cfcuser=session.cfcuser)>
			<cfif arguments.event.getValue('profileID',0) GT 0>
				<cfquery name="local.qryExistingPayProfile" datasource="#application.dsn.memberCentral.dsn#">
					SELECT enableApplePay
					FROM dbo.mp_profiles
					WHERE profileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('profileID')#">;
				</cfquery>				
				<cfif local.qryExistingPayProfile.enableApplePay is 1 and arguments.event.getValue('enableApplePay',0) is 0>
					<cfset local.disablingApplePayThisSave = true>
				<cfelseif local.qryExistingPayProfile.enableApplePay is 0 and arguments.event.getValue('enableApplePay',0) is 1>
					<cfset local.enablingApplePayThisSave = true>
				</cfif>
			<cfelseif arguments.event.getValue('enableApplePay',0) is 1>
				<cfset local.enablingApplePayThisSave = true>
			</cfif>
		</cfif>

		<cftry>
			<cfif arguments.event.getValue('profileID',0) GT 0>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.update">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY

						DECLARE @profileID INT = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('profileID')#">;
						DECLARE @crlf VARCHAR(10), @msgjson VARCHAR(MAX), @gatewayID int, @profileName varchar(100), @tabTitle varchar(100),
							@profileCode varchar(20), @gatewayUsername varchar(50), @gatewayPassword varchar(75), @gatewayMerchantId varchar(50), 
							@allowPayments bit, @allowRefunds bit, @allowRefundsFromAnyProfile bit, @GLAccountID int, @allowPayInvoicesOnline bit,
							@bankAccountName varchar(200), @maxFailedAutoAttempts int, @daysBetweenAutoAttempts int, @minDaysFailedCleanup int, 
							@bankTRN char(9), @bankCompanyNumber char(10), @bankImmediateOrigin char(10), @bankCompanyName varchar(16), 
							@bankImmediateOriginName varchar(23), @bankCompanyEnterDesc varchar(10), @transactionLabel varchar(25), 
							@uid uniqueidentifier, @activateProcessingFeeSettings bit, @enableProcessingFeeDonation bit, @processFeeDonationFeePercent decimal(5,2), 
							@processingFeeLabel varchar(30), @solicitationMessageID int, @processFeeContributionsFELabel varchar(200), 
							@processFeeContributionsFEDenyLabel varchar(200), @processFeeSubscriptionsFELabel varchar(200), @processFeeSubscriptionsFEDenyLabel varchar(200), 
							@processFeeOtherPaymentsFELabel varchar(200), @processFeeOtherPaymentsFEDenyLabel varchar(200), 
							@processFeeDonationDefaultSelect bit, @processFeeDonationRenevueGLAccountID int, 
							@processFeeDonationRevTransDesc varchar(100), @enableMCPay bit, @enableApplePay bit, @enableGooglePay bit, 
							@googlePayMerchantID varchar(20), @gatewayAccountID varchar(20), @enableSurcharge bit, @surchargePercent decimal(5,2), 
							@surchargeRevenueGLAccountID int, @orgIdentityID int, @indivLimit decimal(18,2), @dayLimit decimal(18,2), @monthLimit decimal(18,2);
						SET @gatewayID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.gatewayID#">;
						SET @profileName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getTrimValue('profileName')#">;
						SET @tabTitle = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getTrimValue('tabTitle')#">;
						SET @profileCode = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getTrimValue('new.profileCode')#">;
						SET @gatewayUsername = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getTrimValue('gatewayUsername')#">;
						SET @gatewayPassword = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getTrimValue('gatewayPassword')#">;
						<cfif local.gatewayID is 10>
							SET @gatewayAccountID = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.gatewayAccountID#">;
						</cfif>
						SET @gatewayMerchantId = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getTrimValue('gatewayMerchantId')#">;
						SET @orgIdentityID = nullif(<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('orgIdentityID',0)#">,0);
						SET @allowPayments = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getTrimValue('allowPayments',0)#">;
						<cfif arguments.event.getTrimValue('allowRefundsFromAnyProfile',0) EQ 0>
							SET @allowRefunds = <cfqueryparam cfsqltype="CF_SQL_BIT" value="0">;
							SET @allowRefundsFromAnyProfile = <cfqueryparam cfsqltype="CF_SQL_BIT" value="0">;
						<cfelseif arguments.event.getTrimValue('allowRefundsFromAnyProfile',0) EQ 1>
							SET @allowRefunds = <cfqueryparam cfsqltype="CF_SQL_BIT" value="1">;
							SET @allowRefundsFromAnyProfile = <cfqueryparam cfsqltype="CF_SQL_BIT" value="0">;
						<cfelseif arguments.event.getTrimValue('allowRefundsFromAnyProfile',0) EQ 2>
							SET @allowRefunds = <cfqueryparam cfsqltype="CF_SQL_BIT" value="1">;
							SET @allowRefundsFromAnyProfile = <cfqueryparam cfsqltype="CF_SQL_BIT" value="1">;
						</cfif>	
						SET @GLAccountID = nullif(<cfqueryparam cfsqltype="cf_sql_integer" value="#local.cashGLAccountID#">,0);
						SET @allowPayInvoicesOnline = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getTrimValue('allowPayInvoicesOnline',0)#">;
						SET @bankAccountName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getTrimValue('bankAccountName')#">;
						SET @maxFailedAutoAttempts = nullif(<cfqueryparam cfsqltype="cf_sql_integer" value="#local.maxFailedAutoAttempts#">,0);
						SET @daysBetweenAutoAttempts = nullif(<cfqueryparam cfsqltype="cf_sql_integer" value="#local.daysBetweenAutoAttempts#">,0);
						SET @minDaysFailedCleanup = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.minDaysFailedCleanup#">;
						<cfif local.gatewayID is 16>
							SET @bankTRN = <cfqueryparam cfsqltype="cf_sql_char" value="#local.bankTRN#">;
							SET @bankCompanyNumber = <cfqueryparam cfsqltype="cf_sql_char" value="#local.bankCN#">;
							SET @bankImmediateOrigin = <cfqueryparam cfsqltype="cf_sql_char" value="#local.bankIO#">;
							SET @bankCompanyName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#uCase(arguments.event.getTrimValue('bankCompanyName'))#">;
							SET @bankImmediateOriginName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#uCase(arguments.event.getTrimValue('bankImmediateOriginName'))#">;
							SET @bankCompanyEnterDesc = <cfqueryparam cfsqltype="cf_sql_varchar" value="#uCase(arguments.event.getTrimValue('bankCompanyEnterDesc'))#">;
						</cfif>
						SET @transactionLabel = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.transactionLabel#">;

						SET @activateProcessingFeeSettings = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.processingFeeSettingsActivated#">;
						<cfif local.processingFeeSettingsActivated>
							SET @enableProcessingFeeDonation = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.enableProcessingFeeDonation#">;
							SET @processFeeDonationFeePercent = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.processFeeDonationFeePercent#">;
							SET @processingFeeLabel = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.processingFeeLabel#">;
							SET @solicitationMessageID = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.solicitationMessageID#">,0);
							SET @processFeeContributionsFELabel = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.processFeeContributionsFELabel#">;
							SET @processFeeContributionsFEDenyLabel = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.processFeeContributionsFEDenyLabel#">;
							SET @processFeeSubscriptionsFELabel = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.processFeeSubscriptionsFELabel#">;
							SET @processFeeSubscriptionsFEDenyLabel = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.processFeeSubscriptionsFEDenyLabel#">;
							SET @processFeeOtherPaymentsFELabel = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.processFeeOtherPaymentsFELabel#">;
							SET @processFeeOtherPaymentsFEDenyLabel = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.processFeeOtherPaymentsFEDenyLabel#">;
							SET @processFeeDonationDefaultSelect = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.processFeeDonationDefaultSelect#">;
							SET @processFeeDonationRenevueGLAccountID = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.processFeeDonationRenevueGLAccountID#">,0);
							SET @processFeeDonationRevTransDesc = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.processFeeDonationRevTransDesc#">;
						<cfelse>
							SET @enableProcessingFeeDonation = 0;
							SET @processFeeDonationFeePercent = 0;
						</cfif>

						<cfif local.gatewayID EQ 10 AND application.objUser.isSuperUser(cfcuser=session.cfcuser)>
							SET @enableMCPay = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.enableMCPay#">;
							SET @enableApplePay = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('enableApplePay',0)#">;
							SET @enableGooglePay = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.enableGooglePay#">;
							<cfif local.enableGooglePay>
								SET @googlePayMerchantID = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#application.MCEnvironment EQ 'production' ? arguments.event.getTrimValue('googlePayMerchantID','') : local.googlePayTESTMerchantID#">
							</cfif>
							SET @enableSurcharge = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.enableSurcharge#">;
							SET @surchargePercent = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.surchargePercent#">;
							SET @surchargeRevenueGLAccountID = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.surchargeRevenueGLAccountID#">,0);
						</cfif>
						<cfif local.gatewayID is 19>
							<cfif local.indivLimit GT 0>
								SET @indivLimit = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.indivLimit#">;
							</cfif>
							<cfif local.dayLimit GT 0>
								SET @dayLimit = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.dayLimit#">;
							</cfif>
							<cfif local.monthLimit GT 0>
								SET @monthLimit = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.monthLimit#">;
							</cfif>
						</cfif>
					
						SET @crlf = CHAR(13) + CHAR(10);
						
						IF OBJECT_ID('tempdb..##tblPaymentProfile') IS NOT NULL
							DROP TABLE ##tblExistingRate;
						IF OBJECT_ID('tempdb..##tmpLogMessages') IS NOT NULL
							DROP TABLE ##tmpLogMessages;

						CREATE TABLE ##tblPaymentProfile (gatewayID int, profileName varchar(100), tabTitle varchar(100), profileCode varchar(20), 
							gatewayUsername varchar(50), gatewayPassword varchar(75), gatewayMerchantId varchar(50), gatewayAccountID varchar(20),
							allowPayments bit, allowRefunds bit, allowRefundsFromAnyProfile bit, GLAccountID int, allowPayInvoicesOnline bit, 
							bankAccountName varchar(200), maxFailedAutoAttempts int, daysBetweenAutoAttempts int, minDaysFailedCleanup int, bankTRN char(9), 
							bankCompanyNumber char(10), bankImmediateOrigin char(10), bankCompanyName varchar(16), bankImmediateOriginName varchar(23), 
							bankCompanyEnterDesc varchar(10), transactionLabel varchar(25), uid uniqueidentifier, profileID int,
							activateProcessingFeeSettings bit, enableProcessingFeeDonation bit, 
							processFeeDonationFeePercent decimal(5,2), processingFeeLabel varchar(30), solicitationMessageID int, 
							processFeeContributionsFELabel varchar(200), processFeeContributionsFEDenyLabel varchar(200), processFeeSubscriptionsFELabel varchar(200), 
							processFeeSubscriptionsFEDenyLabel varchar(200), processFeeOtherPaymentsFELabel varchar(200), processFeeOtherPaymentsFEDenyLabel varchar(200),
							processFeeDonationDefaultSelect bit, processFeeDonationRenevueGLAccountID int, processFeeDonationRevTransDesc varchar(100), 
							enableMCPay bit, enableApplePay bit, enableGooglePay bit, googlePayMerchantID varchar(20),
							enableSurcharge bit, surchargePercent decimal(5,2), surchargeRevenueGLAccountID int, orgIdentityID int, indivLimit decimal(18,2),
							dayLimit decimal(18,2), monthLimit decimal(18,2));
						CREATE TABLE ##tmpLogMessages (rowID INT IDENTITY(1,1), msg VARCHAR(MAX));

						INSERT INTO ##tblPaymentProfile(gatewayID,profileName,tabTitle,profileCode, gatewayUsername,
							gatewayPassword, gatewayMerchantId, gatewayAccountID, allowPayments, allowRefunds, 
							allowRefundsFromAnyProfile, GLAccountID, allowPayInvoicesOnline, bankAccountName,
							maxFailedAutoAttempts, daysBetweenAutoAttempts, minDaysFailedCleanup, bankTRN, 
							bankCompanyNumber, bankImmediateOrigin, bankCompanyName, bankImmediateOriginName, 
							bankCompanyEnterDesc, transactionLabel, uid, profileID,  
							activateProcessingFeeSettings, enableProcessingFeeDonation, processFeeDonationFeePercent, 
							processingFeeLabel, solicitationMessageID, processFeeContributionsFELabel, processFeeContributionsFEDenyLabel,
							processFeeSubscriptionsFELabel, processFeeSubscriptionsFEDenyLabel, processFeeOtherPaymentsFELabel, processFeeOtherPaymentsFEDenyLabel, 
							processFeeDonationDefaultSelect, processFeeDonationRenevueGLAccountID, processFeeDonationRevTransDesc,  
							enableMCPay, enableApplePay, enableGooglePay, googlePayMerchantID, enableSurcharge, surchargePercent, surchargeRevenueGLAccountID, orgIdentityID,
							indivLimit, dayLimit, monthLimit)
						SELECT gatewayID,profileName,tabTitle,profileCode, gatewayUsername,
							gatewayPassword, gatewayMerchantId, gatewayAccountID, allowPayments, allowRefunds, 
							allowRefundsFromAnyProfile, GLAccountID, allowPayInvoicesOnline, bankAccountName,
							maxFailedAutoAttempts, daysBetweenAutoAttempts, minDaysFailedCleanup, bankTRN, 
							bankCompanyNumber, bankImmediateOrigin, bankCompanyName, bankImmediateOriginName, 
							bankCompanyEnterDesc, transactionLabel, uid, profileID,  
							activateProcessingFeeSettings, enableProcessingFeeDonation, processFeeDonationFeePercent, 
							processingFeeLabel, solicitationMessageID, processFeeContributionsFELabel, processFeeContributionsFEDenyLabel,
							processFeeSubscriptionsFELabel, processFeeSubscriptionsFEDenyLabel, processFeeOtherPaymentsFELabel, processFeeOtherPaymentsFEDenyLabel, 
							processFeeDonationDefaultSelect, processFeeDonationRenevueGLAccountID, processFeeDonationRevTransDesc,  
							enableMCPay, enableApplePay, enableGooglePay, googlePayMerchantID, enableSurcharge, surchargePercent, surchargeRevenueGLAccountID, orgIdentityID,
							indivLimit, dayLimit, monthLimit
						FROM dbo.mp_profiles
						WHERE profileID = @profileID;

						<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) and len(arguments.event.getTrimValue('profileUID',''))>
							DECLARE @newProfileUID uniqueidentifier;
							SET @newProfileUID = <cfqueryparam cfsqltype="CF_SQL_IDSTAMP" value="#arguments.event.getTrimValue('profileUID')#">;

							IF EXISTS (select profileID from dbo.mp_profiles where uid = @newProfileUID and profileID <> @profileID)
								RAISERROR('Profile UID already exists',16,1);
						</cfif>

						<cfif local.gatewayID EQ 10 AND application.MCEnvironment EQ 'production'>
							-- enforce unique authorize gateway credentials in prod
							IF EXISTS (select profileID from dbo.mp_profiles where gatewayID = @gatewayID and profileID <> @profileID and [status] <> 'D' AND gatewayUsername + gatewayPassword = @gatewayUsername + @gatewayPassword)
								RAISERROR('Authorize Credentials already in use',16,1);
						</cfif>

						UPDATE dbo.mp_profiles
						SET gatewayID =	@gatewayID,
							profileName = @profileName,
							tabTitle = @tabTitle,
							profileCode = @profileCode,
							<cfif len(arguments.event.getTrimValue('gatewayUsername'))>
								gatewayUsername = @gatewayUsername,
								gatewayPassword = @gatewayPassword,
							<cfelse>
								gatewayUsername = null,
								gatewayPassword = null,
							</cfif>
							<cfif len(arguments.event.getTrimValue('gatewayMerchantId'))>
								gatewayMerchantId =	@gatewayMerchantId,
							<cfelse>
								gatewayMerchantId =	null,
							</cfif>
							<cfif local.gatewayID EQ 10>
								gatewayAccountID = @gatewayAccountID,
							</cfif>
							allowPayments = @allowPayments,
							allowRefunds = @allowRefunds,
							allowRefundsFromAnyProfile = @allowRefundsFromAnyProfile,
							GLAccountID = @GLAccountID,
							allowPayInvoicesOnline = @allowPayInvoicesOnline,
							bankAccountName = @bankAccountName,
							maxFailedAutoAttempts = @maxFailedAutoAttempts,
							daysBetweenAutoAttempts = @daysBetweenAutoAttempts,
							<cfif len(local.minDaysFailedCleanup)>
								minDaysFailedCleanup = @minDaysFailedCleanup
							<cfelse>
								minDaysFailedCleanup =	null
							</cfif>
							<cfif local.gatewayID is 16>
								, bankTRN = @bankTRN
								, bankCompanyNumber = @bankCompanyNumber
								, bankImmediateOrigin = @bankImmediateOrigin
								, bankCompanyName = @bankCompanyName
								, bankImmediateOriginName = @bankImmediateOriginName
								, bankCompanyEnterDesc = @bankCompanyEnterDesc
							</cfif>
							<cfif len(local.transactionLabel)>
								, transactionLabel = @transactionLabel
							</cfif>
							<cfif local.processingFeeSettingsActivated>
								, enableProcessingFeeDonation = @enableProcessingFeeDonation
								, processFeeDonationFeePercent = @processFeeDonationFeePercent
								, processingFeeLabel = @processingFeeLabel
								, solicitationMessageID = @solicitationMessageID
								, processFeeContributionsFELabel = @processFeeContributionsFELabel
								, processFeeContributionsFEDenyLabel = @processFeeContributionsFEDenyLabel
								, processFeeSubscriptionsFELabel = @processFeeSubscriptionsFELabel
								, processFeeSubscriptionsFEDenyLabel = @processFeeSubscriptionsFEDenyLabel
								, processFeeOtherPaymentsFELabel = @processFeeOtherPaymentsFELabel
								, processFeeOtherPaymentsFEDenyLabel = @processFeeOtherPaymentsFEDenyLabel
								, processFeeDonationDefaultSelect = @processFeeDonationDefaultSelect
								, processFeeDonationRenevueGLAccountID = @processFeeDonationRenevueGLAccountID
								, processFeeDonationRevTransDesc = @processFeeDonationRevTransDesc
							<cfelse>
								, enableProcessingFeeDonation = @enableProcessingFeeDonation
								, processFeeDonationFeePercent = @processFeeDonationFeePercent
							</cfif>
							<cfif local.gatewayID EQ 10 AND application.objUser.isSuperUser(cfcuser=session.cfcuser)>
								, enableMCPay = @enableMCPay
								, dateUpdatedMCPayEnable = GETDATE()
								, activateProcessingFeeSettings = @activateProcessingFeeSettings
								, enableApplePay = @enableApplePay
								, enableGooglePay = @enableGooglePay
								, googlePayMerchantID = @googlePayMerchantID
								, enableSurcharge = @enableSurcharge
								, surchargePercent = @surchargePercent
								, surchargeRevenueGLAccountID = @surchargeRevenueGLAccountID
							</cfif>
							<cfif local.gatewayID EQ 19>
								, indivLimit = @indivLimit
								, dayLimit = @dayLimit
								, monthLimit = @monthLimit
							</cfif>
							<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) and len(arguments.event.getTrimValue('profileUID',''))>
								, uid = @newProfileUID
							</cfif>
								, orgIdentityID = @orgIdentityID
						WHERE profileID = @profileID
						AND siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteInfo.siteID')#">;
					
						INSERT INTO ##tmpLogMessages(msg)
						SELECT 'GatewayID changed from ' + cast(gatewayID as NVARCHAR(MAX)) + ' to ' + @gatewayID 
						FROM ##tblPaymentProfile
						WHERE profileID = @profileID
						AND gatewayID <> @gatewayID;

						INSERT INTO ##tmpLogMessages(msg)
						SELECT 'profileName changed from ' + profileName + ' to ' + @profileName 
						FROM ##tblPaymentProfile
						WHERE profileID = @profileID
						AND profileName <> @profileName

						INSERT INTO ##tmpLogMessages(msg)
						SELECT 'tabTitle changed from ' + tabTitle + ' to ' + @tabTitle 
						FROM ##tblPaymentProfile
						WHERE profileID = @profileID
						AND tabTitle <> @tabTitle;

						INSERT INTO ##tmpLogMessages(msg)
						SELECT 'profileCode changed from ' + profileCode + ' to ' + @profileCode
						FROM ##tblPaymentProfile
						WHERE profileID = @profileID
						AND profileCode <> @profileCode;

						INSERT INTO ##tmpLogMessages(msg)
						SELECT 'gatewayUsername changed from ' + ISNULL(gatewayUsername,'[blank]') + ' to ' + ISNULL(@gatewayUsername,'[blank]')
						FROM ##tblPaymentProfile
						WHERE profileID = @profileID
						AND ISNULL(gatewayUsername,'') <> ISNULL(@gatewayUsername,'');

						INSERT INTO ##tmpLogMessages(msg)
						SELECT 'gatewayPassword changed from ' + ISNULL(gatewayPassword,'[blank]') + ' to ' + ISNULL(@gatewayPassword,'[blank]')
						FROM ##tblPaymentProfile
						WHERE profileID = @profileID
						AND ISNULL(gatewayPassword,'') <> ISNULL(@gatewayPassword,'');

						INSERT INTO ##tmpLogMessages(msg)
						SELECT 'gatewayMerchantId changed from ' + ISNULL(gatewayMerchantId,'[blank]')  + ' to ' + ISNULL(@gatewayMerchantId,'[blank]')
						FROM ##tblPaymentProfile
						WHERE profileID = @profileID
						AND ISNULL(gatewayMerchantId,'') <> ISNULL(@gatewayMerchantId,'');

						<cfif local.gatewayID EQ 10>
							INSERT INTO ##tmpLogMessages(msg)
							SELECT 'gatewayAccountID changed from ' + ISNULL(gatewayAccountID,'[blank]') + ' to ' + ISNULL(@gatewayAccountID,'[blank]')
							FROM ##tblPaymentProfile
							WHERE profileID = @profileID
							AND ISNULL(gatewayAccountID,'') <> ISNULL(@gatewayAccountID,'');
						</cfif>

						INSERT INTO ##tmpLogMessages(msg)
						SELECT 'allowRefunds changed from ' + CASE WHEN allowRefunds = 1 THEN 'Yes' ELSE 'No' END  + ' to ' + CASE WHEN @allowRefunds = 1 THEN 'Yes' ELSE 'No' END  
						FROM ##tblPaymentProfile
						WHERE profileID = @profileID
						AND allowRefunds <> @allowRefunds;

						INSERT INTO ##tmpLogMessages(msg)
						SELECT 'allowPayments changed from ' + CASE WHEN allowPayments = 1 THEN 'Yes' ELSE 'No' END  + ' to ' + CASE WHEN @allowPayments = 1 THEN 'Yes' ELSE 'No' END
						FROM ##tblPaymentProfile
						WHERE profileID = @profileID
						AND allowPayments <> @allowPayments;

						INSERT INTO ##tmpLogMessages(msg)
						SELECT 'bankAccountName changed from ' + CASE WHEN allowRefundsFromAnyProfile = 1 THEN 'Yes' ELSE 'No' END + ' to ' + CASE WHEN @allowRefundsFromAnyProfile = 1 THEN 'Yes' ELSE 'No' END
						FROM ##tblPaymentProfile
						WHERE profileID = @profileID
						AND allowRefundsFromAnyProfile <> @allowRefundsFromAnyProfile;

						INSERT INTO ##tmpLogMessages(msg)
						SELECT 'GLAccountID changed from ' + cast(GLAccountID as varchar(MAX))  + ' to ' + cast(@GLAccountID as varchar(MAX))
						FROM ##tblPaymentProfile
						WHERE profileID = @profileID
						AND GLAccountID <> @GLAccountID;

						INSERT INTO ##tmpLogMessages(msg)
						SELECT 'allowPayInvoicesOnline changed from ' + CASE WHEN allowPayInvoicesOnline = 1 THEN 'Yes' ELSE 'No' END + ' to ' + CASE WHEN @allowPayInvoicesOnline = 1 THEN 'Yes' ELSE 'No' END
						FROM ##tblPaymentProfile
						WHERE profileID = @profileID
						AND allowPayInvoicesOnline <> @allowPayInvoicesOnline;

						INSERT INTO ##tmpLogMessages(msg)
						SELECT 'bankAccountName changed from ' + ISNULL(bankAccountName,'[blank]') + ' to ' + ISNULL(@bankAccountName,'[blank]')
						FROM ##tblPaymentProfile
						WHERE profileID = @profileID
						AND ISNULL(bankAccountName,'') <> ISNULL(@bankAccountName,'');
						
						INSERT INTO ##tmpLogMessages(msg)
						SELECT 'maxFailedAutoAttempts changed from ' + CAST(maxFailedAutoAttempts AS NVARCHAR(MAX)) + ' to ' + CAST(@maxFailedAutoAttempts AS NVARCHAR(MAX)) 
						FROM ##tblPaymentProfile
						WHERE profileID = @profileID
						AND maxFailedAutoAttempts <> @maxFailedAutoAttempts;
						
						INSERT INTO ##tmpLogMessages(msg)
						SELECT 'daysBetweenAutoAttempts changed from ' + CAST(daysBetweenAutoAttempts AS NVARCHAR(MAX)) + ' to ' + CAST(@daysBetweenAutoAttempts AS NVARCHAR(MAX))
						FROM ##tblPaymentProfile
						WHERE profileID = @profileID
						AND daysBetweenAutoAttempts <> @daysBetweenAutoAttempts;
						
						INSERT INTO ##tmpLogMessages(msg)
						SELECT 'minDaysFailedCleanup changed from ' + CAST(minDaysFailedCleanup AS NVARCHAR(MAX)) + ' to ' + 
						CASE WHEN len(@minDaysFailedCleanup) > 0 THEN CAST(@minDaysFailedCleanup AS NVARCHAR(MAX)) ELSE NULL END
						FROM ##tblPaymentProfile
						WHERE profileID = @profileID
						AND minDaysFailedCleanup <> @minDaysFailedCleanup;
						
						<cfif local.gatewayID is 16>
							INSERT INTO ##tmpLogMessages(msg)
							SELECT 'bankTRN changed from ' + bankTRN + ' to ' + @bankTRN
							FROM ##tblPaymentProfile
							WHERE profileID = @profileID
							AND bankTRN <> @bankTRN;
							
							INSERT INTO ##tmpLogMessages(msg)
							SELECT 'bankCompanyNumber changed from ' + bankCompanyNumber + ' to ' + @bankCompanyNumber
							FROM ##tblPaymentProfile
							WHERE profileID = @profileID
							AND bankCompanyNumber <> @bankCompanyNumber;
							
							INSERT INTO ##tmpLogMessages(msg)
							SELECT 'bankImmediateOrigin changed from ' + bankImmediateOrigin + ' to ' + @bankImmediateOrigin
							FROM ##tblPaymentProfile
							WHERE profileID = @profileID
							AND bankImmediateOrigin <> @bankImmediateOrigin;
							
							INSERT INTO ##tmpLogMessages(msg)
							SELECT 'bankCompanyName changed from ' + bankCompanyName + ' to ' + @bankCompanyName
							FROM ##tblPaymentProfile
							WHERE profileID = @profileID
							AND bankCompanyName <> @bankCompanyName;
							
							INSERT INTO ##tmpLogMessages(msg)
							SELECT 'bankImmediateOriginName changed from ' + bankImmediateOriginName + ' to ' + @bankImmediateOriginName
							FROM ##tblPaymentProfile
							WHERE profileID = @profileID
							AND bankImmediateOriginName <> @bankImmediateOriginName;
							
							INSERT INTO ##tmpLogMessages(msg)
							SELECT 'bankCompanyEnterDesc changed from ' + bankCompanyEnterDesc + ' to ' + @bankCompanyEnterDesc
							FROM ##tblPaymentProfile
							WHERE profileID = @profileID
							AND bankCompanyEnterDesc <> @bankCompanyEnterDesc;
						</cfif>

						<cfif local.gatewayID EQ 10 AND application.objUser.isSuperUser(cfcuser=session.cfcuser)>
							INSERT INTO ##tmpLogMessages(msg)
							SELECT 'Enable MC Pay changed from ' + CASE WHEN enableMCPay = 1 THEN 'Yes' ELSE 'No' END + ' to ' + CASE WHEN @enableMCPay = 1 THEN 'Yes' ELSE 'No' END
							FROM ##tblPaymentProfile
							WHERE profileID = @profileID
							AND enableMCPay <> @enableMCPay;

							INSERT INTO ##tmpLogMessages(msg)
							SELECT 'Activate Voluntary Processing Fee Donation Settings changed from ' + CASE WHEN activateProcessingFeeSettings = 1 THEN 'Yes' ELSE 'No' END + ' to ' + CASE WHEN @activateProcessingFeeSettings = 1 THEN 'Yes' ELSE 'No' END
							FROM ##tblPaymentProfile
							WHERE profileID = @profileID
							AND activateProcessingFeeSettings <> @activateProcessingFeeSettings;

							INSERT INTO ##tmpLogMessages(msg)
							SELECT 'Enable Apple Pay changed from ' + CASE WHEN enableApplePay = 1 THEN 'Yes' ELSE 'No' END + ' to ' + CASE WHEN @enableApplePay = 1 THEN 'Yes' ELSE 'No' END
							FROM ##tblPaymentProfile
							WHERE profileID = @profileID
							AND enableApplePay <> @enableApplePay;

							INSERT INTO ##tmpLogMessages(msg)
							SELECT 'Enable Google Pay changed from ' + CASE WHEN enableGooglePay = 1 THEN 'Yes' ELSE 'No' END + ' to ' + CASE WHEN @enableGooglePay = 1 THEN 'Yes' ELSE 'No' END
							FROM ##tblPaymentProfile
							WHERE profileID = @profileID
							AND enableGooglePay <> @enableGooglePay;

							INSERT INTO ##tmpLogMessages(msg)
							SELECT 'Google Pay Merchant ID changed from ' + ISNULL(googlePayMerchantID,'[blank]') + ' to ' + ISNULL(@googlePayMerchantID,'[blank]')
							FROM ##tblPaymentProfile
							WHERE profileID = @profileID
							AND ISNULL(googlePayMerchantID,'') <> ISNULL(@googlePayMerchantID,'');

							INSERT INTO ##tmpLogMessages(msg)
							SELECT 'Enable Surcharge changed from ' + CASE WHEN enableSurcharge = 1 THEN 'Yes' ELSE 'No' END + ' to ' + CASE WHEN @enableSurcharge = 1 THEN 'Yes' ELSE 'No' END
							FROM ##tblPaymentProfile
							WHERE profileID = @profileID
							AND enableSurcharge <> @enableSurcharge;

							INSERT INTO ##tmpLogMessages(msg)
							SELECT 'Surcharge Percentage changed from ' + CAST(surchargePercent AS varchar(10)) + '% to ' + CAST(@surchargePercent AS varchar(10)) + '%.'
							FROM ##tblPaymentProfile
							WHERE profileID = @profileID
							AND surchargePercent <> @surchargePercent;

							INSERT INTO ##tmpLogMessages(msg)
							SELECT 'Surcharge Revenue GLAccountID changed from ' + cast(surchargeRevenueGLAccountID as varchar(10))  + ' to ' + cast(ISNULL(@surchargeRevenueGLAccountID,0) as varchar(10))
							FROM ##tblPaymentProfile
							WHERE profileID = @profileID
							AND ISNULL(surchargeRevenueGLAccountID,0) <> ISNULL(@surchargeRevenueGLAccountID,0);
						</cfif>

						<cfif local.processingFeeSettingsActivated>
							INSERT INTO ##tmpLogMessages(msg)
							SELECT 'Enable Voluntary Processing Fee Donation changed from ' + CASE WHEN enableProcessingFeeDonation = 1 THEN 'Yes' ELSE 'No' END + ' to ' + CASE WHEN @enableProcessingFeeDonation = 1 THEN 'Yes' ELSE 'No' END
							FROM ##tblPaymentProfile
							WHERE profileID = @profileID
							AND enableProcessingFeeDonation <> @enableProcessingFeeDonation;

							INSERT INTO ##tmpLogMessages(msg)
							SELECT 'Voluntary Processing Fee Donation - Fee Percentage changed from ' + CAST(processFeeDonationFeePercent AS varchar(10)) + '% to ' + CAST(@processFeeDonationFeePercent AS varchar(10)) + '%.'
							FROM ##tblPaymentProfile
							WHERE profileID = @profileID
							AND processFeeDonationFeePercent <> @processFeeDonationFeePercent;

							INSERT INTO ##tmpLogMessages(msg)
							SELECT 'Voluntary Processing Fee Label changed from ' + processingFeeLabel + ' to ' + @processingFeeLabel
							FROM ##tblPaymentProfile
							WHERE profileID = @profileID
							AND processingFeeLabel <> @processingFeeLabel;

							INSERT INTO ##tmpLogMessages(msg)
							SELECT 'Voluntary Processing Fee Donation - Solicitation Message updated.'
							FROM ##tblPaymentProfile
							WHERE profileID = @profileID
							AND ISNULL(solicitationMessageID,0) <> ISNULL(@solicitationMessageID,0);

							INSERT INTO ##tmpLogMessages(msg)
							SELECT 'YES Option Label for Contributions Payments changed from ' + processFeeContributionsFELabel + ' to ' + @processFeeContributionsFELabel
							FROM ##tblPaymentProfile
							WHERE profileID = @profileID
							AND processFeeContributionsFELabel <> @processFeeContributionsFELabel;

							INSERT INTO ##tmpLogMessages(msg)
							SELECT 'NO Option Label for Contributions Payments changed from ' + processFeeContributionsFEDenyLabel + ' to ' + @processFeeContributionsFEDenyLabel
							FROM ##tblPaymentProfile
							WHERE profileID = @profileID
							AND processFeeContributionsFEDenyLabel <> @processFeeContributionsFEDenyLabel;

							INSERT INTO ##tmpLogMessages(msg)
							SELECT 'YES Option Label for Subscriptions Payments changed from ' + processFeeSubscriptionsFELabel + ' to ' + @processFeeSubscriptionsFELabel
							FROM ##tblPaymentProfile
							WHERE profileID = @profileID
							AND processFeeSubscriptionsFELabel <> @processFeeSubscriptionsFELabel;

							INSERT INTO ##tmpLogMessages(msg)
							SELECT 'NO Option Label for Subscriptions Payments changed from ' + processFeeSubscriptionsFEDenyLabel + ' to ' + @processFeeSubscriptionsFEDenyLabel
							FROM ##tblPaymentProfile
							WHERE profileID = @profileID
							AND processFeeSubscriptionsFEDenyLabel <> @processFeeSubscriptionsFEDenyLabel;

							INSERT INTO ##tmpLogMessages(msg)
							SELECT 'YES Option Label for All Other Payments (Events, etc.) changed from ' + processFeeOtherPaymentsFELabel + ' to ' + @processFeeOtherPaymentsFELabel
							FROM ##tblPaymentProfile
							WHERE profileID = @profileID
							AND processFeeOtherPaymentsFELabel <> @processFeeOtherPaymentsFELabel;

							INSERT INTO ##tmpLogMessages(msg)
							SELECT 'NO Option Label for All Other Payments (Events, etc.) changed from ' + processFeeOtherPaymentsFEDenyLabel + ' to ' + @processFeeOtherPaymentsFEDenyLabel
							FROM ##tblPaymentProfile
							WHERE profileID = @profileID
							AND processFeeOtherPaymentsFEDenyLabel <> @processFeeOtherPaymentsFEDenyLabel;

							INSERT INTO ##tmpLogMessages(msg)
							SELECT 'Voluntary Processing Fee Donation - Default Selection changed from ' + CASE WHEN processFeeDonationDefaultSelect = 1 THEN 'Yes' ELSE 'No' END + ' to ' + CASE WHEN @processFeeDonationDefaultSelect = 1 THEN 'Yes' ELSE 'No' END
							FROM ##tblPaymentProfile
							WHERE profileID = @profileID
							AND processFeeDonationDefaultSelect <> @processFeeDonationDefaultSelect;

							INSERT INTO ##tmpLogMessages(msg)
							SELECT 'Voluntary Processing Fee Donation - Revenue GLAccountID changed from ' + cast(processFeeDonationRenevueGLAccountID as varchar(10))  + ' to ' + cast(ISNULL(@processFeeDonationRenevueGLAccountID,0) as varchar(10))
							FROM ##tblPaymentProfile
							WHERE profileID = @profileID
							AND ISNULL(processFeeDonationRenevueGLAccountID,0) <> ISNULL(@processFeeDonationRenevueGLAccountID,0);

							INSERT INTO ##tmpLogMessages(msg)
							SELECT 'Voluntary Processing Fee Donation - Revenue Transaction Description updated.'
							FROM ##tblPaymentProfile
							WHERE profileID = @profileID
							AND processFeeDonationRevTransDesc <> @processFeeDonationRevTransDesc;
						</cfif>
						
						<cfif len(local.transactionLabel)>
							INSERT INTO ##tmpLogMessages(msg)
							SELECT 'TransactionLabel changed from ' + transactionLabel + ' to ' + @transactionLabel
							FROM ##tblPaymentProfile
							WHERE profileID = @profileID
							AND transactionLabel <> @transactionLabel;
						</cfif>

						<cfif local.gatewayID EQ 19>
							INSERT INTO ##tmpLogMessages(msg)
							SELECT 'Individual Limit changed from ' + cast(indivLimit as varchar(20))  + ' to ' + cast(ISNULL(@indivLimit,0) as varchar(20))
							FROM ##tblPaymentProfile
							WHERE profileID = @profileID
							AND ISNULL(indivLimit,0) <> ISNULL(@indivLimit,0);

							INSERT INTO ##tmpLogMessages(msg)
							SELECT 'Day Limit changed from ' + cast(dayLimit as varchar(20))  + ' to ' + cast(ISNULL(@dayLimit,0) as varchar(20))
							FROM ##tblPaymentProfile
							WHERE profileID = @profileID
							AND ISNULL(dayLimit,0) <> ISNULL(@dayLimit,0);

							INSERT INTO ##tmpLogMessages(msg)
							SELECT 'Month Limit changed from ' + cast(monthLimit as varchar(20))  + ' to ' + cast(ISNULL(@monthLimit,0) as varchar(20))
							FROM ##tblPaymentProfile
							WHERE profileID = @profileID
							AND ISNULL(monthLimit,0) <> ISNULL(@monthLimit,0);
						</cfif>
						
						<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) and len(arguments.event.getTrimValue('profileUID',''))>
							INSERT INTO ##tmpLogMessages(msg)
							SELECT 'API ID changed from ' + CAST(uid AS NVARCHAR(MAX))  + ' to ' + CAST(@newProfileUID AS NVARCHAR(MAX)) 
							FROM ##tblPaymentProfile
							WHERE profileID = @profileID
							AND uid <> @newProfileUID;
						</cfif>

						INSERT INTO ##tmpLogMessages(msg)
						SELECT 'Org Identity changed from ' + old.organizationName + ' to ' + new.organizationName
						FROM ##tblPaymentProfile tp
						inner join dbo.orgIdentities as old ON old.orgIdentityID = tp.orgIdentityID
						inner join dbo.orgIdentities as new ON new.orgIdentityID = @orgIdentityID
						WHERE tp.profileID = @profileID
						AND ISNULL(tp.orgIdentityID,0) <> ISNULL(@orgIdentityID,0);

						IF EXISTS(SELECT 1 FROM ##tmpLogMessages) BEGIN
							SELECT @msgjson = 'Payment Profile '+ memberCentral.dbo.fn_cleanInvalidXMLChars(profileName) + ' has been updated.'
							FROM ##tblPaymentProfile
							WHERE profileID = @profileID

							SET @msgjson = @msgjson + @crlf + 'The following changes have been made:';

							SELECT @msgjson = COALESCE(@msgjson + @crlf, '') + memberCentral.dbo.fn_cleanInvalidXMLChars(msg)
							FROM ##tmpLogMessages
							WHERE msg IS NOT NULL;

							INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
							VALUES ('{ "c":"auditLog", "d": {
								"AUDITCODE":"PP",
								"ORGID":' + CAST(#arguments.event.getValue('mc_siteInfo.orgID')# AS VARCHAR(10)) + ',
								"SITEID":' + CAST(#arguments.event.getValue('mc_siteInfo.siteID')# AS VARCHAR(10)) + ',
								"ACTORMEMBERID":' + CAST(#session.cfcuser.memberdata.memberID# AS VARCHAR(20)) + ',
								"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
								"MESSAGE":"' + REPLACE(@msgjson,'"','\"') + '" } }');
						END	
				
					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>
				
				<!--- if not allowing payments, clear anything that may have relied on it --->
				<cfif arguments.event.getTrimValue('allowPayments',0) is 0>
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.update2">
						update dbo.sub_rateFrequenciesMerchantProfiles
						set status = 'D'
						where profileID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('profileID')#">
					</cfquery>
				</cfif>
				
				<cfif local.gatewayID is 11>
					<cfset updateContent(arguments.event.getValue('paymentInstructionsContentID',0), 1, 1, 'Payment Instructions', '', arguments.event.getTrimValue('paymentInstructionsContent',''))>
				</cfif>

			<cfelse>
				<cfstoredproc procedure="mp_insertProfile" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.gatewayID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('profileName')#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('tabTitle')#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('new.profileCode')#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.transactionLabel#">
					<cfif len(arguments.event.getTrimValue('gatewayUsername'))>
						<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('gatewayUsername')#">
						<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('gatewayPassword')#">
					<cfelse>
						<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="yes">
						<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="yes">
					</cfif>
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.event.getTrimValue('allowPayments',0)#">
					<cfif listFind("11,13",local.gatewayID)>
						<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
						<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
					<cfelse>
						<cfif arguments.event.getTrimValue('allowRefundsFromAnyProfile',0) EQ 0>
							<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
							<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
						<cfelseif arguments.event.getTrimValue('allowRefundsFromAnyProfile',0) EQ 1>
							<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="1">
							<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
						<cfelseif arguments.event.getTrimValue('allowRefundsFromAnyProfile',0) EQ 2>
							<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="1">
							<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="1">
						</cfif>			
					</cfif>
					<cfif local.gatewayID is 11>
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
					<cfelse>
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.cashGLAccountID#">
					</cfif>
					<cfif len(arguments.event.getTrimValue('gatewayMerchantId'))>
						<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('gatewayMerchantId')#">
					<cfelse>
						<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="yes">
					</cfif>
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.event.getTrimValue('allowPayInvoicesOnline',0)#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('bankAccountName')#">

					<cfif local.maxFailedAutoAttempts>
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.maxFailedAutoAttempts#">
					<cfelse>
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
					</cfif>
					<cfif local.daysBetweenAutoAttempts>
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.daysBetweenAutoAttempts#">
					<cfelse>
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
					</cfif>
					<cfif len(local.minDaysFailedCleanup)>
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.minDaysFailedCleanup#">
					<cfelse>
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
					</cfif>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getTrimValue('orgIdentityID',0)#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
					<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.profileID">
				</cfstoredproc>

				<cfif local.gatewayID EQ 10>
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdateAuthPayProfile">
						SET XACT_ABORT, NOCOUNT ON;
						BEGIN TRY

							DECLARE @profileID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.profileID#">,
								@gatewayAccountID varchar(20) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.gatewayAccountID#">;

							<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
								DECLARE @enableMCPay bit = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.enableMCPay#">,
									@enableApplePay bit = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('enableApplePay',0)#">,
									@enableGooglePay bit = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.enableGooglePay#">,
									@googlePayMerchantID varchar(20);
								<cfif local.enableGooglePay>
									SET @googlePayMerchantID = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#application.MCEnvironment EQ 'production' ? arguments.event.getTrimValue('googlePayMerchantID','') : local.googlePayTESTMerchantID#">
								</cfif>
								DECLARE	@surchargePercent decimal(5,2) = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.surchargePercent#">,
										@surchargeRevenueGLAccountID int = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.surchargeRevenueGLAccountID#">,0), 
										@enableSurcharge bit = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.enableSurcharge#">;
							</cfif>

							UPDATE dbo.mp_profiles
							SET gatewayAccountID = @gatewayAccountID
								<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
									, enableMCPay = @enableMCPay
									, dateUpdatedMCPayEnable = GETDATE()
									, enableApplePay = @enableApplePay
									, enableGooglePay = @enableGooglePay
									, googlePayMerchantID = @googlePayMerchantID
									, enableSurcharge = @enableSurcharge
									, surchargePercent = @surchargePercent
									, surchargeRevenueGLAccountID = @surchargeRevenueGLAccountID
								</cfif>
							WHERE profileID = @profileID;

						END TRY
						BEGIN CATCH
							IF @@trancount > 0 ROLLBACK TRANSACTION;
							EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
						END CATCH
					</cfquery>
				<cfelseif local.gatewayID is 11>
					<cfquery name="local.qryNewProfile" datasource="#application.dsn.membercentral.dsn#">
						select paymentInstructionsContentID
						from dbo.mp_profiles
						where profileID = #local.profileID#
					</cfquery>
					<cfset updateContent(local.qryNewProfile.paymentInstructionsContentID, 1, 1, 'Payment Instructions', '', arguments.event.getTrimValue('paymentInstructionsContent',''))>
				<cfelseif local.gatewayID is 16>
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.update2">
						update dbo.mp_profiles
						set bankTRN = <cfqueryparam cfsqltype="cf_sql_char" value="#local.bankTRN#">,
							bankCompanyNumber = <cfqueryparam cfsqltype="cf_sql_char" value="#local.bankCN#">,
							bankImmediateOrigin = <cfqueryparam cfsqltype="cf_sql_char" value="#local.bankIO#">,
							bankCompanyName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#uCase(arguments.event.getTrimValue('bankCompanyName'))#">,
							bankImmediateOriginName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#uCase(arguments.event.getTrimValue('bankImmediateOriginName'))#">,
							bankCompanyEnterDesc = <cfqueryparam cfsqltype="cf_sql_varchar" value="#uCase(arguments.event.getTrimValue('bankCompanyEnterDesc'))#">
						where profileID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.profileID#">
					</cfquery>
				<cfelseif local.gatewayID is 19>
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.update2">
						update dbo.mp_profiles
						set indivLimit = NULLIF(<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.indivLimit#">,0),
							dayLimit = NULLIF(<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.dayLimit#">,0),
							monthLimit = NULLIF(<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.monthLimit#">,0)
						where profileID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.profileID#">
					</cfquery>
				</cfif>

				<cfif local.processingFeeSettingsActivated>
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdateProfile">
						UPDATE dbo.mp_profiles
						SET enableProcessingFeeDonation = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.enableProcessingFeeDonation#">,
							processFeeDonationFeePercent = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.processFeeDonationFeePercent#">,
							processingFeeLabel = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.processingFeeLabel#">,
							solicitationMessageID = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.solicitationMessageID#">,0),
							processFeeContributionsFELabel = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.processFeeContributionsFELabel#">,
							processFeeContributionsFEDenyLabel = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.processFeeContributionsFEDenyLabel#">,
							processFeeSubscriptionsFELabel = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.processFeeSubscriptionsFELabel#">,
							processFeeSubscriptionsFEDenyLabel = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.processFeeSubscriptionsFEDenyLabel#">,
							processFeeOtherPaymentsFELabel = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.processFeeOtherPaymentsFELabel#">,
							processFeeOtherPaymentsFEDenyLabel = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.processFeeOtherPaymentsFEDenyLabel#">,
							processFeeDonationDefaultSelect = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.processFeeDonationDefaultSelect#">,
							processFeeDonationRenevueGLAccountID = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.processFeeDonationRenevueGLAccountID#">,0),
							processFeeDonationRevTransDesc = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.processFeeDonationRevTransDesc#">
							<cfif local.gatewayID EQ 10>
								, activateProcessingFeeSettings = 1
							</cfif>
						WHERE profileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.profileID#">
					</cfquery>
				</cfif>
				
				<cfset arguments.event.setValue('profileID',local.profileID)>
			</cfif>

			<!--- register apple pay merchant if enabling apple pay and unregister apple pay merchant if disabling apple pay --->
			<cfif local.enablingApplePayThisSave OR local.disablingApplePayThisSave>
				<cfset local.applePayUtils = new model.system.platform.gateways.applePayUtils(PAYMENTUTILITIESURL=application.paths.paymentUtilities.url)>

				<cfif local.enablingApplePayThisSave>
					<cfset local.strApplePayResult = local.applePayUtils.registerMerchant(profileID=local.profileID, siteCode=arguments.event.getValue('mc_siteInfo.sitecode'))>
					<cfif NOT local.strApplePayResult.success>
						<cfthrow detail="Unable to register with Apple Pay">
					</cfif>

					<cfset local.strMerchant = local.applePayUtils.getMerchant(profileID=local.profileID, siteCode=arguments.event.getValue('mc_siteInfo.sitecode'))>
					<cfif local.strMerchant.success>
						<cfquery name="local.qrySaveApplePayDomains" datasource="#application.dsn.membercentral.dsn#">
							EXEC dbo.tr_addApplePayDomains
								@merchantIdentifier = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strMerchant.data.partnerInternalMerchantIdentifier#">,
								@hostNameList = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arrayToList(local.strMerchant.data.domainNames)#">;
						</cfquery>
					</cfif>
				<cfelseif local.disablingApplePayThisSave>
					<cfset local.expectedMerchantIdentifier = local.applePayUtils.getDerivedApplePayMerchantIdentifier(profileID=local.profileID, siteID=arguments.event.getValue('mc_siteInfo.siteID'))>
					<cfset local.strApplePayResult = local.applePayUtils.unregisterMerchant(profileID=local.profileID, siteCode=arguments.event.getValue('mc_siteInfo.sitecode'))>
					<cfif NOT local.strApplePayResult.success>
						<cfthrow detail="Unable to unregister with Apple Pay">
					</cfif>

					<cfquery name="local.qryDeleteApplePayDomains" datasource="#application.dsn.membercentral.dsn#">
						DELETE FROM dbo.tr_applePayDomains
						WHERE merchantIdentifier = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.expectedMerchantIdentifier#">
					</cfquery>
				</cfif>
			</cfif>

		<cfcatch type="Any">
			<cfset local.errorCode = 'ESMP'>
			<cfif structKeyExists(cfcatch,"detail")>
				<cfif findNoCase("Unable to register with Apple Pay", cfcatch.detail)>
					<cfset local.errorCode = 'APREG'>
				<cfelseif findNoCase("Unable to unregister with Apple Pay", cfcatch.detail)>
					<cfset local.errorCode = 'APUNREG'>
				<cfelseif findNoCase("Merchant ID already exists", cfcatch.detail)>
					<cfset local.errorCode = 'EGMID'>
				<cfelseif findNoCase("Authorize Credentials already in use", cfcatch.detail)>
					<cfset local.errorCode = 'AZCRD'>
				</cfif>
			</cfif>
			<cfif local.errorCode EQ 'ESMP'>
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfif>
			<cflocation url="#this.link.message#&ec=#local.errorCode#" addtoken="no">
		</cfcatch>
		</cftry>

		<!--- delete all cc and fields in database for profileID --->
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDelTypes">
			DELETE from dbo.mp_profileCardTypes
			WHERE profileID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('profileID')#">
		</cfquery>
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDelCodes">
			DELETE from dbo.mp_profileSecCodes
			WHERE profileID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('profileID')#">
		</cfquery>
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDelFields">
			DELETE from dbo.mp_profileFields
			WHERE profileID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('profileID')#">
		</cfquery>
		
		<cfloop collection="#arguments.event.getCollection()#" item="local.key">
			<cfif GetToken(local.key,1,'_') EQ "cardTypeID">
				<cfset local.cardTypeID = val(GetToken(local.key,2,'_'))>
				<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="mp_insertProfileCardTypes">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('profileID')#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.cardTypeID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(arguments.event.getValue('GLAccountID_' & local.cardTypeID,0))#">
				</cfstoredproc>
			<cfelseif GetToken(local.key,1,'_') EQ "secCodeID">
				<cfset local.secCodeID = val(GetToken(local.key,2,'_'))>
				<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="mp_insertProfileSecCodes">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('profileID')#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.secCodeID#">
				</cfstoredproc>
			<cfelseif GetToken(local.key,1,'_') EQ "fieldID">
				<cfset local.fieldID = val(GetToken(local.key,3,'_'))>
				<cfset local.isRequired = arguments.event.getValue('isRequired_' & GetToken(local.key,2,'_') & '_' & GetToken(local.key,3,'_'),0)>
				<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="mp_insertProfileField">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('profileID')#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.fieldID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#local.isRequired#">
				</cfstoredproc>
			</cfif>
		</cfloop>

		<!--- handle image deletion --->
		<cfif arguments.event.getValue('profileID',0) GT 0 and Len(arguments.event.getValue('deleteImage',''))>
			<cfquery name="local.qryImageExt" datasource="#application.dsn.membercentral.dsn#">
				SELECT refundImageExt 
				FROM dbo.mp_profiles
				WHERE profileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('profileID')#">
				AND siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">
				AND [status] = 'A'
			</cfquery>
			<cfif fileExists("#application.paths.RAIDUserAssetRoot.path#common/refunds/#arguments.event.getValue('profileID')#.#local.qryImageExt.refundImageExt#")>
				<cffile action="delete" file="#application.paths.RAIDUserAssetRoot.path#common/refunds/#arguments.event.getValue('profileID')#.#local.qryImageExt.refundImageExt#">
			</cfif>
			<cfquery name="local.qryImageExt" datasource="#application.dsn.membercentral.dsn#">
				UPDATE dbo.mp_profiles
				SET refundImageExt = NULL
				WHERE profileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('profileID')#">
				AND siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#">
				AND [status] = 'A'
			</cfquery>
		</cfif>
		
		<!--- handle image uploading --->
		<cfif len(arguments.event.getValue('refundImage',""))>
			<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
			<cfset local.destination = "#application.paths.RAIDUserAssetRoot.path#common/refunds/">
			<cffile action="upload" filefield="refundImage" destination="#local.strFolder.folderPath#" nameConflict="makeUnique" result="local.upload">
			
			<cfif listFindNoCase("png,gif",local.upload.ServerFileExt)>
				<cfscript>
				local.imageOK = true;
				try {
					local.origPhoto = "#local.upload.ServerDirectory#/#local.upload.ServerFile#";
					local.origPhotoInfo = application.objCommon.thumborImageInfo(filePath=local.origPhoto);

					if (local.origPhotoInfo.imageInfo.source.width neq 400 OR local.origPhotoInfo.imageInfo.source.height neq 150) {
						local.imageOK = false;
					}
				}
				catch( any excpt ){
					local.imageOK = false;
					application.objError.sendError(cfcatch=excpt);
				}
				</cfscript>

				<cfif local.imageOK>
					<cffile action="move" destination="#local.destination##arguments.event.getValue('profileID')#.#local.upload.ServerFileExt#" source="#local.upload.ServerDirectory#/#local.upload.ServerFile#">
					<cfquery name="local.qryImageExt" datasource="#application.dsn.membercentral.dsn#">
						UPDATE dbo.mp_profiles
						SET refundImageExt = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.upload.ServerFileExt#">
						WHERE profileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('profileID')#">
						AND siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">
						AND [status] = 'A'
					</cfquery>
				</cfif>
			<cfelse>
				<cfset local.imageOK = false>
			</cfif>

			<cfif NOT local.imageOK and FileExists("#local.upload.ServerDirectory#/#local.upload.ServerFile#")>
				<cffile action="delete" file="#local.upload.ServerDirectory#/#local.upload.ServerFile#">
				<cflocation url="#this.link.edit#&profileID=#arguments.event.getValue('profileID')#&err=1" addtoken="no">
			</cfif>

		</cfif>
		
		<cfif NOT arguments.noRedirect>
			<cflocation url="#this.link.list#" addtoken="no">
		</cfif>
	</cffunction>	

	<cffunction name="doMoveProfile" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="profileID" type="numeric" required="true">
		<cfargument name="dir" type="string" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfstoredproc procedure="mp_moveProfile" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.profileID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.dir#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="updateContent" access="public" output="false" returntype="void">
		<cfargument name="contentID" type="numeric" required="true">
		<cfargument name="languageID" type="numeric" required="true">
		<cfargument name="isHTML" type="boolean" required="true">
		<cfargument name="contentTitle" type="string" required="true">
		<cfargument name="contentDesc" type="string" required="true">
		<cfargument name="rawContent" type="string" required="true">
		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="dbo.cms_updateContent">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.contentID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.languageID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.isHTML#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.contentTitle#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.contentDesc#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.rawContent#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).orgID)#">
		</cfstoredproc>
	</cffunction>

	<cffunction name="message" access="public" output="false" returntype="struct" hint="Messages for this App">
		<cfargument name="Event" type="any">
		<cfset var local = structNew()>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<h4>We're Sorry!</h4>
				<div>
					<cfswitch expression="#arguments.event.getValue('ec','')#">
						<cfcase value="APREG">The Payment Profile was saved but we were unable to register with Apple Pay. Contact MemberCentral for assistance.</cfcase>
						<cfcase value="APUNREG">The Payment Profile was saved but we were unable to unregister with Apple Pay. Contact MemberCentral for assistance.</cfcase>
						<cfcase value="LNP">You do not have the necessary permissions to manage Payment Profiles.<br/><br/>Should you need assistance, contact your site administrator.</cfcase>
						<cfcase value="EGMID">Gateway Merchant ID already exists.</cfcase>
						<cfcase value="AZCRD">Authorize Credentials already in use.</cfcase>
						<cfcase value="INVMP">Invalid Payment Profile.</cfcase>
						<cfcase value="AUTHINVAPIKEY">The API user name is invalid and/or the transaction key or API key is invalid.</cfcase>
						<cfdefaultcase>An error has occurred. Contact MemberCentral for assistance.</cfdefaultcase>
					</cfswitch>
				</div>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="sampleBankDraftImportTemplate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.data = CreateObject("component","merchantProfilesImport").generateBankDraftImportTemplate()>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="sampleAuthorizeCIMImportTemplate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.data = CreateObject("component","merchantProfilesImport").generateAuthorizeCIMImportTemplate()>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="sampleAffiniPayImportTemplate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.data = CreateObject("component","merchantProfilesImport").generateAffiniPayImportTemplate()>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="processBankDraftImport" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objImport = CreateObject("component","merchantProfilesImport")>

		<cfsetting requesttimeout="500">

		<cfset local.processResult = local.objImport.importBankDraftProfiles(event=arguments.event)>
		<cfset local.data = local.objImport.showImportResults(strResult=local.processResult, doAgainURL=this.link.list, data="bdProfiles")>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="processAuthorizeCIMImport" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objImport = CreateObject("component","merchantProfilesImport")>

		<cfsetting requesttimeout="500">

		<cfset local.processResult = local.objImport.importAuthorizeCIMProfiles(event=arguments.event)>
		<cfset local.data = local.objImport.showImportResults(strResult=local.processResult, doAgainURL=this.link.list, data="authcimProfiles")>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="exportCOF" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder()>
		<cfset local.reportFileName = "MemberPaymentMethods.csv">

		<cfstoredproc procedure="mp_exportCOF" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('f_e_pid',0)#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.strFolder.folderPathUNC#\#local.reportFileName#">
		</cfstoredproc>

		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
		<cfset application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<!--- affinipay functions --->
	<cffunction name="connectToAffinipay" access="public" output="false" returntype="struct">
		<cfargument name="h" type="string" required="true">

		<cfreturn CreateObject("component","model.system.platform.gateways.AffiniPayCC").connectToAffinipay(h=arguments.h)>
	</cffunction>
	<cffunction name="linkAffinipay" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset CreateObject("component","model.system.platform.gateways.AffiniPayCC").linkAffinipay(token=arguments.event.getValue('token',''), 
			siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteCode=arguments.event.getValue('mc_siteInfo.siteCode'), 
			orgID=arguments.event.getValue('mc_siteInfo.orgID'), mpadmin=this, event=arguments.event)>

		<cflocation url="#this.link.list#" addtoken="false">
	</cffunction>
	<cffunction name="affinipayOneTimeTokenToCard" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="row" type="numeric" required="true">
		<cfargument name="memberNumber" type="string" required="true">
		<cfargument name="profileCode" type="string" required="true">
		<cfargument name="nickName" type="string" required="true">
		<cfargument name="token_id" type="string" required="true">

		<cfset var local = structNew()>

		<cftry>
			<!--- Find out which gateway to use based on profile --->
			<cfquery name="local.qryGateWayID" datasource="#application.dsn.membercentral.dsn#" cachedwithin="#CreateTimeSpan(0,0,5,0)#">
				select pr.siteID, pr.profileID, pr.profileCode, ga.gatewayType, pr.gatewayUsername, pr.gatewayPassword, pr.gatewayMerchantID
				from dbo.mp_profiles as pr
				inner join dbo.mp_gateways as ga on ga.gatewayid = pr.gatewayID
				where pr.profileCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.profileCode#">
				and pr.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
				and pr.status = 'A'
				and ga.isActive = 1
			</cfquery>

			<cfif NOT local.qryGateWayID.recordCount>
				<cfthrow message="Profile Code is invalid."> 
			</cfif>

			<cfquery name="local.qryGetActiveMember" datasource="#application.dsn.membercentral.dsn#">
				select mActive.memberID
				from dbo.ams_members as m
				inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
				where m.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
				and m.memberNumber = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.memberNumber#">
			</cfquery>

			<cfif NOT local.qryGetActiveMember.recordCount>
				<cfthrow message="Invalid MemberNumber."> 
			</cfif>

			<cfset local.returnStruct = CreateObject("component","model.system.platform.gateways.AffiniPayCC").insertPaymentProfile(qryGateWayID=local.qryGateWayID, 
				pmid=local.qryGetActiveMember.memberID, token_id=arguments.token_id, nickName=arguments.nickName)>

			<cfif local.returnStruct.success>
				<cfset local.data['success'] = true>
			<cfelse>
				<cfset local.data['success'] = false>
				<cfset local.data['errmsg'] = "Row #arguments.row#: #local.returnStruct.error#">
			</cfif>

		<cfcatch type="Any">
			<cfset local.data['success'] = false>
			<cfset local.data['errmsg'] = "Row #arguments.row#: #cfcatch.message#">
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

</cfcomponent>