<cfcomponent extends="model.AppLoader" output="no">

	<cfset defaultEvent = "controller">
	<cfset variables.instanceSettings = structNew()>
	<cfset variables.applicationReservedURLParams = "dirAction,dirMemberID,memPageNum,mg_gid,fs_match,seed,compare,msg">
	<cfset variables.supportedSocialIcons = "facebook,linkedin,twitter,500px,deliciou,digg,rss,path,google+,vimeo,youtube,wordpress,spotify,blogger,tumblr,blog">

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfscript>
		var local = structNew();
		local.returnStruct = structNew();
		local.returnStruct.objMemberConfig = CreateObject('component','model.members.MemberConfig');
		variables.instanceSettings = getInstanceSettings(this.appInstanceID);
		local.returnStruct.memberDirectoryInfo = local.returnStruct.objMemberConfig.getDirectoryInfo(arguments.event.getValue('mc_siteInfo.siteID'),this.appInstanceID);
		local.returnStruct.memberDirectoryClassificationInfo = local.returnStruct.objMemberConfig.getClassifications(memberDirectoryID=variables.instanceSettings.memberDirectoryID);
		variables.memberDirectoryInfo = local.returnStruct.memberDirectoryInfo;
		local.returnStruct.instanceSettings = variables.instanceSettings;
		local.returnStruct.appInstanceID = this.appInstanceID;

		this.objMFS = CreateObject("component","model.system.platform.memberFieldsets");
			
		// add search params to applicationReservedURLParams
		local.qryFieldsetID = getLocatorFieldsetID(siteResourceID=local.returnStruct.instanceSettings.siteresourceID, area='search');
		local.xmlFields = this.objMFS.getMemberFieldsXML(fieldsetid=val(local.qryFieldsetID.fieldsetID), usage="memberDirectorySearch");
		local.arrFieldCodes = XMLSearch(local.xmlFields,"//mf/@fieldCode");
		local.arrRadiusFields = [];
		for (local.i=ArrayLen(local.arrFieldCodes); local.i gt 0; local.i--){
			if (findNoCase("_postalcode",local.arrFieldCodes[local.i].xmlValue)) { 
				arrayAppend(local.arrFieldCodes,"#local.arrFieldCodes[local.i].xmlValue#_radius");
				local.arrRadiusFields.append("#local.arrFieldCodes[local.i].xmlValue#_radius");
			}
		}
		variables.applicationReservedURLParams = reReplaceNoCase(listAppend(variables.applicationReservedURLParams,replaceNoCase(arrayToList(local.arrFieldCodes),'<?xml version="1.0" encoding="UTF-8"?>','','ALL')),"\s+","","ALL");
		local.returnStruct.baseQueryString = getBaseQueryString(false,true);

		if (application.objCMS.getTemplateSetting(arguments.event,"supportsBootstrap") eq "true")
			local.viewDirectory = "responsive";
		else
			local.viewDirectory = "default";

		// prevent sql attacks by cleaning vars
		local.cleanIntVars = "dirMemberID,memPageNum,seed";
		for (local.i=1; local.i lte listLen(local.cleanIntVars); local.i++) {
			if (arguments.event.getValue(listgetat(local.cleanIntVars,local.i),'') neq '' and NOT isValid('integer',arguments.event.getValue(listgetat(local.cleanIntVars,local.i)))) {
				application.objCommon.redirect('/?#local.returnStruct.baseQueryString#');
			}
		}
		// mg_gid can be a list of ints
		local.tmpGIDList = arguments.event.getValue('mg_gid','');
		if (local.tmpGIDList neq '') {
			for (local.i=1; local.i lte listLen(local.tmpGIDList); local.i++) {
				if (NOT isValid('integer',trim(listgetat(local.tmpGIDList,local.i)))) {
					application.objCommon.redirect('/?#local.returnStruct.baseQueryString#');
				}
			}
		}
		// _postalcode_radius fields must be one of the accepted values
		local.radiusOK = true;
		for (local.i=1; local.i lte ArrayLen(local.arrRadiusFields); local.i++) {
			if (len(arguments.event.getValue(local.arrRadiusFields[local.i],'')) AND NOT listFind("5,10,25,50,100",arguments.event.getValue(local.arrRadiusFields[local.i],5)))
				local.radiusOK = false;
		}
		if (NOT local.radiusOK)
			application.objCommon.redirect('/?#local.returnStruct.baseQueryString#');

		// build app menu
		application.objCMS.addAppMenu(arguments.event,this.siteResourceID,local.returnStruct.memberDirectoryInfo.applicationInstanceName,"members");
		application.objCMS.addAppMenuItem(arguments.event,this.siteResourceID,"Search","/?#local.returnStruct.baseQueryString#&dirAction=search");
		application.objCMS.addAppMenuItem(arguments.event,this.siteResourceID,"Update Your Listing","/?pg=updateMember");

		if (structKeyExists(session.mcStruct["deviceProfile"],"is_small_screen") and session.mcStruct["deviceProfile"]["is_small_screen"])
			local.returnStruct.makePhoneNumbersClckable = true;
		else
			local.returnStruct.makePhoneNumbersClckable = false;

		if (structKeyExists(session.mcStruct["deviceProfile"],"device_os") and session.mcStruct["deviceProfile"]["device_os"] eq "ios")
			local.returnStruct.mappingBaseLink = 'https://maps.apple.com/?q=';
		else
			local.returnStruct.mappingBaseLink = 'https://maps.google.com/maps?f=q&source=s_q&hl=en&geocode=&z=16&q=';
		</cfscript>

		<cfquery name="local.returnStruct.qrySocialNetwork" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select sn.socialNetworkID, sn.applicationInstanceID, sn.masterSiteID, sn.masterOrgID, sn.masterSocialNetworkID
			from dbo.sn_socialNetworks sn
			inner join dbo.cms_applicationInstances ai on ai.applicationInstanceID = sn.applicationInstanceID 
				and ai.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteInfo.siteID')#">
			inner join dbo.cms_siteResources sr on sr.siteResourceID = ai.siteResourceID
			inner join dbo.cms_siteResourceStatuses srs on srs.siteResourceStatusID = sr.siteResourceStatusID 
				and srs.siteResourceStatusDesc = <cfqueryparam cfsqltype="cf_sql_varchar" value="Active">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif not len(arguments.event.getValue('dirAction',''))>
			<cfset arguments.event.setValue('dirAction',local.returnStruct.instanceSettings.defaultAction)>
		</cfif>

		<!--- link to edit directory page from toolbar --->
		<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
			<cfset local.appPageCPLink = { linkText="Edit Directory", link="/?pg=admin&jumpToTool=MemberDirectoryAdmin%7Clist%7Cedit&mdID=#variables.instanceSettings.memberDirectoryID#" }>
			<cfset arguments.event.setValue('mc_appPageCPLink', local.appPageCPLink)>
		</cfif>

		<cfswitch expression="#arguments.event.getValue('dirAction')#">
			<cfcase value="searchResults">
				<!--- generate results seed --->
				<!--- If no seed in criteria, create it and set in session --->
				<!--- If seed in criteria, validate against session else create it and set in session --->
				<cfparam name="session.mcStruct.memberDirectorySearch" default="#arrayNew(1)#">
				<cfset local.SearchSeed = arguments.event.getTrimValue('seed','')>
				<cfset local.SearchSeedChanged = false>
				<cfset local.newSeedNeeded = false>
				
				<cfif not len(local.SearchSeed)>
					<!--- anyone without a seed (bots included) need to get one --->
					<cfset local.newSeedNeeded = true>
				<cfelseif isDefined("session.mcstruct.deviceProfile.is_bot") and session.mcstruct.deviceProfile.is_bot is 1>
					<!--- bots with an existing seed are good to go --->
					<cfset local.newSeedNeeded = false>
				<cfelse>
					<!--- non bots need a new seed, if this wasn't created in their session --->
					<cfset local.newSeedNeeded =  (NOT listFindNoCase(arrayToList(session.mcStruct.memberDirectorySearch,chr(7)),local.SearchSeed,chr(7)))>
				</cfif>
				
				<cfif local.newSeedNeeded>
					<cfset local.SearchSeedChanged = true>
					<cfset local.SearchSeed = randRange(100000,999999,'SHA1PRNG')>
					<cfset arrayAppend(session.mcStruct.memberDirectorySearch,local.SearchSeed)>
					<cfset arguments.event.setValue('seed',local.SearchSeed)>
				</cfif>

				<!--- if posting from a search form, put form vars into url vars and redirect. --->
				<!--- helps create the real link as well as allows the back button to not cause a browser alert --->
				<cfif CGI.REQUEST_METHOD eq "post">
					<cfset local.returnStruct.directlink = "/?#local.returnStruct.baseQueryString#&dirAction=SearchResults">
					<cfset local.returnStruct.membersearchform = duplicate(form)>
					<cfset structDelete(local.returnStruct.membersearchform,"btnSubmit")>
					<cfset structDelete(local.returnStruct.membersearchform,"fieldnames")>
					<cfloop collection="#local.returnStruct.membersearchform#" item="local.currentitem">
						<cfif ReFindNoCase('mat?_[0-9]+_postalcode_radius',local.currentitem)>
							<cfif len(local.returnStruct.membersearchform[replaceNoCase(local.currentitem,'_radius','')])>
								<cfset local.returnStruct.directlink = local.returnStruct.directlink & "&" & lcase(local.currentitem) & "=" & urlencodedformat(local.returnStruct.membersearchform[local.currentitem])>
							</cfif>
						<cfelseif len(local.returnStruct.membersearchform[local.currentitem])>
							<cfset local.returnStruct.directlink = local.returnStruct.directlink & "&" & lcase(local.currentitem) & "=" & urlencodedformat(local.returnStruct.membersearchform[local.currentitem])>
						</cfif>
					</cfloop>
					<cfset local.returnStruct.directlink = local.returnStruct.directlink & "&seed=" & local.SearchSeed>
					<cflocation url="#local.returnStruct.directlink#" addtoken="no">

				<!--- if a get, ensure the correct seed is in the URL --->
				<cfelseif CGI.REQUEST_METHOD eq "GET" and local.SearchSeedChanged>
					<cfset local.neqQS = "">
					<cfloop collection="#url#" item="local.x">
						<cfif (not listfindnocase('seed',local.x))>
							<cfset local.neqQS = local.neqQS & "&#lcase(local.x)#=#url[local.x]#">
						</cfif>
					</cfloop>
					<cfset local.neqQS = local.neqQS & "&seed=" & local.SearchSeed>
					<cflocation url="/?#local.neqQS#" addtoken="no">
				</cfif>


				<!--- set up comparison struct --->
				<cfif local.returnStruct.memberDirectoryInfo.enableCompare>
					<cfparam name="session.mcStruct.memberDirectorySearchCompare" default="#structNew()#">
					<cfif NOT structKeyExists(session.mcStruct.memberDirectorySearchCompare,local.SearchSeed)>
						<cfset structInsert(session.mcStruct.memberDirectorySearchCompare,local.SearchSeed,arrayNew(1))>
					</cfif>
					<cfset local.returnStruct.numInCompare = arrayLen(session.mcStruct.memberDirectorySearchCompare[local.SearchSeed])>
					<cfset local.returnStruct.linkForToggleCompare = "/?event=cms.showResource&resID=#local.returnStruct.instanceSettings.siteResourceID#&mode=stream&dirAction=toggleCompare&seed=#local.SearchSeed#">

					<cfset local.returnStruct.linkForCompare = "/?">
					<cfloop collection="#url#" item="local.x">
						<cfif (not listfindnocase('memPageNum,compare',local.x))>
							<cfset local.returnStruct.linkForCompare = local.returnStruct.linkForCompare & "&#lcase(local.x)#=#url[local.x]#">
						</cfif>
					</cfloop>
					<cfset local.returnStruct.linkForCompare = local.returnStruct.linkForCompare & "&compare=1">
				</cfif>

				<cfset local.returnStruct.membersearchurl = duplicate(url)>
				<cfset local.returnStruct.arrSearchCriteria = getSearchCriteria(
											orgid=arguments.event.getValue('mc_siteinfo.orgID'), 
											defaultLanguageID=arguments.event.getValue('mc_siteinfo.defaultLanguageID'),
											memberDirectoryInfo=local.returnStruct.memberDirectoryInfo,
											siteResourceID=local.returnStruct.instanceSettings.siteResourceID,
											searchData=local.returnStruct.membersearchurl)>

				<cfquery name="local.resultDisplayDifferencesClassifications" dbtype="query">
					SELECT ClassificationID
					FROM [local].[returnStruct].memberDirectoryClassificationInfo
					where (showGroupImage <> showGroupImageInSearchDetail) or (showInSearchResults <> showInSearchDetail)
				</cfquery>
				
				<cfquery name="local.resultDisplayUnionedFieldSetsPre" dbtype="query">
					SELECT fieldsetID
					FROM [variables].[instanceSettings].[MemberDirectoryFieldsets].results
						union
					SELECT fieldsetID
					FROM [variables].[instanceSettings].[MemberDirectoryFieldsets].details
				</cfquery>
				<cfquery name="local.resultDisplayUnionedFieldSets" dbtype="query">
					SELECT distinct fieldsetID
					FROM [local].resultDisplayUnionedFieldSetsPre
				</cfquery>
				<cfset local.returnStruct.resultDetailDisplayDifferences = structNew()>
				<cfset local.returnStruct.resultDetailDisplayDifferences.isAnythingDifferent = false>
				
				<cfif local.resultDisplayDifferencesClassifications.recordcount>
					<cfset local.returnStruct.resultDetailDisplayDifferences.areClassificationsDifferent = true>
					<cfset local.returnStruct.resultDetailDisplayDifferences.isAnythingDifferent = true>
				<cfelse>
					<cfset local.returnStruct.resultDetailDisplayDifferences.areClassificationsDifferent = false>
				</cfif>
		
				<cfif local.resultDisplayUnionedFieldSets.recordcount eq min(variables.instanceSettings.MemberDirectoryFieldsets.details.recordCount,variables.instanceSettings.MemberDirectoryFieldsets.results.recordCount)>
					<cfset local.returnStruct.resultDetailDisplayDifferences.areFieldsetsDifferent= false>
				<cfelse>
					<cfset local.returnStruct.resultDetailDisplayDifferences.areFieldsetsDifferent = true>
					<cfset local.returnStruct.resultDetailDisplayDifferences.isAnythingDifferent = true>
				</cfif>

				<!--- set up link for paging --->
				<cfset local.returnStruct.linkForPaging = "/?">
				<cfloop collection="#url#" item="local.x">
					<cfif (not listfindnocase('memPageNum',local.x))>
						<cfset local.returnStruct.linkForPaging = local.returnStruct.linkForPaging & "&#lcase(local.x)#=#url[local.x]#">
					</cfif>
				</cfloop>

				<cfset arguments.event.setValue('memPageNum',int(val(arguments.event.getValue('memPageNum',1))))>
				<cfset local.returnStruct.rowsize = local.returnStruct.memberDirectoryInfo.recordsPerPage>
				<cfset local.returnStruct.currPage = arguments.event.getValue('memPageNum')>
				<cfset local.returnStruct.nextPage = local.returnStruct.currPage + 1>
				<cfset local.returnStruct.prevPage = local.returnStruct.currPage - 1>

				<!--- do the search --->
				<cfif local.returnStruct.memberDirectoryInfo.enableCompare and arguments.event.getValue('compare',0)>
					<cfset local.memberIDList = arrayToList(session.mcStruct.memberDirectorySearchCompare[local.searchSeed])>
					<cfset local.returnStruct.runningCompare = 1>
				<cfelse>
					<cfset local.memberIDList = "">
					<cfset local.returnStruct.runningCompare = 0>
				</cfif>
				<cfset local.qryMembers = getMembers(
					siteID=arguments.event.getValue('mc_siteinfo.siteID'), 
					orgid=arguments.event.getValue('mc_siteinfo.orgID'), 
					siteResourceID=local.returnStruct.instanceSettings.siteresourceID,
					memberDirectoryID=local.returnStruct.memberDirectoryInfo.memberDirectoryID, 
					event=arguments.event,
					startrow="#((local.returnStruct.currPage-1)*local.returnStruct.rowsize)+1#",
					endrow="#((local.returnStruct.currPage-1)*local.returnStruct.rowsize)+local.returnStruct.rowsize#",
					maxSearchResults=local.returnStruct.memberDirectoryInfo.maxSearchResults,
					compareMemberIDList=local.memberIDList
					)>

				<cfif (local.returnStruct.memberDirectoryInfo.maxSearchResults gt 0) and (local.qryMembers.totalMatches gte local.returnStruct.memberDirectoryInfo.maxSearchResults)>
					<cfset local.returnStruct.overlimitmessage = "<div><b>Note:</b> Your search returned the maximum allowed number of matching records. This maximum is in place for the protection of our members. Below you will find the first <b>#local.returnStruct.memberDirectoryInfo.maxSearchResults#</b> results that match your search criteria.</div>">
				</cfif>

				<!--- gather the results to display (local.qryFieldsetID could contain multiple fieldsets) --->
				<cfset local.returnStruct.qryMembers = local.qryMembers>
				<cfif (local.returnStruct.memberDirectoryInfo.maxSearchResults eq 0) or (local.returnStruct.memberDirectoryInfo.maxSearchResults gt local.qryMembers.totalMatches)>
					<cfset local.returnStruct.qryMembersCount = local.qryMembers.totalMatches>
				<cfelse>
					<cfset local.returnStruct.qryMembersCount = local.returnStruct.memberDirectoryInfo.maxSearchResults>
				</cfif>
				<cfset local.returnStruct.arrMembers = arrayNew(1)>
				<cfset local.returnStruct.memberPermissions = structNew()>
				<cfset local.returnStruct.multipleAddressTypesDetected = false>
			
				<cfloop query="local.qryMembers">
					<cfquery name="local.qryViewMemberData" dbtype="query">
						select *
						from  [local].qryMembers
						where memberID = #local.qryMembers.memberid#
					</cfquery>

					<cfset local.strLogStats = {}>
					<cfif local.returnStruct.memberDirectoryInfo.enableStats is 1 and NOT application.objUser.isSiteAdmin(cfcuser=session.cfcuser) AND NOT application.objUser.isSuperUser(cfcuser=session.cfcuser) AND isDefined("session.mcStruct.deviceProfile.type") and NOT findnocase("Bot",session.mcStruct.deviceProfile.type) AND session.cfcuser.memberdata.memberID NEQ local.qryMembers.memberid>
						<cfset local.strLogStats = { dirAction=arguments.event.getValue('dirAction'), statsSessionID=session.cfcuser.statsSessionID }>
						<cfif local.returnStruct.memberDirectoryInfo.enableCompare and arguments.event.getValue('compare',0)>
							<cfset local.strLogStats.dirAction = "compareResults">
						</cfif>
					</cfif>
					<cfset local.stThisMember = getMember(
						memberDirectoryID=local.returnStruct.memberDirectoryInfo.memberDirectoryID,
						memberID=local.qryMembers.memberid,
						orgID=arguments.event.getValue('mc_siteInfo.orgID'),
						logStats=local.strLogStats)>

					<!--- Get the permissions for this member --->
					<cfif not structKeyExists(local.returnStruct.memberPermissions,local.stThisMember.qryMember.groupPrintID)>
						<cfset local.returnStruct.memberPermissions[local.stThisMember.qryMember.groupPrintID] = application.objSiteResource.buildCachePermRightAssignmentsForGroupPrint(siteResourceID=local.returnStruct.instanceSettings.siteresourceID, siteID=arguments.event.getValue('mc_siteInfo.siteID'), groupPrintID=local.stThisMember.qryMember.groupPrintID)>
					</cfif>

					<!--- if comparing, is member in comparison? --->
					<cfif local.returnStruct.memberDirectoryInfo.enableCompare>
						<cfset local.stThisMember.inCompare = listFindNoCase(arrayToList(session.mcStruct.memberDirectorySearchCompare[arguments.event.getValue('seed')],chr(7)),local.qryMembers.memberid,chr(7))>
					</cfif>

					<!--- determine the fieldset to show for this member --->
					<cfif local.returnStruct.instanceSettings.MemberDirectoryFieldsets.results.recordcount eq 1>
						<cfset local.qryFieldsetID = local.returnStruct.instanceSettings.MemberDirectoryFieldsets.results>
					<cfelse>
						<cfset local.qryFieldsetID = getLocatorFieldsetID(siteResourceID=local.returnStruct.instanceSettings.siteresourceID, area='results', permissionMemberID=local.qryMembers.memberid)>
					</cfif>
					
					<cfset local.tmpArrEl = getSearchResults_memberFieldSetValues(orgCode=arguments.event.getValue('mc_siteinfo.orgcode'),
												siteCode=arguments.event.getValue('mc_siteinfo.sitecode'),
												siteID=arguments.event.getValue('mc_siteinfo.siteid'),
												orgID=arguments.event.getValue('mc_siteinfo.orgID'),
												fieldsetID=val(local.qryFieldsetID.fieldSetID), 
												memberID=local.qryMembers.memberid,
												strMember=local.stThisMember,
												qryViewMemberData=local.qryViewMemberData,
												memberDirectoryInfo=local.returnStruct.memberDirectoryInfo,
												qrySocialNetwork=local.returnStruct.qrySocialNetwork,
												dirAction=arguments.event.getValue('dirAction'))>
	
					<cfset local.tmpArrEl.qryViewMemberData = local.qryViewMemberData>
	
					<cfset StructAppend(local.tmpArrEl,local.stThisMember)>
					<cfset arrayAppend(local.returnStruct.arrMembers,local.tmpArrEl)>
	
					<cfif not local.returnStruct.multipleAddressTypesDetected and arrayLen(local.tmpArrEl.mc_combinedAddresses) gt 1>
						<cfset local.returnStruct.multipleAddressTypesDetected = true />
					</cfif>
				</cfloop>
				<cfset local.returnStruct.pageName = arguments.event.getValue('mc_pageDefinition.pageName')>

				<cfset local.viewToUse = "members/#local.viewDirectory#/dsp_searchResults">
			</cfcase>
			<cfcase value="memberDetails">
				<cfset local.returnStruct.qryMembersCount = 1>
				<cfset local.returnStruct.arrMembers = arrayNew(1)>
				<cfset local.returnStruct.memberPermissions = structNew()>
				<cfset local.returnStruct.multipleAddressTypesDetected = false>

				<cfset local.activeMemberID = application.objMember.getActiveMemberID(val(arguments.event.getValue('dirMemberID',0)))>
				<!--- check for valid member id --->
				<cfif local.activeMemberID is 0>
					<cflocation url="/?#local.returnStruct.baseQueryString#" addtoken="no">
				<cfelse>
					<cfset local.returnStruct.activeMemberID = local.activeMemberID>
					<cfset local.returnStruct.groupPrintID = 0>
	 				<cfset local.resultFieldsArray = arrayNew(1)>
					<cfloop query="variables.instanceSettings.MemberDirectoryFieldsets.details">
						<cfset arrayappend(local.resultFieldsArray,this.objMFS.getMemberFieldsXML(fieldsetid=variables.instanceSettings.MemberDirectoryFieldsets.details.fieldsetID, usage="memberDirectoryDetails"))>
					</cfloop>

					<cfset local.memberGroupPrintID = application.objMember.getMemberGroupPrintID(memberid=local.activeMemberID)>
					<cfset local.hasPermission = application.objSiteResource.checkResourceRightsForGroupPrint(resourceID=variables.instanceSettings.siteResourceID,functionName="AppearInDirectory",groupPrintID=local.memberGroupPrintID,siteID=variables.instanceSettings.siteID)>

					<cfif local.hasPermission>
						<cfset local.strLogStats = {}>
						<cfif local.returnStruct.memberDirectoryInfo.enableStats is 1 and NOT application.objUser.isSiteAdmin(cfcuser=session.cfcuser) AND NOT application.objUser.isSuperUser(cfcuser=session.cfcuser) AND isDefined("session.mcStruct.deviceProfile.type") and NOT findnocase("Bot",session.mcStruct.deviceProfile.type) AND session.cfcuser.memberdata.memberID NEQ local.activeMemberID>
							<cfset local.strLogStats = { dirAction=arguments.event.getValue('dirAction'), statsSessionID=session.cfcuser.statsSessionID }>
						</cfif>
						<cfset local.stThisMember = getMember(memberDirectoryID=local.returnStruct.memberDirectoryInfo.memberDirectoryID, siteResourceID=local.returnStruct.instanceSettings.siteresourceID, memberID=local.activeMemberID, orgID=arguments.event.getValue('mc_siteInfo.orgID'), logStats=local.strLogStats, fieldsetArrayToInclude=local.resultFieldsArray)>
					</cfif>

					<!--- check for valid memberid again. the check above wont catch members not belonging to this org --->
					<cfif not local.hasPermission or local.stThisMember.qryMember.recordcount is 0>
						<cflocation url="/?#local.returnStruct.baseQueryString#" addtoken="no">
					<cfelse>
						<cfif not structKeyExists(local.returnStruct.memberPermissions,local.stThisMember.qryMember.groupPrintID)>
							<cfset local.returnStruct.memberPermissions[local.stThisMember.qryMember.groupPrintID] = application.objSiteResource.buildCachePermRightAssignmentsForGroupPrint(siteResourceID=local.returnStruct.instanceSettings.siteresourceID, siteID=arguments.event.getValue('mc_siteInfo.siteID'), groupPrintID=local.stThisMember.qryMember.groupPrintID)>
						</cfif>
						<cfset local.returnStruct.groupPrintID = local.stThisMember.qryMember.groupPrintID>
	
						<!--- contact form --->
						<cfset local.stThisMember.qryViewMemberData = local.stThisMember.qryMember>
						<cfif local.returnStruct.memberDirectoryInfo.includeContactFormOnDetails is 1 and session.cfcuser.memberdata.memberID NEQ local.activeMemberID>
							<cfset local.stThisMember.mainEmail = application.objMember.getMainEmail(memberID=local.activeMemberID).email>
							<cfset local.returnStruct.contactMsg = arguments.event.getValue('msg','')>
							<cfif len(local.stThisMember.mainEmail)>
								<cfset local.returnStruct.offerContactForm = true>
							<cfelse>
								<cfset local.returnStruct.offerContactForm = false>
							</cfif>
						<cfelse>
							<cfset local.returnStruct.contactMsg = ''>
							<cfset local.returnStruct.offerContactForm = false>
						</cfif>

						<!--- should we show Stats? --->
						<cfif local.returnStruct.memberDirectoryInfo.enableStats is 1 and session.cfcuser.memberdata.memberID EQ local.activeMemberID>
							<cfset local.returnStruct.showStats = true>
							<cfset local.returnStruct.arrListingStats = getListingStats(memberDirectoryID=local.returnStruct.memberDirectoryInfo.memberDirectoryID, memberID=local.activeMemberID)>
						<cfelse>
							<cfset local.returnStruct.showStats = false>
						</cfif>

						<!--- determine the fieldset to show for this member --->
						<cfif local.returnStruct.instanceSettings.MemberDirectoryFieldsets.details.recordcount eq 1>
							<cfset local.qryFieldsetID = local.returnStruct.instanceSettings.MemberDirectoryFieldsets.details>
						<cfelse>
							<cfset local.qryFieldsetID = getLocatorFieldsetID(siteResourceID=local.returnStruct.instanceSettings.siteresourceID, area='details', permissionMemberID=local.activeMemberID)>
						</cfif>
						
						<cfset local.tmpArrEl = getMemberFieldSetValues(arguments.event.getValue('mc_siteinfo.orgcode'),
																		arguments.event.getValue('mc_siteinfo.sitecode'),
																		arguments.event.getValue('mc_siteinfo.siteid'),
																		val(local.qryFieldsetID.fieldSetID), 
																		local.activeMemberID,
																		local.stThisMember,
																		local.stThisMember.qryViewMemberData,
																		local.returnStruct.memberDirectoryInfo,
																		local.returnStruct.qrySocialNetwork,
																		arguments.event.getValue('dirAction'),
																		arguments.event.getValue('mc_siteinfo.orgID'))>
						<cfset local.tmpArrEl.qryViewMemberData = local.stThisMember.qryViewMemberData>
		
						<cfset StructAppend(local.tmpArrEl,local.stThisMember)>
						<cfset arrayAppend(local.returnStruct.arrMembers,local.tmpArrEl)>
						<cfif not local.returnStruct.multipleAddressTypesDetected and arrayLen(local.tmpArrEl.mc_combinedAddresses) gt 1>
							<cfset local.returnStruct.multipleAddressTypesDetected = true />
						</cfif>
						<!--- check for bots --->
						<cfif isDefined("session.mcstruct.deviceProfile.is_bot") and session.mcstruct.deviceProfile.is_bot is 1>
							<cfset local.returnStruct.offerContactForm = false>
							<cfset local.returnStruct.showStats = false>
						</cfif>
						<cfset local.returnStruct.pageName = arguments.event.getValue('mc_pageDefinition.pageName')>
						
						<cfset local.viewToUse = "members/#local.viewDirectory#/dsp_memberDetails">
					</cfif>
				</cfif>
			</cfcase>
			<cfcase value="memberDetailsVCard">
				<cfif (isDefined("session.mcstruct.deviceProfile.is_bot") and session.mcstruct.deviceProfile.is_bot is 1) 
					OR (structKeyExists(session,"MCMemDirLastVCard") AND dateDiff("s",session.MCMemDirLastVCard,now()) LT 3)>
					<cfabort>
				<cfelse>
					<cfset session.MCMemDirLastVCard = now()>
				</cfif>

				<cfset local.returnStruct.qryMembersCount = 1>
				<cfset local.returnStruct.arrMembers = arrayNew(1)>

				<cfset local.qryMembers = QueryNew("memberid","integer")>
				<cfif QueryAddRow(local.qryMembers)>
					<cfset local.activeMemberID = application.objMember.getActiveMemberID(val(arguments.event.getValue('dirMemberID',0)))>
					<cfset QuerySetCell(local.qryMembers,"memberid",local.activeMemberID)>
				</cfif>
				<!--- check for valid member id --->
				<cfif local.qryMembers.memberid is 0>
					<cfabort>
				</cfif>
				
				<cfset local.tmpArrEl = { memberid=local.qryMembers.memberid }>
 				<cfset local.resultFieldsArray = arrayNew(1)>
				<cfloop query="variables.instanceSettings.MemberDirectoryFieldsets.details">
					<cfset arrayappend(local.resultFieldsArray,this.objMFS.getMemberFieldsXML(fieldsetid=variables.instanceSettings.MemberDirectoryFieldsets.details.fieldsetID, usage="memberDirectoryDetails"))>
				</cfloop>

				<cfset local.memberGroupPrintID = application.objMember.getMemberGroupPrintID(memberid=local.qryMembers.memberid)>
				<cfset local.hasPermission = application.objSiteResource.checkResourceRightsForGroupPrint(resourceID=variables.instanceSettings.siteResourceID,functionName="AppearInDirectory",groupPrintID=local.memberGroupPrintID,siteID=variables.instanceSettings.siteID)>

				<cfif local.hasPermission>
					<cfset local.strLogStats = {}>
					<cfif local.returnStruct.memberDirectoryInfo.enableStats is 1 and NOT application.objUser.isSiteAdmin(cfcuser=session.cfcuser) AND NOT application.objUser.isSuperUser(cfcuser=session.cfcuser) AND isDefined("session.mcStruct.deviceProfile.type") and NOT findnocase("Bot",session.mcStruct.deviceProfile.type) AND session.cfcuser.memberdata.memberID NEQ local.qryMembers.memberid>
						<cfset local.strLogStats = { dirAction=arguments.event.getValue('dirAction'), statsSessionID=session.cfcuser.statsSessionID }>
					</cfif>
					<cfset local.stThisMember = getMember(memberDirectoryID=local.returnStruct.memberDirectoryInfo.memberDirectoryID, memberID=local.qryMembers.memberid, orgID=arguments.event.getValue('mc_siteInfo.orgID'), logStats=local.strLogStats, fieldsetArrayToInclude=local.resultFieldsArray)>
				</cfif>

				<!--- check for valid memberid again. the check above wont catch members not belonging to this org --->
				<cfif not local.hasPermission or local.stThisMember.qryMember.recordcount is 0>
					<cfabort>
				</cfif>
				
				<cfset local.stThisMember.qryViewMemberData = local.stThisMember.qryMember />

				<!--- determine the fieldset to show for this member --->
				<cfif local.returnStruct.instanceSettings.MemberDirectoryFieldsets.details.recordcount eq 1>
					<cfset local.qryFieldsetID = local.returnStruct.instanceSettings.MemberDirectoryFieldsets.details>
				<cfelse>
					<cfset local.qryFieldsetID = getLocatorFieldsetID(siteResourceID=local.returnStruct.instanceSettings.siteresourceID, area='details', permissionMemberID=local.qryMembers.memberid)>
				</cfif>
				<cfset StructInsert(local.tmpArrEl,'fieldsetid',val(local.qryFieldsetID.fieldSetID))>

				<cfset StructAppend(local.tmpArrEl,local.stThisMember)>
				<cfset arrayAppend(local.returnStruct.arrMembers,local.tmpArrEl)>
				<cfset local.viewToUse = "members/dsp_memberVCard">
			</cfcase>

			<cfcase value="memberDetailsDocDownload,memberResultsDocDownload" delimiters=",">
				<cftry>
					<cfset local.lstDoc = decrypt(toString(toBinary(URLDecode(replace(arguments.event.getTrimValue('doc',''),"xPcmKx","%","ALL")))),"M3mbeR_CenTR@l")>
					<cfif (isDefined("session.mcstruct.deviceProfile.is_bot") and session.mcstruct.deviceProfile.is_bot is 1) OR listLen(local.lstDoc,'|') is not 3>
						<cfabort>
					</cfif>
					<cfset local.strDoc = { columnID=getToken(local.lstDoc,1,'|'), memberID=getToken(local.lstDoc,3,'|') }>
		
					<cfset local.activeMemberID = application.objMember.getActiveMemberID(local.strDoc.memberID)>
					<cfif local.activeMemberID is 0>
						<cfabort>
					<cfelse>
						<cfset local.docNode = arrayNew(1)>					
						<cfset local.memberGroupPrintID = application.objMember.getMemberGroupPrintID(memberid=local.activeMemberID)>

						<cfset local.hasPermission = application.objSiteResource.checkResourceRightsForGroupPrint(resourceID=variables.instanceSettings.siteResourceID, 
							functionName="AppearInDirectory", groupPrintID=local.memberGroupPrintID, siteID=variables.instanceSettings.siteID)>
						<cfif NOT local.hasPermission>
							<cfabort>
						<cfelse>
							<cfset local.stThisMember = getMember(memberDirectoryID=local.returnStruct.memberDirectoryInfo.memberDirectoryID, 
								siteResourceID=local.returnStruct.instanceSettings.siteresourceID, memberID=local.activeMemberID, 
								orgID=arguments.event.getValue('mc_siteInfo.orgID'))>
							<cfif local.stThisMember.qryMember.recordcount is 0>
								<cfabort>
							<cfelse>
								<cfif arguments.event.getValue('dirAction') eq "memberDetailsDocDownload">
									<cfloop query="variables.instanceSettings.MemberDirectoryFieldsets.details">
										<cfset local.xmlResultFields = this.objMFS.getMemberFieldsXML(fieldsetid=variables.instanceSettings.MemberDirectoryFieldsets.details.fieldsetID, usage="memberDirectoryDetails")>
										<cfset local.docNode = XMLSearch(local.xmlResultFields,"//mf[@mdColumnID='#local.strDoc.columnID#' and @dataTypeCode='DOCUMENTOBJ']")>
									</cfloop>
								<cfelse>
									<cfloop query="variables.instanceSettings.MemberDirectoryFieldsets.results">
										<cfset local.xmlResultFields = this.objMFS.getMemberFieldsXML(fieldsetid=variables.instanceSettings.MemberDirectoryFieldsets.results.fieldsetID, usage="memberDirectoryResults")>
										<cfset local.docNode = XMLSearch(local.xmlResultFields,"//mf[@mdColumnID='#local.strDoc.columnID#' and @dataTypeCode='DOCUMENTOBJ']")>
									</cfloop>
								</cfif>
								<cfif arrayLen(local.docNode) is 0>
									<cfabort>
								<cfelse>
									<cfset local.xmlAdditionalData_Member = CreateObject("component","model.admin.members.members").getMemberAdditionalData(memberid=local.activeMemberID)>
									<cfset local.actualValue = XMLSearch(local.xmlAdditionalData_Member,"string(//column[@columnID=#local.strDoc.columnID#]/@actualColumnValue)")>
									<cfset local.getDocument =	createObject("model.system.platform.document").getDocumentDataBySiteResourceID(local.actualValue)>
							
									<cfif len(local.getDocument.DOCUMENTID) is 0>
										<cfabort>
									<cfelse>
										<cfset local.theFile = "#application.paths.RAIDSiteDocuments.path##local.getDocument.orgCode#/#local.getDocument.siteCode#/#local.getdocument.documentVersionID#.#local.getDocument.fileExt#">
										<cfset local.s3keyMod = numberFormat(local.getdocument.documentVersionID mod 1000,"0000")>
										<cfset local.s3objectKey = lcase("sitedocuments/#local.getDocument.orgCode#/#local.getDocument.siteCode#/#local.s3keyMod#/#local.getdocument.documentVersionID#.#local.getDocument.fileExt#")>
										<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath=local.theFile, 
											displayName=local.getDocument.fileName, forceDownload=0, s3bucket="membercentralcdn", s3objectKey=local.s3objectKey, 
											s3expire=10, s3requesttype="ssl")>
									</cfif>
							</cfif>
						</cfif>						
					</cfif>
				</cfif>
				<cfcatch type="Any">
					<cfset application.objError.sendError(cfcatch=cfcatch)>
					<cfabort>
				</cfcatch>
				</cftry>
			</cfcase>

			<cfcase value="sendContact">
				<cfset local.activeMemberID = application.objMember.getActiveMemberID(val(arguments.event.getValue('dirMemberID',0)))>
				<!--- check for valid member id OR exceeded 10 contact submissions per session --->
				<cfif local.activeMemberID is 0 
					OR (isDefined("session.mcstruct.deviceProfile.is_bot") and session.mcstruct.deviceProfile.is_bot is 1) 
					OR (structKeyExists(session,"MCMemDirSendContactCounter") AND session.MCMemDirSendContactCounter GT 9)>
					<cflocation url="/?#local.returnStruct.baseQueryString#" addtoken="no">
				<cfelse>
	 				<cfset local.resultFieldsArray = arrayNew(1)>

					<cfset local.memberGroupPrintID = application.objMember.getMemberGroupPrintID(memberid=local.activeMemberID)>
					<cfset local.hasPermission = application.objSiteResource.checkResourceRightsForGroupPrint(resourceID=variables.instanceSettings.siteResourceID,functionName="AppearInDirectory",groupPrintID=local.memberGroupPrintID,siteID=variables.instanceSettings.siteID)>

					<cfif local.hasPermission>
						<cfset local.strLogStats = {}>
						<cfif local.returnStruct.memberDirectoryInfo.enableStats is 1 and NOT application.objUser.isSiteAdmin(cfcuser=session.cfcuser) AND NOT application.objUser.isSuperUser(cfcuser=session.cfcuser) AND isDefined("session.mcStruct.deviceProfile.type") and NOT findnocase("Bot",session.mcStruct.deviceProfile.type) AND session.cfcuser.memberdata.memberID NEQ local.activeMemberID>
							<cfset local.strLogStats = { dirAction=arguments.event.getValue('dirAction'), statsSessionID=session.cfcuser.statsSessionID }>
						</cfif>
						<cfset local.stThisMember = getMember(memberDirectoryID=local.returnStruct.memberDirectoryInfo.memberDirectoryID, memberID=local.activeMemberID, orgID=arguments.event.getValue('mc_siteInfo.orgID'), logStats=local.strLogStats, fieldsetArrayToInclude=local.resultFieldsArray)>
					</cfif>

					<!--- check for valid memberid again. the check above wont catch members not belonging to this org --->
					<cfif not local.hasPermission or local.stThisMember.qryMember.recordcount is 0>
						<cflocation url="/?#local.returnStruct.baseQueryString#" addtoken="no">
					<cfelse>
						<cfset local.mainEmail = application.objMember.getMainEmail(memberID=local.stThisMember.qryMember.memberID).email>
						
						<cfif len(local.mainEmail)>
							<cfset local.objCffp = CreateObject("component","model.cfformprotect.cffpVerify").init()>
							<cfif NOT local.objCffp.testSubmission(arguments.event.getCollection())>
								<cfabort>
							</cfif>

							<cfset local.contactName = arguments.event.getTrimValue('frmName','')>
							<cfset local.contactEmail = arguments.event.getTrimValue('frmEmail','')>
							<cfset local.contactPhone = arguments.event.getTrimValue('frmPhone','')>
							<cfset local.contactMsg = arguments.event.getTrimValue('frmMsg','')>
							
							<cfif len(local.contactName) and (len(local.contactEmail) OR len(local.contactPhone)) and len(local.contactMsg)>
								<cfif len(local.contactEmail) and isValid("regex",local.contactEmail,application.regex.Email)>
									<cfset local.replyto = local.contactEmail>
								<cfelse>
									<cfset local.replyto = arguments.event.getValue('mc_siteinfo.supportProviderEmail')>
								</cfif>
								
								<cfset local.emailTitle = "#arguments.event.getValue('mc_siteinfo.sitename')# Contact Request From #local.returnStruct.memberDirectoryInfo.applicationInstanceName#">

								<cfsavecontent variable="local.emailContent">
									<cfoutput>
									<table>
									<tr><td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">Name:</td><td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#local.contactName#</td></tr>
									<tr><td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">E-mail:</td><td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#local.contactEmail#</td></tr>
									<tr><td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">Phone:</td><td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#local.contactPhone#</td></tr>
									<tr valign="top"><td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">Message:</td><td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">#ParagraphFormat(local.contactMsg)#</td></tr>
									</table>
									</cfoutput>
								</cfsavecontent>

								<cfscript>
								local.arrEmailTo = [];
								local.arrEmailTo.append({ name:'#local.stThisMember.qryMember.firstname# #local.stThisMember.qryMember.lastname#', email:local.mainEmail });
								if (Len(local.returnStruct.memberDirectoryInfo.bccContactForm)) {
									local.toEmailArr = listToArray(local.returnStruct.memberDirectoryInfo.bccContactForm.replaceAll(',',';'),';');
									for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
										local.arrEmailTo.append({ name:'', email:trim(local.toEmailArr[local.i]) });
									}
								}

								local.strResult = application.objEmailWrapper.sendMailESQ(
									emailfrom={ name=arguments.event.getValue('mc_siteinfo.orgname'), email=arguments.event.getValue('mc_siteinfo.networkEmailFrom') },
									emailto=local.arrEmailTo,
									emailreplyto=local.replyto,
									emailsubject=local.emailTitle,
									emailtitle=local.emailTitle,
									emailhtmlcontent=local.emailcontent,
									emailAttachments=[],
									siteID=arguments.event.getValue('mc_siteinfo.siteid'),
									memberID=local.activeMemberID,
									messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="MEMDIRECTORY"),
									sendingSiteResourceID=variables.instanceSettings.siteResourceID
								);
								</cfscript>
								<cfparam name="session.MCMemDirSendContactCounter" default="0">
								<cfset session.MCMemDirSendContactCounter++>

								<cflocation url="/?#local.returnStruct.baseQueryString#&dirAction=memberDetails&dirMemberID=#local.activeMemberID#&msg=sent" addtoken="no">
							</cfif>
						</cfif>
							
						<cflocation url="/?#local.returnStruct.baseQueryString#&dirAction=memberDetails&dirMemberID=#local.activeMemberID#" addtoken="no">
					</cfif>
				</cfif>
			</cfcase>
			<cfcase value="toggleCompare">
				<cftry>
					<!--- set up comparison struct --->
					<cfparam name="session.mcStruct.memberDirectorySearchCompare" default="#structNew()#">
					<cfif NOT len(arguments.event.getValue('seed',''))>
						<cfthrow message="No seed.">
					</cfif>
					<cfset local.searchSeed = arguments.event.getValue('seed')>
					<cfif NOT structKeyExists(session.mcStruct.memberDirectorySearchCompare,local.searchSeed)>
						<cfset structInsert(session.mcStruct.memberDirectorySearchCompare,local.searchSeed,arrayNew(1))>
					</cfif>

					<cfif NOT arguments.event.getValue('dirMemberID',0) gt 0>
						<cfthrow message="No memberID.">
					</cfif>

					<cfset local.inComp = listFindNoCase(arrayToList(session.mcStruct.memberDirectorySearchCompare[local.searchSeed],chr(7)),arguments.event.getValue('dirMemberID'),chr(7))>
					<cfif NOT local.inComp>
						<cfset ArrayAppend(session.mcStruct.memberDirectorySearchCompare[local.searchSeed],arguments.event.getValue('dirMemberID'))>
						<cfset local.inComp = true>
					<cfelse>
						<cfset local.arrPOS = session.mcStruct.memberDirectorySearchCompare[local.searchSeed].indexOf(arguments.event.getValue('dirMemberID')) + 1> 
						<cfset arrayDeleteAt(session.mcStruct.memberDirectorySearchCompare[local.searchSeed],local.arrPOS)>
						<cfset local.inComp = false>
					</cfif>

					<cfset local.numInCompare = arrayLen(session.mcStruct.memberDirectorySearchCompare[local.SearchSeed])>

					<cfset local.returnStruct = { success=true, memincomp=iif(local.inComp,de('true'),de('false')), numincomp=local.numInCompare }>
				<cfcatch type="Any">
					<cfset application.objError.sendError(cfcatch=cfcatch)>
					<cfset local.returnStruct = { success=false, memincomp=false, numincomp=0 }>
				</cfcatch>
				</cftry>
				<cfreturn returnAppStruct(SerializeJSON(local.returnStruct),"echo")>
			</cfcase>
			<cfdefaultcase>
				<cfset local.objMemberDirectory = CreateObject('component','model.system.platform.memberDirectory')>
				<cfset local.returnStruct.searchContentStruct = variables.getStaticContent(local.returnStruct.memberDirectoryInfo.searchContentID,arguments.event.getValue('mc_siteInfo.defaultLanguageID'))>

				<cfset local.qryFieldsetID = local.returnStruct.instanceSettings.MemberDirectoryFieldsets.search>
				<cfset local.returnStruct.xmlFields = CreateObject("component","model.system.platform.memberFieldsets").getMemberFieldsXML(fieldsetid=val(local.qryFieldsetID.fieldsetID), usage="memberDirectorySearch")>
				<cfset local.returnStruct.fieldsetInfo = StructNew()>
				<cfset local.returnStruct.fieldsetInfo.fieldsetName = local.qryFieldsetID.fieldsetName>
				<cfset local.returnStruct.fieldsetInfo.nameFormat = local.qryFieldsetID.nameFormat>
				<cfset local.returnStruct.fieldsetInfo.showHelp = local.qryFieldsetID.showHelp>
				
				<cfset local.qryClassifications = local.returnStruct.objMemberConfig.getClassifications(local.returnStruct.memberDirectoryInfo.memberDirectoryID)>
				<cfset local.returnStruct.arrClassifications = arrayNew(1)>	
				<cfloop query="local.qryClassifications">
					<cfif local.qryClassifications.allowSearch is 1>

						<cfset local.stClass = structNew()>
						<cfset local.stClass.name = local.qryClassifications.name>
						<cfset local.stClass.qryClassificationslinks = local.returnStruct.objMemberConfig.getGroupSets(local.qryClassifications.classificationid)>

						<cfset arrayAppend(local.returnStruct.arrClassifications, local.stClass)>
						
					</cfif>
					
				</cfloop>

				<cfset local.returnStruct.arrFormFields = getMemberFormFields(arguments.event.getValue('mc_siteinfo.orgID'),
																				local.returnStruct.xmlFields.xmlRoot.xmlChildren, 
																				local.returnStruct.memberDirectoryInfo)>
				<cfset local.returnStruct.qrySettings = CreateObject("component","model.admin.accountLocator.accountLocatorAdmin").getAccountLocatorSettings(arguments.event.getValue('mc_siteinfo.siteID'))>
				<cfset local.viewToUse = "members/#local.viewDirectory#/frm_search">
			</cfdefaultcase>
		</cfswitch>

		<!--- record app hit --->
		<cfset application.objPlatformStats.recordAppHit(appname="members",appsection="")>
		
		<!--- return the app struct --->
		<cfreturn returnAppStruct(local.returnStruct,local.viewToUse)>
	</cffunction>

	<cffunction name="setInstanceSettings" access="public" returntype="void">
		<cfargument name="instanceSettings" type="struct" required="yes"/>
		<cfset variables.instanceSettings = arguments.instanceSettings>
	</cffunction>

	<cffunction name="getInstanceSettings" access="public" returntype="struct" output="no">
		<cfargument name="applicationInstanceID" type="numeric" required="yes">
		
		<cfset var local = structNew()>
		
		<cfquery name="local.getInstanceSettings" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select ai.siteresourceID, ai.applicationInstanceID, ai.siteID, ai.applicationInstanceName, ai.settingsXML, md2.memberDirectoryID, s.orgID
			from dbo.ams_memberDirectories as md2 
			inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = md2.applicationInstanceID
				and ai.applicationInstanceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.applicationInstanceID#">
			inner join dbo.sites s on s.siteID = ai.siteID;	

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
				
		<cfset local.instanceSettings = structNew()>
		<cfloop index="local.thisField" list="#local.getInstanceSettings.columnList#">
			<cfset local.instanceSettings[local.thisField] = local.getInstanceSettings[local.thisField]>
		</cfloop>
		
		<cfif isXML(local.instanceSettings.settingsXML)>
			<cfset local.settingsXML = xmlparse(local.instanceSettings.settingsXML)>
			<cfset local.xmlSettings = structNew()>
			<cfloop from="1" to="#arraylen(local.settingsXML.settings.xmlChildren)#" index="local.x">
				<cfset local.xmlSettings[local.settingsXML.settings.xmlChildren[local.x].XmlAttributes.name] = local.settingsXML.settings.xmlChildren[local.x].XmlAttributes.value>
			</cfloop>
			<cfset local.instanceSettings.parsedXML = local.xmlSettings>
			<cfif isdefined("local.xmlSettings.defaultAction") and local.xmlSettings.defaultAction neq "">
				<cfset local.instanceSettings.defaultAction = local.xmlSettings.defaultAction>
			<cfelse>
				<cfset local.instanceSettings.defaultAction = "search">
			</cfif>
		<cfelse>
				<cfset local.instanceSettings.defaultAction = "search">
		</cfif>

		<!--- Get fieldsets --->
		<cfset local.instanceSettings.MemberDirectoryFieldsets = structNew()>
		<cfset local.instanceSettings.MemberDirectoryFieldsets.search = getLocatorFieldsetID(siteResourceID=local.instanceSettings.siteresourceID, area='search')>
		<cfset local.instanceSettings.MemberDirectoryFieldsets.results = getLocatorFieldsetID(siteResourceID=local.instanceSettings.siteresourceID, area='results')>
		<cfset local.instanceSettings.MemberDirectoryFieldsets.details = getLocatorFieldsetID(siteResourceID=local.instanceSettings.siteresourceID, area='details')>

		<cfreturn local.instanceSettings>
	</cffunction>
	
	<cffunction name="getMembers" output="no" returntype="query" access="private">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="siteResourceID" type="numeric" required="yes">
		<cfargument name="memberDirectoryID" type="numeric" required="yes">
		<cfargument name="startRow" type="numeric" required="yes">
		<cfargument name="endRow" type="numeric" required="yes">
		<cfargument name="maxSearchResults" type="numeric" required="yes">
		<cfargument name="Event" type="any">
		<cfargument name="compareMemberIDList" type="string" required="no" default="">

		<cfset var local = structnew()>

		<!--- get search and result fieldsets--->
		<cfset local.qrysearchFieldsetID = getLocatorFieldsetID(siteResourceID=arguments.siteResourceID, area='search')>
		<cfset local.resultsFSIDList = valueList(variables.instanceSettings.MemberDirectoryFieldsets.results.fieldsetID)>
	 
		<!--- sort criteria --->
		<cfquery name="local.qrySortCriteria" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT s.sortDefaultID, d.MemberDataValue, s.userGroupId, s.AscOrDesc
			FROM dbo.ams_memberDirectorySorts AS s
			LEFT OUTER JOIN dbo.ams_memberDirectorySortDefaults AS d ON s.sortDefaultID = d.sortDefaultID 
			LEFT OUTER JOIN dbo.ams_groups AS ug ON s.userGroupId = ug.groupID and ug.status <> 'D'
			WHERE s.memberDirectoryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberDirectoryID#">  
			ORDER BY s.sortOrder;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.pivotGroups = "">
		<cfset local.pvtGroupList = "">
		<cfif local.qrySortCriteria.recordCount>
			<cfset local.OrderByClause = "">
			<cfloop query="local.qrySortCriteria">
				<cfif local.qrySortCriteria.MemberDataValue eq "Random">
					<cfset local.OrderByClause = listAppend(local.orderByClause,"rand(m.memberID+@seed)")>
				<cfelseif len(local.qrySortCriteria.MemberDataValue)>
					<cfset local.OrderByClause = listAppend(local.orderByClause,"#local.qrySortCriteria.MemberDataValue# #local.qrySortCriteria.AscOrDesc#")>
				<cfelseif val(local.qrySortCriteria.usergroupid) gt 0>
					<cfset local.pivotValue = "[grp_#local.qrySortCriteria.userGroupID#]">
					<cfset local.pivotGroups = listAppend(local.pivotGroups,local.pivotValue)>
					<cfset local.pvtGroupList = listAppend(local.pvtGroupList,local.qrySortCriteria.userGroupId)>
					<cfset local.OrderByClause = listAppend(local.orderByClause,"#local.pivotValue# #local.qrySortCriteria.AscOrDesc#")>
				</cfif>
			</cfloop>
		<cfelse>
			<cfset local.OrderByClause = "m.lastname,m.firstname,m.middlename,m.suffix">
		</cfif>
		
		<cfif arguments.maxSearchResults eq 0>
			<cfset local.maxSearchResults = 100000>
		<cfelse>
			<cfset local.maxSearchResults = arguments.maxSearchResults>
		</cfif>

		<cfset local.AppearInDirectoryRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="members", functionName="AppearInDirectory")>

		<!--- determine search conditions --->
		<cfset local.strSearchConditions = this.objMFS.getSQLSearchConditionsFromFieldSet(fieldsetID=local.qrySearchFieldsetID.fieldsetID, fieldsetUsage="memberDirectorySearch", event=arguments.event)>
		<cfset structInsert(local.strSearchConditions.sqlParams,'orgID',{ value=arguments.event.getValue('mc_siteinfo.orgID'), cfsqltype="CF_SQL_INTEGER" })>
		<cfset structInsert(local.strSearchConditions.sqlParams,'siteID',{ value=arguments.event.getValue('mc_siteinfo.siteID'), cfsqltype="CF_SQL_INTEGER" })>
		<cfset structInsert(local.strSearchConditions.sqlParams,'resultsFSIDList',{ value=local.resultsFSIDList, cfsqltype="CF_SQL_VARCHAR" })>
		<cfset structInsert(local.strSearchConditions.sqlParams,'startrow',{ value=arguments.startRow, cfsqltype="CF_SQL_INTEGER" })>
		<cfset structInsert(local.strSearchConditions.sqlParams,'endrow',{ value=arguments.endRow, cfsqltype="CF_SQL_INTEGER" })>
		<cfset structInsert(local.strSearchConditions.sqlParams,'maxAllowed',{ value=local.maxSearchResults, cfsqltype="CF_SQL_INTEGER" })>
		<cfset structInsert(local.strSearchConditions.sqlParams,'seed',{ value=arguments.event.getValue('seed'), cfsqltype="CF_SQL_INTEGER" })>
		<cfset structInsert(local.strSearchConditions.sqlParams,'memberDirectoryID',{ value=arguments.memberDirectoryID, cfsqltype="CF_SQL_INTEGER" })>
		<cfset structInsert(local.strSearchConditions.sqlParams,'AppearInDirectoryRFID',{ value=local.AppearInDirectoryRFID, cfsqltype="CF_SQL_INTEGER" })>

		<cfsavecontent variable="local.stClassificationJoin">
			<cfoutput>
			<cfset local.mgGroupsDontJoin = "">
			<cfif listLen(arguments.event.getValue('mg_gid',''))>
				<cfloop list="#arguments.event.getValue('mg_gid')#" index="local.groupid">
					<cfif isValid('integer',local.groupid)>
						<cfset local.mgGroupsDontJoin = listAppend(local.mgGroupsDontJoin,"mg#local.groupid#")>
						INNER JOIN dbo.cache_members_groups AS mg#local.groupid# ON mg#local.groupid#.orgID = @orgID AND mg#local.groupid#.memberID = m.memberID AND mg#local.groupid#.groupID = #local.groupid#
					</cfif>
				</cfloop>
			</cfif>
			</cfoutput>
		</cfsavecontent>

		<!--- classification sort join --->
		<cfset local.stClassificationSortJoin = "">
		<cfif listLen(local.pivotGroups)>
			<cfsavecontent variable="local.stClassificationSortJoin">
				<cfoutput>
					LEFT OUTER JOIN (
						SELECT memberID, 'grp_' + cast(groupID as varchar(10)) as groupID
						FROM dbo.cache_members_groups
						WHERE orgid = @orgID
						AND groupID IN (#local.pvtGroupList#) 
					) as sortgrps 
					PIVOT (count(groupID) FOR groupID IN (#local.pivotGroups#)) AS p 
					ON p.memberID = m.memberID
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfset local.qryMembers = queryExecute("
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpMAMemberIDs') IS NOT NULL 
				DROP TABLE ##tmpMAMemberIDs;
			IF OBJECT_ID('tempdb..##tmpMAResults') IS NOT NULL 
				DROP TABLE ##tmpMAResults;
			CREATE TABLE ##tmpMAMemberIDs (memberID int PRIMARY KEY, row int INDEX IX_memberID_row);
			CREATE TABLE ##tmpMAResults (MFSAutoID int IDENTITY(1,1) not null);

			declare @functionID int, @orgCode varchar(10), @siteID int, @orgID int, @startRow int, @endRow int, @totalMatches int, 
				@maxAllowed int, @seed int, @fieldsetIDList varchar(max),@outputFieldsXML xml;
			set @functionID = :AppearInDirectoryRFID;
			set @siteID = :siteID;
			set @orgID = :orgID;
			set @startrow = :startrow;
			set @endrow = :endrow;
			set @maxAllowed = :maxAllowed;
			set @seed = :seed;
			set @fieldsetIDList = :resultsFSIDList;
			select @orgcode = orgcode from organizations where orgID = @orgID;			

			#local.strSearchConditions.stFilterPrep#

			INSERT INTO ##tmpMAMemberIDs (memberID, row)
			select tmp.memberid, min(tmp.mc_row) as row
			from (
				select m.memberid, ROW_NUMBER() OVER (ORDER BY #local.OrderByClause#) as mc_row
				from (
					select m.memberid
					from dbo.ams_memberDirectories as md2
					inner join dbo.cms_applicationInstances as ai on ai.siteID = @siteID
						and ai.applicationInstanceID = md2.applicationInstanceID
						and md2.memberDirectoryID = :memberDirectoryID
					inner join dbo.cache_perms_siteResourceFunctionRightPrints as srfrp on srfrp.siteID = @siteID
						and srfrp.siteResourceID = ai.siteResourceID
						and srfrp.functionID = @functionID
					inner join dbo.cache_perms_groupPrintsRightPrints as gprp on gprp.siteID = @siteID
						and gprp.rightPrintID = srfrp.rightPrintID
					inner join dbo.cache_perms_groupPrints as gp on gp.groupPrintID = gprp.groupPrintID
					inner join dbo.ams_members as m on m.orgID = @orgID 
						and m.groupPrintID = gp.groupPrintID
					#local.stClassificationJoin#
					where m.orgID = @orgID
					and m.memberid = m.activeMemberID
					and m.isProtected = 0
					#variables.memberDirectoryInfo.inactivemembersVisible ? 'and m.status in (''A'',''I'')' : 'and m.status = ''A'''#
					#listLen(arguments.compareMemberIDList) ? 'and m.memberID in (#arguments.compareMemberIDList#)' : ''#
					#local.strSearchConditions.stFilter#
				) as tmpInner
				inner join dbo.ams_members AS m on m.orgID = @orgID 
					and m.memberID = tmpInner.memberID
				#local.stClassificationSortJoin#
				ORDER BY #local.OrderByClause#
				OFFSET 0 ROWS
				FETCH FIRST @maxAllowed ROWS ONLY
			) as tmp
			group by tmp.memberID
			order by row;

			#local.strSearchConditions.stFilterPost#

			select @totalMatches = count(*) from ##tmpMAMemberIDs;
			
			delete from ##tmpMAMemberIDs
			where row > @endRow;

			delete from ##tmpMAMemberIDs
			where row < @startRow;

			-- get members fieldset data and set back to snapshot because proc ends in read committed
			EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, @fieldsetIDList=@fieldsetIDList, 
				@existingFields='', @ovNameFormat=NULL, @ovMaskEmails=NULL, 
				@membersTableName='##tmpMAMemberIDs', @membersResultTableName='##tmpMAResults', 
				@linkedMembers=0, @mode='directory', @outputFieldsXML=@outputFieldsXML OUTPUT;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT tmpM.*, m.row, @totalMatches as totalMatches
			FROM ##tmpMAMemberIDs AS m 
			INNER JOIN ##tmpMAResults AS tmpM ON tmpM.memberID = m.memberID
			ORDER BY m.row;

			IF OBJECT_ID('tempdb..##tmpMAMemberIDs') IS NOT NULL 
				DROP TABLE ##tmpMAMemberIDs;
			IF OBJECT_ID('tempdb..##tmpMAResults') IS NOT NULL 
				DROP TABLE ##tmpMAResults;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			", 
			local.strSearchConditions.sqlParams, { datasource=application.dsn.memberCentral.dsn, cachedwithin=createTimeSpan(0,0,2,0), result="local.qryMembersResult" }
		)>
		
		<cfreturn local.qryMembers>
	</cffunction>

	<cffunction name="getMember" access="public" output="false" returntype="struct">
		<cfargument name="memberDirectoryID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="yes">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="logStats" type="struct" required="no">
		<cfargument name="fieldsetArrayToInclude" type="array" required="no">

		<cfset var local = structNew()>
		<cfif not structKeyExists(this,"objMFS")>
			<cfset this.objMFS = CreateObject("component","model.system.platform.memberFieldsets")>
		</cfif>
		<cfset local.returnStruct = structNew()>
		<cfif isdefined("arguments.fieldsetArrayToInclude") and arrayLen(arguments.fieldsetArrayToInclude)>
			<cfset local.xmlResultFields = this.objMFS.getUnionFieldsetXML(arguments.fieldsetArrayToInclude)>
			<cfset local.xmlResultFields = normalizeNameFields(inputXML=local.xmlResultFields)>
		</cfif>

		<cfquery name="local.returnStruct.qryMember" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @memberDirectoryID int, @dirMemberID int, @statType varchar(20), @statsSessionID int, @functionID int, @orgID int;

			set @memberDirectoryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberDirectoryID#">;
			set @dirMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">;
			set @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;

			select m.memberid, m.orgid, m.groupPrintID, 
				<cfif isdefined("arguments.fieldsetArrayToInclude") and arrayLen(arguments.fieldsetArrayToInclude)>
					<cfloop array="#local.xmlResultFields.fields.xmlChildren#" index="local.thisField">
						<cfif NOT listFindNoCase("ml,grps",local.thisField.xmlattributes.dbObjectAlias)>
							<cfif local.thisField.xmlattributes.dataTypeCode eq "DATE">
	 							convert(varchar(10),convert(datetime,#local.thisField.xmlattributes.dbObjectAlias#.[#local.thisField.xmlattributes.dbField#]),101) as [#local.thisField.xmlattributes.dbField#], 
							<cfelseif local.thisField.xmlattributes.dataTypeCode eq "BIT">
								case when #local.thisField.xmlattributes.dbObjectAlias#.[#local.thisField.xmlattributes.dbField#] = 1 then 'Yes' when #local.thisField.xmlattributes.dbObjectAlias#.[#local.thisField.xmlattributes.dbField#] = 0 then 'No' else null end as [#local.thisField.xmlattributes.dbField#], 
							<cfelse>
								#local.thisField.xmlattributes.dbObjectAlias#.[#local.thisField.xmlattributes.dbField#],
							</cfif>
						<cfelseif local.thisField.xmlattributes.dbField eq "ml_datelastlogin_0">
							(select max(dateLastLogin) from dbo.ams_memberNetworkProfiles where memberID = m.memberID and status = 'A') as [#local.thisField.xmlattributes.dbField#], 
						<cfelseif left(local.thisField.xmlattributes.dbField,17) eq "ml_datelastlogin_">
							(select max(dateLastLogin) from dbo.ams_memberNetworkProfiles where memberID = m.memberID and siteID = #val(GetToken(local.thisField.xmlattributes.dbField,3,'_'))# and status = 'A') as [#local.thisField.xmlattributes.dbField#], 
						</cfif>
					</cfloop>
					<cfif not arrayLen(local.xmlResultFields.xmlRoot.xmlChildren)>
						,
					</cfif>
				</cfif>
				m.prefix, m.firstname, m.middlename, m.lastname, m.suffix, m.professionalSuffix, m.company, m.memberNumber, 
				m.hasMemberPhoto, m.hasMemberPhotoThumb
			FROM dbo.ams_members AS m
			INNER JOIN dbo.organizations as o on o.orgID = m.orgID
			INNER JOIN dbo.ams_memberTypes AS mt ON mt.memberTypeID = m.memberTypeID
			<cfif isdefined("arguments.fieldsetArrayToInclude") and arrayLen(arguments.fieldsetArrayToInclude)>
				<cfset local.aliasesAlreadyCreated = "m,ml,mt,o,grps,acct">
				<cfloop array="#local.xmlResultFields.xmlRoot.xmlChildren#" index="local.thisField">
					<cfif not listFindNoCase(local.aliasesAlreadyCreated,local.thisField.xmlattributes.dbObjectAlias)>
						inner join dbo.#local.thisField.xmlattributes.dbObject# as #local.thisField.xmlattributes.dbObjectAlias# on #local.thisField.xmlattributes.dbObjectAlias#.memberid = m.memberid
						<cfset local.aliasesAlreadyCreated = listAppend(local.aliasesAlreadyCreated,local.thisField.xmlattributes.dbObjectAlias)>
					</cfif>
				</cfloop>
			</cfif>
			WHERE m.orgID = <cfqueryparam value="#arguments.orgID#" cfsqltype="CF_SQL_INTEGER">
			AND m.memberid = @dirMemberID
			AND m.isProtected = 0;

			<cfif isDefined("arguments.logStats.dirAction") and listFindNoCase("searchResults,memberDetails,memberDetailsVCard,sendContact,compareResults",arguments.logStats.dirAction)>
				set @statType = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.logStats.dirAction#">;
				set @statsSessionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.logStats.statsSessionID)#">;
				EXEC platformstatsMC.dbo.ams_logMemberDirectoryStat @memberDirectoryID=@memberDirectoryID, @dirMemberID=@dirMemberID, @statType=@statType, @statsSessionID=@statsSessionID;
			</cfif>

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfif local.returnStruct.qryMember.recordcount>
			<cfset local.activeMemberID = val(local.returnStruct.qryMember.memberid)>
			<cfset arguments.orgID = local.returnStruct.qryMember.orgID>
		<cfelse>
			<cfset local.activeMemberID = 0>
			<cfset arguments.orgID = arguments.orgID>
		</cfif>
		
		<cfquery name="local.returnStruct.classifications" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT distinct g.groupID, g.imageExt, isnull(nullif(mgsg.labelOverride,''),g.groupName) as groupName, 
				g.groupDesc, c.ClassificationID, c.name, c.showGroupImage, c.imagePlacement, c.showInSearchResults,
				c.showInSearchDetail, c.classificationOrder, c.showGroupImageInSearchDetail,
				isNull(name,mgs.groupSetName) as classificationName, c.allowSearch, c.memberDirectoryID, c.classificationOrder
			FROM dbo.ams_groups AS g
			INNER JOIN dbo.cache_members_groups AS mg ON mg.orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orgID#">
				AND g.groupID = mg.groupID
				AND mg.memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.activeMemberID#">
				AND g.status <> 'D'
			inner join dbo.ams_memberGroupSetGroups mgsg on mgsg.groupID = mg.groupID
			inner join dbo.ams_memberGroupSets mgs on mgs.groupSetID = mgsg.groupSetID
			INNER JOIN dbo.ams_memberDirectoryClassifications AS c ON c.groupSetID = mgs.groupSetID
				and c.memberDirectoryID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberDirectoryID#">
			ORDER by c.classificationOrder, groupName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn local.returnStruct>
	</cffunction>	

	<cffunction name="getLocatorFieldsetID" access="public" output="no" returntype="query" hint="May return more than one">
		<cfargument name="siteresourceID" type="numeric" required="yes">
		<cfargument name="area" type="string" required="yes">
		<cfargument name="permissionMemberID" type="numeric" required="no" default="0">
		
		<cfset var qryFieldSet = "">
		
		<!--- validate areas --->
		<cfif NOT listFindNoCase("search,results,details",arguments.area)>
			<cfset arguments.area = "search">
		</cfif>
		
		<!--- get fieldset --->
		<cfset qryFieldSet = super.getLocatorFieldsetID(arguments.siteresourceID, arguments.area, arguments.permissionMemberID)>

		<cfreturn qryFieldSet>
	</cffunction>

	<cffunction name="getMemberFieldSetValues" access="private" output="no" returntype="struct">
		<cfargument name="orgCode" type="string">
		<cfargument name="siteCode" type="string">
		<cfargument name="siteID" type="numeric">
		<cfargument name="fieldsetID" type="numeric">
		<cfargument name="memberID" type="numeric">
		<cfargument name="strMember" type="struct">
		<cfargument name="qryViewMemberData" type="query">
		<cfargument name="memberDirectoryInfo" type="query">
		<cfargument name="qrySocialNetwork" type="query">
		<cfargument name="dirAction" type="string">
		<cfargument name="orgID" type="numeric">

		<cfset var local = structNew()>
		<cfset local.retStruct = structNew()>

		<cfset local.retStruct.memberID = arguments.memberID>
		<cfset local.retStruct.arrPhones = ArrayNew(1)>
		<cfset local.retStruct.arrEmails = ArrayNew(1)>
		<cfset local.retStruct.arrWebsites = ArrayNew(1)>
		<cfset local.retStruct.arrWebsitesSocialIcons = ArrayNew(1)>
		<cfset local.retStruct.arrMemberDataDistricting = ArrayNew(1)>
		<cfset local.retStruct.arrAboveClassifications = ArrayNew(1)>
		<cfset local.retStruct.arrBelowClassifications = ArrayNew(1)>
		<cfset local.retStruct.arrUnderClassifications = ArrayNew(1)>
		<cfset local.retStruct.arrClValues = ArrayNew(1)>
		<cfset local.retStruct.arrLicenseData = ArrayNew(1)>
				
		<cfif arguments.fieldsetID gt 0>
			<cfset local.xmlResultFields = this.objMFS.getMemberFieldsXML(fieldsetid=arguments.fieldsetID, usage="memberDirectoryResults")>
			<cfset local.xmlResultFields = normalizeNameFields(inputXML=local.xmlResultFields)>
		<cfelse>
			<cfset local.xmlResultFields = XMLParse('<fields nameformat="LSXPFM" />')>
		</cfif>

		<!--- combine name fields if there are any --->
		<cfset local.arrNamesNodes = XMLSearch(local.xmlResultFields,"//mf[@fieldCode='m_prefix' or @fieldCode='m_firstname' or @fieldCode='m_middlename' or @fieldCode='m_lastname' or @fieldCode='m_suffix' or @fieldCode='m_professionalsuffix']")>
		<cfif arrayLen(local.arrNamesNodes)>
			<cfset local.lstNameFields = "">
			<cfloop array="#local.arrNamesNodes#" index="local.thisNameNode">
				<cfset local.lstNameFields = listAppend(local.lstNameFields,local.thisNameNode.xmlattributes.fieldCode)>
			</cfloop>
			<cfset local.stFullName = "">
			<cfif local.xmlResultFields.xmlRoot.xmlattributes.nameformat eq "LSXPFM">
				<cfif listFindNoCase(local.lstNameFields,'m_lastname')><cfset local.stFullName &= "#arguments.strMember.qryMember.lastname#, "></cfif>
				<cfif listFindNoCase(local.lstNameFields,'m_suffix') and len(arguments.strMember.qryMember.suffix)><cfset local.stFullName &= "#arguments.strMember.qryMember.suffix#, "></cfif>
				<cfif listFindNoCase(local.lstNameFields,'m_professionalsuffix') and len(arguments.strMember.qryMember.professionalsuffix)><cfset local.stFullName &= "#arguments.strMember.qryMember.professionalsuffix#, "></cfif>
				<cfif listFindNoCase(local.lstNameFields,'m_prefix') and len(arguments.strMember.qryMember.prefix)><cfset local.stFullName &= "#arguments.strMember.qryMember.prefix#"></cfif>
				<cfif listFindNoCase(local.lstNameFields,'m_firstname')><cfset local.stFullName &= " #arguments.strMember.qryMember.firstname#"></cfif>
				<cfif listFindNoCase(local.lstNameFields,'m_middlename') and len(arguments.strMember.qryMember.middlename)><cfset local.stFullName &= " #arguments.strMember.qryMember.middlename#"></cfif>
			<cfelse>
				<cfif listFindNoCase(local.lstNameFields,'m_prefix') and len(arguments.strMember.qryMember.prefix)><cfset local.stFullName &= "#arguments.strMember.qryMember.prefix#"></cfif>
				<cfif listFindNoCase(local.lstNameFields,'m_firstname')><cfset local.stFullName &= " #arguments.strMember.qryMember.firstname# "></cfif>
				<cfif listFindNoCase(local.lstNameFields,'m_middlename') and len(arguments.strMember.qryMember.middlename)><cfset local.stFullName &= " #arguments.strMember.qryMember.middlename#"></cfif>
				<cfif listFindNoCase(local.lstNameFields,'m_lastname')><cfset local.stFullName &= " #arguments.strMember.qryMember.lastname#"></cfif><cfif listFindNoCase(local.lstNameFields,'m_suffix') and len(arguments.strMember.qryMember.suffix)><cfset local.stFullName &= ", #arguments.strMember.qryMember.suffix#"></cfif>
				<cfif listFindNoCase(local.lstNameFields,'m_professionalsuffix') and len(arguments.strMember.qryMember.professionalsuffix)><cfset local.stFullName &= ", #arguments.strMember.qryMember.professionalsuffix#"></cfif>
			</cfif>
			<cfset local.retStruct.stFullName = replace(replace(local.stFullName,'  ',' ','ALL'),' ,',',','ALL')>
		<cfelse>
			<cfset local.retStruct.stFullName = "">
		</cfif>

		<cfset local.orgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID=arguments.orgid, includeTags=1)>
		<cfset local.strOrgAddressTypes = structNew()>
		<cfloop query="local.orgAddressTypes">
			<cfif local.orgAddressTypes.isTag is 1>
				<cfset local.atKey = "t#local.orgAddressTypes.addressTypeID#">
			<cfelse>
				<cfset local.atKey = local.orgAddressTypes.addressTypeID>
			</cfif>
			<cfset local.strOrgAddressTypes[local.atKey] = {addressType = local.orgAddressTypes.addressType, isTag=local.orgAddressTypes.isTag}>
		</cfloop>
		
		<!--- combine address fields if there are any --->
		<cfset local.retStruct.mc_combinedAddresses = arrayNew(1)>
		<cfset local.retStruct.mc_combinedAddressArrayMap = structNew()>
		<cfset local.tmp = XMLSearch(local.xmlResultFields,"//mf[substring(@fieldCode,1,3)='ma_' or substring(@fieldCode,1,4)='mat_']")>
		<cfloop array="#local.tmp#" index="local.thisField">
			<cfset local.thisATID = listGetAt(local.thisField.xmlattributes.fieldcode,2,"_")>
			<cfif listFindNoCase("mat,mpt",listGetAt(local.thisField.xmlattributes.fieldcode,1,"_"))>
				<cfset local.strKey = "t#local.thisATID#">
			<cfelse>
				<cfset local.strKey = local.thisATID>
			</cfif>
			<cfif NOT structKeyExists(local.retStruct.mc_combinedAddressArrayMap, local.strKey)>
				<cfif local.strOrgAddressTypes[local.strKey].isTag>
					<cfset local.typeLabel = replaceNoCase(local.strOrgAddressTypes[local.strKey].addressType, "Designated ", "")>
				<cfelse>
					<cfset local.typeLabel = local.strOrgAddressTypes[local.strKey].addressType>
				</cfif>
				<cfset local.newAddrTemplate = {key=local.strKey, addr='', type=local.strOrgAddressTypes[local.strKey].addressType, label=local.typeLabel }>
				<cfset arrayAppend(local.retStruct.mc_combinedAddresses, local.newAddrTemplate)>
				<cfset local.retStruct.mc_combinedAddressArrayMap[local.strKey] = arrayLen(local.retStruct.mc_combinedAddresses)>
			</cfif>
		</cfloop>

		<cfset local.elementsToDelete = "">

		<cfloop index="local.i" from="1" to="#arrayLen(local.retStruct.mc_combinedAddresses)#">

			<cfset local.thisATID = local.retStruct.mc_combinedAddresses[local.i].key>
			<cfif left(local.thisATID,1) eq "t">
				<cfset local.MAfcPrefix = "mat_#right(local.thisATID,len(local.thisATID)-1)#_">
				<cfset local.MPfcPrefix = "mpt_#right(local.thisATID,len(local.thisATID)-1)#_">
			<cfelse>
				<cfset local.MAfcPrefix = "ma_#local.thisATID#_">
				<cfset local.MPfcPrefix = "mp_#local.thisATID#_">
			</cfif>

			<cfset local.thisAddressStruct = {
				address1="",
				address2="",
				address3="",
				city="",
				stateprov="",
				postalcode="",
				county="",
				country=""
			}>
			<cfset local.tmp = XMLSearch(local.xmlResultFields,"//mf[@fieldCode='#local.MAfcPrefix#address1']")>
			<cfif arrayLen(local.tmp) is 1 and len(arguments.qryViewMemberData[local.tmp[1].xmlAttributes.dbField][1])>
				<cfset local.thisAddressStruct["address1"] = arguments.qryViewMemberData[local.tmp[1].xmlAttributes.dbField][1] />
			</cfif>
			<cfset local.tmp = XMLSearch(local.xmlResultFields,"//mf[@fieldCode='#local.MAfcPrefix#address2']")>
			<cfif arrayLen(local.tmp) is 1 and len(arguments.qryViewMemberData[local.tmp[1].xmlAttributes.dbField][1])>
				<cfset local.thisAddressStruct["address2"] = arguments.qryViewMemberData[local.tmp[1].xmlAttributes.dbField][1]>
			</cfif>
			<cfset local.tmp = XMLSearch(local.xmlResultFields,"//mf[@fieldCode='#local.MAfcPrefix#address3']")>
			<cfif arrayLen(local.tmp) is 1 and len(arguments.qryViewMemberData[local.tmp[1].xmlAttributes.dbField][1])>
				<cfset local.thisAddressStruct["address3"] = arguments.qryViewMemberData[local.tmp[1].xmlAttributes.dbField][1]>
			</cfif>
			<cfset local.tmp = XMLSearch(local.xmlResultFields,"//mf[@fieldCode='#local.MAfcPrefix#city']")>
			<cfif arrayLen(local.tmp) is 1 and len(arguments.qryViewMemberData[local.tmp[1].xmlAttributes.dbField][1])>
				<cfset local.thisAddressStruct["city"] = arguments.qryViewMemberData[local.tmp[1].xmlAttributes.dbField][1]>
			</cfif>
			<cfset local.tmp2 = XMLSearch(local.xmlResultFields,"//mf[@fieldCode='#local.MAfcPrefix#stateprov']")>
			<cfif arrayLen(local.tmp2) is 1 and len(arguments.qryViewMemberData[local.tmp2[1].xmlAttributes.dbField][1])>
				<cfset local.thisAddressStruct["stateprov"] = arguments.qryViewMemberData[local.tmp2[1].xmlAttributes.dbField][1]>
			</cfif>
			<cfset local.tmp = XMLSearch(local.xmlResultFields,"//mf[@fieldCode='#local.MAfcPrefix#postalcode']")>
			<cfif arrayLen(local.tmp) is 1 and len(arguments.qryViewMemberData[local.tmp[1].xmlAttributes.dbField][1])>
				<cfset local.thisAddressStruct["postalcode"] = arguments.qryViewMemberData[local.tmp[1].xmlAttributes.dbField][1]>
			</cfif>
			<cfset local.tmp = XMLSearch(local.xmlResultFields,"//mf[@fieldCode='#local.MAfcPrefix#county']")>
			<cfif arrayLen(local.tmp) is 1 and len(arguments.qryViewMemberData[local.tmp[1].xmlAttributes.dbField][1])>
				<cfset local.thisAddressStruct["county"] = arguments.qryViewMemberData[local.tmp[1].xmlAttributes.dbField][1] & " County"><br/>
			</cfif>
			<cfset local.tmp = XMLSearch(local.xmlResultFields,"//mf[@fieldCode='#local.MAfcPrefix#country']")>
			<cfif arrayLen(local.tmp) is 1 and len(arguments.qryViewMemberData[local.tmp[1].xmlAttributes.dbField][1])>
				<cfset local.thisAddressStruct["country"] = arguments.qryViewMemberData[local.tmp[1].xmlAttributes.dbField][1]>
			</cfif>
			<cfset local.tmp = XMLSearch(local.xmlResultFields,"//mf[substring(@fieldCode,1,#len(local.MPfcPrefix)#)='#local.MPfcPrefix#']")>

			<cfsavecontent variable="local.thisATFull">
				<cfoutput>
					<div itemprop="address" itemscope itemtype="http://schema.org/PostalAddress">
						<cfif len(local.thisAddressStruct["address1"]) or len(local.thisAddressStruct["address2"]) or len(local.thisAddressStruct["address3"])>
							<span itemprop="streetAddress">
								<cfif len(local.thisAddressStruct["address1"])>#local.thisAddressStruct["address1"]#<br /></cfif>
								<cfif len(local.thisAddressStruct["address2"])>#local.thisAddressStruct["address2"]#<br /></cfif>
								<cfif len(local.thisAddressStruct["address3"])>#local.thisAddressStruct["address3"]#<br /></cfif>
							</span>
						</cfif>
						<cfif len(local.thisAddressStruct["city"])><span itemprop="addressLocality">#local.thisAddressStruct["city"]#</span></cfif><cfif len(local.thisAddressStruct["stateprov"])>, <span itemprop="addressRegion">#local.thisAddressStruct["stateprov"]#</span></cfif>
						<cfif len(local.thisAddressStruct["postalcode"])> <span itemprop="postalCode">#local.thisAddressStruct["postalcode"]#</span></cfif>
						<cfif len(local.thisAddressStruct["address1"]) or len(local.thisAddressStruct["address2"]) or len(local.thisAddressStruct["address3"])>
							<br />
						</cfif>
						<cfif len(local.thisAddressStruct["county"])>#local.thisAddressStruct["county"]# <br /></cfif>
						<cfif len(local.thisAddressStruct["country"])><span itemprop="addressCountry">#local.thisAddressStruct["country"]#</span><br /></cfif>
					</div>
 				</cfoutput>
			</cfsavecontent>
			<cfsavecontent variable="local.thisATMap">
				<cfoutput>
					<cfif len(local.thisAddressStruct["address1"])>#local.thisAddressStruct["address1"]#</cfif>
					<cfif len(local.thisAddressStruct["address2"])>#local.thisAddressStruct["address2"]#</cfif>
					<cfif len(local.thisAddressStruct["address3"])>#local.thisAddressStruct["address3"]#</cfif>
					<cfif len(local.thisAddressStruct["city"])>#local.thisAddressStruct["city"]#</cfif><cfif len(local.thisAddressStruct["stateprov"])>, #local.thisAddressStruct["stateprov"]#</cfif><cfif len(local.thisAddressStruct["postalcode"])> #local.thisAddressStruct["postalcode"]#</cfif>
					<cfif len(local.thisAddressStruct["county"])>#local.thisAddressStruct["county"]# <br /></cfif>
					<cfif len(local.thisAddressStruct["country"])>#local.thisAddressStruct["country"]#</cfif>
 				</cfoutput>
			</cfsavecontent>

			<cfset local.thisATfull = trim(replace(replace(local.thisATFull,'  ',' ','ALL'),' ,',',','ALL'))>
			<cfif left(local.thisATfull,2) eq ", ">
				<cfset local.thisATfull = right(local.thisATfull,len(local.thisATfull)-2)>
			</cfif>
			
			<cfif len(local.thisATMap)>
				<cfset local.retStruct.mc_combinedAddresses[local.i]['addr'] = local.thisATfull>
				<cfset local.retStruct.mc_combinedAddresses[local.i]['mapaddr'] = local.thisATMap>
			<cfelse>
				<cfset local.elementsToDelete = listPrepend(local.elementsToDelete, local.i)>
			</cfif>
		</cfloop>
		<cfloop index="local.i" list="#local.elementsToDelete#"> 
			<cfset arrayDeleteAt(local.retStruct.mc_combinedAddresses, local.i)>
		</cfloop> 

		<cfif arrayLen(XMLSearch(local.xmlResultFields,"//mf[@fieldCode='m_membernumber']"))>
			<cfset local.retStruct.stMemberNumber = arguments.strMember.qryMember.membernumber>
		<cfelse>
			<cfset local.retStruct.stMemberNumber = ''>
		</cfif>

		<cfif arrayLen(XMLSearch(local.xmlResultFields,"//mf[@fieldCode='m_company']")) and len(arguments.strMember.qryMember.company)>
			<cfset local.retStruct.stCompany = arguments.strMember.qryMember.company>
		<cfelse>
			<cfset local.retStruct.stCompany = ''>
		</cfif>

		<cfset local.retStruct.imgToUse = ''>
		<cfif arguments.strMember.qryMember.hasMemberPhotoThumb is 1>
			<cfset local.retStruct.imgToUse = '<img class="tsAppDirPhoto" itemprop="image" src="/memberphotosth/#LCASE(arguments.strMember.qryMember.membernumber)#.jpg">'>
		</cfif>

		<cfloop array="#local.xmlResultFields.xmlRoot.xmlChildren#" index="local.thisField">
			<cfif ReFindNoCase('mp_[0-9]+_[0-9]+',local.thisField.xmlattributes.fieldcode) and len(arguments.qryViewMemberData[local.thisField.xmlattributes.dbField][1])>
				<!--- add phone --->
				<cfset local.memdataentry = StructNew()>
				<cfset local.memdataentry.fieldLabel = local.thisField.xmlattributes.fieldLabel>
				<cfset local.memdataentry.phone = arguments.qryViewMemberData[local.thisField.xmlattributes.dbField][1]>
				<cfset arrayAppend(local.retStruct.arrPhones, local.memdataentry)>
			<cfelseif ReFindNoCase('mpt_[0-9]+_[0-9]+',local.thisField.xmlattributes.fieldcode) and len(arguments.qryViewMemberData[local.thisField.xmlattributes.dbField][1])>
				<!--- add phone --->
				<cfset local.memdataentry = StructNew()>
				<cfset local.memdataentry.fieldLabel = local.thisField.xmlattributes.fieldLabel>
				<cfset local.memdataentry.phone = arguments.qryViewMemberData[local.thisField.xmlattributes.dbField][1]>
				<cfset arrayAppend(local.retStruct.arrPhones, local.memdataentry)>

			<cfelseif (ReFindNoCase('me_[0-9]+_email',local.thisField.xmlattributes.fieldcode) or ReFindNoCase('met_[0-9]+_email',local.thisField.xmlattributes.fieldcode)) and len(arguments.qryViewMemberData[local.thisField.xmlattributes.dbField][1])>
				<!--- add email --->
				<cfset local.memdataentry = StructNew()>
				<cfset local.memdataentry.fieldLabel = local.thisField.xmlattributes.fieldLabel>
				<cfset local.memdataentry.email = arguments.qryViewMemberData[local.thisField.xmlattributes.dbField][1]>
				<cfset arrayAppend(local.retStruct.arrEmails, local.memdataentry)>

			<cfelseif ReFindNoCase('mw_[0-9]+_website',local.thisField.xmlattributes.fieldcode) and len(arguments.qryViewMemberData[local.thisField.xmlattributes.dbField][1])>
				<cfif listFindNoCase(variables.supportedSocialIcons, local.thisField.xmlattributes.fieldLabel)>
					<cfset local.memdataentry = StructNew()>
					<cfset local.memdataentry.fieldLabel = local.thisField.xmlattributes.fieldLabel>
					<cfset local.memdataentry.website = arguments.qryViewMemberData[local.thisField.xmlattributes.dbField][1]>
					<cfset local.memdataentry.socialLink = getSocialIconLink(local.memdataentry)>
					<cfset arrayAppend(local.retStruct.arrWebsitesSocialIcons, local.memdataentry)>
				<cfelse>
					<!--- add website --->
					<cfset local.memdataentry = StructNew()>
					<cfset local.memdataentry.socialLink = "">
					<cfset local.memdataentry.fieldLabel = local.thisField.xmlattributes.fieldLabel>
					<cfset local.memdataentry.website = arguments.qryViewMemberData[local.thisField.xmlattributes.dbField][1]>
					<cfset arrayAppend(local.retStruct.arrWebsites, local.memdataentry)>					
				</cfif>

			<cfelseif (ReFindNoCase('md_[0-9]+',local.thisField.xmlattributes.fieldcode) or ReFindNoCase('mad_[0-9]+_[0-9]+',local.thisField.xmlattributes.fieldcode)) and len(arguments.qryViewMemberData[local.thisField.xmlattributes.dbField][1])>
				<!--- add member data / districting --->
				<cfset local.memdataentry = StructNew()>
				<cfset local.memdataentry.fieldLabel = local.thisField.xmlattributes.fieldLabel>
				<cfset local.memdataentry.value = arguments.qryViewMemberData[local.thisField.xmlattributes.dbField][1]>
				<cfset local.memdataentry.allowMultiple = local.thisField.xmlAttributes.allowMultiple>
				<cfset local.memdataentry.dataTypeCode = local.thisField.xmlAttributes.dataTypeCode>
				<cfset local.memdataentry.columnID = local.thisField.xmlAttributes.mdColumnID>
				<cfset arrayAppend(local.retStruct.arrMemberDataDistricting, local.memdataentry)>
			<cfelseif left(local.thisField.xmlattributes.dbField,17) eq "ml_datelastlogin_" and len(arguments.qryViewMemberData[local.thisField.xmlattributes.dbField][1])>
				<!--- add last login --->
				<cfset local.memdataentry = StructNew()>
				<cfset local.memdataentry.fieldLabel = local.thisField.xmlattributes.fieldLabel>
				<cfset local.memdataentry.value = dateformat(arguments.qryViewMemberData[local.thisField.xmlattributes.dbField][1],"m/d/yyyy")>
				<cfset local.memdataentry.allowMultiple = local.thisField.xmlAttributes.allowMultiple>
				<cfset arrayAppend(local.retStruct.arrMemberDataDistricting, local.memdataentry)>

			<cfelseif ReFindNoCase('mpl_[0-9]+_[a-z]+',local.thisField.xmlattributes.fieldcode) and len(arguments.qryViewMemberData[local.thisField.xmlattributes.dbField][1])>
				<cfset local.memdataentry = StructNew()>
				<cfset local.memdataentry.fieldLabel = local.thisField.xmlattributes.fieldLabel>
				<cfset local.memdataentry.value = arguments.qryViewMemberData[local.thisField.xmlattributes.dbField][1]>
				<cfif listFindNoCase(local.thisField.xmlattributes.fieldcode,"activeDate","_")>
					<cfset local.memdataentry.value = dateFormat(local.memdataentry.value, "mm/dd/yyyy")>
				</cfif>
				<cfset arrayAppend(local.retStruct.arrLicenseData, local.memdataentry)>

			</cfif>
		</cfloop>

		<cfif structKeyExists(arguments.strMember, "classifications")>		
			<cfloop query="arguments.strMember.classifications">	
				<cfset local.clStruct = structNew()>
				<cfset local.clStruct.groupID = arguments.strMember.classifications.groupID>
				<cfset local.clStruct.imageext = arguments.strMember.classifications.imageext>
				<cfset local.clStruct.groupName = arguments.strMember.classifications.groupName>
				<cfset local.clStruct.clName = replace(arguments.strMember.classifications.name, '_', ' ', 'ALL')>

				<cfif ((arguments.dirAction eq "searchResults" and arguments.strMember.classifications.showGroupImage is 1) 
						or (arguments.dirAction eq "memberDetails" and arguments.strMember.classifications.showGroupImageInSearchDetail is 1)) 
						AND FileExists('#application.paths.localUserAssetRoot.path#common/groupImages/#arguments.strMember.classifications.groupID#.#arguments.strMember.classifications.imageext#')> 
					<cfif arguments.strMember.classifications.imagePlacement eq "above">
						<cfset arrayAppend(local.retStruct.arrAboveClassifications, local.clStruct)>
					</cfif>	
					<cfif arguments.strMember.classifications.imagePlacement eq "below">
						<cfset arrayAppend(local.retStruct.arrBelowClassifications, local.clStruct)>
					</cfif>	
					<cfif arguments.strMember.classifications.imagePlacement eq "under">
						<cfset arrayAppend(local.retStruct.arrUnderClassifications, local.clStruct)>
					</cfif>	
				</cfif>
				<cfif (arguments.dirAction eq "searchResults" and arguments.strMember.classifications.showInSearchResults is 1) or (arguments.dirAction eq "memberDetails" and arguments.strMember.classifications.showInSearchDetail is 1)>
					<cfset arrayAppend(local.retStruct.arrClValues, local.clStruct)>
				</cfif>						

			</cfloop>
		</cfif>

		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="getSearchResults_memberFieldSetValues" access="private" output="no" returntype="struct">
		<cfargument name="orgCode" type="string" required="true">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="fieldsetID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="strMember" type="struct" required="true">
		<cfargument name="qryViewMemberData" type="query" required="true">
		<cfargument name="memberDirectoryInfo" type="query" required="true">
		<cfargument name="qrySocialNetwork" type="query" required="true">
		<cfargument name="dirAction" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.retStruct = structNew()>

		<cfset local.retStruct.memberID = arguments.memberID>
		<cfset local.retStruct.arrPhones = ArrayNew(1)>
		<cfset local.retStruct.arrEmails = ArrayNew(1)>
		<cfset local.retStruct.arrWebsites = ArrayNew(1)>
		<cfset local.retStruct.arrWebsitesSocialIcons = ArrayNew(1)>
		<cfset local.retStruct.arrMemberDataDistricting = ArrayNew(1)>
		<cfset local.retStruct.arrAboveClassifications = ArrayNew(1)>
		<cfset local.retStruct.arrBelowClassifications = ArrayNew(1)>
		<cfset local.retStruct.arrUnderClassifications = ArrayNew(1)>
		<cfset local.retStruct.arrClValues = ArrayNew(1)>
		<cfset local.retStruct.arrLicenseData = ArrayNew(1)>

		<cfif NOT arguments.qryViewMemberData.recordCount>
			<cfreturn local.retStruct>
		</cfif>
		
		<cfif arguments.fieldsetID gt 0>
			<cfset local.xmlResultFields = this.objMFS.getMemberFieldsXML(fieldsetid=arguments.fieldsetID, usage="memberDirectoryResults")>
			<cfset local.xmlResultFields = normalizeNameFields(inputXML=local.xmlResultFields)>
		<cfelse>
			<cfset local.xmlResultFields = XMLParse('<fields nameformat="LSXPFM" />')>
		</cfif>

		<cfset local.strMemberData = QueryRowData(arguments.qryViewMemberData,1)>

		<!--- qryViewMemberData columns  --->
		<cfset local.arrQryCols = arguments.qryViewMemberData.ColumnArray()>
		<cfset local.appendFSColName = "_mfs#arguments.fieldsetID#">
		<cfif ArrayContains(local.arrQryCols,local.appendFSColName,true)>
			<cfloop array="#local.arrQryCols#" index="local.thisCol">
				<cfif local.thisCol.FindNoCase(local.appendFSColName)>
					<cfset local.strMemberData[local.thisCol.replace(local.appendFSColName,'')] = local.strMemberData[local.thisCol]>
				</cfif>
			</cfloop>
		</cfif>

		<!--- combine name fields if there are any --->
		<cfset local.arrNamesNodes = XMLSearch(local.xmlResultFields,"//mf[@fieldCode='m_prefix' or @fieldCode='m_firstname' or @fieldCode='m_middlename' or @fieldCode='m_lastname' or @fieldCode='m_suffix' or @fieldCode='m_professionalsuffix']")>
		<cfif arrayLen(local.arrNamesNodes)>
			<cfset local.lstNameFields = "">
			<cfloop array="#local.arrNamesNodes#" index="local.thisNameNode">
				<cfset local.lstNameFields = listAppend(local.lstNameFields,local.thisNameNode.xmlattributes.fieldCode)>
			</cfloop>

			<cfset local.stFullName = "">
			<cfif local.xmlResultFields.xmlRoot.xmlattributes.nameformat eq "LSXPFM">
				<cfif listFindNoCase(local.lstNameFields,'m_lastname')><cfset local.stFullName &= "#arguments.strMember.qryMember.lastname#, "></cfif>
				<cfif listFindNoCase(local.lstNameFields,'m_suffix') and len(arguments.strMember.qryMember.suffix)><cfset local.stFullName &= "#arguments.strMember.qryMember.suffix#, "></cfif>
				<cfif listFindNoCase(local.lstNameFields,'m_professionalsuffix') and len(arguments.strMember.qryMember.professionalsuffix)><cfset local.stFullName &= "#arguments.strMember.qryMember.professionalsuffix#, "></cfif>
				<cfif listFindNoCase(local.lstNameFields,'m_prefix') and len(arguments.strMember.qryMember.prefix)><cfset local.stFullName &= "#arguments.strMember.qryMember.prefix#"></cfif>
				<cfif listFindNoCase(local.lstNameFields,'m_firstname')><cfset local.stFullName &= " #arguments.strMember.qryMember.firstname#"></cfif>
				<cfif listFindNoCase(local.lstNameFields,'m_middlename') and len(arguments.strMember.qryMember.middlename)><cfset local.stFullName &= " #arguments.strMember.qryMember.middlename#"></cfif>
			<cfelse>
				<cfif listFindNoCase(local.lstNameFields,'m_prefix') and len(arguments.strMember.qryMember.prefix)><cfset local.stFullName &= "#arguments.strMember.qryMember.prefix#"></cfif>
				<cfif listFindNoCase(local.lstNameFields,'m_firstname')><cfset local.stFullName &= " #arguments.strMember.qryMember.firstname# "></cfif>
				<cfif listFindNoCase(local.lstNameFields,'m_middlename') and len(arguments.strMember.qryMember.middlename)><cfset local.stFullName &= " #arguments.strMember.qryMember.middlename#"></cfif>
				<cfif listFindNoCase(local.lstNameFields,'m_lastname')><cfset local.stFullName &= " #arguments.strMember.qryMember.lastname#"></cfif><cfif listFindNoCase(local.lstNameFields,'m_suffix') and len(arguments.strMember.qryMember.suffix)><cfset local.stFullName &= ", #arguments.strMember.qryMember.suffix#"></cfif>
				<cfif listFindNoCase(local.lstNameFields,'m_professionalsuffix') and len(arguments.strMember.qryMember.professionalsuffix)><cfset local.stFullName &= ", #arguments.strMember.qryMember.professionalsuffix#"></cfif>
			</cfif>
			<cfset local.retStruct.stFullName = replace(replace(local.stFullName,'  ',' ','ALL'),' ,',',','ALL')>
		<cfelse>
			<cfset local.retStruct.stFullName = "">
		</cfif>

		<cfset local.orgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID=arguments.orgid, includeTags=1)>
		<cfset local.strOrgAddressTypes = structNew()>
		<cfloop query="local.orgAddressTypes">
			<cfif local.orgAddressTypes.isTag is 1>
				<cfset local.atKey = "t#local.orgAddressTypes.addressTypeID#">
			<cfelse>
				<cfset local.atKey = local.orgAddressTypes.addressTypeID>
			</cfif>
			<cfset local.strOrgAddressTypes[local.atKey] = {addressType = local.orgAddressTypes.addressType, isTag=local.orgAddressTypes.isTag}>
		</cfloop>
		
		<!--- combine address fields if there are any --->
		<cfset local.retStruct.mc_combinedAddresses = arrayNew(1)>
		<cfset local.retStruct.mc_combinedAddressArrayMap = structNew()>
		<cfset local.tmp = XMLSearch(local.xmlResultFields,"//mf[substring(@fieldCode,1,3)='ma_' or substring(@fieldCode,1,4)='mat_']")>
		<cfloop array="#local.tmp#" index="local.thisField">
			<cfset local.thisATID = listGetAt(local.thisField.xmlattributes.fieldcode,2,"_")>
			<cfif listFindNoCase("mat,mpt",listGetAt(local.thisField.xmlattributes.fieldcode,1,"_"))>
				<cfset local.strKey = "t#local.thisATID#">
			<cfelse>
				<cfset local.strKey = local.thisATID>
			</cfif>
			<cfif NOT structKeyExists(local.retStruct.mc_combinedAddressArrayMap, local.strKey)>
				<cfif local.strOrgAddressTypes[local.strKey].isTag>
					<cfset local.typeLabel = replaceNoCase(local.strOrgAddressTypes[local.strKey].addressType, "Designated ", "")>
				<cfelse>
					<cfset local.typeLabel = local.strOrgAddressTypes[local.strKey].addressType>
				</cfif>
				<cfset local.newAddrTemplate = {key=local.strKey, addr='', type=local.strOrgAddressTypes[local.strKey].addressType, label=local.typeLabel }>
				<cfset arrayAppend(local.retStruct.mc_combinedAddresses, local.newAddrTemplate)>
				<cfset local.retStruct.mc_combinedAddressArrayMap[local.strKey] = arrayLen(local.retStruct.mc_combinedAddresses)>
			</cfif>
		</cfloop>

		<cfset local.elementsToDelete = "">

		<cfloop index="local.i" from="1" to="#arrayLen(local.retStruct.mc_combinedAddresses)#">

			<cfset local.thisATID = local.retStruct.mc_combinedAddresses[local.i].key>
			<cfif left(local.thisATID,1) eq "t">
				<cfset local.MAfcPrefix = "mat_#right(local.thisATID,len(local.thisATID)-1)#_">
				<cfset local.MPfcPrefix = "mpt_#right(local.thisATID,len(local.thisATID)-1)#_">
			<cfelse>
				<cfset local.MAfcPrefix = "ma_#local.thisATID#_">
				<cfset local.MPfcPrefix = "mp_#local.thisATID#_">
			</cfif>

			<cfset local.thisAddressStruct = {
				address1="",
				address2="",
				address3="",
				city="",
				stateprov="",
				postalcode="",
				county="",
				country=""
			}>
			<cfset local.tmp = XMLSearch(local.xmlResultFields,"//mf[@fieldCode='#local.MAfcPrefix#address1']")>
			<cfif arrayLen(local.tmp) is 1 and local.strMemberData.keyExists(local.tmp[1].xmlAttributes.fieldLabel) and len(local.strMemberData[local.tmp[1].xmlAttributes.fieldLabel])>
				<cfset local.thisAddressStruct["address1"] = local.strMemberData[local.tmp[1].xmlAttributes.fieldLabel] />
			</cfif>
			<cfset local.tmp = XMLSearch(local.xmlResultFields,"//mf[@fieldCode='#local.MAfcPrefix#address2']")>
			<cfif arrayLen(local.tmp) is 1 and local.strMemberData.keyExists(local.tmp[1].xmlAttributes.fieldLabel) and len(local.strMemberData[local.tmp[1].xmlAttributes.fieldLabel])>
				<cfset local.thisAddressStruct["address2"] = local.strMemberData[local.tmp[1].xmlAttributes.fieldLabel]>
			</cfif>
			<cfset local.tmp = XMLSearch(local.xmlResultFields,"//mf[@fieldCode='#local.MAfcPrefix#address3']")>
			<cfif arrayLen(local.tmp) is 1 and local.strMemberData.keyExists(local.tmp[1].xmlAttributes.fieldLabel) and len(local.strMemberData[local.tmp[1].xmlAttributes.fieldLabel])>
				<cfset local.thisAddressStruct["address3"] = local.strMemberData[local.tmp[1].xmlAttributes.fieldLabel]>
			</cfif>
			<cfset local.tmp = XMLSearch(local.xmlResultFields,"//mf[@fieldCode='#local.MAfcPrefix#city']")>
			<cfif arrayLen(local.tmp) is 1 and local.strMemberData.keyExists(local.tmp[1].xmlAttributes.fieldLabel) and len(local.strMemberData[local.tmp[1].xmlAttributes.fieldLabel])>
				<cfset local.thisAddressStruct["city"] = local.strMemberData[local.tmp[1].xmlAttributes.fieldLabel]>
			</cfif>
			<cfset local.tmp2 = XMLSearch(local.xmlResultFields,"//mf[@fieldCode='#local.MAfcPrefix#stateprov']")>
			<cfif arrayLen(local.tmp2) is 1 and local.strMemberData.keyExists(local.tmp2[1].xmlAttributes.fieldLabel) and len(local.strMemberData[local.tmp2[1].xmlAttributes.fieldLabel])>
				<cfset local.thisAddressStruct["stateprov"] = local.strMemberData[local.tmp2[1].xmlAttributes.fieldLabel]>
			</cfif>
			<cfset local.tmp = XMLSearch(local.xmlResultFields,"//mf[@fieldCode='#local.MAfcPrefix#postalcode']")>
			<cfif arrayLen(local.tmp) is 1 and local.strMemberData.keyExists(local.tmp[1].xmlAttributes.fieldLabel) and len(local.strMemberData[local.tmp[1].xmlAttributes.fieldLabel])>
				<cfset local.thisAddressStruct["postalcode"] = local.strMemberData[local.tmp[1].xmlAttributes.fieldLabel]>
			</cfif>
			<cfset local.tmp = XMLSearch(local.xmlResultFields,"//mf[@fieldCode='#local.MAfcPrefix#county']")>
			<cfif arrayLen(local.tmp) is 1 and local.strMemberData.keyExists(local.tmp[1].xmlAttributes.fieldLabel) and len(local.strMemberData[local.tmp[1].xmlAttributes.fieldLabel])>
				<cfset local.thisAddressStruct["county"] = local.strMemberData[local.tmp[1].xmlAttributes.fieldLabel] & " County"><br/>
			</cfif>
			<cfset local.tmp = XMLSearch(local.xmlResultFields,"//mf[@fieldCode='#local.MAfcPrefix#country']")>
			<cfif arrayLen(local.tmp) is 1 and local.strMemberData.keyExists(local.tmp[1].xmlAttributes.fieldLabel) and len(local.strMemberData[local.tmp[1].xmlAttributes.fieldLabel])>
				<cfset local.thisAddressStruct["country"] = local.strMemberData[local.tmp[1].xmlAttributes.fieldLabel]>
			</cfif>
			<cfset local.tmp = XMLSearch(local.xmlResultFields,"//mf[substring(@fieldCode,1,#len(local.MPfcPrefix)#)='#local.MPfcPrefix#']")>

			<cfsavecontent variable="local.thisATFull">
				<cfoutput>
					<div itemprop="address" itemscope itemtype="http://schema.org/PostalAddress">
						<cfif len(local.thisAddressStruct["address1"]) or len(local.thisAddressStruct["address2"]) or len(local.thisAddressStruct["address3"])>
							<span itemprop="streetAddress">
								<cfif len(local.thisAddressStruct["address1"])>#local.thisAddressStruct["address1"]#<br /></cfif>
								<cfif len(local.thisAddressStruct["address2"])>#local.thisAddressStruct["address2"]#<br /></cfif>
								<cfif len(local.thisAddressStruct["address3"])>#local.thisAddressStruct["address3"]#<br /></cfif>
							</span>
						</cfif>
						<cfif len(local.thisAddressStruct["city"])><span itemprop="addressLocality">#local.thisAddressStruct["city"]#</span></cfif><cfif len(local.thisAddressStruct["stateprov"])>, <span itemprop="addressRegion">#local.thisAddressStruct["stateprov"]#</span></cfif>
						<cfif len(local.thisAddressStruct["postalcode"])> <span itemprop="postalCode">#local.thisAddressStruct["postalcode"]#</span></cfif>
						<cfif len(local.thisAddressStruct["address1"]) or len(local.thisAddressStruct["address2"]) or len(local.thisAddressStruct["address3"])>
							<br />
						</cfif>
						<cfif len(local.thisAddressStruct["county"])>#local.thisAddressStruct["county"]# <br /></cfif>
						<cfif len(local.thisAddressStruct["country"])><span itemprop="addressCountry">#local.thisAddressStruct["country"]#</span><br /></cfif>
					</div>
 				</cfoutput>
			</cfsavecontent>
			<cfsavecontent variable="local.thisATMap">
				<cfoutput>
					<cfif len(local.thisAddressStruct["address1"])>#local.thisAddressStruct["address1"]#</cfif>
					<cfif len(local.thisAddressStruct["address2"])>#local.thisAddressStruct["address2"]#</cfif>
					<cfif len(local.thisAddressStruct["address3"])>#local.thisAddressStruct["address3"]#</cfif>
					<cfif len(local.thisAddressStruct["city"])>#local.thisAddressStruct["city"]#</cfif><cfif len(local.thisAddressStruct["stateprov"])>, #local.thisAddressStruct["stateprov"]#</cfif><cfif len(local.thisAddressStruct["postalcode"])> #local.thisAddressStruct["postalcode"]#</cfif>
					<cfif len(local.thisAddressStruct["county"])>#local.thisAddressStruct["county"]# <br /></cfif>
					<cfif len(local.thisAddressStruct["country"])>#local.thisAddressStruct["country"]#</cfif>
 				</cfoutput>
			</cfsavecontent>

			<cfset local.thisATfull = trim(replace(replace(local.thisATFull,'  ',' ','ALL'),' ,',',','ALL'))>
			<cfif left(local.thisATfull,2) eq ", ">
				<cfset local.thisATfull = right(local.thisATfull,len(local.thisATfull)-2)>
			</cfif>
			
			<cfif len(local.thisATMap)>
				<cfset local.retStruct.mc_combinedAddresses[local.i]['addr'] = local.thisATfull>
				<cfset local.retStruct.mc_combinedAddresses[local.i]['mapaddr'] = local.thisATMap>
			<cfelse>
				<cfset local.elementsToDelete = listPrepend(local.elementsToDelete, local.i)>
			</cfif>
		</cfloop>
		<cfloop index="local.x" list="#local.elementsToDelete#"> 
			<cfset arrayDeleteAt(local.retStruct.mc_combinedAddresses, local.x)>
		</cfloop> 

		<cfif arrayLen(XMLSearch(local.xmlResultFields,"//mf[@fieldCode='m_membernumber']"))>
			<cfset local.retStruct.stMemberNumber = arguments.strMember.qryMember.membernumber>
		<cfelse>
			<cfset local.retStruct.stMemberNumber = ''>
		</cfif>

		<cfif arrayLen(XMLSearch(local.xmlResultFields,"//mf[@fieldCode='m_company']")) and len(arguments.strMember.qryMember.company)>
			<cfset local.retStruct.stCompany = arguments.strMember.qryMember.company>
		<cfelse>
			<cfset local.retStruct.stCompany = ''>
		</cfif>

		<cfset local.retStruct.imgToUse = ''>
		<cfif arguments.strMember.qryMember.hasMemberPhotoThumb is 1>
			<cfset local.retStruct.imgToUse = '<img class="tsAppDirPhoto" itemprop="image" src="/memberphotosth/#LCASE(arguments.strMember.qryMember.membernumber)#.jpg">'>
		</cfif>

		<cfloop array="#local.xmlResultFields.xmlRoot.xmlChildren#" index="local.thisField">
			<cfif ReFindNoCase('mp_[0-9]+_[0-9]+',local.thisField.xmlattributes.fieldcode) and local.strMemberData.keyExists(local.thisField.xmlattributes.fieldLabel) and len(local.strMemberData[local.thisField.xmlattributes.fieldLabel])>
				<!--- add phone --->
				<cfset local.memdataentry = StructNew()>
				<cfset local.memdataentry.fieldLabel = local.thisField.xmlattributes.fieldLabel>
				<cfset local.memdataentry.phone = local.strMemberData[local.thisField.xmlattributes.fieldLabel]>
				<cfset arrayAppend(local.retStruct.arrPhones, local.memdataentry)>
			<cfelseif ReFindNoCase('mpt_[0-9]+_[0-9]+',local.thisField.xmlattributes.fieldcode) and local.strMemberData.keyExists(local.thisField.xmlattributes.fieldLabel) and len(local.strMemberData[local.thisField.xmlattributes.fieldLabel])>
				<!--- add phone --->
				<cfset local.memdataentry = StructNew()>
				<cfset local.memdataentry.fieldLabel = local.thisField.xmlattributes.fieldLabel>
				<cfset local.memdataentry.phone = local.strMemberData[local.thisField.xmlattributes.fieldLabel]>
				<cfset arrayAppend(local.retStruct.arrPhones, local.memdataentry)>

			<cfelseif (ReFindNoCase('me_[0-9]+_email',local.thisField.xmlattributes.fieldcode) or ReFindNoCase('met_[0-9]+_email',local.thisField.xmlattributes.fieldcode)) and local.strMemberData.keyExists(local.thisField.xmlattributes.fieldLabel) and len(local.strMemberData[local.thisField.xmlattributes.fieldLabel])>
				<!--- add email --->
				<cfset local.memdataentry = StructNew()>
				<cfset local.memdataentry.fieldLabel = local.thisField.xmlattributes.fieldLabel>
				<cfset local.memdataentry.email = local.strMemberData[local.thisField.xmlattributes.fieldLabel]>
				<cfset arrayAppend(local.retStruct.arrEmails, local.memdataentry)>

			<cfelseif ReFindNoCase('mw_[0-9]+_website',local.thisField.xmlattributes.fieldcode) and local.strMemberData.keyExists(local.thisField.xmlattributes.fieldLabel) and len(local.strMemberData[local.thisField.xmlattributes.fieldLabel])>
				<cfif listFindNoCase(variables.supportedSocialIcons, local.thisField.xmlattributes.fieldLabel)>
					<cfset local.memdataentry = StructNew()>
					<cfset local.memdataentry.fieldLabel = local.thisField.xmlattributes.fieldLabel>
					<cfset local.memdataentry.website = local.strMemberData[local.thisField.xmlattributes.fieldLabel]>
					<cfset local.memdataentry.socialLink = getSocialIconLink(local.memdataentry)>
					<cfset arrayAppend(local.retStruct.arrWebsitesSocialIcons, local.memdataentry)>
				<cfelse>
					<!--- add website --->
					<cfset local.memdataentry = StructNew()>
					<cfset local.memdataentry.socialLink = "">
					<cfset local.memdataentry.fieldLabel = local.thisField.xmlattributes.fieldLabel>
					<cfset local.memdataentry.website = local.strMemberData[local.thisField.xmlattributes.fieldLabel]>
					<cfset arrayAppend(local.retStruct.arrWebsites, local.memdataentry)>					
				</cfif>

			<cfelseif (ReFindNoCase('md_[0-9]+',local.thisField.xmlattributes.fieldcode) or ReFindNoCase('mad_[0-9]+_[0-9]+',local.thisField.xmlattributes.fieldcode)) and local.strMemberData.keyExists(local.thisField.xmlattributes.fieldLabel) and len(local.strMemberData[local.thisField.xmlattributes.fieldLabel])>
				<!--- add member data / districting --->
				<cfset local.memdataentry = StructNew()>
				<cfset local.memdataentry.fieldLabel = local.thisField.xmlattributes.fieldLabel>
				<cfset local.memdataentry.value = local.strMemberData[local.thisField.xmlattributes.fieldLabel]>
				<cfset local.memdataentry.allowMultiple = local.thisField.xmlAttributes.allowMultiple>
				<cfset local.memdataentry.dataTypeCode = local.thisField.xmlAttributes.dataTypeCode>
				<cfset local.memdataentry.columnID = local.thisField.xmlAttributes.mdColumnID>
				<cfset arrayAppend(local.retStruct.arrMemberDataDistricting, local.memdataentry)>
			<cfelseif left(local.thisField.xmlattributes.dbField,17) eq "ml_datelastlogin_" and local.strMemberData.keyExists(local.thisField.xmlattributes.fieldLabel) and len(local.strMemberData[local.thisField.xmlattributes.fieldLabel])>
				<!--- add last login --->
				<cfset local.memdataentry = StructNew()>
				<cfset local.memdataentry.fieldLabel = local.thisField.xmlattributes.fieldLabel>
				<cfset local.memdataentry.value = dateformat(local.strMemberData[local.thisField.xmlattributes.fieldLabel],"m/d/yyyy")>
				<cfset local.memdataentry.allowMultiple = local.thisField.xmlAttributes.allowMultiple>
				<cfset arrayAppend(local.retStruct.arrMemberDataDistricting, local.memdataentry)>

			<cfelseif ReFindNoCase('mpl_[0-9]+_[a-z]+',local.thisField.xmlattributes.fieldcode) and local.strMemberData.keyExists(local.thisField.xmlattributes.fieldLabel) and len(local.strMemberData[local.thisField.xmlattributes.fieldLabel])>
				<cfset local.memdataentry = StructNew()>
				<cfset local.memdataentry.fieldLabel = local.thisField.xmlattributes.fieldLabel>
				<cfset local.memdataentry.value = local.strMemberData[local.thisField.xmlattributes.fieldLabel]>
				<cfif listFindNoCase(local.thisField.xmlattributes.fieldcode,"activeDate","_")>
					<cfset local.memdataentry.value = dateFormat(local.memdataentry.value, "mm/dd/yyyy")>
				</cfif>
				<cfset arrayAppend(local.retStruct.arrLicenseData, local.memdataentry)>
			
			</cfif>
		</cfloop>

		<cfif structKeyExists(arguments.strMember, "classifications")>		
			<cfloop query="arguments.strMember.classifications">	
				<cfset local.clStruct = structNew()>
				<cfset local.clStruct.groupID = arguments.strMember.classifications.groupID>
				<cfset local.clStruct.imageext = arguments.strMember.classifications.imageext>
				<cfset local.clStruct.groupName = arguments.strMember.classifications.groupName>
				<cfset local.clStruct.clName = replace(arguments.strMember.classifications.name, '_', ' ', 'ALL')>

				<cfif ((arguments.dirAction eq "searchResults" and arguments.strMember.classifications.showGroupImage is 1) 
						or (arguments.dirAction eq "memberDetails" and arguments.strMember.classifications.showGroupImageInSearchDetail is 1)) 
						AND FileExists('#application.paths.localUserAssetRoot.path#common/groupImages/#arguments.strMember.classifications.groupID#.#arguments.strMember.classifications.imageext#')> 
					<cfif arguments.strMember.classifications.imagePlacement eq "above">
						<cfset arrayAppend(local.retStruct.arrAboveClassifications, local.clStruct)>
					</cfif>	
					<cfif arguments.strMember.classifications.imagePlacement eq "below">
						<cfset arrayAppend(local.retStruct.arrBelowClassifications, local.clStruct)>
					</cfif>	
					<cfif arguments.strMember.classifications.imagePlacement eq "under">
						<cfset arrayAppend(local.retStruct.arrUnderClassifications, local.clStruct)>
					</cfif>	
				</cfif>
				<cfif (arguments.dirAction eq "searchResults" and arguments.strMember.classifications.showInSearchResults is 1) or (arguments.dirAction eq "memberDetails" and arguments.strMember.classifications.showInSearchDetail is 1)>
					<cfset arrayAppend(local.retStruct.arrClValues, local.clStruct)>
				</cfif>						

			</cfloop>
		</cfif>

		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="getMemberFormFields" access="public" output="no" returntype="array">
		<cfargument name="orgID" type="numeric">
		<cfargument name="xmlFields" type="any">
		<cfargument name="memberDirectoryInfo" type="query">

		<cfset var local = structnew()>
		<cfset local.retArrFormFields = arrayNew(1)>

		<!--- looking up in case we need it, this way just looked up the once --->
		<cfset local.objMemberDirectory = CreateObject('component','model.system.platform.memberDirectory')>
		<cfloop array="#arguments.xmlFields#" index="local.thisfield">

			<cfset local.thisFormField = structNew()>
			<cfset local.thisFormField.isRequired = local.thisfield.xmlattributes.isRequired>
			<cfset local.thisFormField.fieldCode = local.thisfield.xmlattributes.fieldCode>
			<cfset local.thisFormField.fieldLabel = local.thisfield.xmlattributes.fieldLabel>
			<cfset local.thisFormField.fieldDescription = local.thisfield.xmlattributes.fieldDescription>
			<cfset local.thisFormField.displayTypeCode = local.thisfield.xmlAttributes.displayTypeCode>
			<cfset local.thisFormField.dataTypeCode = local.thisfield.xmlAttributes.dataTypeCode>

			<cfswitch expression="#local.thisfield.xmlAttributes.displayTypeCode#">
				<cfcase value="TEXTBOX">
					<cfif ReFindNoCase('mat?_[0-9]+_postalcode',local.thisfield.xmlattributes.fieldCode)>
						<cfset local.thisFormField.isPostalCode = true>
					</cfif>
				</cfcase>
				<cfcase value="RADIO">
					<cfset local.thisFormField.arrOptions = arrayNew(1)>

					<cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt"> 
						<cfswitch expression="#local.thisfield.xmlAttributes.dataTypeCode#">
							<cfcase value="STRING">
								<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueString>
							</cfcase>
							<cfcase value="DECIMAL2">
								<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueDecimal2>
							</cfcase>
							<cfcase value="INTEGER">
								<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueInteger>
							</cfcase>
							<cfcase value="DATE">
								<cfset local.thisOptColValue = dateformat(replaceNoCase(local.thisOpt.xmlAttributes.columnValueDate,'T',' '),"mm/dd/yyyy")>
							</cfcase>
							<cfcase value="BIT">
								<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueBit>
							</cfcase>
							<cfdefaultcase>
								<cfset local.thisOptColValue = "">
							</cfdefaultcase>
						</cfswitch>
						<cfset arrayAppend(local.thisFormField.arrOptions, local.thisOptColValue)>
					</cfloop>
				</cfcase>
				<cfcase value="SELECT,CHECKBOX">
					<cfif ReFindNoCase('mat?_[0-9]+_stateprov',local.thisfield.xmlattributes.fieldCode)>
						<cfif not structKeyExists(local,"qryStates")>
							<cfset local.qryStates = local.objMemberDirectory.getStates(arguments.memberDirectoryInfo.memberDirectoryID,arguments.orgID)>
						</cfif>
						<cfset local.thisFormField.qryData = local.qryStates>
						<cfset local.thisFormField.optValue = 'stateCode'>
						<cfset local.thisFormField.optDisplay = 'stateName'>
					<cfelse>
						<cfset local.thisFormField.arrOptions = arrayNew(1)>

						<cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt"> 
							<cfswitch expression="#local.thisfield.xmlAttributes.dataTypeCode#">
								<cfcase value="STRING">
									<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueString>
								</cfcase>
								<cfcase value="DECIMAL2">
									<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueDecimal2>
								</cfcase>
								<cfcase value="INTEGER">
									<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueInteger>
								</cfcase>
								<cfcase value="DATE">
									<cfset local.thisOptColValue = dateformat(replaceNoCase(local.thisOpt.xmlAttributes.columnValueDate,'T',' '),"mm/dd/yyyy")>
								</cfcase>
								<cfcase value="BIT">
									<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueBit>
								</cfcase>
								<cfdefaultcase>
									<cfset local.thisOptColValue = "">
								</cfdefaultcase>
							</cfswitch>
							<cfset arrayAppend(local.thisFormField.arrOptions, local.thisOptColValue)>
						</cfloop>
					</cfif>
				</cfcase>
			</cfswitch>

			<cfset arrayAppend(local.retArrFormFields, local.thisFormField)>

		</cfloop>

		<cfreturn local.retArrFormFields>
	</cffunction>

	<cffunction name="getSearchCriteria" access="private" returntype="array" output="no">
		<cfargument name="orgID" type="numeric">
		<cfargument name="defaultLanguageID" type="numeric">
		<cfargument name="memberDirectoryInfo" type="query">
		<cfargument name="siteresourceID" type="numeric">
		<cfargument name="searchData" type="struct">
		
		<cfset var local = structNew()>
		<cfset local.arrSearchCriteria = ArrayNew(1)>

		<!--- Remove the keys we are not interested in --->		
		<cfset structDelete(arguments.searchData,"PG")>
		<cfset structDelete(arguments.searchData,"DirAction")>
		<cfset structDelete(arguments.searchData,"memPageNum")>
		<cfset structDelete(arguments.searchData,"dirMemberID")>
		<cfparam name="arguments.searchData.fs_match" default="s">

		<cfset local.objMemberDirectory = CreateObject('component','model.system.platform.memberDirectory')>

		<cfset local.returnStruct.searchContentStruct = variables.getStaticContent(arguments.memberDirectoryInfo.searchContentID,arguments.defaultLanguageID)>

		<cfset local.qryFieldsetID = getLocatorFieldsetID(siteResourceID=arguments.siteresourceID, area='search')>
		<cfset local.returnStruct.xmlFields = CreateObject("component","model.system.platform.memberFieldsets").getMemberFieldsXML(fieldsetid=val(local.qryFieldsetID.fieldsetID), usage="memberDirectorySearch")>
		<cfset local.returnStruct.fieldsetInfo = StructNew()>
		<cfset local.returnStruct.fieldsetInfo.fieldsetName = local.qryFieldsetID.fieldsetName>
		<cfset local.returnStruct.fieldsetInfo.nameFormat = local.qryFieldsetID.nameFormat>
		<cfset local.returnStruct.fieldsetInfo.showHelp = local.qryFieldsetID.showHelp>

		<cfset local.returnStruct.arrFormFields = getMemberFormFields(arguments.orgID, local.returnStruct.xmlFields.xmlRoot.xmlChildren, arguments.memberDirectoryInfo)>
		
		<cfloop array="#local.returnStruct.arrFormFields#" index="local.x">
			<cfif structKeyExists(arguments.searchData, local.x.fieldcode) and len(arguments.searchData[local.x.fieldcode])>
				<cfset local.searchField = { fieldLabel=local.x.fieldLabel, fieldseparator=': ', fieldValue=arguments.searchData[local.x.fieldcode] } >
				<cfif local.x.datatypecode eq "STRING" and local.x.displayTypeCode eq "TEXTBOX">
					<cfswitch expression="#arguments.searchData.fs_match#">
					<cfcase value="c">
						<cfset local.searchField.fieldseparator = " contains: ">
					</cfcase>
					<cfcase value="e">
						<cfset local.searchField.fieldseparator = " exactly matches: ">
					</cfcase>
					<cfdefaultcase>
						<cfset local.searchField.fieldseparator = " starts with: ">
					</cfdefaultcase>
					</cfswitch>
				<cfelseif local.x.datatypecode eq "BIT" and local.x.displayTypeCode eq "SELECT">
					<cfset local.searchField.fieldValue = YesNoFormat(arguments.searchData[local.x.fieldcode])>
				</cfif>
				<cfset ArrayAppend(local.arrSearchCriteria, local.searchField)>
			</cfif>
		</cfloop>																		

		<!--- look only if mg_gid is set --->
		<cfif structKeyExists(arguments.searchData, "mg_gid")>
			<cfset local.objMemberConfig = CreateObject('component','model.members.MemberConfig')>
			<cfset local.qryClassifications = local.objMemberConfig.getClassifications(arguments.memberDirectoryInfo.memberDirectoryID)>
			<cfset local.returnStruct.arrClassifications = arrayNew(1)>	
			<cfloop query="local.qryClassifications">
				<cfif local.qryClassifications.allowSearch is 1>
					<cfset local.stClass = structNew()>
					<cfset local.stClass.name = local.qryClassifications.name>
					<cfset local.stClass.qryClassificationslinks = local.objMemberConfig.getGroupSets(local.qryClassifications.classificationid)>
					<cfloop query="local.stClass.qryClassificationslinks">
						<cfif listFindNoCase(arguments.searchData["mg_gid"],local.stClass.qryClassificationslinks.groupID)>
							<cfset local.searchField = StructNew()>
							<cfset local.searchField.fieldLabel = local.stClass.name>
							<cfset local.searchField.fieldValue = local.stClass.qryClassificationslinks.groupName>
							<cfset local.searchField.fieldseparator = ": ">
							<cfset ArrayAppend(local.arrSearchCriteria, local.searchField)>
						</cfif>
					</cfloop>
				</cfif>
			</cfloop>
		</cfif>
		
		<cfreturn local.arrSearchCriteria>																		
	</cffunction>	

	<cffunction name="getSocialIconLink" access="private" output="no" returntype="string">
		<cfargument name="thisField" type="struct">

		<cfset var local = structnew()>
		<cfset local.socialLink = "">

		<cfloop list="#variables.supportedSocialIcons#" index="local.linkItem">
			<cfif findNoCase(local.linkItem, arguments.thisField.fieldLabel)>
				<cfsavecontent variable="local.socialLink">
					<cfoutput>
						<a href="#arguments.thisField.website#" target="_blank"><img src="/assets/common/images/socialMediaIconSets/social-icons/32px/#local.linkItem#.png"></a>
					</cfoutput>
				</cfsavecontent>
				<cfbreak>
			</cfif>
		</cfloop>

		<cfreturn local.socialLink>
	</cffunction>

	<cffunction name="createAppInstance" access="public" output="true" returntype="void">
		<cfargument name="Event" type="any">
		<cfargument name="baseLink" type="string">
		<cfargument name="appInfo" type="query">
		<cfargument name="returnToAppAdmin" type="boolean" required="false" default="0">
		<cfargument name="isFromCommunity" type="boolean" required="false" default="0">

		<cfscript>
			var local 											= structNew();		
			// SET EVENT SPECIFICATION ----------------------------------------------
			local.appInfo 									= arguments.appInfo;
			variables.isCommunityReady 			= XMLSearch(local.appInfo.settingsXML,"string(//setting[@name='isCommunityReady']/@value)");
			variables.isMultiInstanceReady 	= XMLSearch(local.appInfo.settingsXML,"string(//setting[@name='isMultiInstanceReady']/@value)");
			arguments.event.paramValue('appTypeID','0');
			// LOAD OBJECTS ---------------------------------------------------------
			local.objAppCreation 						= CreateObject("component","model.admin.pages.appCreationProcess");
			// call the contruct to do all the page validation and form params ------			
			contructAppInstanceForm(arguments.event,local.appInfo);			
		</cfscript>
		<cfif cgi.request_method eq "POST" AND NOT arguments.event.getValue('error.formErrors')>
			<cftry>
				<cfif variables.isCommunityReady AND arguments.event.getValue('deployToComm','0')>
					<cfset local.viewRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="Community", functionName="view")>
					<cfset local.participateRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="Community", functionName="participate")>
					<cfset local.fsQualifyRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="memberFieldset", functionName="fsQualify")>
					<cfset local.AppearInDirectoryRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="Members", functionName="AppearInDirectory")>
				</cfif>

				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.createApp">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY

						DECLARE @siteID	int, @languageID int, @sectionID int, @isVisible bit, @pageName varchar(50), 
							@pageTitle varchar(200), @pagedesc varchar(400), @zoneID int, @pageTemplateID int,
							@pageModeID int, @pgResourceTypeID int, @pgParentResourceID int,  @allowReturnAfterLogin bit,
							@applicationInstanceName varchar(100), @applicationInstanceDesc varchar(200),
							@applicationInstanceID int, @siteResourceID int, @pageID int, @commSRID int;

						SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">;
						SET @languageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('lid')#">;
						SET @isVisible = 1;
						SELECT @pageName = dbo.fn_regexReplace(<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('pageName')#">,'[^A-Z0-9\-]+','');
						SET @pageTitle = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('pageTitle')#">;
						SET @pagedesc = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('pageDesc')#">;
						SELECT @zoneID = dbo.fn_getZoneID('Main');
						SET @pageTemplateID = NULL;
						SET @pageModeID = <cfif arguments.event.getValue('pageModeID','0') EQ 0>NULL<cfelse><cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('pageModeID')#"></cfif>;
						SET @allowReturnAfterLogin = 1;
						SET @applicationInstanceName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('appInstanceName')#">;
						SET @applicationInstanceDesc = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('appInstanceDesc')#">;

						<cfif variables.isCommunityReady AND arguments.event.getValue('deployToComm','0')>
							SET @commSRID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('commSRID')#">;
							
							SELECT @sectionID = comm.rootSectionID 
							FROM dbo.comm_communities comm 
							INNER JOIN dbo.cms_applicationInstances ai on comm.applicationInstanceID = ai.applicationInstanceID
								AND ai.siteResourceID = @commSRID
							AND ai.siteID = @siteID;

							SELECT @pgResourceTypeID = dbo.fn_getResourceTypeID('ApplicationSubPage');
							SET @pgParentResourceID = @commSRID;
						<cfelse>
							SET @sectionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('sectionID')#">
							SELECT @pgResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedPage')
							SET @pgParentResourceID = NULL;
						</cfif>

						BEGIN TRAN;
							EXEC dbo.cms_createApplicationInstanceMemberDirectory @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, 
								@isVisible=@isVisible, @pageName=@pageName, @pageTitle=@pageTitle, @pagedesc=@pagedesc,
								@zoneID=@zoneID, @pageTemplateID=@pageTemplateID, @pageModeID=@pageModeID,
								@pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID=@pgParentResourceID,
								@allowReturnAfterLogin=@allowReturnAfterLogin, @applicationInstanceName=@applicationInstanceName,
								@applicationInstanceDesc=@applicationInstanceDesc, @applicationInstanceID=@applicationInstanceID OUTPUT,
								@siteResourceID=@siteResourceID OUTPUT, @pageID=@pageID OUTPUT;

							<cfif variables.isCommunityReady AND arguments.event.getValue('deployToComm','0')>
								DECLARE @viewFunctionID int, @participateFunctionID int, @siteCSRID int, @FSDefaultCategoryID int,
									@searchFSID int, @resultFSID int, @searchUseID int, @resultsUseID int, @resultsUseSiteResourceID int,
									@memberFieldsetFunctionID int, @detailsUseID int, @detailsUseSiteResourceID int, @MemberManagerResourceTypeID int,
									@appearInDirectoryID int;
								
								SET @viewFunctionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.viewRFID#">;
								SET @participateFunctionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.participateRFID#">;
								SET @memberFieldsetFunctionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.fsQualifyRFID#">;
								SET @appearInDirectoryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.AppearInDirectoryRFID#">;
								SELECT @siteCSRID = siteResourceID from dbo.sites where siteID = @siteID;

								SELECT TOP 1 @FSDefaultCategoryID = c.categoryID
								FROM dbo.cms_categoryTrees AS ct
								INNER JOIN dbo.cms_categories AS c ON c.categoryTreeID = ct.categoryTreeID
								WHERE ct.controllingSiteResourceID = @siteCSRID
								AND ct.categoryTreeCode = 'MEMFIELDSETS'
								ORDER BY c.categoryID;

								SELECT @searchFSID = fieldSetID
								FROM dbo.ams_memberFieldSets
								WHERE categoryID = @FSDefaultCategoryID
								AND fieldSetName = 'Account Locator Search Form';

								SELECT @resultFSID = fieldSetID
								FROM dbo.ams_memberFieldSets
								WHERE categoryID = @FSDefaultCategoryID
								AND fieldSetName = 'Account Locator Search Results';

								IF @searchFSID IS NOT NULL
									EXEC dbo.ams_createMemberFieldUsage @siteResourceID=@siteResourceID, @fieldsetID=@searchFSID, @area='search',
										@createSiteResourceID=0, @useID=@searchUseID OUTPUT;
								
								IF @resultFSID IS NOT NULL BEGIN
									EXEC dbo.ams_createMemberFieldUsage @siteResourceID=@siteResourceID, @fieldsetID=@resultFSID, @area='results',
										@createSiteResourceID=1, @useID=@resultsUseID OUTPUT;

									-- lookup usesiteResourceID from useID returned from previous step
									SELECT @resultsUseSiteResourceID = mfu.useSiteResourceID
									FROM dbo.ams_memberFieldusage AS mfu
									INNER JOIN dbo.cms_siteResources AS sr ON sr.siteID = @siteID
										AND sr.siteResourceID = mfu.useSiteResourceID
									WHERE mfu.useID = @resultsUseID;

									-- call cms_createSiteResourceRight to inherit qualify perm for results fieldset usage from participate
									EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@resultsUseSiteResourceID, @include=1, 
										@functionIDList=@memberFieldsetFunctionID, @roleID=null, @groupID=null, @inheritedRightsResourceID=@commSRID, 
										@inheritedRightsFunctionID=@participateFunctioniD;

									-- set the details fieldset for the member directory
									EXEC dbo.ams_createMemberFieldUsage @siteResourceID=@siteResourceID, @fieldsetID=@resultFSID, @area='details',
										@createSiteResourceID=1, @useID=@detailsUseID OUTPUT;

									-- lookup usesiteResourceID from useID returned from previous step
									SELECT @detailsUseSiteResourceID = mfu.useSiteResourceID
										FROM dbo.ams_memberFieldusage mfu
										INNER JOIN dbo.cms_siteResources sr ON sr.siteResourceID = mfu.useSiteResourceID
										WHERE mfu.useID = @detailsUseID;

									-- call cms_createSiteResourceRight to inherit qualify perm for results fieldset usage from participate
									EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@detailsUseSiteResourceID,
										@include=1, @functionIDList=@memberFieldsetFunctionID, @roleID=null, @groupID=null, 
										@inheritedRightsResourceID=@commSRID, @inheritedRightsFunctionID=@participateFunctionID;
								END

								UPDATE dbo.cms_applicationInstances
								SET settingsXML = '<settings><setting name="defaultAction" value="SearchResults" /></settings>'
								WHERE applicationInstanceID = @applicationInstanceID;

								EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @include=1, 
									@functionIDList=@appearInDirectoryID, @roleID=null, @groupID=null, @inheritedRightsResourceID=@commSRID, 
									@inheritedRightsFunctionID=@participateFunctionID;
								EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @include=1, 
									@functionIDList=@viewFunctionID, @roleID=null, @groupID=null, @inheritedRightsResourceID=@commSRID, 
									@inheritedRightsFunctionID=@viewFunctionID;
							</cfif>
						COMMIT TRAN;
						
						SELECT @applicationInstanceID as applicationInstanceID, @siteResourceID as siteResourceID, @pageID as pageID;
					
					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>
				<cfset local.message = 1>
				<cfcatch type="any">
					<cfset application.objError.sendError(cfcatch=cfcatch)>
					<cfset local.message = 2>
				</cfcatch>
			</cftry>
			<cfif arguments.isFromCommunity>
				<cfoutput>
					<script language="javascript">
						top.reloadCommSubPagesTable();
						top.MCModalUtils.hideModal();
					</script>
				</cfoutput>
			<cfelseif arguments.returnToAppAdmin>
				<cfset local.pageAdminTool = CreateObject("component","model.admin.admin").buildLinkToTool(toolType='MemberDirectoryAdmin',mca_ta='edit')>
				<cfset local.mdID = CreateObject("component","model.admin.memberDirectory.memberDirectoryAdmin").getMemberDirectoryIDFromAppID(local.createApp.applicationInstanceID)>
				<cfoutput>
					<script language="javascript">
						top.reloadMDTable();
						top.MCModalUtils.hideModal();
					</script>
				</cfoutput>
			<cfelse>
				<cfoutput>
					<script language="javascript">
						top.reloadPageTable();
						top.MCModalUtils.hideModal();
					</script>
				</cfoutput>
			</cfif>
			
		<cfelse>
			<cfoutput>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.getMode">
					select dbo.fn_getmodeId('Full') as pageModeID
				</cfquery>
				<cfset arguments.event.setValue('pageModeID',local.getMode.pageModeID)>
				<cfset showAppInstanceForm(arguments.event,local.appInfo)>
			</cfoutput>
		</cfif>		
	</cffunction>

	<cffunction name="getListSideBarCount" access="public" returntype="numeric" output="no">
		<cfargument name="daysToLookBack" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfif arguments.daysToLookBack gt 0>
			<cfset local.daysToLookBack = -1 * arguments.daysToLookBack>
		<cfelse>
			<cfset local.daysToLookBack = arguments.daysToLookBack>
		</cfif>

		<cfset local.AppearInDirectoryRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="Members", functionName="AppearInDirectory")>

		<cfquery name="local.data" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @FID int, @orgID int;
			SET @FID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.AppearInDirectoryRFID#">;
			SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.instanceSettings.orgID#">;

			SELECT m.memberID
			FROM dbo.ams_members AS m
			INNER JOIN dbo.cache_members_groups AS mg ON mg.orgID = @orgID
				AND mg.memberID = m.memberID
				and m.status = 'A'
			INNER JOIN cms_siteResourceRights as srr ON srr.groupID = mg.groupID
				AND functionID = @FID
				AND srr.resourceID = 
					(select siteResourceID
					from dbo.ams_memberDirectories as md2
					inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = md2.applicationInstanceID
					where md2.memberDirectoryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.instanceSettings.memberDirectoryID#">)
			WHERE m.orgID = @orgID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;					
		</cfquery>
		<cfreturn local.data.recordCount>
	</cffunction>	

	<cffunction name="getListingStats" access="private" returntype="array" output="no">
		<cfargument name="memberDirectoryID" type="numeric" required="yes">
		<cfargument name="memberID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.arrData = arrayNew(1)>

		<cfstoredproc datasource="#application.dsn.platformstatsMC.dsn#" procedure="ams_getMemberDirectoryStats">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.memberDirectoryID#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
			<cfprocresult name="local.qryStats" resultset="1">
		</cfstoredproc>

		<cfquery name="local.qrySearchResults" dbtype="query">
			select statCount 
			from [local].qryStats
			where statType = 'searchResults'
		</cfquery>
		<cfset local.strTemp = { label='Search Results', count=val(local.qrySearchResults.statCount) }>
		<cfset arrayAppend(local.arrData,local.strTemp)>

		<cfquery name="local.qrymemberDetails" dbtype="query">
			select statCount 
			from [local].qryStats
			where statType = 'memberDetails'
		</cfquery>
		<cfset local.strTemp = { label='Profile Hits', count=val(local.qrymemberDetails.statCount) }>
		<cfset arrayAppend(local.arrData,local.strTemp)>

		<cfquery name="local.qrycompareResults" dbtype="query">
			select statCount 
			from [local].qryStats
			where statType = 'compareResults'
		</cfquery>
		<cfset local.strTemp = { label='Comparisons', count=val(local.qrycompareResults.statCount) }>
		<cfset arrayAppend(local.arrData,local.strTemp)>

		<cfquery name="local.qrymemberDetailsVCard" dbtype="query">
			select statCount 
			from [local].qryStats
			where statType = 'memberDetailsVCard'
		</cfquery>
		<cfset local.strTemp = { label='VCARD Downloads', count=val(local.qrymemberDetailsVCard.statCount) }>
		<cfset arrayAppend(local.arrData,local.strTemp)>

		<cfquery name="local.qrysendContact" dbtype="query">
			select statCount 
			from [local].qryStats
			where statType = 'sendContact'
		</cfquery>
		<cfset local.strTemp = { label='Contact E-mails', count=val(local.qrysendContact.statCount) }>
		<cfset arrayAppend(local.arrData,local.strTemp)>

		<cfreturn local.arrData>
	</cffunction>	

	<cffunction name="normalizeNameFields" access="private" returntype="xml" output="false">
		<cfargument name="inputXML" type="xml" required="true">

		<cfscript>
			var local = structNew();
			local.XML = arguments.inputXML;

			if (not arrayLen(XMLSearch(local.XML,"/fields/mf[@fieldCode='m_company' or @fieldCode='m_firstname' or @fieldCode='m_lastname']"))) {
				/*
					If the Company, First Name, and Last Name are
					all absent from the results, add all three.
				*/

				/* Create a generic first name entry */
				local.thisElement = XMLElemNew(local.XML,'mf');
				local.thisElement.xmlAttributes["allowMultiple"] = 0;
				local.thisElement.xmlAttributes["allowNull"] = 1;
				local.thisElement.xmlAttributes["dataTypeCode"] = 'STRING';
				local.thisElement.xmlAttributes["dbField"] = 'firstname';
				local.thisElement.xmlAttributes["dbObject"] = 'ams_members';
				local.thisElement.xmlAttributes["dbObjectAlias"] = 'm';
				local.thisElement.xmlAttributes["displayTypeCode"] = 'TEXTBOX';
				local.thisElement.xmlAttributes["fieldCode"] = 'm_firstname';
				local.thisElement.xmlAttributes["fieldDescription"] = '';
				local.thisElement.xmlAttributes["fieldID"] = 0;
				local.thisElement.xmlAttributes["fieldLabel"] = 'First Name';
				local.thisElement.xmlAttributes["isGrouped"] = 0;
				local.thisElement.xmlAttributes["isReadOnly"] = 0;
				local.thisElement.xmlAttributes["isRequired"] = 0;
				local.thisElement.xmlAttributes["mdColumnID"] = 0;

				/* Add it to the results list of fields */
				arrayAppend(local.XML.fields.xmlChildren, local.thisElement);

				/* Modify it so it becomes a last name entry */
				local.thisElement.xmlAttributes["dbField"] = 'lastname';
				local.thisElement.xmlAttributes["fieldCode"] = 'm_lastname';
				local.thisElement.xmlAttributes["fieldLabel"] = 'Last Name';

				/* Add it to the results list of fields */
				arrayAppend(local.XML.fields.xmlChildren, local.thisElement);

				/* Modify it so it becomes a company entry */
				local.thisElement.xmlAttributes["dbField"] = 'company';
				local.thisElement.xmlAttributes["fieldCode"] = 'm_company';
				local.thisElement.xmlAttributes["fieldLabel"] = 'Company';

				/* Add it to the results list of fields */
				arrayAppend(local.XML.fields.xmlChildren, local.thisElement);
			} else if (
				not arrayLen(XMLSearch(local.XML,"/fields/mf[@fieldCode='m_firstname' or @fieldCode='m_lastname']"))
				and arrayLen(XMLSearch(local.XML,"/fields/mf[@fieldCode='m_prefix' or @fieldCode='m_middlename' or @fieldCode='m_suffix' or @fieldCode='m_professionalsuffix']"))
			) {
				/*
					Otherwise, if the First Name, and Last Name are
					absent, but other name fields are present, then
					add the first name and last name fields.
				*/

				/* Create a generic first name entry */
				local.thisElement = XMLElemNew(local.XML,'mf');
				local.thisElement.xmlAttributes["allowMultiple"] = 0;
				local.thisElement.xmlAttributes["allowNull"] = 1;
				local.thisElement.xmlAttributes["dataTypeCode"] = 'STRING';
				local.thisElement.xmlAttributes["dbField"] = 'firstname';
				local.thisElement.xmlAttributes["dbObject"] = 'ams_members';
				local.thisElement.xmlAttributes["dbObjectAlias"] = 'm';
				local.thisElement.xmlAttributes["displayTypeCode"] = 'TEXTBOX';
				local.thisElement.xmlAttributes["fieldCode"] = 'm_firstname';
				local.thisElement.xmlAttributes["fieldDescription"] = '';
				local.thisElement.xmlAttributes["fieldID"] = 0;
				local.thisElement.xmlAttributes["fieldLabel"] = 'First Name';
				local.thisElement.xmlAttributes["isGrouped"] = 0;
				local.thisElement.xmlAttributes["isReadOnly"] = 0;
				local.thisElement.xmlAttributes["isRequired"] = 0;
				local.thisElement.xmlAttributes["mdColumnID"] = 0;

				/* Add it to the results list of fields */
				arrayAppend(local.XML.fields.xmlChildren, local.thisElement);

				/* Modify it so it becomes a last name entry */
				local.thisElement.xmlAttributes["dbField"] = 'lastname';
				local.thisElement.xmlAttributes["fieldCode"] = 'm_lastname';
				local.thisElement.xmlAttributes["fieldLabel"] = 'Last Name';

				/* Add it to the results list of fields */
				arrayAppend(local.XML.fields.xmlChildren, local.thisElement);
			}
		</cfscript>

		<cfreturn local.XML>
	</cffunction>
	
	<cffunction name="getLinkRecordsList" access="public" output="false" returntype="struct">
		<cfargument name="orgID" type="numeric" required="no" default="0">
		<cfargument name="siteResourceID" type="numeric" required="no" default="0">
		<cfargument name="memberDirectoryID" type="numeric" required="no" default="0">
		<cfargument name="memberID" type="numeric" required="no" default="0">
		<cfargument name="showPhotoRule" type="numeric" required="no" default="0">
		<cfargument name="baseQryStr" type="string" required="no" default="">
		<cfargument name="posStart" type="numeric" required="no" default="-1">
		
		<cfscript>
			var local = structNew();
			local.count = 10;
			local.returnStruct = structNew();
			local.returnStruct.success = true;
			local.returnStruct.data = arrayNew(1);
			local.returnStruct.totalCount = 0;
		</cfscript>

		<!--- bots / direct access to this function with no arguments dont need to trigger an exception. --->
		<cfif arguments.orgID is 0 OR arguments.siteResourceID is 0 or arguments.memberDirectoryID is 0 or arguments.memberID is 0 
			or arguments.showPhotoRule is 0 or arguments.posStart lt 0 or not len(arguments.baseQryStr)>
			<cfset local.returnStruct.success = false>
			<cfreturn local.returnStruct>
		</cfif>

		<cfset local.objMFS = CreateObject('component','model.system.platform.memberFieldsets')>
		<cfset local.orgCode = application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgCode>
		<cfset local.siteCode = application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).siteCode>
		<cfset local.siteID = application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).siteID>
		<cfset local.qryFieldset = getLocatorFieldsetID(siteResourceID=arguments.siteResourceID,area='details')>

		<!--- sort criteria --->
		<cfquery name="local.qrySortCriteria" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT s.sortDefaultID, d.MemberDataValue, s.userGroupId, s.AscOrDesc
			FROM dbo.ams_memberDirectorySorts AS s
			LEFT OUTER JOIN dbo.ams_memberDirectorySortDefaults AS d ON s.sortDefaultID = d.sortDefaultID 
			LEFT OUTER JOIN dbo.ams_groups AS ug ON s.userGroupId = ug.groupID and ug.status <> 'D'
			WHERE s.memberDirectoryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberDirectoryID#">  
			ORDER BY s.sortOrder;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		<cfset local.pivotGroups = "">
		<cfset local.pvtGroupList = "">
		<cfif local.qrySortCriteria.recordCount>
			<cfset local.OrderByClause = "">
			<cfloop query="local.qrySortCriteria">
				<cfif local.qrySortCriteria.MemberDataValue eq "Random">
					<cfset local.OrderByClause = listAppend(local.orderByClause,"rand(m.memberID)")>
				<cfelseif len(local.qrySortCriteria.MemberDataValue)>
					<cfset local.OrderByClause = listAppend(local.orderByClause,"#local.qrySortCriteria.MemberDataValue# #local.qrySortCriteria.AscOrDesc#")>
				<cfelseif val(local.qrySortCriteria.usergroupid) gt 0>
					<cfset local.pivotValue = "[grp_#local.qrySortCriteria.userGroupID#]">
					<cfset local.pivotGroups = listAppend(local.pivotGroups,local.pivotValue)>
					<cfset local.pvtGroupList = listAppend(local.pvtGroupList,local.qrySortCriteria.userGroupId)>
					<cfset local.OrderByClause = listAppend(local.orderByClause,"#local.pivotValue# #local.qrySortCriteria.AscOrDesc#")>
				</cfif>
			</cfloop>
		<cfelse>
			<cfset local.OrderByClause = "m.lastname,m.firstname,m.middlename,m.suffix">
		</cfif>
		
		<cfset local.ListedAsLinkedRecordRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="Members", functionName="ListedAsLinkedRecord")>
		<cfset local.AppearInDirectoryRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="Members", functionName="AppearInDirectory")>

		<cfquery name="local.qryMembers" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @functionID int, @orgID int, @startRow int, @endRow int, @totalCount int, @siteResourceID int, @memberID int, @siteID int;
			set @functionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.ListedAsLinkedRecordRFID#">;
			set @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;
			set @siteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteResourceID#">;
			set @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">;
			set @startRow = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.posStart#">;
			set @endRow = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.posStart + local.count#">;
			
			select @siteID = siteID
			from dbo.cms_siteResources
			where siteResourceID = @siteResourceID;

			IF OBJECT_ID('tempdb..##linkedRecords') IS NOT NULL 
				DROP TABLE ##linkedRecords;
			IF OBJECT_ID('tempdb..##memberIDs') IS NOT NULL 
				DROP TABLE ##memberIDs;
			
			CREATE TABLE ##linkedRecords (memberID int PRIMARY KEY);
			CREATE TABLE ##memberIDs (memberID int PRIMARY KEY, row int);
			
			insert into ##linkedRecords (memberID)
			select masterMember.memberID
			from dbo.ams_members as m
			inner join dbo.ams_recordRelationships as rr on rr.orgID = @orgID 
				and rr.childMemberID = m.memberID
				and m.memberID = @memberID
				and rr.isActive = 1
			inner join dbo.ams_members as masterMember on rr.masterMemberID = masterMember.memberID
				and masterMember.status <> 'D'
			left outer join dbo.ams_recordTypes as rtActualMaster on rtActualMaster.recordTypeID = masterMember.RecordTypeID
			left outer join dbo.ams_recordTypes as rtActualChild on rtActualChild.recordTypeID = m.RecordTypeID
			inner join dbo.ams_recordTypesRelationshipTypes as rtrt on rtrt.recordTypeRelationshipTypeID = rr.recordTypeRelationshipTypeID
				and rtrt.isActive = 1
			inner join dbo.ams_recordRelationshipTypes as rrt on rrt.relationshipTypeID = rtrt.relationshipTypeID
			
			union
			
			select childMember.memberID
			from dbo.ams_members as m
			inner join dbo.ams_recordRelationships as rr on rr.orgID = @orgID 
				and rr.masterMemberID = m.memberID
				and m.memberID = @memberID
				and rr.isActive = 1
			inner join dbo.ams_members as childMember on rr.childMemberID = childMember.memberID
				and childMember.status <> 'D'
			left outer join dbo.ams_recordTypes as rtActualMaster on rtActualMaster.recordTypeID = m.RecordTypeID
			left outer join dbo.ams_recordTypes as rtActualChild on rtActualChild.recordTypeID = childMember.RecordTypeID
			inner join dbo.ams_recordTypesRelationshipTypes as rtrt on rtrt.recordTypeRelationshipTypeID = rr.recordTypeRelationshipTypeID
				and rtrt.isActive = 1
			inner join dbo.ams_recordRelationshipTypes as rrt on rrt.relationshipTypeID = rtrt.relationshipTypeID
			
			insert into ##memberIDs (memberID, row)
			SELECT tmp.memberid, min(row) as row
			FROM (
				SELECT mActive.memberid, ROW_NUMBER() OVER (ORDER BY #local.OrderByClause#) as row
				FROM ##linkedRecords as r
				INNER JOIN dbo.ams_members as m ON m.memberID = r.memberID
				INNER JOIN dbo.ams_members as mActive ON mActive.memberID = m.activeMemberID
				INNER JOIN dbo.cache_perms_groupPrints as gp on mActive.groupPrintID = gp.groupPrintID
				INNER JOIN dbo.cache_perms_groupPrintsRightPrints as gprp on gprp.siteID = @siteID
					and gp.groupPrintID = gprp.groupPrintID
				INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints as srfrp on srfrp.siteID = @siteID
					and srfrp.siteResourceID = @siteResourceID
					AND srfrp.functionID = @functionID
					AND gprp.rightPrintID = srfrp.rightPrintID
				
				<!--- classification sort join --->
				<cfif listLen(local.pivotGroups)>
					LEFT OUTER JOIN (
						SELECT memberID, 'grp_' + cast(groupID as varchar(10)) as groupID
						FROM dbo.cache_members_groups
						WHERE groupID IN (#local.pvtGroupList#) 
						AND orgid = @orgID
						) as sortgrps 
						PIVOT (count(groupID) FOR groupID IN (#local.pivotGroups#)) AS p 
						ON p.memberID = m.memberID
				</cfif>
				WHERE mActive.status <> 'D'
			) as tmp
			GROUP BY tmp.memberID
			ORDER BY row;
			
			SET @totalCount = @@rowcount;

			select temp.row, m.memberID, @totalCount as totalCount
			from ##memberIDs temp
			inner join dbo.ams_members m on m.memberID = temp.memberID
			where temp.row > @startRow and temp.row <= @endRow
			order by temp.row;

			IF OBJECT_ID('tempdb..##linkedRecords') IS NOT NULL 
				DROP TABLE ##linkedRecords;
			IF OBJECT_ID('tempdb..##memberIDs') IS NOT NULL 
				DROP TABLE ##memberIDs;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfset local.returnStruct.totalCount = local.qryMembers.totalCount>
		
		<cfif local.returnStruct.totalCount gt 0>
			<cfset local.linkedBaseLink = "">

			<cfquery name="local.qryViewProfilePage" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				declare @functionAppearInDirectoryID int, @functionListedAsLinkedRecordID int, @siteResourceID int, @siteID int;
				select @functionAppearInDirectoryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.AppearInDirectoryRFID#">;
				select @functionListedAsLinkedRecordID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.ListedAsLinkedRecordRFID#">;
				set @siteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteResourceID#">;
				set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.siteID#">;
				
				SELECT top 1 ai.applicationInstanceName, ai.applicationInstanceID
				FROM dbo.ams_memberDirectories as md
				INNER JOIN dbo.cms_applicationInstances as ai ON ai.applicationInstanceID = md.applicationInstanceID 
				INNER JOIN dbo.cms_siteResourceRights srr ON srr.resourceID = ai.siteResourceID and srr.siteID = @siteID
				INNER JOIN dbo.cms_siteResourceFunctions f ON srr.functionID = f.functionID 
				INNER JOIN dbo.cms_siteResourceTypeFunctions srtf ON srtf.functionID = f.functionID
				INNER JOIN dbo.cms_siteResources sr ON sr.siteResourceID = srr.resourceID AND sr.resourceTypeID = srtf.resourceTypeID 
				INNER JOIN dbo.ams_groups g ON srr.groupID = g.groupID AND g.status = 'A'
				WHERE ai.siteID = @siteID
				and srr.functionID = @functionAppearInDirectoryID
				and g.groupID in (
					SELECT g.groupID
					FROM dbo.cms_siteResourceRights srr 
					INNER JOIN dbo.cms_siteResourceFunctions f ON srr.functionID = f.functionID 
					INNER JOIN dbo.cms_siteResourceTypeFunctions srtf ON srtf.functionID = f.functionID
					INNER JOIN dbo.cms_siteResources sr ON sr.siteResourceID = srr.resourceID AND sr.resourceTypeID = srtf.resourceTypeID 
					INNER JOIN dbo.ams_groups g ON srr.groupID = g.groupID AND g.status = 'A'
					WHERE srr.resourceID = @siteResourceID
					and srr.functionID = @functionListedAsLinkedRecordID and srr.siteID = @siteID
				);

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfif local.qryViewProfilePage.recordCount>
				<cfset local.linkedBaseLink = application.objApplications.getAppBaseLink(applicationInstanceID=local.qryViewProfilePage.applicationInstanceID, siteid=local.siteID)>
			</cfif>	
		
			<cfset local.supportedSocialIcons = "facebook,linkedin,twitter,500px,deliciou,digg,rss,path,google+,vimeo,youtube,wordpress,spotify,blogger,tumblr,blog">
			<cfquery name="local.qrySocialNetwork" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select sn.socialNetworkID, sn.applicationInstanceID, sn.masterSiteID, sn.masterOrgID, sn.masterSocialNetworkID
				from dbo.sn_socialNetworks sn
				inner join dbo.cms_applicationInstances ai on ai.applicationInstanceID = sn.applicationInstanceID 
					and ai.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.siteID#">
				inner join dbo.cms_siteResources sr on sr.siteResourceID = ai.siteResourceID
				inner join dbo.cms_siteResourceStatuses srs on srs.siteResourceStatusID = sr.siteResourceStatusID 
					and srs.siteResourceStatusDesc = <cfqueryparam cfsqltype="cf_sql_varchar" value="Active">;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			
			<cfset local.memberParamStruct = structNew()>
			
			<cfif structKeyExists(session.mcStruct["deviceProfile"],"device_os") and session.mcStruct["deviceProfile"]["device_os"] eq "ios">
				<cfset local.memberParamStruct.mappingBaseLink = 'https://maps.apple.com/?q='>
			<cfelse>
				<cfset local.memberParamStruct.mappingBaseLink = 'https://maps.google.com/maps?f=q&source=s_q&hl=en&geocode=&z=16&q='>
			</cfif>
		
			<cfif structKeyExists(session.mcStruct["deviceProfile"],"is_small_screen") and session.mcStruct["deviceProfile"]["is_small_screen"]>
				<cfset local.memberParamStruct.makePhoneNumbersClickable = true>
			<cfelse>
				<cfset local.memberParamStruct.makePhoneNumbersClickable = false>
			</cfif>
			
			<cfset local.memberParamStruct.multipleAddressTypesDetected = false>
			<cfset local.arrMembers = arrayNew(1)>
			
			<cfset local.strMemberFSID = {}>
			<cfset local.strFSMembers = {}>
			<cfloop query="local.qryMembers">
				<cfif local.qryFieldset.recordcount eq 1>
					<cfset local.qryLinkedRecordFieldsetID = local.qryFieldset>
				<cfelse>
					<cfset local.qryLinkedRecordFieldsetID = getLocatorFieldsetID(siteResourceID=arguments.siteresourceID, area='details', permissionMemberID=local.qryMembers.memberID)>
				</cfif>
				<cfif local.qryLinkedRecordFieldsetID.fieldsetID gt 0>
					<cfset local.fieldSetID = local.qryLinkedRecordFieldsetID.fieldsetID>
				<cfelse>
					<cfset local.fieldSetID = local.qryFieldsetID.fieldsetID>
				</cfif>
				<cfif NOT local.strFSMembers.keyExists(local.fieldSetID)>
					<cfset local.strFSMembers[local.fieldSetID] = { "memberIDList":"", "qryViewMemberData":queryNew('') }>
				</cfif>
				<cfset local.strFSMembers[local.fieldSetID]['memberIDList'] = listAppend(local.strFSMembers[local.fieldSetID]['memberIDList'],local.qryMembers.memberID)>
				<cfset local.strMemberFSID[local.qryMembers.memberID] = local.fieldSetID>
			</cfloop>

			<cfloop collection="#local.strFSMembers#" item="local.FSID">
				<cfquery name="local.qryThisViewMemberData" datasource="#application.dsn.membercentral.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">,
						@outputFieldsXML xml;
				
					IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL
						DROP TABLE ##tmpMembers;
					IF OBJECT_ID('tempdb..##tmpMembersFS') IS NOT NULL
						DROP TABLE ##tmpMembersFS;
					CREATE TABLE ##tmpMembers (memberID int PRIMARY KEY, lastname varchar(75), firstname varchar(75), company varchar(200), mc_row int IDENTITY(1,1));
					CREATE TABLE ##tmpMembersFS (MFSAutoID int IDENTITY(1,1) NOT NULL);

					INSERT INTO ##tmpMembers (memberID, lastname, firstname, company)
					select m.memberID, m.lastname, m.firstname, m.company
					from dbo.ams_members as m
					inner join dbo.ams_members as mActive on mActive.orgID = @orgID
						and mActive.memberID = m.activeMemberID
					where m.orgID = @orgID
					and m.memberID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#local.strFSMembers[local.FSID].memberIDList#">)
					and m.isProtected = 0
					and m.status <> 'D';

					-- get fieldset data and set back to snapshot because proc ends in read committed
					EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, 
						@fieldsetIDList=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.FSID#">,
						@existingFields='m_lastname,m_firstname,m_company', @ovNameFormat=NULL, @ovMaskEmails=0,
						@membersTableName='##tmpMembers', @membersResultTableName='##tmpMembersFS', @linkedMembers=0, 
						@mode='view', @outputFieldsXML=@outputFieldsXML OUTPUT;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
					
					SELECT m.lastname, m.firstname, m.company, tmp.*,
						CASE WHEN mc_row = 1 THEN @outputFieldsXML ELSE NULL END AS mc_outputFieldsXML
					FROM ##tmpMembers AS m
					INNER JOIN ##tmpMembersFS AS tmp ON tmp.memberID = m.memberID;

					IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL
						DROP TABLE ##tmpMembers;
					IF OBJECT_ID('tempdb..##tmpMembersFS') IS NOT NULL
						DROP TABLE ##tmpMembersFS;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
				<cfset local.strFSMembers[local.FSID].qryViewMemberData = duplicate(local.qryThisViewMemberData)>
			</cfloop>

			<cfset local.orgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID=arguments.orgid, includeTags=1)>
			<cfset local.strOrgAddressTypes = structNew()>
			<cfloop query="local.orgAddressTypes">
				<cfif local.orgAddressTypes.isTag is 1>
					<cfset local.atKey = "t#local.orgAddressTypes.addressTypeID#">
				<cfelse>
					<cfset local.atKey = local.orgAddressTypes.addressTypeID>
				</cfif>
				<cfset local.strOrgAddressTypes[local.atKey] = {addressType = local.orgAddressTypes.addressType, isTag=local.orgAddressTypes.isTag}>
			</cfloop>
			
			<cfloop query="local.qryMembers">
				<cfset var memberID = local.qryMembers.memberid>
				<cfset local.fieldStruct = structNew()>
				<cfset local.fieldStruct.memberID = local.qryMembers.memberid>
				<cfset local.fieldStruct.arrPhones = ArrayNew(1)>
				<cfset local.fieldStruct.arrEmails = ArrayNew(1)>
				<cfset local.fieldStruct.arrWebsites = ArrayNew(1)>
				<cfset local.fieldStruct.arrWebsitesSocialIcons = ArrayNew(1)>
				<cfset local.fieldStruct.arrMemberDataDistricting = ArrayNew(1)>
				<cfset local.fieldStruct.arrAboveClassifications = ArrayNew(1)>
				<cfset local.fieldStruct.arrBelowClassifications = ArrayNew(1)>
				<cfset local.fieldStruct.arrUnderClassifications = ArrayNew(1)>
				<cfset local.fieldStruct.arrClValues = ArrayNew(1)>
				<cfset local.fieldStruct.arrLicenseData = ArrayNew(1)>
				<cfset local.fieldStruct.memberPermissions = structNew()>
				
				<cfset local.fieldSetID = local.strMemberFSID[local.qryMembers.memberID]>
				<cfset local.qryViewMemberData = QueryFilter(local.strFSMembers[local.fieldSetID].qryViewMemberData, function(thisRow) { return arguments.thisRow.memberID EQ memberID })>
				<cfset local.xmlResultFields = XmlParse(local.strFSMembers[local.fieldSetID].qryViewMemberData.mc_outputFieldsXML[1])>
				
				<cfset local.strLogStats = {}>
				<cfset local.stThisMember = getMember(
							memberDirectoryID=arguments.memberDirectoryID,
							memberID=local.qryMembers.memberid,
							orgID=arguments.orgID,
							logStats=local.strLogStats)>

				<!--- Get the permissions for this member --->
				<cfif not structKeyExists(local.fieldStruct.memberPermissions,local.stThisMember.qryMember.groupPrintID)>
					<cfset local.fieldStruct.memberPermissions[local.stThisMember.qryMember.groupPrintID] = application.objSiteResource.buildCachePermRightAssignmentsForGroupPrint(siteResourceID=arguments.siteresourceID, siteID=local.siteID, groupPrintID=local.stThisMember.qryMember.groupPrintID)>
				</cfif>
				
				<cfif local.fieldStruct.memberPermissions[local.stThisMember.qryMember.groupPrintID].ListedAsLinkedRecord>
					<cfset local.fieldStruct.stFullName = local.qryViewMemberData["Extended Name"]>
					
					<!--- combine address fields if there are any --->
					<cfset local.fieldStruct.mc_combinedAddresses = arrayNew(1)>
					<cfset local.fieldStruct.mc_combinedAddressArrayMap = structNew()>
					<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,3)='ma_' or substring(@fieldCode,1,4)='mat_']")>
					<cfloop array="#local.tmp#" index="local.thisField">
						<cfset local.thisATID = listGetAt(local.thisField.xmlattributes.fieldcode,2,"_")>
						<cfif listFindNoCase("mat,mpt",listGetAt(local.thisField.xmlattributes.fieldcode,1,"_"))>
							<cfset local.strKey = "t#local.thisATID#">
						<cfelse>
							<cfset local.strKey = local.thisATID>
						</cfif>
						<cfif NOT structKeyExists(local.fieldStruct.mc_combinedAddressArrayMap, local.strKey)>
							<cfif local.strOrgAddressTypes[local.strKey].isTag>
								<cfset local.typeLabel = replaceNoCase(local.strOrgAddressTypes[local.strKey].addressType, "Designated ", "")>
							<cfelse>
								<cfset local.typeLabel = local.strOrgAddressTypes[local.strKey].addressType>
							</cfif>
							<cfset local.newAddrTemplate = {key=local.strKey, addr='', type=local.strOrgAddressTypes[local.strKey].addressType, label=local.typeLabel }>
							<cfset arrayAppend(local.fieldStruct.mc_combinedAddresses, local.newAddrTemplate)>
							<cfset local.fieldStruct.mc_combinedAddressArrayMap[local.strKey] = arrayLen(local.fieldStruct.mc_combinedAddresses)>
						</cfif>
					</cfloop>
					
					<cfset local.elementsToDelete = "">
					
					<cfloop index="local.i" from="1" to="#arrayLen(local.fieldStruct.mc_combinedAddresses)#">
			
						<cfset local.thisATID = local.fieldStruct.mc_combinedAddresses[local.i].key>
						<cfif left(local.thisATID,1) eq "t">
							<cfset local.MAfcPrefix = "mat_#right(local.thisATID,len(local.thisATID)-1)#_">
							<cfset local.MPfcPrefix = "mpt_#right(local.thisATID,len(local.thisATID)-1)#_">
						<cfelse>
							<cfset local.MAfcPrefix = "ma_#local.thisATID#_">
							<cfset local.MPfcPrefix = "mp_#local.thisATID#_">
						</cfif>
			
						<cfset local.thisAddressStruct = {
							address1="",
							address2="",
							address3="",
							city="",
							stateprov="",
							postalcode="",
							county="",
							country=""
						}>
						<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address1']")>
						<cfif arrayLen(local.tmp) is 1 and len(local.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1])>
							<cfset local.thisAddressStruct["address1"] = local.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1] />
						</cfif>
						<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address2']")>
						<cfif arrayLen(local.tmp) is 1 and len(local.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1])>
							<cfset local.thisAddressStruct["address2"] = local.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1]>
						</cfif>
						<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address3']")>
						<cfif arrayLen(local.tmp) is 1 and len(local.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1])>
							<cfset local.thisAddressStruct["address3"] = local.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1]>
						</cfif>
						<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#city']")>
						<cfif arrayLen(local.tmp) is 1 and len(local.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1])>
							<cfset local.thisAddressStruct["city"] = local.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1]>
						</cfif>
						<cfset local.tmp2 = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#stateprov']")>
						<cfif arrayLen(local.tmp2) is 1 and len(local.qryViewMemberData[local.tmp2[1].xmlAttributes.fieldLabel][1])>
							<cfset local.thisAddressStruct["stateprov"] = local.qryViewMemberData[local.tmp2[1].xmlAttributes.fieldLabel][1]>
						</cfif>
						<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#postalcode']")>
						<cfif arrayLen(local.tmp) is 1 and len(local.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1])>
							<cfset local.thisAddressStruct["postalcode"] = local.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1]>
						</cfif>
						<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#county']")>
						<cfif arrayLen(local.tmp) is 1 and len(local.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1])>
							<cfset local.thisAddressStruct["county"] = local.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1] & " County"><br/>
						</cfif>
						<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#country']")>
						<cfif arrayLen(local.tmp) is 1 and len(local.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1])>
							<cfset local.thisAddressStruct["country"] = local.qryViewMemberData[local.tmp[1].xmlAttributes.fieldLabel][1]>
						</cfif>
						<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,#len(local.MPfcPrefix)#)='#local.MPfcPrefix#']")>
			
						<cfsavecontent variable="local.thisATFull">
							<cfoutput>
								<div itemprop="address" itemscope itemtype="http://schema.org/PostalAddress">
									<cfif len(local.thisAddressStruct["address1"]) or len(local.thisAddressStruct["address2"]) or len(local.thisAddressStruct["address3"])>
										<span itemprop="streetAddress">
											<cfif len(local.thisAddressStruct["address1"])>#local.thisAddressStruct["address1"]#<br /></cfif>
											<cfif len(local.thisAddressStruct["address2"])>#local.thisAddressStruct["address2"]#<br /></cfif>
											<cfif len(local.thisAddressStruct["address3"])>#local.thisAddressStruct["address3"]#<br /></cfif>
										</span>
									</cfif>
									<cfif len(local.thisAddressStruct["city"])><span itemprop="addressLocality">#local.thisAddressStruct["city"]#</span></cfif><cfif len(local.thisAddressStruct["stateprov"])>, <span itemprop="addressRegion">#local.thisAddressStruct["stateprov"]#</span></cfif>
									<cfif len(local.thisAddressStruct["postalcode"])> <span itemprop="postalCode">#local.thisAddressStruct["postalcode"]#</span></cfif>
									<cfif len(local.thisAddressStruct["address1"]) or len(local.thisAddressStruct["address2"]) or len(local.thisAddressStruct["address3"])>
										<br />
									</cfif>
									<cfif len(local.thisAddressStruct["county"])>#local.thisAddressStruct["county"]# <br /></cfif>
									<cfif len(local.thisAddressStruct["country"])><span itemprop="addressCountry">#local.thisAddressStruct["country"]#</span><br /></cfif>
								</div>
			 				</cfoutput>
						</cfsavecontent>
						<cfsavecontent variable="local.thisATMap">
							<cfoutput>
								<cfif len(local.thisAddressStruct["address1"])>#local.thisAddressStruct["address1"]#</cfif>
								<cfif len(local.thisAddressStruct["address2"])>#local.thisAddressStruct["address2"]#</cfif>
								<cfif len(local.thisAddressStruct["address3"])>#local.thisAddressStruct["address3"]#</cfif>
								<cfif len(local.thisAddressStruct["city"])>#local.thisAddressStruct["city"]#</cfif><cfif len(local.thisAddressStruct["stateprov"])>, #local.thisAddressStruct["stateprov"]#</cfif><cfif len(local.thisAddressStruct["postalcode"])> #local.thisAddressStruct["postalcode"]#</cfif>
								<cfif len(local.thisAddressStruct["county"])>#local.thisAddressStruct["county"]# <br /></cfif>
								<cfif len(local.thisAddressStruct["country"])>#local.thisAddressStruct["country"]#</cfif>
			 				</cfoutput>
						</cfsavecontent>
			
						<cfset local.thisATfull = trim(replace(replace(local.thisATFull,'  ',' ','ALL'),' ,',',','ALL'))>
						<cfif left(local.thisATfull,2) eq ", ">
							<cfset local.thisATfull = right(local.thisATfull,len(local.thisATfull)-2)>
						</cfif>
						
						<cfif len(local.thisATMap)>
							<cfset local.fieldStruct.mc_combinedAddresses[local.i]['addr'] = local.thisATfull>
							<cfset local.fieldStruct.mc_combinedAddresses[local.i]['mapaddr'] = local.thisATMap>
						<cfelse>
							<cfset local.elementsToDelete = listPrepend(local.elementsToDelete, local.i)>
						</cfif>
					</cfloop>
					<cfloop index="local.i" list="#local.elementsToDelete#"> 
						<cfset arrayDeleteAt(local.fieldStruct.mc_combinedAddresses, local.i)>
					</cfloop> 
			
					<cfif arrayLen(XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_membernumber']"))>
						<cfset local.fieldStruct.stMemberNumber = local.stThisMember.qryMember.membernumber>
					<cfelse>
						<cfset local.fieldStruct.stMemberNumber = ''>
					</cfif>
			
					<cfif arrayLen(XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_company']")) and len(local.stThisMember.qryMember.company)>
						<cfset local.fieldStruct.stCompany = local.stThisMember.qryMember.company>
					<cfelse>
						<cfset local.fieldStruct.stCompany = ''>
					</cfif>
			
					<cfset local.fieldStruct.imgToUse = ''>
					<cfif local.stThisMember.qryMember.hasMemberPhotoThumb is 1>
						<cfset local.fieldStruct.imgToUse = '<img class="tsAppDirPhoto" itemprop="image" src="/memberphotosth/#LCASE(local.stThisMember.qryMember.membernumber)#.jpg">'>
					</cfif>
		
					<cfloop array="#local.xmlResultFields.xmlRoot.xmlChildren#" index="local.thisField">
						<cfif ReFindNoCase('mp_[0-9]+_[0-9]+',local.thisField.xmlattributes.fieldcode) and len(local.qryViewMemberData[local.thisField.xmlattributes.fieldLabel][1])>
							<!--- add phone --->
							<cfset local.memdataentry = StructNew()>
							<cfset local.memdataentry.fieldLabel = local.thisField.xmlattributes.fieldLabel>
							<cfset local.memdataentry.phone = local.qryViewMemberData[local.thisField.xmlattributes.fieldLabel][1]>
							<cfset arrayAppend(local.fieldStruct.arrPhones, local.memdataentry)>
						<cfelseif ReFindNoCase('mpt_[0-9]+_[0-9]+',local.thisField.xmlattributes.fieldcode) and len(local.qryViewMemberData[local.thisField.xmlattributes.fieldLabel][1])>
							<!--- add phone --->
							<cfset local.memdataentry = StructNew()>
							<cfset local.memdataentry.fieldLabel = local.thisField.xmlattributes.fieldLabel>
							<cfset local.memdataentry.phone = local.qryViewMemberData[local.thisField.xmlattributes.fieldLabel][1]>
							<cfset arrayAppend(local.fieldStruct.arrPhones, local.memdataentry)>
			
						<cfelseif (ReFindNoCase('me_[0-9]+_email',local.thisField.xmlattributes.fieldcode) or ReFindNoCase('met_[0-9]+_email',local.thisField.xmlattributes.fieldcode)) and len(local.qryViewMemberData[local.thisField.xmlattributes.fieldLabel][1])>
							<!--- add email --->
							<cfset local.memdataentry = StructNew()>
							<cfset local.memdataentry.fieldLabel = local.thisField.xmlattributes.fieldLabel>
							<cfset local.memdataentry.email = local.qryViewMemberData[local.thisField.xmlattributes.fieldLabel][1]>
							<cfset arrayAppend(local.fieldStruct.arrEmails, local.memdataentry)>
			
						<cfelseif ReFindNoCase('mw_[0-9]+_website',local.thisField.xmlattributes.fieldcode) and len(local.qryViewMemberData[local.thisField.xmlattributes.fieldLabel][1])>
							<cfif listFindNoCase(local.supportedSocialIcons, local.thisField.xmlattributes.fieldLabel)>
								<cfset local.memdataentry = StructNew()>
								<cfset local.memdataentry.fieldLabel = local.thisField.xmlattributes.fieldLabel>
								<cfset local.memdataentry.website = local.qryViewMemberData[local.thisField.xmlattributes.fieldLabel][1]>
								<cfset local.memdataentry.socialLink = getSocialIconLink(local.memdataentry)>
								<cfset arrayAppend(local.fieldStruct.arrWebsitesSocialIcons, local.memdataentry)>
							<cfelse>
								<!--- add website --->
								<cfset local.memdataentry = StructNew()>
								<cfset local.memdataentry.socialLink = "">
								<cfset local.memdataentry.fieldLabel = local.thisField.xmlattributes.fieldLabel>
								<cfset local.memdataentry.website = local.qryViewMemberData[local.thisField.xmlattributes.fieldLabel][1]>
								<cfset arrayAppend(local.fieldStruct.arrWebsites, local.memdataentry)>					
							</cfif>
			
						<cfelseif (ReFindNoCase('md_[0-9]+',local.thisField.xmlattributes.fieldcode) or ReFindNoCase('mad_[0-9]+_[0-9]+',local.thisField.xmlattributes.fieldcode)) and len(local.qryViewMemberData[local.thisField.xmlattributes.fieldLabel][1])>
							<!--- add member data / districting --->
							<cfset local.memdataentry = StructNew()>
							<cfset local.memdataentry.fieldLabel = local.thisField.xmlattributes.fieldLabel>
							<cfset local.memdataentry.value = local.qryViewMemberData[local.thisField.xmlattributes.fieldLabel][1]>
							<cfset local.memdataentry.allowMultiple = local.thisField.xmlAttributes.allowMultiple>
							<cfset arrayAppend(local.fieldStruct.arrMemberDataDistricting, local.memdataentry)>

						<cfelseif ReFindNoCase('mpl_[0-9]+_[a-z]+',local.thisField.xmlattributes.fieldcode) and len(local.qryViewMemberData[local.thisField.xmlattributes.fieldLabel][1])>
							<cfset local.memdataentry = StructNew()>
							<cfset local.memdataentry.fieldLabel = local.thisField.xmlattributes.fieldLabel>
							<cfset local.memdataentry.value = local.qryViewMemberData[local.thisField.xmlattributes.fieldLabel][1]>
							<cfif listFindNoCase(local.thisField.xmlattributes.fieldcode,"activeDate","_")>
								<cfset local.memdataentry.value = dateFormat(local.memdataentry.value, "mm/dd/yyyy")>
							</cfif>
							<cfset arrayAppend(local.fieldStruct.arrLicenseData, local.memdataentry)>
						</cfif>
					</cfloop>
			
					<cfif structKeyExists(local.stThisMember, "classifications")>		
						<cfloop query="local.stThisMember.classifications">	
							<cfset local.clStruct = structNew()>
							<cfset local.clStruct.groupID = local.stThisMember.classifications.groupID>
							<cfset local.clStruct.imageext = local.stThisMember.classifications.imageext>
							<cfset local.clStruct.groupName = local.stThisMember.classifications.groupName>
							<cfset local.clStruct.clName = replace(local.stThisMember.classifications.name, '_', ' ', 'ALL')>
			
							<cfif local.stThisMember.classifications.showGroupImageInSearchDetail is 1
									AND FileExists('#application.paths.localUserAssetRoot.path#common/groupImages/#local.stThisMember.classifications.groupID#.#local.stThisMember.classifications.imageext#')> 
								<cfif local.stThisMember.classifications.imagePlacement eq "above">
									<cfset arrayAppend(local.fieldStruct.arrAboveClassifications, local.clStruct)>
								</cfif>	
								<cfif local.stThisMember.classifications.imagePlacement eq "below">
									<cfset arrayAppend(local.fieldStruct.arrBelowClassifications, local.clStruct)>
								</cfif>	
								<cfif local.stThisMember.classifications.imagePlacement eq "under">
									<cfset arrayAppend(local.fieldStruct.arrUnderClassifications, local.clStruct)>
								</cfif>	
							</cfif>
							<cfif local.stThisMember.classifications.showInSearchDetail is 1>
								<cfset arrayAppend(local.fieldStruct.arrClValues, local.clStruct)>
							</cfif>						
			
						</cfloop>
					</cfif>
		
					<cfset local.fieldStruct.qryViewMemberData = local.qryViewMemberData>
	
					<cfset StructAppend(local.fieldStruct,local.stThisMember)>
					<cfset arrayAppend(local.arrMembers,local.fieldStruct)>
	
					<cfif not local.memberParamStruct.multipleAddressTypesDetected and arrayLen(local.fieldStruct.mc_combinedAddresses) gt 1>
						<cfset local.memberParamStruct.multipleAddressTypesDetected = true>
					</cfif>
				</cfif>
			</cfloop>
			
			<cfset local.memberParamStruct.photoFound = false>
			<cfloop array="#local.arrMembers#" index="local.thisMember">
				<cfif local.thisMember['memberPermissions'][local.thisMember.qrymember.groupPrintID].resultsShowPhoto>
					<cfset local.memberParamStruct.photoFound = false>
					<cfbreak>
				</cfif>
			</cfloop>
			<cfset local.baseQryStr = arguments.baseQryStr/>
			<cfloop array="#local.arrMembers#" index="local.thisMember">
				<cfset local.thisMemberPerms = local.thisMember['memberPermissions'][local.thisMember.qrymember.groupPrintID]>
				<cfif local.thisMemberPerms.resultsShowDetailsLink and (
						(local.thisMemberPerms.resultsShowVcard neq local.thisMemberPerms.detailsShowVcard) 
						or (local.thisMemberPerms.resultsShowPhoto neq local.thisMemberPerms.detailsShowPhoto)
						or (local.thisMemberPerms.resultsShowMap neq local.thisMemberPerms.detailsShowMap)
					)>
					<cfset local.memberParamStruct.thisMemberShowDetailLink = true>
				<cfelseif len(local.linkedBaseLink)>					
					<cfset local.baseQryStr = local.linkedBaseLink/>
					<cfset local.memberParamStruct.thisMemberShowDetailLink = true>
				<cfelse>
					<cfset local.memberParamStruct.thisMemberShowDetailLink = false>
				</cfif>
				
				<cfset local.tmpHolder = ''>
				<cfset local.tmpHolder = getLinkedRecordsListData(siteResourceID=arguments.siteResourceID, thisMember=local.thisMember, baseQryStr=local.baseQryStr, memberParamStruct=local.memberParamStruct)>
				<cfset arrayAppend(local.returnStruct.data, application.objCommon.minText(local.tmpHolder))>
			</cfloop>
		</cfif>
		
		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="getLinkedRecordsListData" access="private" output="false" returntype="string">
		<cfargument name="siteResourceID" type="numeric" required="yes">
		<cfargument name="thisMember" type="struct" required="yes">
		<cfargument name="baseQryStr" type="string" required="yes">
		<cfargument name="memberParamStruct" type="struct" required="yes">
		
		<cfset local = structNew()>
		<cfset local.gridClasses = structNew()>
		
		<cfif arguments.memberParamStruct.photoFound>
			<cfset local.gridClasses.photo = "span4">
			<cfset local.gridClasses.fieldset = "span8">
		<cfelse>
			<cfset local.gridClasses.photo = "span4">
			<cfset local.gridClasses.fieldset = "span8">
		</cfif>
		
		<cfsavecontent variable="local.returnData">
			<cfoutput>
			<div class="span12 well">
				<div class="container-fluid">
					<div class="row-fluid"></div>
					<div class="row-fluid">
					<cfif arguments.thisMember['memberPermissions'][arguments.thisMember.qrymember.groupPrintID].resultsShowPhoto>
						<div class="memberDirectory-photo #local.gridClasses.photo# clearfix" style="padding-bottom: 15px;">
							<cfif arrayLen(arguments.thisMember.arrAboveClassifications)>
								<cfloop array="#arguments.thisMember.arrAboveClassifications#" index="local.thisImg">
									<div class="pull-left"><img class="directory-groupImage directory-groupImage-aboveClassifications" src="/userassets/common/groupImages/#local.thisImg.groupID#.#local.thisImg.imageext#?rand=#randrange(1,1000)#" alt="#local.thisImg.groupName#" title="#local.thisImg.groupName#" /></div>
								</cfloop>
								<br clear="all"/>
							</cfif>
							<cfset local.imgToUse = arguments.thisMember.imgToUse>
							<cfif len(trim(local.imgToUse))>
								<a href="/?#arguments.baseQryStr#&dirAction=memberDetails&dirMemberid=#arguments.thisMember.memberID#">#local.imgToUse#</a>
							</cfif>
							<cfif arrayLen(arguments.thisMember.arrBelowClassifications)>
								<br clear="all"/>
								<cfloop array="#arguments.thisMember.arrBelowClassifications#" index="local.thisImg">
									<div class="pull-left"><img class="directory-groupImage directory-groupImage-belowClassifications" src="/userassets/common/groupImages/#local.thisImg.groupID#.#local.thisImg.imageext#?rand=#randrange(1,1000)#" alt="#local.thisImg.groupName#" title="#local.thisImg.groupName#" /></div>
								</cfloop>
								<br clear="all"/>
							</cfif>
						</div>
					<cfelse>
						<div class="#local.gridClasses.photo# clearfix" style="margin-bottom:10px;">
							<cfif arrayLen(arguments.thisMember.arrAboveClassifications)>
								<cfloop array="#arguments.thisMember.arrAboveClassifications#" index="local.thisImg">
									<div class="thumbnail pull-left"><img class="directory-groupImage directory-groupImage-aboveClassifications" src="/userassets/common/groupImages/#local.thisImg.groupID#.#local.thisImg.imageext#?rand=#randrange(1,1000)#" alt="#local.thisImg.groupName#" title="#local.thisImg.groupName#" /></div>
								</cfloop>
							</cfif>
							<cfif arrayLen(arguments.thisMember.arrBelowClassifications)>
								<cfloop array="#arguments.thisMember.arrBelowClassifications#" index="local.thisImg">
									<div class="thumbnail pull-left"><img class="directory-groupImage directory-groupImage-belowClassifications" src="/userassets/common/groupImages/#local.thisImg.groupID#.#local.thisImg.imageext#?rand=#randrange(1,1000)#" alt="#local.thisImg.groupName#" title="#local.thisImg.groupName#" /></div>
								</cfloop>
							</cfif>
						</div>
					</cfif>
					<div class="#local.gridClasses.fieldset# clearfix" style="margin-bottom:10px;">
						<!--- name/company --->
						<cfif len(arguments.thisMember.stFullName)>
							<b>#arguments.thisMember.stFullName#</b><br/>
						</cfif>
						<cfif not len(arguments.thisMember.stFullName) and len(arguments.thisMember.stCompany)>
							<b>#arguments.thisMember.stCompany#</b><br/>
						<cfelseif len(arguments.thisMember.stCompany)>
							#arguments.thisMember.stCompany#<br/>
						</cfif>
						
						<cfif arrayLen(arguments.thisMember.arrClValues)>
							<br />
							<cfset local.clTitle = "">
							<cfloop array="#arguments.thisMember.arrClValues#" index="local.thisClItem">
								<cfif local.clTitle neq local.thisClItem.clName>
									<cfif local.clTitle neq ""><br/></cfif>
									<cfset local.clTitle = local.thisClItem.clName>
									<b>#local.thisClItem.clName#</b><br>
								</cfif>
								#local.thisClItem.groupName#<br/>
							</cfloop>
						</cfif>
						<cfif arrayLen(arguments.thisMember.arrUnderClassifications)>
							<ul class="groupImage">
							<cfloop array="#arguments.thisMember.arrUnderClassifications#" index="local.thisImg">
								<li><img class="directory-groupImage directory-groupImage-underClassifications" src="/userassets/common/groupImages/#local.thisImg.groupID#.#local.thisImg.imageext#?rand=#randrange(1,1000)#" alt="#local.thisImg.groupName#" title="#local.thisImg.groupName#" /></li>
							</cfloop>
							</ul>
						</cfif>
						
						<!--- addresses --->
						<cfif arrayLen(arguments.thisMember.mc_combinedAddresses)>
							<cfloop index="local.addri" from="1" to="#arrayLen(arguments.thisMember.mc_combinedAddresses)#">
								<cfif len(arguments.thisMember.mc_combinedAddresses[local.addri].addr)>
									<br/>
									<cfif arguments.memberParamStruct.multipleAddressTypesDetected>
										<b>#arguments.thisMember.mc_combinedAddresses[local.addri].label#</b><br/>
									</cfif>
									#arguments.thisMember.mc_combinedAddresses[local.addri].addr#
								</cfif>	
							</cfloop>
							<br/>
						</cfif>

						<!--- phones --->
						<cfloop array="#arguments.thisMember.arrPhones#" index="local.thisField">
							<cfif len(local.thisField.phone) and arguments.memberParamStruct.makePhoneNumbersClickable>
								#htmlEditFormat(local.thisField.fieldLabel)#: <a href="tel:#local.thisField.phone#">#local.thisField.phone#</a><br/>
							<cfelseif len(local.thisField.phone)>
								#htmlEditFormat(local.thisField.fieldLabel)#: #local.thisField.phone#<br/>
							</cfif>
						</cfloop>
						<!--- emails --->
						<cfloop array="#arguments.thisMember.arrEmails#" index="local.thisField">
							<cfif len(local.thisField.email)>
								<div style="white-space:nowrap;overflow:hidden;text-overflow: ellipsis;">
									#htmlEditFormat(local.thisField.fieldLabel)#: <a href="mailto:#local.thisField.email#">#local.thisField.email#</a><br/>
								</div>
							</cfif>
						</cfloop>
						<!--- websites --->
						<cfloop array="#arguments.thisMember.arrWebsites#" index="local.thisField">
							<cfif len(local.thisField.website)>
								<div style="white-space:nowrap;overflow:hidden;text-overflow: ellipsis;">
								#htmlEditFormat(local.thisField.fieldLabel)#: <a href="#local.thisField.website#" target="_blank">#local.thisField.website#</a><br/>
								</div>
							</cfif>
						</cfloop>
	
						<!--- membernumber --->
						<cfif len(arguments.thisMember.stMemberNumber)><div>#arguments.thisMember.stMemberNumber#</div></cfif>

						<!--- memberdata / districting --->
						<cfloop array="#arguments.thisMember.arrMemberDataDistricting#" index="local.thisField">
							<cfif len(local.thisField.value)>
								#htmlEditFormat(local.thisField.fieldLabel)#: 
									<cfif local.thisField.allowMultiple is 1>
										#ReplaceNoCase(local.thisField.value,"|",", ","ALL")#<br/>
									<cfelse>
										#local.thisField.value#<br/>
									</cfif>
							</cfif>
						</cfloop>
						
						<!--- member prof license data --->
						<cfloop array="#arguments.thisMember.arrLicenseData#" index="local.thisField">
							<cfif len(local.thisField.value)>
								#htmlEditFormat(local.thisField.fieldLabel)#: #local.thisField.value#<br/>
							</cfif>
						</cfloop>

						<!--- website social icons --->
						<cfif ArrayLen(arguments.thisMember.arrWebsitesSocialIcons) gt 0 OR
								arguments.thisMember['memberPermissions'][arguments.thisMember.qrymember.groupPrintID].resultsShowVcard or 
									(arguments.thisMember['memberPermissions'][arguments.thisMember.qrymember.groupPrintID].resultsShowMap and arrayLen(arguments.thisMember.mc_combinedAddresses) and len(arguments.thisMember.mc_combinedAddresses[1].mapaddr))>
							<div style="padding:2px;">
								<cfloop array="#arguments.thisMember.arrWebsitesSocialIcons#" index="local.thisField">
									#local.thisField.socialLink#
								</cfloop>

								<!--- VCARD / Google Maps link --->
								<cfif arguments.thisMember['memberPermissions'][arguments.thisMember.qrymember.groupPrintID].resultsShowVcard>
									<a href="/?event=cms.showResource&dirAction=memberDetailsVCard&resID=#arguments.siteResourceID#&mode=stream&dirMemberid=#arguments.thisMember.memberID#">
										<img src="/assets/common/images/vcard-icon3.png" alt="[vCard]" width="32" height="32" border="0">
									</a>
								</cfif>
								<cfif arguments.thisMember['memberPermissions'][arguments.thisMember.qrymember.groupPrintID].resultsShowMap and arrayLen(arguments.thisMember.mc_combinedAddresses) and len(arguments.thisMember.mc_combinedAddresses[1].mapaddr)>
									<cfset local.thisMap = arguments.memberParamStruct.mappingBaseLink & URLEncodedFormat(arguments.thisMember.mc_combinedAddresses[1].mapaddr)>
									<a href="#local.thisMap#" target="_blank"><img src="/assets/common/images/maps-icon.png" alt="[View Map]" width="32" height="32" border="0" align="absmiddle"></a>
								</cfif>
							</div>
						</cfif>
						<cfif arguments.memberParamStruct.thisMemberShowDetailLink>							
							<br /><br />
							<button type="button" class="btn btn-info" onclick="self.location.href='/?#arguments.baseQryStr#&dirAction=memberDetails&dirMemberid=#arguments.thisMember.memberID#';">View Full Profile</button>						
						</cfif>
					</div>
					
					</div>
				</div>
			</div>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn local.returnData>
	</cffunction>

	<cffunction name="getMemberDirectoryClassificationsForMemberID" access="public" output="false" returntype="array">
		<cfargument name="memberDirectoryID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		<cfset local.classifications = arrayNew(1)>

		<cfquery name="local.getClassifications" datasource="#application.dsn.memberCentral.dsn#">
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select mdc.classificationID, isnull(nullif(mdc.name,''),mgs.groupSetName) as name
			from dbo.ams_memberDirectoryClassifications mdc
			inner join dbo.ams_memberGroupSets as mgs on mgs.groupSetID = mdc.groupSetID
			where mdc.memberDirectoryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberDirectoryID#">
			order by mdc.classificationOrder;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfquery name="local.qryMemberClassifications" datasource="#application.dsn.memberCentral.dsn#">
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select distinct mg.memberID, g.groupID, g.imageExt, isnull(nullif(mgsg.labelOverride,''),g.groupName) as groupName, 
				g.groupDesc, mdc.ClassificationID, mdc.showInSearchResults, mdc.showGroupImage, mdc.imagePlacement
			from dbo.ams_memberDirectoryClassifications as mdc   
			inner join dbo.ams_memberGroupSets as mgs on mgs.groupSetID = mdc.groupSetID
				and mdc.ClassificationID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="yes" value="#ValueList(local.getClassifications.classificationID)#">)
			inner join dbo.ams_memberGroupSetGroups as mgsg on mgsg.groupSetID = mgs.groupSetID
			inner join dbo.cache_members_groups as mg on mg.orgID = mgs.orgID
				AND mg.groupID = mgsg.groupID
				and mg.memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
			inner join dbo.ams_groups AS g on g.groupID = mg.groupID
			where g.status <> 'D'
			order by mg.memberID, mdc.ClassificationID, groupName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfloop query="local.getClassifications">
			<cfset local.thisGroupName = replace(reReplace(local.getClassifications.name,"[^a-zA-Z0-9]"," ","ALL"),' ','_','ALL')>
			
			<cfquery name="local.qryClass" dbtype="query">
				select memberID, groupID, imageExt, groupName, groupDesc, classificationID,showInSearchResults,showGroupImage,imagePlacement
				from [local].qryMemberClassifications
				where classificationID = #local.getClassifications.classificationID#
			</cfquery>

			<cfset local.classificationsStruct = structNew()>
			<cfset structInsert(local.classificationsStruct,"qryClass",local.qryClass)>
			<cfset structInsert(local.classificationsStruct,"name",replace(reReplace(local.getClassifications.name,"[^a-zA-Z0-9]"," ","ALL"),' ','_','ALL'))>
			<cfset arrayAppend(local.classifications,duplicate(local.classificationsStruct))>				
		</cfloop>

		<cfreturn local.classifications>
	</cffunction>

</cfcomponent>