$(document).ready(function(){
    $('.fa').each(function() {
        if ($.trim($(this).html()) == '&nbsp;') {
            $(this).html('');
        }
    });
    $('.fas').each(function() {
        if ($.trim($(this).html()) == '&nbsp;') {
            $(this).html('');
        }
    });
    
    $('.menuHolder > ul > li').addClass('menu-item');
    $('.menuHolder > ul > li:first-child').addClass('home-menu-item');
    
    $("#headerTop > #headerBtns > a:first-child").addClass('headerLink');
    $("#headerContent .headerHelpLink a").addClass('headerLink');
    $('#headerBtns .headerButton > a').eq(0).addClass('button tiny signin myAccount');
    $('#headerBtns .headerButton > a').eq(1).addClass('button tiny join');
    $("#headerBtns .headerButton > a:last-child").addClass('button tiny donate');
    $("#headerBtns .headerButton > a").unwrap();
   
    if($('#headerBtns > a.myAccount').length > 0 && $('#headerBtns > a.loggedIn').length > 0){
        var _myaccountTxt = $('#headerBtns > a.myAccount').text();
        var _myaccountHtml = $('#headerBtns > a.myAccount').html().replaceAll(_myaccountTxt,'');
        $('#headerBtns > a.myAccount').html(_myaccountHtml);
        $('#headerBtns > a.myAccount').append('<span class="visible-desktop-inline">'+_myaccountTxt+'</span>');
    }    
    var headerContentWrapper = $('.headerContentWrapper > ul > li');

    breadcrumb = $('.breadcrumbHolder').html();
    if(headerContentWrapper.length) {
        var title="";
        var dateWrapper="";
        var description="";
        var link="";
        var i = 0;

        if(headerContentWrapper.length > i){
            title = '<h1 class="headTitleExist">'+$('h1',headerContentWrapper[i]).html()+'</h1>';
        }
        
        if(headerContentWrapper.length == 5){
            i=i+1;
            if(headerContentWrapper.length > i){
                dateWrapper = '<span class="date">'+$(headerContentWrapper[i]).html()+'</span>';
            }
        }else if(headerContentWrapper.length == 6){
            i=i+1;
            if($.trim($(headerContentWrapper[i]).html().replace(/\&nbsp;/g, '')).length){
                if(headerContentWrapper.length > i){
                    dateWrapper = '<span class="date">'+$(headerContentWrapper[i]).html()+'</span>';
                }
            }
            i=i+1;
            if(dateWrapper.length){
                dateWrapper = dateWrapper + ' - ';
            }
            if($.trim($(headerContentWrapper[i]).html().replace(/\&nbsp;/g, '')).length){
                if(headerContentWrapper.length > i){
                    dateWrapper = dateWrapper + '<span class="date">'+$(headerContentWrapper[i]).html()+'</span>';
                }
            }
        }

        if(dateWrapper.length){
            dateWrapper =  '<div class="post-meta">'+dateWrapper+'</div>';
        }
        i=i+1;
        if(headerContentWrapper.length > i){
            description = '<div class="bannerTxt">'+$(headerContentWrapper[i]).html()+'</div>';
        }
        i=i+1;
        if($('a',headerContentWrapper[i]).length){
            if(headerContentWrapper.length > i){
                link = '<div class="rowLinks bannerLinks"><a href="'+$('a',headerContentWrapper[i]).attr('href')+'" class="button">'+$('a',headerContentWrapper[i]).html()+'</a>';
            }
        }
        i=i+1;
        if($('a',headerContentWrapper[i]).length){
            if(headerContentWrapper.length > i){
                link = link+'<a href="'+$('a',headerContentWrapper[i]).attr('href')+'" class="rowLink">'+$('a',headerContentWrapper[i]).html()+'</a></div>';
            }
        }
        $('.headerContentHolder').replaceWith(dateWrapper+title+breadcrumb+description+link);
    }
    
    if($(".headTitleExist").length == 0){
        $(".headTitle").show();
        $('.headerContentHolder').html(breadcrumb);
    }

    $('.headerBackgroundHolder').css('background', 'url(' + $('img','.headerBackgroundWrapper').attr('src') + ') no-repeat');

    $('.sidebarNavLinks > ul').addClass('sidebar-nav');

    var sidebarAlertText = '';
    if(($('.sidebarAlertWrapper ul')).length){
        var sidebarAlertWrapper = $('.sidebarAlertWrapper ul');
        sidebarAlertWrapper.each(function() {
            if ($($('li', $(this))[0]).length) {
                sidebarAlertText = sidebarAlertText + '<h3 class="widget-title"><i class="fas fa-flag-alt"></i>' + $($('li', $(this))[0]).html()+'</h3>';
            }
            if ($($('li', $(this))[1]).length) {
                sidebarAlertText = sidebarAlertText + '<div class="alert-text">' + $($('li', $(this))[1]).html()+'</div>';
            }

            if ($('a', $($('li', $(this))[2])).length) {
                sidebarAlertText = sidebarAlertText + '<div class="alert-btn"><a href="'+$('a', $($('li', $(this))[2])).attr('href')+'" class="button sml btn-outline-dark">'+$('a', $($('li', $(this))[2])).html()+'</div>';
            }
        });
    } else if($('.sidebarAlertWrapper img').length){
        $('.sidebarAlertHolder').addClass('sidebarAlertImg');
        sidebarAlertText = $('.sidebarAlertWrapper').html();
    }
    $('.sidebarAlertHolder').html(sidebarAlertText);

    var sidebarAdSection = '';
    $('.sidebarAdWrapper ul').each(function() {
        if ($($('li', $(this))[0]).length) {
            sidebarAdSection = sidebarAdSection + $($('li', $(this))[0]).html();
        }
        if ($($('li', $(this))[1]).length) {
            sidebarAdSection = sidebarAdSection + '<div class="advertisementTag">' + $($('li', $(this))[1]).html() + '</div>';
        }
    });
    $('.sidebar-ad').html(sidebarAdSection);

    var blogWrapper = $('.blogWrapper ul');
    var blogString = '';

    blogWrapper.each(function() {
        var heading = '';
        var newsLinkHref = '';
        var summary = '';
        var img = '';
        var imgClass = '';
        var imgString = '';
        
        if ($('a', $($('li', $(this))[0])).length) {
            heading = $('a', $($('li', $(this))[0])).html();
            newsLinkHref = $('a', $($('li', $(this))[0])).attr('href');
        }
        if ($($('li', $(this))[1]).length) {
            summary = $($('li', $(this))[1]).html();
        }
        if ($('img', $($('li', $(this))[2])).length) {
            img = $($('li', $(this))[2]).html();
            imgClass = "withImg";
            imgString = '<div class="post-list-img"><a href="'+newsLinkHref+'">'+img+'</a></div>';
        }
        blogString = blogString + '<div class="post-list-bx '+imgClass+'">'+imgString+'<div class="post-list-content"><h3><a href="'+newsLinkHref+'">'+heading+'</a></h3><div class="post-summary">'+summary+'</div><a href="'+newsLinkHref+'" class="button sml grey" target="_blank">Read More</a></div></div>';
    });

    var blogHeader = "";
    if($('.blogWrapper h3').length){
        blogHeader = '<h3 class="group-title">' + $('.blogWrapper h3').html() + '</h3>';
    }
    var blogLink = "";
    if($('.blogWrapper a').length){
        blogLink = '<div class="group-head-btn"><a href="' + $('.blogWrapper a').attr('href') + '" class="button sml btn-outline-white" target="_blank"> ' + $('.blogWrapper a').html() + '</a></div>';
    }
    blogString = '<div class="group-head">'+blogHeader +  '</div><div id="news-posts">' + blogString + '</div><div class="group-foot">'+blogLink + '</div>';
    if ($('.blogWrapper').length) {
        $('.blogHolder').replaceWith(blogString);
    }
    
    var groupBlogWrapper = $('.groupBlogWrapper ul');
    var blogString = '';

    groupBlogWrapper.each(function() {
        var heading = '';
        var newsLinkHref = '';
        var summary = '';
        var img = '';
        var imgString = '';
        
        if ($('a', $($('li', $(this))[0])).length) {
            heading = $('a', $($('li', $(this))[0])).html();
            newsLinkHref = $('a', $($('li', $(this))[0])).attr('href');
        }
        if ($($('li', $(this))[1]).length) {
            summary = $($('li', $(this))[1]).html();
        }
        if ($($('li', $(this))[2]).length) {
            img = $($('li', $(this))[2]).html();
            imgString = '<div class="post-list-img"><a href="'+newsLinkHref+'">'+img+'</a></div>';
        }
        blogString = blogString + '<div class="span6"><div class="post-list-bx aos-item withImg" data-aos="fade-right">'+imgString+'<div class="post-list-content"><h3><a href="'+newsLinkHref+'">'+heading+'</a></h3><div class="post-summary">'+summary+'</div><a href="'+newsLinkHref+'" class="button sml grey">Read More</a></div></div></div>';
    });

    var blogHeader = "";
    if($('.groupBlogWrapper h3').length){
        blogHeader = '<h3 class="group-title">' + $('.groupBlogWrapper h3').html() + '</h3>';
    }

    var blogLink = "";
    if($('.groupBlogWrapper a').length){
        blogLink = '<div class="group-head-btn"><a href="' + $('.groupBlogWrapper a').attr('href') + '" class="button sml btn-outline-white">' + $('.groupBlogWrapper a').html() + '</a></div>';
    }

    blogString = '<div class="group-head">'+blogHeader + blogLink + '</div><div id="news-posts" class="row-fluid">' + blogString + '</div>';
    if ($('.groupBlogWrapper').length) {
        $('.groupBlogHolder').replaceWith(blogString);
    }
    $('#latest-news .post-list-img img').addClass('img-fluid lazy loaded');

    var eventWrapper = $('.eventWrapper ul');
    var upcommingEventString = '';
    var eventString = '';
    $('.upcomingEvents').hide();
    eventWrapper.each(function() {
        var heading = '';
        var eventLinkHref = '';
		var eventLinkId = '';
        var datetime = '';

        if ($('a', $($('li', $(this))[0])).length) {
            heading = $('a', $($('li', $(this))[0])).html();
            eventLinkHref = $('a', $($('li', $(this))[0])).attr('href');
			eventLinkId = $('a', $($('li', $(this))[0])).attr('id');
			if(typeof eventLinkId == 'undefined') eventLinkId = "";
        }
        if ($($('li', $(this))[1]).length) {
            datetime = $($('li', $(this))[1]).html();
        }
        eventString = eventString + '<div class="event-list-bx"><h3><a href="'+eventLinkHref+'" id="'+eventLinkId+'">'+heading+'</a></h3><div class="event-date">'+datetime+'</div></div>';
        
    });
    
    upcommingEventString = '<div id="event-posts">'+eventString+'</div>';
    
    var eventHeader = "";
    if($('.eventWrapper h3').length){
        eventHeader = '<h3 class="group-title text-blue">' + $('.eventWrapper h3').html() + '</h3>';
    }
    
    var eventLink = "";
    $('.eventWrapper > p a').each(function() {
        eventLink = eventLink + '<div class="group-head-btn"><a href="' + $(this).attr('href') + '" id="' + $(this).attr('id') + '" class="button sml btn-outline-white" target="_blank">' + $(this).html() + '</a></div>';
    });
    
    if (eventWrapper.length) {
        $('#home .eventHolder').replaceWith('<div class="group-head">'+eventHeader+'</div><div class="widget-events-list">'+upcommingEventString+'</div><div class="group-foot">'+eventLink+'</div>');
        $('.single-default .eventHolder').replaceWith('<div class="group-head">'+eventHeader+'</div><div class="widget-events-list">'+upcommingEventString+'</div><div class="group-foot">'+eventLink+'</div>');
        $('.groupSectionPage .eventHolder').replaceWith('<div class="group-head">'+eventHeader+'</div><div class="widget-events-list">'+upcommingEventString+'</div>');
        $('.groupSectionPage #upcoming-events #event-posts').append(eventLink);
        $('.upcomingEvents').show();
    }
    
    var advertisingWrapper = $('.advertisingWrapper ul');
    var advertisingString = '';
    var title = '';
    var description = '';
    var descriptionLink = '';
	var descriptionId = '';
    var adtext = '';
    var adLink = '';
    advertisingWrapper.each(function() {
        if ($($('li', $(this))[0]).length) {
            title = $($('li', $(this))[0]).html();
        }
        if ($('a', $($('li', $(this))[1])).length) {
            description = $('a', $($('li', $(this))[1])).html();
            descriptionLink = $('a', $($('li', $(this))[1])).attr('href');
			descriptionId = $('a', $($('li', $(this))[1])).attr('id');
			if(typeof descriptionId == 'undefined') descriptionId = "";
        }
        if ($('a', $($('li', $(this))[2])).length) {
            adtext = $('a', $($('li', $(this))[2])).html();
            adLink = $('a', $($('li', $(this))[2])).attr('href');
        }
    });

    advertisingString = advertisingString + '<div class="event-list-bx aos-item" data-aos="fade-right"><div class="advertising_tag">'+title+'</div><h3><a href="'+descriptionLink+'" id="'+descriptionId+'">'+description+'</a></h3><div class="event-date"><strong><a href="'+adLink+'">'+adtext+'</a></strong></div>';

    $('#event-posts').append(advertisingString);


    var sponsorWrapper = $('.sponsorWrapper > ul > li');
    var sponsorString='';
    sponsorWrapper.each(function(key){
        sponsorString = sponsorString + '<div class="item flex">'+$(this).html()+'</div>';
    });

    var sponsorHeader = "";

    if($('.sponsorWrapper h3').length){
        sponsorHeader = '<h3 class="sponsors-label">' + $('.sponsorWrapper h3').html() + '</h3>';
    }
       
    sponsorString = sponsorHeader + '<div class="sponsor-logos"><div class="owl-carousel owl-theme">' + sponsorString + '</div></div>' ;
   
	var sponsorsAdd = "";
	if($('.sponsorWrapper > img').length){
		var paddingTop = "";
		if($('.sponsorWrapper li').length == 0){
			paddingTop = 'style="padding-top:0px!important;"';
		}
	   sponsorsAdd = sponsorsAdd + '<div class="sponsors-ad" '+paddingTop+'><img src="'+$('.sponsorWrapper > img').attr('src')+'"></div>';
	}
    $('.sponsorHolder').replaceWith('<div id="sponsors">'+sponsorString+sponsorsAdd+'</div>');	

    var linkWrapper = $('.footerLinkWrapper > ul > li');
    var hmlString1='<div class="span3">';
    if(linkWrapper.length <= 3){
        linkWrapper.each(function(key){
            var keyIndex = key + 1;
            var innerHeader = '';
            $('>ul > li h2 ',this).each(function(key){
                var innerHeader = '';
                $('a',this).each(function(key){
                    innerHeader = innerHeader +'<div class="footer-nav-head">'+$(this).html()+'</div>';
                });
                hmlString1 = hmlString1 + innerHeader;
            });
            hmlString1 = hmlString1 +  '<div class="footer-nav">'+$(this).html()+'</div>';

            if(keyIndex == linkWrapper.length){
            hmlString1 = hmlString1 + '</div>';
            }else{
                hmlString1 = hmlString1 + '</div><div class="span3">';
            }
        });

    }else{
        linkWrapper.each(function(key){
            var keyIndex = key + 1;
            var innerHeader = '';
            $('>ul > li h2 ',this).each(function(key){
                var innerHeader = '';
                $('a',this).each(function(key){
                    innerHeader = innerHeader +'<div class="footer-nav-head">'+$(this).html()+'</div>';
                });
                hmlString1 = hmlString1 + innerHeader;
            });
            hmlString1 = hmlString1 +  '<div class="footer-nav">'+$(this).html()+'</div>';

            if(keyIndex % 2 == 0 &&  keyIndex != linkWrapper.length){
            hmlString1 = hmlString1 + '</div><div class="span3">';
            }else if(keyIndex == linkWrapper.length){
            hmlString1 = hmlString1 + '</div>';
            }
        });
    }
    
    if($(".footerLinkWrapper > ul > li").length){
        $('.footerLinkHolder').replaceWith(hmlString1); 
    }
    $(".footer-nav > ul > li:first-child").remove();

    var footerIconString='';
    var footerLogo = "";
    if($('.footerMediaIconWrapper > ul').length){
        $('.footerMediaIconWrapper > ul li').addClass('social');
        footerIconString = $('.footerMediaIconWrapper > ul').html();
        $('#footerSocial').html('<ul class="social-profiles">'+footerIconString+'</ul>');
        $('#headerSocial').html('<ul class="social-profiles">'+footerIconString+'</ul>');
    }
    if($('.footerMediaIconWrapper > a').length){
        footerLogo = '<a href="'+$('.footerMediaIconWrapper > a').attr('href') +'">'+$('.footerMediaIconWrapper > a').html()+'</a>';
        $('#footerLogo').html(footerLogo);
    }
    
    var linkString='';
    if($('.copyrightWrapper > ul').length){
        $('.copyrightWrapper > ul li').each(function(key){
            linkString = linkString +$(this).html()+'<span>|</span>';
        });
        $('#copyright-nav').html(linkString);
        $('.copyrightWrapper > ul').remove();
    }
    $('#copyright').html($('.copyrightWrapper').html());
    $('#copyright-nav span:last-child').remove();
   
    var zoneJWrapper = $('.zoneJWrapper ul');
    var zoneJString = "";
    var count = 1;
    var image = "";
    
    zoneJWrapper.each(function() {
		var title = "";
		var description = "";
		var callBtn = "";
		var callBtnLink = "";
		var callBtnId = "";
        if ($('h2', $($('li', $(this))[0])).length) {
            title = $('h2', $($('li', $(this))[0])).html();
        }
        if ($($('li', $(this))[1]).length) {
            description = $($('li', $(this))[1]).html();
        }
        if ($('a', $($('li', $(this))[2])).length) {
            callBtn = $('a', $($('li', $(this))[2])).html();
            callBtnLink = $('a', $($('li', $(this))[2])).attr('href');
			callBtnId = $('a', $($('li', $(this))[2])).attr('id');
			if(typeof callBtnId == 'undefined') callBtnId = "";
        }
		
        if(count == 1){
            zoneJString = '<h2 class="sectionTitle text-blue">'+title+'</h2><p>'+description+'</p><p><a href="'+callBtnLink+'" id="'+callBtnId+'" class="button btn-outline-dark sml 1" target="_blank">'+callBtn+'</a></p>';
            $('.subSection1Content').html(zoneJString);
            zoneJString = "";
        } else {
            zoneJString = zoneJString + '<div class="span6 text-center"><h2 class="sectionTitle text-blue">'+title+'</h2><p>'+description+'</p><p><a href="'+callBtnLink+'" id="'+callBtnId+'" class="button btn-outline-dark sml 2" target="_blank">'+callBtn+'</a></p></div>';
        }
        count++;
    });
    $('.subSection2Content').html(zoneJString);
    image = $("<div />").append($('.zoneJWrapper img').clone()).html();
    $('#section-3 .bg-image').html(image);
    $('#section-3 .bg-image > img').addClass('lazy img-fluid');

    var slideWrapper = $('.slideWrapper ul');
    var slideString = "";
    slideWrapper.each(function() {
        var slideImage = "";
        var title = "";
        var subTitle = "";
        var btn = "";
        var slideLinkHref = "";
        var btn2 = "";
        var slideLinkHref2 = "";
        var slideLinkId = "";
		var slideLink2Id = "";
        if ($('img', $($('li', $(this))[0])).length) {
            slideImage = $('img', $($('li', $(this))[0])).attr('src');
        }
        if ($($('li', $(this))[1]).length && $.trim($($('li', $(this))[1]).html()).length) {
            title = $($('li', $(this))[1]).html();
        }
        if ($($('li', $(this))[2]).length && $.trim($($('li', $(this))[2]).html()).length) {
            subTitle = '<div class="bannerTxt">' + $($('li', $(this))[2]).html() + '</div>';
        }
        if ($('a', $($('li', $(this))[3])).length) {
            btn = $('a', $($('li', $(this))[3])).html();
            slideLinkHref = $('a', $($('li', $(this))[3])).attr('href');
            slideLinkId = $('a', $($('li', $(this))[3])).attr('id');
			if(typeof slideLinkId == 'undefined') slideLinkId = "";
        }
        if ($('a', $($('li', $(this))[4])).length) {
            btn2 = $('a', $($('li', $(this))[4])).html();
            slideLinkHref2 = $('a', $($('li', $(this))[4])).attr('href');
            slideLink2Id = $('a', $($('li', $(this))[4])).attr('id');
			if(typeof slideLink2Id == 'undefined') slideLink2Id = "";
        }
        slideString = slideString + '<li><div class="banner-slide"><div class="banner-image aos-item" data-aos="fade-in" style="background-image: url('+slideImage+');"></div><div class="container"><div class="row-fluid"><div class="col span7 animated fadeInLeft delay-1"><div class="bannerTxtBx">'+title+subTitle+'<div class="rowLinks bannerLinks">';
        if(slideLinkHref.length) {
            slideString = slideString + '<a href="'+slideLinkHref+'" class="button" id="'+slideLinkId+'" target="_blank">'+btn+'</a>';
        }
        if(slideLinkHref2.length){
			slideString = slideString + '<a href="'+slideLinkHref2+'" class="rowLink" id="'+slideLink2Id+'" target="_blank">'+btn2+'</a>';
        }
        slideString = slideString + '</div></div></div></div></div></div>';

    });

    $('.slideHolder').html(slideString);

    var zoneHWrapper = $('.zoneHWrapper ul');
    var optionalString = "";
    var optionalImage = "";
    var title = "";
    var description = "";
    var callBtn = "";
    var callBtnLink = "";
    zoneHWrapper.each(function() {
        if ($('img', $($('li', $(this))[0])).length) {
            optionalImage = $($('li', $(this))[0]).html();
        }
        if ($($('li', $(this))[1]).length) {
            title = $($('li', $(this))[1]).html();
        }
        if ($($('li', $(this))[2]).length) {
            description = $($('li', $(this))[2]).html();
        }
        if ($('a', $($('li', $(this))[3])).length) {
            callBtn = $('a', $($('li', $(this))[3])).html();
            callBtnLink = $('a', $($('li', $(this))[3])).attr('href');
        }
        $('#home-sections #section-1 .bg-image').html(optionalImage);
        $('#home-sections #section-1 .bg-image img').addClass('lazy img-fluid');
        
        optionalString = optionalString + title + '<p>'+description+'</p><p><a href="'+callBtnLink+'" class="button btn-outline-white sml" target="_blank">'+callBtn+'</a></p>';
    });
    $('#home-sections #section-1 .section1Content').html(optionalString);
    $('#home-sections #section-1 .section1Content h2').addClass('sectionTitle');
    
    if($('.zoneNWrapper ul').length){
        var tab1 = '';
        var tab2 = '';
        var tab1Content = '';
        var tab2Content = '';
        var tabString = '';
        if ($('a', $($('li', $('.zoneNWrapper ul'))[0])).length) {
            tab1 = $('a', $($('li', $('.zoneNWrapper ul'))[0])).html();
        }
        if ($('a', $($('li', $('.zoneNWrapper ul'))[1])).length) {
            tab2 = $('a', $($('li', $('.zoneNWrapper ul'))[1])).html();
        }
        if ($($('li', $('.zoneNWrapper ul'))[2]).length) {
            tab1Content = $($('li', $('.zoneNWrapper ul'))[2]).html();
        }
        if ($($('li', $('.zoneNWrapper ul'))[3]).length) {
            tab2Content = $($('li', $('.zoneNWrapper ul'))[3]).html();
        }
        tabString = '<ul class="tab-menu"><li><a href="#tab-tab1">'+tab1+'</a></li><li><a href="#tab-tab2">'+tab2+'</a></li></ul><div id="tab-tab1" class="tab-content" >'+tab1Content+'</div><div id="tab-tab2" class="tab-content" style="display: none;">'+tab2Content+'</div>';
        $('#social-tabs').html(tabString);
    }

    var zoneIWrapper = $('.zoneIWrapper ul');
    var optionalString = "";
  
    zoneIWrapper.each(function() {
		var optionalImage = "";
		var title = "";
		var description = "";
		var callBtn = "";
		var callBtnLink = "";
        var callBtnId = "";
		if ($('img', $($('li', $(this))[0])).length) {
            optionalImage = $($('li', $(this))[0]).html();
        }
        if ($($('li', $(this))[1]).length) {
            title = $($('li', $(this))[1]).html();
        }
        if ($($('li', $(this))[2]).length) {
            description = $($('li', $(this))[2]).html();
        }
        if ($('a', $($('li', $(this))[3])).length) {
            callBtn = $('a', $($('li', $(this))[3])).html();
            callBtnLink = $('a', $($('li', $(this))[3])).attr('href');
			callBtnId = $('a', $($('li', $(this))[3])).attr('id');
			if(typeof callBtnId == 'undefined') callBtnId = "";
        }
        $('#home-sections #section-2 .bg-image').html(optionalImage);
        $('#home-sections #section-2 .bg-image img').addClass('lazy img-fluid');
        
        optionalString = optionalString + title + '<p>'+description+'</p><p><a href="'+callBtnLink+'" id="'+callBtnId+'" class="button btn-outline-dark sml" target="_blank">'+callBtn+'</a></p>';
    });
    $('#home-sections #section-2 .section2Content').html(optionalString);
    $('#home-sections #section-2 .section2Content h2').addClass('sectionTitle');

    $('#footerBtns > a:first-child').addClass('button signin');
    $('#footerBtns > a').eq(1).addClass('button join');
    $('#footerBtns > a:last-child').addClass('button donate');
    
    $('#home-sections').removeClass('hide');
    myAccountLink = '';
    $('#headerBtns > a').each(function(key,val){
        if($(this).hasClass('myAccount')){
            myAccountLink = $(this);
        }
    });
    if(myAccountLink != ''){
        $('.headerButton').before(myAccountLink);
    }
});