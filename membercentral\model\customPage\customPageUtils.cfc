<cfcomponent output="no">
	<cffunction name="setFormDefaults" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any" required="true">
		<cfargument name="formName" type="string" required="true">
		<cfargument name="formNameDisplay" type="string" required="true">
		<cfargument name="orgEmailTo" type="string" required="true">
		<cfargument name="orgEmailFrom" type="string" required="false" default="<EMAIL>">
		<cfargument name="memberEmailFrom" type="string" required="true">
		
		<cfscript>
		var local = structNew();
		
		local.organization = arguments.event.getValue('mc_siteInfo.ORGName');
		local.orgID = arguments.event.getValue('mc_siteInfo.orgID');
		local.orgCode = arguments.event.getValue('mc_siteInfo.orgCode');
		local.siteID = arguments.event.getValue('mc_siteInfo.siteID');
		local.siteCode = arguments.event.getValue('mc_siteInfo.siteCode');
		local.orgSysMemberID = arguments.event.getValue('mc_siteInfo.sysMemberID');
		local.orgDefaultSiteID = application.objOrgInfo.getOrgDefaultSiteID(orgID=local.orgID);
		local.orgDefaultSiteCode = application.objSiteInfo.getSiteCodeFromSiteID(siteID=local.orgDefaultSiteID);
		local.statsSessionID = session.cfcUser.statsSessionID;
		
		local.formName = arguments.formName;
		local.formNameDisplay = arguments.formNameDisplay;

		// EMAIL SETTINGS
		local.ORGEmail = { type='html', subject='#local.Organization# - #local.formNameDisplay#' };
		local.memberEmail = { type='html', subject='#local.Organization# - #local.formNameDisplay# received' };
		if (application.MCEnvironment neq "production") { 
			local.ORGEmail.FROM = "<EMAIL>";
			local.ORGEmail.TO = "<EMAIL>";
			local.ORGEmail.SUBJECT = 'TESTMODE: #local.ORGEmail.SUBJECT#';
			local.memberEmail.FROM = "<EMAIL>";
			local.memberEmail.TO = "<EMAIL>";
			local.memberEmail.SUBJECT = 'TESTMODE: #local.memberEmail.SUBJECT#';
		} else { 
			local.ORGEmail.FROM = arguments.orgEmailFrom;
			local.ORGEmail.TO = arguments.orgEmailTo;
			local.memberEmail.FROM = arguments.memberEmailFrom;
			local.memberEmail.TO = arguments.event.getValue('email','');
		}

		// FORM PROTECTION
		local.objCffp = CreateObject("component","model.cfformprotect.cffpVerify").init();

		// Member Object				
		local.memberID = session.cfcUser.memberData.memberID;
		if (application.objUser.isLoggedIn(cfcuser=session.cfcuser))
			local.useMID = local.memberID;
		else
			local.useMID = int(val(arguments.event.getValue('memberID',0)));
		local.data.address = application.objMember.getCompleteAddresses(local.useMID); 
		local.data.phone = application.objMember.getMemberPhones(orgID=local.orgID, memberID=local.useMID);
		local.strProfLicenseLabels = QueryRowData(application.objOrgInfo.getOrgProfLicenseLabels(orgID=local.orgID),1);
		</cfscript>
		
		<cfsavecontent variable="local.pageJS">
			<cfoutput>
			<script type="text/javascript">
				function _FB_hasValue(obj, obj_type){
					if (obj_type == 'TEXT' || obj_type == 'TEXTAREA'){ tmp = obj.value; tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,''); if (tmp.length == 0) return false; else return true; }
					else if (obj_type == 'SELECT'){ for (var i=0; i < obj.length; i++) { if (obj.options[i].selected){ tmp = obj.options[i].value; tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,''); if (tmp.length > 0) return true; } } return false; } 
					else if (obj_type == 'SINGLE_VALUE_RADIO' || obj_type == 'SINGLE_VALUE_CHECKBOX'){ if (obj.checked) return true; else return false;	} 
					else if (obj_type == 'RADIO' || obj_type == 'CHECKBOX'){ if (obj.length == undefined && obj.checked) return true; else{for (var i=0; i < obj.length; i++){ if (obj[i].checked) return true; }} return false; }
					else{ return true; }
				}
				function _FB_withinResponseLimit(obj, obj_type, optRespLimit) {
					var cnt = 0;
					for (var i=0; i < obj.length; i++) {
						if (obj_type == 'SELECT' && obj.options[i].selected) cnt++;
						else if (obj_type == 'CHECKBOX' && obj[i].checked) cnt++;
					}					
					if (cnt <= optRespLimit) return true;
					else return false;
				}
				function getSelectedRadio(buttonGroup) {
					if (buttonGroup[0]) {
						for (var i=0; i<buttonGroup.length; i++) { if (buttonGroup[i].checked) return i }
					} else { if (buttonGroup.checked) return 0; }
					return -1;
				}
				function formatCurrency(num) {
					num = num.toString().replace(/\$|\,/g,'');
					if(isNaN(num)) num = "0";
					num = Math.abs(num);	// added by tl 5/16/2006. force positive values only
					sign = (num == (num = Math.abs(num)));
					num = Math.floor(num*100+0.***********);
					cents = num%100;
					num = Math.floor(num/100).toString();
					if(cents<10) cents = "0" + cents;
					for (var i = 0; i < Math.floor((num.length-(1+i))/3); i++) num = num.substring(0,num.length-(4*i+3))+','+
					num.substring(num.length-(4*i+3));
					return (((sign)?'':'-') + '$' + num + '.' + cents);
				}
				
				function selectMember() {
					$.colorbox( {innerWidth:550, innerHeight:330, href:'/?pg=accountLocator&autoClose=0&retFunc=addMember', iframe:true, overlayClose:false} );
				}
				function resizeBox(newW,newH) { 
					var windowWidth = $(window).width();
					var _popupWidth = newW;
					if(windowWidth < 585) {
						_popupWidth = windowWidth - 30;
					}
					$.colorbox.resize({innerWidth:_popupWidth,innerHeight:newH});
				}
				function addMember(memObj) {
					$.colorbox.close();
					assignMemberData(memObj);
				}
					
				function checkPhFormat(x){
					var numberEntered = x.value.toString();
					var fieldEntered = x;
					var messageField = document.getElementById(x.id + '_message');
					var pattern = /\d{3}-\d{3}-\d{4}/;
					var match = pattern.test(numberEntered);
					
					if ( match == false ){
						fieldEntered.value = '';
						messageField.style.display = '';
					}
					else{
						messageField.style.display = 'none';	
					}
				}
				
				function checkNaN(x){
					if(!x.id){
						var x = document.getElementById(x);
					}
					var numField = $('##' + x.id);
					var msgTR		 = $('##' + x.id + '_message');

					if (numField[0].value != ''){
						if(isNaN(numField[0].value) == true || numField[0].value == 0 ){
						numField[0].value = '';
						msgTR.show();
					}
						else{
							msgTR.hide();	
						}
					}
				}

				function hideAlert() { 
					$('##divFrmErr').html('').hide();
				}
				function showAlert(msg,afterProcessCallBack) { 
					$('##divFrmErr').html(msg).show(); 
					if($.isFunction(afterProcessCallBack)){
						afterProcessCallBack(); 
					}else{
						$('html,body').animate({scrollTop: $('##divFrmErr').position().top},500);
					}					
				};
				function mc_validrange_decimal2(obj,min,max) {
					var a = $.trim(obj.val());
					var pa = parseFloat('0'+a);
					if (a.length > 0 && (pa < min || pa > max)) return false;
					return true;
				}
				function mc_validrange_int(obj,min,max) {
					var a = $.trim(obj.val());
					var pa = parseInt('0'+a);
					if (a.length > 0 && (pa < min || pa > max)) return false;
					return true;
				}
				function mc_validrange_string(obj,min,max) {
					var a = $.trim(obj.val()).length;
					if (a > 0 && (a < min || a > max)) return false;
					return true;
				}
				function mc_validrange_select(obj,min,max) {
					var a = obj.length;
					if (a > 0 && (a < min || a > max)) return false;
					return true;
				}
				function mc_valid_int(val) {
  					var n = ~~Number(val);
					return String(n) === val;
				}
				function mc_loadDataForForm(theForm,np,afterDataLoadCallBack) {
					var fn = theForm.attr("name");
					var showNextData = function(data) {
						var newContent = $(data);
						//replace .html() with empty(), append(), show() on separate lines. Works better on Jquery 2+ for some reason. Doesn't work when chained.
						$('div##div'+fn+'wrapper').empty();
						$('div##div'+fn+'wrapper').append(newContent);
						$('div##div'+fn+'wrapper').show(); 
						$('div##div'+fn+'loading').html('').hide();
						
						if($.isFunction(afterDataLoadCallBack)){
							afterDataLoadCallBack(); 
						}else{
							$('html,body').animate({scrollTop: $('div##div'+fn+'wrapper').position().top-50},500);
						}
					};
					$('div##div'+fn+'wrapper').hide();
					$('div##div'+fn+'loading').html('<i class="icon-spin icon-spinner icon-3x"></i> Please wait... We\'re preparing the '+np+' step for you.').css('text-align','center').show();
					if($.isFunction(afterDataLoadCallBack)){
						afterDataLoadCallBack(); 
					}else{
						$('html,body').animate({scrollTop: $('div##div'+fn+'loading').position().top-50},250);
					}
					
					
					$.ajax({ type:theForm.attr("method"), url:theForm.attr("action"), data:theForm.serialize()})
						.done(function(data) { 
							showNextData(data); 
						})
						.fail(function(data) {
							showNextData(data);
						});
				}
				function mc_continueForm(theForm,afterDataLoadCallBack) {
				    step++;
				    location.hash = step;	
									
					mc_loadDataForForm(theForm,'next',afterDataLoadCallBack);
				}
				function mc_goBackForm(theForm,fa) {
				    if (step > 0) {        
				        step--;
				        location.hash = step;
				    } else {
				        step = 1;
				    }
					return false;
				}			
			</script>
			<style>
			.icon-spin.icon-spinner.icon-3x{width:auto!important;}
			</style>
			</cfoutput>
		</cfsavecontent>

		<cfsavecontent variable="local.pageCSS">
			<cfoutput>
			<style type="text/css">
				.customPage{font-family: Tahoma, Verdana, Arial, Helvetica, sans-serif;}
				<!--- ----------------------------------------------------------------------------------------------------------- --->
				.frmContent{ padding:10px; background:##dddddd; }
				.frmRow1{ background:##ffffff; }
				.frmRow2{ background:##dedede; }
				.frmRow3{ background:##aeaeae; }
				.frmTotals{ background:##666666; color:##ffffff; font-weight:bold; }
				.frmText{ font-size:9pt; color:##505050; }
				.frmButtons{ padding:5px 0; border-top:1px solid ##666666; border-bottom:1px solid ##666666; }
				<!--- ----------------------------------------------------------------------------------------------------------- --->
				<!--- background:url(/assets/common/images/interior_titleBG_Tan.jpg) repeat-x; --->
				.TitleText { font-family:Tahoma; font-size:16pt; color:##03608b; font-weight:bold; }
				.CPSection{ border:1px solid ##666666; margin-bottom:15px; }
				.CPSectionTitle { font-size:14pt; height:20px; font-weight:bold; color:##ffffff; padding:10px; background:##336699; }
				.CPSectionContent{ padding:0 10px; }
				.subCPSectionArea1 { padding:10px 15px 10px 15px; background-color:##cde4f3; }
				.subCPSectionArea2 { padding:10px 15px 10px 15px; background-color:##dbdedf; }
				.subCPSectionArea3 { padding:10px 15px 10px 15px; background-color:##aaaaaa;}
				.subCPSectionTitle { font-size:10pt; font-weight:bold; }
				.subCPSectionText { font-size:9pt; color:##36617d; }
				<!--- ----------------------------------------------------------------------------------------------------------- --->
				.info{ font-style:italic; font-size:8pt; color:##777777; }
				.small{ font-size:7pt;}
				<!--- ----------------------------------------------------------------------------------------------------------- --->
				.r { text-align:right; }
				.l { text-align:left; }
				.c { text-align:center; }
				.i { font-style:italic; }
				.b{ font-weight:bold; }
				<!--- ----------------------------------------------------------------------------------------------------------- --->
				.P{padding:10px;}
				.PL{padding-left:10px;}
				.PR{padding-right:10px;}
				.PB{padding-bottom:10px;}
				.PT{padding-top:10px;}
				.noPadding {padding:0px;}
				
				<!--- ----------------------------------------------------------------------------------------------------------- --->
				.BB { border-bottom:1px solid ##666666; }
				.BL { border-left:1px solid ##666666; }
				.BT { border-top:1px solid ##666666; }
				.block { display:block; }
				.black{ color:##000000; }
				.red{ color:##ff0000; }
				<!--- EMAIL CSS: ------------------------------------------------------------------------------------------------ --->
				.msgHeader{ background:##224563; color:##ffffff; font-weight:bold; text-transform:uppercase; padding:5px; }
				.msgSubHeader{background:##dddddd;}
				<!--- ----------------------------------------------------------------------------------------------------------- --->
				.tsAppBodyText { color:##03608b;}
				select.tsAppBodyText{color:##666666;}
				<!--- ----------------------------------------------------------------------------------------------------------- --->
				.alert{ background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
				.paymentGateway{ background-color:##ededed; padding:10px; }
				##memberNumber{ display:inline-block; width:140px; }

				##associatedMemberIDSelect p { padding:0px; margin:1px 6px; }

				<!--- ----------------------------------------------------------------------------------------------------------- --->
				.required { color:red; font-weight:bold; }
			</style>
			</cfoutput>
		</cfsavecontent>
	
		<cfreturn local>
	</cffunction>


	<!--- -------------------- --->
	<!--- Accounting functions --->
	<!--- -------------------- --->
	<cffunction name="acct_getProfile" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="profileCode" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.strReturn = { _profileCode=arguments.profileCode, _profileID=acct_getProfileID(siteID=arguments.siteID, profileCode=arguments.profileCode) }>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="acct_getProfileID" access="public" output="false" returntype="numeric">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="profileCode" type="string" required="true">
		
		<cfset var qryData = "">
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryData">
			select profileID
			from dbo.mp_profiles
			where siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#"> 
			AND profileCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.profileCode#">
			and [status] = 'A'
		</cfquery>

		<cfreturn val(qryData.profileID)>
	</cffunction>

	<cffunction name="acct_associateCardOnFile" access="public" returntype="void" output="false">
		<cfargument name="invoiceIDList" type="string" required="true">
		<cfargument name="MPProfileID" type="numeric" required="true">
		<cfargument name="mppID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgID)>

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="tr_updateInvoicePaymentProfile">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).siteID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.invoiceIDList#">
			<cfif arguments.mppID>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.MPProfileID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mppID#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
			</cfif>
			<cfif local.useMID>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.useMID#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#application.objCommon.getMCSystemMemberID()#">
			</cfif>
		</cfstoredproc>
	</cffunction>

	<cffunction name="getUnpaidInvoices" access="public" returnType="query" hint="I get unpaid invoices by memberid">
		<cfargument name="memberid" type="numeric" required="true">
		<cfargument name="profileName" type="string" required="true">
		
		<cfset var local = structNew()>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryInvoicesWithAmountDue">				
			SET NOCOUNT ON;

			declare @memberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberid#">;
			declare @orgID int;
			select @orgID = orgID from dbo.ams_members where memberID = @memberID;

			select i.invoiceID, i.invoiceCode, i.invoiceProfileID, i.fullInvoiceNumber as invoiceNumber, i.dateDue,
				ip.profileName as invoiceProfile, sum(it.cache_invoiceAmountAfterAdjustment) as InvAmt,
				sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount) as InvCanPay
			from dbo.tr_invoices as i
			inner join dbo.tr_invoiceStatuses as istat on istat.statusID = i.statusID
			inner join dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
			inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = i.assignedToMemberID
			inner join dbo.organizations as o on o.orgID = @orgID
			left outer join dbo.tr_invoiceTransactions as it 
				inner join dbo.tr_transactions as t on t.transactionID = it.transactionID
				on it.orgID = @orgID and it.invoiceID = i.invoiceID
			where i.orgID = @orgID
			and istat.status in ('Closed','Delinquent')
			and m.activeMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberid#">
			and ip.profileName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(arguments.profileName)#">
			and i.dateDue < getdate()
			group by i.invoiceID, i.fullInvoiceNumber, i.dateDue, ip.profileName, i.invoiceCode, i.invoiceProfileID
			having sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount) > 0
			order by i.invoiceCode;
		</cfquery>
		
		<cfreturn local.qryInvoicesWithAmountDue>
	</cffunction>

	<!--- ---------------- --->
	<!--- Member functions --->
	<!--- ---------------- --->
	<cffunction name="mem_getStatesByCountry" access="public" returntype="query" output="false">
		<cfargument name="country" required="Yes" type="string">
	
		<cfset var qryData = "">
	
		<cfquery name="qryData" datasource="#application.dsn.membercentral.dsn#">
			Select s.stateid, s.code, s.name
			FROM dbo.ams_states s
			INNER JOIN dbo.ams_countries c ON s.countryID = c.countryID
			WHERE c.country = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.country#">
			ORDER BY s.orderpref, s.name
		</cfquery>
	
		<cfreturn qryData>
	</cffunction>

	<cffunction name="mem_getOrgAdditionalDataColumns" access="public" output="false" returntype="xml">
		<cfargument name="orgID" type="numeric" required="yes">

		<cfset var qryData = "">

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_getOrgMemberDataColumns">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
			<cfprocresult name="qryData" resultset="1">
		</cfstoredproc>	

		<cfreturn XMLParse(qryData.additionalDataXML)>		
	</cffunction>

	<cffunction name="mem_getCustomFieldData" access="public" output="false" returntype="struct">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="columnName" type="string" required="false">
		<cfargument name="columnID" type="numeric" required="false">
		<cfargument name="columnValueStringList" type="string" required="false" hint="optional">
		<cfargument name="xmlAdditionalData" type="xml" required="false" hint="optional">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { columnid=0, columnValueArr=arrayNew(1) }>
		<cfset local.firstPass = true>

		<cfif not isDefined("arguments.xmlAdditionalData")>
			<cfset arguments.xmlAdditionalData = mem_getOrgAdditionalDataColumns(orgID=arguments.orgID)>
		</cfif>

		<cfloop array="#arguments.xmlAdditionalData.data.XMlChildren#" index="local.column">
			<cfset local.columnMatch = false>
			<cfif (Len(arguments.columnName) and local.column.xmlAttributes.columnName eq arguments.columnName) OR (Len(arguments.columnID) and local.column.xmlAttributes.columnID eq arguments.columnID)>
				<cfset local.columnMatch = true>
			</cfif>
			<cfif local.columnMatch>
				<cfif local.firstPass>
					<cfset local.returnStruct.columnID = local.column.xmlAttributes.columnID>
					<cfset local.firstPass = false>
				</cfif>
				<cfloop array="#local.column.XMlChildren#" index="local.thisOpt"> 
					<cfswitch expression="#local.column.xmlAttributes.dataTypeCode#">
						<cfcase value="bit">
							<cfset local.isDefault = 0>
							<cfif isDefined("local.column.xmlAttributes.defaultValueID")>
								<cfif local.thisOpt.xmlAttributes.valueID eq local.column.xmlAttributes.defaultValueID>
									<cfset local.isDefault = 1>
								</cfif>	
							</cfif>
							<cfif local.isDefault eq 1>
								<cfset local.tmpStr = { columnValueString=local.thisOpt.xmlAttributes.columnValueBit, valueID=local.thisOpt.xmlAttributes.valueID,isDefault=1 }>
							<cfelse>
								<cfset local.tmpStr = { columnValueString=local.thisOpt.xmlAttributes.columnValueBit, valueID=local.thisOpt.xmlAttributes.valueID }>
							</cfif>
							<cfset arrayAppend(local.returnStruct.columnValueArr,local.tmpStr)>
						</cfcase>
						<cfdefaultcase>
							<cfif not isDefined("arguments.columnValueStringList") or (isDefined("arguments.columnValueStringList") and listFindNoCase(arguments.columnValueStringList, local.thisOpt.xmlAttributes.columnValueString))> 
								
								<cfset local.isDefault = 0>
								<cfif isDefined("local.column.xmlAttributes.defaultValueID")>
									<cfif local.thisOpt.xmlAttributes.valueID eq local.column.xmlAttributes.defaultValueID>
										<cfset local.isDefault = 1>
									</cfif>								
								</cfif>
								<cfif structKeyExists(local.thisOpt.xmlAttributes, "columnValueString")>
									<cfif local.isDefault eq 1>
										<cfset local.tmpStr = { columnValueString=local.thisOpt.xmlAttributes.columnValueString, valueID=local.thisOpt.xmlAttributes.valueID,isDefault=1 }>
									<cfelse>
										<cfset local.tmpStr = { columnValueString=local.thisOpt.xmlAttributes.columnValueString, valueID=local.thisOpt.xmlAttributes.valueID }>
									</cfif>
									<cfset arrayAppend(local.returnStruct.columnValueArr,local.tmpStr)>
								</cfif>								
								
							</cfif>
						</cfdefaultcase>
					</cfswitch>
				</cfloop>
			</cfif>
		</cfloop>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="mem_createAccount" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="prefix" type="string" required="true">
		<cfargument name="firstname" type="string" required="true">
		<cfargument name="middlename" type="string" required="true">
		<cfargument name="lastname" type="string" required="true">
		<cfargument name="suffix" type="string" required="true">
		<cfargument name="professionalsuffix" type="string" required="true">
		<cfargument name="company" type="string" required="true">
		<cfargument name="email" type="string" required="false" default="">
		<cfargument name="customFields" type="array" required="no" default="#arrayNew(1)#">

		<cfset var local = structNew()>
		<cfset local.data.success = false>
		<cfset local.data.memberid = 0>
		<cfset local.data.membernumber = ''>

		<cfquery name="local.qryOrgMemberFields" datasource="#application.dsn.membercentral.dsn#">
			select o.hasPrefix, o.hasMiddleName, o.hasSuffix, o.hasProfessionalSuffix
			from dbo.organizations as o
			where o.orgID = <cfqueryparam value="#arguments.mcproxy_orgID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<cfscript>
		if (local.qryOrgMemberFields.hasPrefix is 0) arguments.prefix = '';
		if (local.qryOrgMemberFields.hasMiddleName is 0) arguments.middlename = '';
		if (local.qryOrgMemberFields.hasSuffix is 0) arguments.suffix = '';
		if (local.qryOrgMemberFields.hasProfessionalSuffix is 0) arguments.professionalsuffix = '';
		</cfscript>

		<cftry>
			<cfscript>
			local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=0);
			local.objSaveMember.setDemo(
				prefix=arguments.prefix,
				firstname=arguments.firstname,
				middlename=arguments.middlename,
				lastname=arguments.lastname,
				suffix=arguments.suffix,
				professionalsuffix=arguments.professionalsuffix,
				company=arguments.company
			);
			local.objSaveMember.setMemberStatus(memberStatus='Active');
			if(len(arguments.email)){
				if(isValid("regex",arguments.email,application.regEx.email)){
					local.objSaveMember.setEmail(type='Email', value=arguments.email);
				}
			}
			local.objSaveMember.setMemberType(memberType='Guest');
			if (ArrayLen(arguments.customFields)) {
				for (local.i=1; local.i lte ArrayLen(arguments.customFields); local.i++) {
					local.tmpField = arguments.customFields[local.i];
					if (structKeyExists(arguments,local.tmpField)) {
						local.objSaveMember.setCustomField(field=local.tmpField, value=arguments[local.tmpField]);
					}
				}
			}
			local.strResult = local.objSaveMember.saveData();
			</cfscript>
			
			<cfif NOT local.strResult.success>
				<cfthrow message="Member could not be created.">
			</cfif>
			
			<cfset local.data.success = true>
			<cfset local.data.memberid = local.strResult.memberID>
			<cfset local.data.membernumber = local.strResult.memberNumber>
		<cfcatch type="Any">
			<cfset local.data.success = false>
			<cfset local.tmpCatch = { type="", message="Unable to create account.", detail="#cfcatch.message# #cfcatch.detail#", tagContext=cfcatch.tagContext } >
			<cfset application.objError.sendError(cfcatch=local.tmpCatch, objectToDump=arguments)>
		</cfcatch>
		</cftry>

		<cfif val(local.data.memberid)>
			<cfset local.newMemIdArr = application.mcCacheManager.sessionGetValue(keyname='newMemIdArr', defaultValue=arrayNew(1))>
			<cfset arrayAppend(local.newMemIdArr, local.data.memberid)>
			<cfset application.mcCacheManager.sessionSetValue(keyname='newMemIdArr', value=local.newMemIdArr)>
		</cfif>
	
		<cfreturn local.data>
	</cffunction>

	<cffunction name="mem_updateAccount" access="public" output="false" returntype="boolean">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="prefix" type="string" required="true">
		<cfargument name="firstname" type="string" required="true">
		<cfargument name="middlename" type="string" required="true">
		<cfargument name="lastname" type="string" required="true">
		<cfargument name="suffix" type="string" required="true">
		<cfargument name="professionalsuffix" type="string" required="true">
		<cfargument name="company" type="string" required="true">
		<cfargument name="email" type="string" required="false" default="">
		<cfargument name="customFields" type="array" required="false" default="#arrayNew(1)#">

		<cfset var local = structNew()>
		<cfset local.success = true>		
		
		<cfquery name="local.qryOrgMemberFields" datasource="#application.dsn.membercentral.dsn#">
			select o.hasPrefix, o.hasMiddleName, o.hasSuffix, o.hasProfessionalSuffix
			from dbo.organizations as o
			where o.orgID = <cfqueryparam value="#arguments.orgid#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<cfscript>
		if (local.qryOrgMemberFields.hasPrefix is 0) arguments.prefix = '';
		if (local.qryOrgMemberFields.hasMiddleName is 0) arguments.middlename = '';
		if (local.qryOrgMemberFields.hasSuffix is 0) arguments.suffix = '';
		if (local.qryOrgMemberFields.hasProfessionalSuffix is 0) arguments.professionalsuffix = '';
		</cfscript>

		<cftry>
			<cfscript>
			local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=arguments.memberID);
			local.objSaveMember.setDemo(
				prefix=arguments.prefix,
				firstname=arguments.firstname,
				middlename=arguments.middlename,
				lastname=arguments.lastname,
				suffix=arguments.suffix,
				professionalsuffix=arguments.professionalsuffix,
				company=arguments.company
			);
			if(len(arguments.email)){
				if(isValid("regex",arguments.email,application.regEx.email)){
					local.objSaveMember.setEmail(type='Email', value=arguments.email);
				}
			}
			if (ArrayLen(arguments.customFields)) {
				for (local.i=1; local.i lte ArrayLen(arguments.customFields); local.i++) {
					local.tmpField = arguments.customFields[local.i];
					if (structKeyExists(arguments,local.tmpField)) {
						local.objSaveMember.setCustomField(field=local.tmpField, value=arguments[local.tmpField]);
					}
				}
			}
			local.strResult = local.objSaveMember.saveData();
			</cfscript>
			
			<cfif NOT local.strResult.success>
				<cfthrow message="Member could not be updated.">
			</cfif>
			
			<cfset local.success = true>
		<cfcatch type="Any">
			<cfset local.success = false>
			<cfset local.tmpCatch = { type="", message="Unable to update account.", detail="#cfcatch.message# #cfcatch.detail#", tagContext=cfcatch.tagContext } >
			<cfset application.objError.sendError(cfcatch=local.tmpCatch, objectToDump=arguments)>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>	
	
	<cffunction name="mem_getRecordType" access="public" output="false" returntype="query">
		<cfargument name="memberID" required="Yes" type="string">
	
		<cfset var qryData = "">
	
		<cfquery name="qryData" datasource="#application.dsn.membercentral.dsn#">
			select m.memberID, m.recordTypeID, rt.recordTypeCode, rt.isPerson, rt.isOrganization
			from ams_members as m
			inner join ams_recordTypes as rt on rt.recordTypeID = m.recordTypeID
			where memberID =  <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.memberID#">
		</cfquery>
	
		<cfreturn qryData>
	</cffunction>
	
	<cffunction name="mem_getGroups" access="public" output="false" returntype="query">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="groupCode" type="string" required="false">
		<cfargument name="UID" type="string" required="false">
	
		<cfset var qryData = "">
	
		<cfquery name="qryData" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @orgID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orgID#">;

			select g.groupID, g.groupName
			from dbo.ams_members m
			inner join dbo.cache_members_groups mg on mg.orgID = @orgID and m.memberID = mg.memberID
			inner join dbo.ams_groups g on g.orgID = @orgID 
				and mg.groupID = g.groupID
				<cfif isDefined("arguments.groupCode")>
					and g.groupCode = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.groupCode#">
				</cfif>
				<cfif isDefined("arguments.UID")>
					and g.uid = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.UID#">
				</cfif>
			where m.memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">
			and m.orgID = @orgID
			and m.memberID = m.activeMemberID;
		</cfquery>
	
		<cfreturn qryData>
	</cffunction>

	<cffunction name="mem_getGroupsAJX" access="public" output="false" returntype="query">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="groupCode" type="string" required="false">
		
		<cfreturn mem_getGroups(memberID=arguments.memberID, orgID=arguments.mcproxy_orgID, groupCode=arguments.groupCode)>
	</cffunction>

	<cffunction name="mem_getCustomFieldValueFromValueID" access="public" output="false" returntype="string">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="columnName" type="string" required="false">
		<cfargument name="valueIDList" type="string" required="true">
		<cfargument name="columnID" type="string" required="false">
	
		<cfset var local = structNew()>

		<cfquery name="local.qryDataTypeCode" datasource="#application.dsn.membercentral.dsn#">			
			select mdc.columnID, dt.dataTypeCode
			from dbo.ams_memberDataColumns as mdc
			inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			inner join dbo.ams_memberDataColumnDisplayTypes as ddt on ddt.displayTypeID = mdc.displayTypeID
			where mdc.orgID = #arguments.orgID#
			<cfif Len(arguments.columnName)>
				and mdc.columnName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.columnName#">
			</cfif>
			<cfif Len(arguments.columnID)>
				and mdc.columnID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.columnID#">
			</cfif>
		</cfquery>

		<cfquery name="local.qryData" datasource="#application.dsn.membercentral.dsn#">			
			select 
				<cfif local.qryDataTypeCode.dataTypeCode eq "Integer">
					STRING_AGG(newVal,'|') as newVal
				<cfelseif local.qryDataTypeCode.dataTypeCode eq "Decimal2">
					STRING_AGG(newVal,'|') as newVal
				<cfelseif local.qryDataTypeCode.dataTypeCode eq "Date"> <!--- no multi support --->
					newVal
				<cfelseif local.qryDataTypeCode.dataTypeCode eq "Bit"> <!--- no multi support --->
					newVal
				<cfelse>
					STRING_AGG(newVal,'|') as newVal
				</cfif>
			from (
				select 
				<cfif local.qryDataTypeCode.dataTypeCode eq "Integer">
					distinct mdcv2.columnValueInteger as newVal
				<cfelseif local.qryDataTypeCode.dataTypeCode eq "Decimal2">
					distinct mdcv2.columnValueDecimal2 as newVal
				<cfelseif local.qryDataTypeCode.dataTypeCode eq "Date"> <!--- no multi support --->
					convert(varchar(10),mdcv2.columnValueDate,101) as newVal
				<cfelseif local.qryDataTypeCode.dataTypeCode eq "Bit"> <!--- no multi support --->
					case when mdcv2.columnValueBit = 1 then '1' when mdcv2.columnValueBit = 0 then '0' else '' end as newVal
				<cfelse>
					distinct mdcv2.columnValueString as newVal
				</cfif>
				from dbo.ams_memberDataColumns mdc
				inner join dbo.ams_memberDataColumnValues mdcv2 on mdcv2.columnID = mdc.columnID and mdcv2.valueID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="0#arguments.valueIDList#" list="true">)
				where mdc.orgID = #arguments.orgid#
				and mdc.columnID = #local.qryDataTypeCode.columnID#
			) as tmp
		</cfquery>
	
		<cfreturn local.qryData.newVal>
	</cffunction>

	<cffunction name="mem_getCustomFieldDataByUID" access="public" output="false" returntype="Query">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="UID" type="string" required="true">

		<cfset var qryColumnValues = "">
		
		<cfquery name="qryColumnValues" datasource="#application.dsn.membercentral.dsn#">
			select mdcv.valueID, mdcv.columnID, mdcv.columnValueString, mdcv.columnValueBit
			from dbo.ams_memberDataColumns mdc
			inner join dbo.ams_memberdatacolumnvalues mdcv on mdcv.columnID = mdc.columnID 
			where mdc.orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orgID#">
			and mdc.uid = <cfqueryparam cfsqltype="cf_sql_idstamp" value="#arguments.UID#">
		</cfquery>

		<cfreturn qryColumnValues>
	</cffunction>
	
	<cffunction name="mem_getCustomFieldByUID" access="public" output="false" returntype="Query">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="UID" type="string" required="true">

		<cfset var qryColumn = "">
		
		<cfquery name="qryColumn" datasource="#application.dsn.membercentral.dsn#">
			select columnID
			from dbo.ams_memberDataColumns
			where orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orgID#"> 
			and [uid] = <cfqueryparam cfsqltype="cf_sql_idstamp" value="#arguments.UID#">		
		</cfquery>

		<cfreturn qryColumn>
	</cffunction>

	<cffunction name="mem_objSaveMember" access="public" output="false" returntype="struct">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="sitecode" type="string" required="no" default="#session.mcStruct.sitecode#">
		<cfreturn createObject("component","model.system.platform.saveMemberObj").init(memberID=arguments.memberID, siteCode=arguments.sitecode)>
	</cffunction>

	<cffunction name="mem_saveMemberInfo" access="public" output="false" returntype="struct">
		<cfargument name="rc" type="struct" required="true">
		<cfargument name="runImmediately" type="boolean" required="false" default="1">
		<cfargument name="memberID" type="numeric" required="false" default="0">

		<cfset var local = structNew()>

		<!--- Make a copy so we dont actually change the event collection in the caller --->
		<cfset local.rcCopy = duplicate(arguments.rc)>

		<!--- if the member was created in the same session, then save info to that memberid. otherwise, create a new member and save it to that --->
		<cfset local.newMemIdArr = application.mcCacheManager.sessionGetValue(keyname='newMemIdArr', defaultValue=arrayNew(1))>
		<cfif NOT (IsArray(local.newMemIdArr) AND listFind(ArrayToList(local.newMemIdArr),local.rcCopy.memberID)) AND not val(arguments.memberID)>
			<cfset local.rcCopy.memberID = 0>
		</cfif>

		<cfif val(arguments.memberID) AND not val(local.rcCopy.memberID)>
			<cfset local.rcCopy.memberID = arguments.memberID>
		</cfif>

		<cfset local.objSaveMember = mem_objSaveMember(memberID=local.rcCopy.memberID)>
		<cfset local.objSaveMember.setMemberType('User')>
		<cfset local.objSaveMember.setRecordType('Individual')>
		<cfset local.objSaveMember.setMemberStatus('Active')>

		<!--- Demo data --->
		<cfset local.strDemoArgs = {}>
		<cfif isDefined("local.rcCopy.m_membernumber")>
			<cfset local.strDemoArgs.membernumber = local.rcCopy.m_membernumber>
		</cfif>
		<cfif isDefined("local.rcCopy.m_prefix")>
			<cfset local.strDemoArgs.prefix = local.rcCopy.m_prefix>
		</cfif>
		<cfif isDefined("local.rcCopy.m_firstname")>
			<cfset local.strDemoArgs.firstname = local.rcCopy.m_firstname>
		</cfif>
		<cfif isDefined("local.rcCopy.m_middlename")>
			<cfset local.strDemoArgs.middlename = local.rcCopy.m_middlename>
		</cfif>
		<cfif isDefined("local.rcCopy.m_lastname")>
			<cfset local.strDemoArgs.lastname = local.rcCopy.m_lastname>
		</cfif>
		<cfif isDefined("local.rcCopy.m_suffix")>
			<cfset local.strDemoArgs.suffix = local.rcCopy.m_suffix>
		</cfif>
		<cfif isDefined("local.rcCopy.m_professionalSuffix")>
			<cfset local.strDemoArgs.professionalSuffix = local.rcCopy.m_professionalSuffix>
		</cfif>
		<cfif isDefined("local.rcCopy.m_company")>
			<cfset local.strDemoArgs.company = local.rcCopy.m_company>
		</cfif>
		<cfset local.objSaveMember.setDemo(argumentcollection=local.strDemoArgs)>

		<!--- emails --->
		<cfset local.qryOrgEmailTypes = application.objOrgInfo.getOrgEmailTypes(orgID=local.rcCopy.mc_siteinfo.orgid)>
		<cfloop query="local.qryOrgEmailTypes">
			<cfif isDefined("local.rcCopy.me_#local.qryOrgEmailTypes.emailTypeID#_email")>
				<cfset local.tmpVal = left(local.rcCopy["me_#local.qryOrgEmailTypes.emailTypeID#_email"],255)>
				<cfif len(local.tmpVal) and isValid("regex",local.tmpVal,application.regEx.email)>
					<cfset local.objSaveMember.setEmail(type=local.qryOrgEmailTypes.emailType, value=local.tmpVal)>
				</cfif>
			</cfif>
		</cfloop>

		<!--- websites --->
		<cfset local.qryOrgWebsiteTypes = application.objOrgInfo.getOrgWebsiteTypes(orgID=local.rcCopy.mc_siteinfo.orgid)>
		<cfloop query="local.qryOrgWebsiteTypes">
			<cfif isDefined("local.rcCopy.mw_#local.qryOrgWebsiteTypes.websiteTypeID#_website")>
				<cfset local.tmpVal = left(local.rcCopy["mw_#local.qryOrgWebsiteTypes.websiteTypeID#_website"],400)>
				<cfif len(local.tmpVal) and isValid("regex",local.tmpVal,application.regEx.url)>
					<cfset local.objSaveMember.setWebsite(type=local.qryOrgWebsiteTypes.websiteType, value=local.tmpVal)>
				</cfif>
			</cfif>
		</cfloop>

		<!--- prof licenses --->
		<cfset local.qryOrgPlTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=local.rcCopy.mc_siteinfo.orgid)>
		<cfloop query="local.qryOrgPlTypes">
			<cfset local.tmpLicenseNumber = "">
			<cfset local.tmpActiveDate = "">
			<cfset local.tmpStatus = "">
			<cfif isDefined("local.rcCopy.mpl_#local.qryOrgPlTypes.PLTypeID#_licenseNumber")>
				<cfset local.tmpLicenseNumber = left(local.rcCopy["mpl_#local.qryOrgPlTypes.PLTypeID#_licenseNumber"],200)>
			</cfif>
			<cfif isDefined("local.rcCopy.mpl_#local.qryOrgPlTypes.PLTypeID#_activeDate")>
				<cfset local.tmpActiveDate = local.rcCopy["mpl_#local.qryOrgPlTypes.PLTypeID#_activeDate"]>
			</cfif>
			<cfif isDefined("local.rcCopy.mpl_#local.qryOrgPlTypes.PLTypeID#_status")>
				<cfset local.tmpStatus = local.rcCopy["mpl_#local.qryOrgPlTypes.PLTypeID#_status"]>
			</cfif>
			<cfif len(local.qryOrgPlTypes.PLName) and len(local.tmpStatus)>
				<cfset local.objSaveMember.setProLicense(name=local.qryOrgPlTypes.PLName, status=local.tmpStatus, license=local.tmpLicenseNumber, date=local.tmpActiveDate)>
			</cfif>
		</cfloop>

		<!--- address/phone --->
		<cfset local.qryOrgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID=local.rcCopy.mc_siteinfo.orgid, includeTags=0)>
		<cfset local.qryOrgPhoneTypes = application.objOrgInfo.getOrgPhoneTypes(orgID=local.rcCopy.mc_siteinfo.orgid)>
		<cfloop query="local.qryOrgAddressTypes">
			<cfset local.tmpAddressTypeID = local.qryOrgAddressTypes.addressTypeID>
			<cfset local.tmpAddressType = local.qryOrgAddressTypes.addressType>

			<cfset local.strAddrArgs = {}>
			<cfset local.strAddrArgs.type = local.tmpAddressType>
			<cfif isDefined("local.rcCopy.ma_#local.tmpAddressTypeID#_attn")>
				<cfset local.strAddrArgs.attn = local.rcCopy["ma_#local.tmpAddressTypeID#_attn"]>
			</cfif>
			<cfif isDefined("local.rcCopy.ma_#local.tmpAddressTypeID#_address1")>
				<cfset local.strAddrArgs.address1 = left(local.rcCopy["ma_#local.tmpAddressTypeID#_address1"],100)>
			</cfif>
			<cfif isDefined("local.rcCopy.ma_#local.tmpAddressTypeID#_address2")>
				<cfset local.strAddrArgs.address2 = left(local.rcCopy["ma_#local.tmpAddressTypeID#_address2"],100)>
			</cfif>
			<cfif isDefined("local.rcCopy.ma_#local.tmpAddressTypeID#_address3")>
				<cfset local.strAddrArgs.address3 = left(local.rcCopy["ma_#local.tmpAddressTypeID#_address3"],100)>
			</cfif>
			<cfif isDefined("local.rcCopy.ma_#local.tmpAddressTypeID#_city")>
				<cfset local.strAddrArgs.city = left(local.rcCopy["ma_#local.tmpAddressTypeID#_city"],75)>
			</cfif>
			<cfif isDefined("local.rcCopy.ma_#local.tmpAddressTypeID#_stateprov")>
				<cfset local.strAddrArgs.stateID = val(local.rcCopy["ma_#local.tmpAddressTypeID#_stateprov"])>
			</cfif>
			<cfif isDefined("local.rcCopy.ma_#local.tmpAddressTypeID#_postalCode")>
				<cfset local.strAddrArgs.postalCode = left(local.rcCopy["ma_#local.tmpAddressTypeID#_postalCode"],25)>
			</cfif>
			<cfif isDefined("local.rcCopy.ma_#local.tmpAddressTypeID#_county")>
				<cfset local.strAddrArgs.county = left(local.rcCopy["ma_#local.tmpAddressTypeID#_county"],50)>
			</cfif>
			<cfif structCount(local.strAddrArgs) gt 1>
				<cfset local.objSaveMember.setAddress(argumentcollection=local.strAddrArgs)>
			</cfif>

			<cfloop query="local.qryOrgPhoneTypes">
				<cfif isDefined("local.rcCopy.mp_#local.tmpAddressTypeID#_#local.qryOrgPhoneTypes.phoneTypeID#")>
					<cfset local.tmpVal = left(local.rcCopy["mp_#local.tmpAddressTypeID#_#local.qryOrgPhoneTypes.phoneTypeID#"],40)>
					<cfif len(local.tmpVal)>
						<cfset local.objSaveMember.setPhone(addresstype=local.tmpAddressType, type=local.qryOrgPhoneTypes.phoneType, value=local.tmpVal)>
					</cfif>
				</cfif>
			</cfloop>
		</cfloop>

		<!--- address tags --->
		<cfset local.qryOrgAddressTags = application.objOrgInfo.getOrgAddressTagTypes(orgID=local.rcCopy.mc_siteinfo.orgid)>
		<cfloop query="local.qryOrgAddressTags">
			<cfif local.qryOrgAddressTags.allowMembersToUpdate is 1 and isDefined("local.rcCopy.mat_#local.qryOrgAddressTags.addressTagTypeID#_addresstype")>
				<cfset local.selectedAddressTypeID = local.rcCopy["mat_#local.qryOrgAddressTags.addressTagTypeID#_addresstype"]>
				<cfif len(local.selectedAddressTypeID)>
					<cfquery name="local.qrySelectedAddressType" dbtype="query">
						select addressType
						from [local].qryOrgAddressTypes
						where addressTypeID = #local.selectedAddressTypeID#
					</cfquery>
					<cfset local.objSaveMember.setAddressTag(tag=local.qryOrgAddressTags.addresstagtype, type=local.qrySelectedAddressType.addressType)>
				</cfif>
			</cfif>
		</cfloop>

		<!--- custom fields --->
		<cfset local.xmlDataColumns = application.objOrgInfo.getOrgMemberDataColumns(orgID=local.rcCopy.mc_siteinfo.orgid).additionalDataXML>
		<cfloop collection="#arguments.rc#" item="local.thisField">
			<cfif left(local.thisField,3) eq "md_" and listLen(local.thisField,'_') eq 2>
				<cfset local.xmlFieldNode = XMLSearch(local.xmlDataColumns,'/data/column[@columnID="#val(GetToken(local.thisField,2,'_'))#"]')>
				<cfset local.tmpval = local.rcCopy[local.thisField]>

				<cfset local.tmpStr = { field=local.xmlFieldNode[1].xmlAttributes.columnName }>
				<cfif listFindNoCase("RADIO,CHECKBOX,SELECT",local.xmlFieldNode[1].xmlAttributes.displayTypeCode)>
					<cfset local.tmpStr.valueID = local.tmpval>
				<cfelse>
					<cfset local.tmpStr.value = local.tmpval>
				</cfif>
				<cfset local.objSaveMember.setCustomField(argumentcollection=local.tmpStr)>
			</cfif>
		</cfloop>

		<cfset local.strResult = local.objSaveMember.saveData(runImmediately=arguments.runImmediately)>

		<cfif val(local.strResult.memberID) and not val(local.rcCopy.memberID)>
			<cfset local.newMemIdArr = application.mcCacheManager.sessionGetValue(keyname='newMemIdArr', defaultValue=arrayNew(1))>
			<cfset arrayAppend(local.newMemIdArr, local.strResult.memberid)>
			<cfset application.mcCacheManager.sessionSetValue(keyname='newMemIdArr', value=local.newMemIdArr)>
		</cfif>		

		<cfreturn local.strResult>
	</cffunction>

	<cffunction name="mem_PrefillMemberData" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">

		<cfset var local = structNew()>

		<!--- if the member was just created, load the members data for fields in the account locator new account form so they dont have to enter them again --->
		<cfset local.newMemIdArr = application.mcCacheManager.sessionGetValue(keyname='newMemIdArr', defaultValue=arrayNew(1))>
		<cfif IsArray(local.newMemIdArr) AND listFind(ArrayToList(local.newMemIdArr),arguments.memberID)>
			<cfset local.objAccountLocator = CreateObject("component","model.system.user.accountlocater")>
			<cfset local.NewAcctFormFieldsetID = local.objAccountLocator.getLocatorFieldsetID(siteid=arguments.siteID, area='newacct')>
			<cfset local.NewAcctFormXMLFields = CreateObject("component","model.system.platform.memberFieldsets").getMemberFieldsXML(fieldsetid=local.NewAcctFormFieldsetID, usage='accountLocaterNew')>

			<cfset local.siteResourceID = local.objAccountLocator.getToolSiteResourceID(siteID=arguments.siteID)>
			<cfset local.qryCurrentProfessionalLicenses = local.objAccountLocator.getCurrentProfessionalLicenses(siteResourceID=local.siteResourceID)>		
			<cfloop query="local.qryCurrentProfessionalLicenses">
				<cfset local.newNode = xmlElemNew(local.NewAcctFormXMLFields,"mf")>
				<cfset local.newNode.xmlAttributes["dbObject"] = "ams_memberProfessionalLicenses">
				<cfset local.newNode.xmlAttributes["dbObjectAlias"] = "mpl#local.qryCurrentProfessionalLicenses.PLTypeID#">
				<cfset local.newNode.xmlAttributes["dbField"] = "#local.qryCurrentProfessionalLicenses.PLName#_licenseNumber">
				<cfset local.newNode.xmlAttributes["fieldCode"] = "mpl_#local.qryCurrentProfessionalLicenses.PLTypeID#_licenseNumber">
				<cfset local.newNode.xmlAttributes["dataTypeCode"] = "STRING">
				<cfset arrayAppend(local.NewAcctFormXMLFields.xmlRoot.xmlChildren,local.newNode)>

				<cfset local.newNode = xmlElemNew(local.NewAcctFormXMLFields,"mf")>
				<cfset local.newNode.xmlAttributes["dbObject"] = "ams_memberProfessionalLicenses">
				<cfset local.newNode.xmlAttributes["dbObjectAlias"] = "mpl#local.qryCurrentProfessionalLicenses.PLTypeID#">
				<cfset local.newNode.xmlAttributes["dbField"] = "#local.qryCurrentProfessionalLicenses.PLName#_activeDate">
				<cfset local.newNode.xmlAttributes["fieldCode"] = "mpl_#local.qryCurrentProfessionalLicenses.PLTypeID#_activeDate">
				<cfset local.newNode.xmlAttributes["dataTypeCode"] = "DATE">
				<cfset arrayAppend(local.NewAcctFormXMLFields.xmlRoot.xmlChildren,local.newNode)>
			</cfloop>

			<cfreturn mem_fieldsetData(memberID=arguments.memberID, xmlFields=local.NewAcctFormXMLFields)>

		<!--- if the member was NOT just created, then just load the member name fields --->
		<cfelse>
			<cfset local.qryMember = application.objMember.getMemberInfo(memberID=arguments.memberID, orgID=arguments.orgID)>
			<cfset local.strReturn = structNew()>
			<cfset StructInsert(local.strReturn, "m_prefix", local.qryMember.prefix)>
			<cfset StructInsert(local.strReturn, "m_firstname", local.qryMember.firstname)>
			<cfset StructInsert(local.strReturn, "m_middlename", local.qryMember.middlename)>
			<cfset StructInsert(local.strReturn, "m_lastname", local.qryMember.lastname)>
			<cfset StructInsert(local.strReturn, "m_suffix", local.qryMember.suffix)>
			<cfset StructInsert(local.strReturn, "m_professionalsuffix", local.qryMember.professionalsuffix)>
			<cfset StructInsert(local.strReturn, "m_company", local.qryMember.company)>
			<cfreturn local.strReturn>
		</cfif>
	</cffunction>

	<cffunction name="mem_fieldsetData" access="public" output="false" returntype="struct">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="xmlFields" type="xml" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cfloop array="#XMLSearch(arguments.xmlFields,"/fields/mf[substring(@fieldCode,1,3)='me_']")#" index="local.thisNode">
			<cfset local.emailTypeID = getToken(local.thisNode.xmlattributes.fieldCode,2,"_")>
			<cfset local.thisNode.xmlAttributes["dbObject"] = "ams_memberEmails">
			<cfset local.thisNode.xmlAttributes["dbObjectAlias"] = "me#local.emailTypeID#">
			<cfset local.thisNode.xmlAttributes["emailTypeID"] = local.emailTypeID>
			<cfset local.thisNode.xmlAttributes["dbField"] = "email">
		</cfloop>
		<cfloop array="#XMLSearch(arguments.xmlFields,"/fields/mf[substring(@fieldCode,1,3)='mw_']")#" index="local.thisNode">
			<cfset local.websiteTypeID = getToken(local.thisNode.xmlattributes.fieldCode,2,"_")>
			<cfset local.thisNode.xmlAttributes["dbObject"] = "ams_memberWebsites">
			<cfset local.thisNode.xmlAttributes["dbObjectAlias"] = "mw#local.websiteTypeID#">
			<cfset local.thisNode.xmlAttributes["websiteTypeID"] = local.websiteTypeID>
			<cfset local.thisNode.xmlAttributes["dbField"] = "website">
		</cfloop>
		<cfloop array="#XMLSearch(arguments.xmlFields,"/fields/mf[substring(@fieldCode,1,4)='mpl_']")#" index="local.thisNode">
			<cfset local.licenseTypeID = getToken(local.thisNode.xmlattributes.fieldCode,2,"_")>
			<cfset local.licenseField = getToken(local.thisNode.xmlattributes.fieldCode,3,"_")>
			<cfset local.thisNode.xmlAttributes["dbObject"] = "ams_memberProfessionalLicenses">
			<cfset local.thisNode.xmlAttributes["dbObjectAlias"] = "mpl#local.licenseTypeID#">
			<cfset local.thisNode.xmlAttributes["PLTypeID"] = local.licenseTypeID>
			<cfif local.licenseField eq "status">
				<cfset local.thisNode.xmlAttributes["dbField"] = "PLStatusID">
			<cfelse>
				<cfset local.thisNode.xmlAttributes["dbField"] = local.licenseField>
			</cfif>
		</cfloop>
		<cfloop array="#XMLSearch(arguments.xmlFields,"/fields/mf[substring(@fieldCode,1,3)='ma_']")#" index="local.thisNode">
			<cfset local.addressTypeID = getToken(local.thisNode.xmlattributes.fieldCode,2,"_")>
			<cfset local.addressField = getToken(local.thisNode.xmlattributes.fieldCode,3,"_")>
			<cfset local.thisNode.xmlAttributes["dbObject"] = "ams_memberAddresses">
			<cfset local.thisNode.xmlAttributes["dbObjectAlias"] = "ma#local.addressTypeID#">
			<cfset local.thisNode.xmlAttributes["addressTypeID"] = local.addressTypeID>
			<cfif local.addressField eq "stateprov">
				<cfset local.thisNode.xmlAttributes["dbField"] = "stateID">
			<cfelseif local.addressField eq "country">
				<cfset local.thisNode.xmlAttributes["dbField"] = "countryID">
			<cfelse>
				<cfset local.thisNode.xmlAttributes["dbField"] = local.addressField>
			</cfif>
		</cfloop>
		<cfloop array="#XMLSearch(arguments.xmlFields,"/fields/mf[substring(@fieldCode,1,4)='mat_']")#" index="local.thisNode">
			<cfset local.addressTagTypeID = getToken(local.thisNode.xmlattributes.fieldCode,2,"_")>
			<cfset local.addressField = getToken(local.thisNode.xmlattributes.fieldCode,3,"_")>
			<cfset local.thisNode.xmlAttributes["dbObject"] = "ams_memberAddressTags">
			<cfset local.thisNode.xmlAttributes["dbObjectAlias"] = "matag#local.addressTagTypeID#">
			<cfset local.thisNode.xmlAttributes["addressTagTypeID"] = local.addressTagTypeID>
			<cfif local.addressField eq "addressType">
				<cfset local.thisNode.xmlAttributes["dbField"] = "addressTypeID">
			<cfelseif local.addressField eq "stateprov">
				<cfset local.thisNode.xmlAttributes["dbField"] = "stateID">
			<cfelseif local.addressField eq "country">
				<cfset local.thisNode.xmlAttributes["dbField"] = "countryID">
			<cfelse>
				<cfset local.thisNode.xmlAttributes["dbField"] = local.addressField>
			</cfif>
		</cfloop>
		<cfloop array="#XMLSearch(arguments.xmlFields,"/fields/mf[substring(@fieldCode,1,3)='mp_']")#" index="local.thisNode">
			<cfset local.addressTypeID = getToken(local.thisNode.xmlattributes.fieldCode,2,"_")>
			<cfset local.phoneTypeID = getToken(local.thisNode.xmlattributes.fieldCode,3,"_")>
			<cfset local.thisNode.xmlAttributes["dbObject"] = "ams_memberPhones">
			<cfset local.thisNode.xmlAttributes["dbObjectAlias"] = "mp#local.addressTypeID##local.phoneTypeID#">
			<cfset local.thisNode.xmlAttributes["addressTypeID"] = local.addressTypeID>
			<cfset local.thisNode.xmlAttributes["phoneTypeID"] = local.phoneTypeID>
			<cfset local.thisNode.xmlAttributes["dbField"] = "phone">
		</cfloop>
		<cfloop array="#XMLSearch(arguments.xmlFields,"/fields/mf[substring(@fieldCode,1,4)='mpt_']")#" index="local.thisNode">
			<cfset local.addressTagTypeID = getToken(local.thisNode.xmlattributes.fieldCode,2,"_")>
			<cfset local.phoneTypeID = getToken(local.thisNode.xmlattributes.fieldCode,3,"_")>
			<cfset local.thisNode.xmlAttributes["dbObject"] = "ams_memberAddressTags">
			<cfset local.thisNode.xmlAttributes["dbObjectAlias"] = "mptag#local.addressTagTypeID##local.phoneTypeID#">
			<cfset local.thisNode.xmlAttributes["addressTagTypeID"] = local.addressTagTypeID>
			<cfset local.thisNode.xmlAttributes["phoneTypeID"] = local.phoneTypeID>
			<cfset local.thisNode.xmlAttributes["dbField"] = "phone">
		</cfloop>
		<cfloop array="#XMLSearch(arguments.xmlFields,"/fields/mf[substring(@fieldCode,1,3)='md_']")#" index="local.thisNode">
			<cfset local.columnID = local.thisNode.xmlattributes.mdColumnID>
			<cfset local.thisNode.xmlAttributes["dbObject"] = "ams_memberData">
			<cfset local.thisNode.xmlAttributes["dbObjectAlias"] = "md#local.columnID#">
			<cfif local.thisNode.xmlattributes.dataTypeCode eq "DATE">
				<cfset local.thisNode.xmlAttributes["dbField"] = "columnValueDate">
			<cfelseif local.thisNode.xmlattributes.dataTypeCode eq "DECIMAL2">
				<cfset local.thisNode.xmlAttributes["dbField"] = "columnValueDecimal2">
			<cfelseif local.thisNode.xmlattributes.dataTypeCode eq "INTEGER">
				<cfset local.thisNode.xmlAttributes["dbField"] = "columnValueInteger">
			<cfelseif local.thisNode.xmlattributes.dataTypeCode eq "BIT">
				<cfset local.thisNode.xmlAttributes["dbField"] = "columnValueBit">
			<cfelseif local.thisNode.xmlattributes.dataTypeCode eq "STRING">
				<cfset local.thisNode.xmlAttributes["dbField"] = "columnValueString">
			</cfif>
		</cfloop>

		<cfset local.orgID = application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgID>

		<cfquery name="local.qryMemberData" datasource="#application.dsn.membercentral.dsn#" result="local.qryMemberDataResult">
			select top 1 m.memberid
				<cfloop array="#arguments.xmlFields.xmlRoot.xmlChildren#" index="local.thisField">
					<cfset local.fieldCodeSect = getToken(local.thisField.xmlattributes.fieldCode,1,"_")>
					<cfif listFindNoCase("m,me,mw,ma,mp",local.fieldCodeSect)>
						, #local.thisField.xmlattributes.dbObjectAlias#.#local.thisField.xmlattributes.dbField# as [#local.thisField.xmlattributes.fieldCode#]
					<cfelseif local.fieldCodeSect eq "mpl" and local.thisField.xmlattributes.dataTypeCode eq "DATE">
						, convert(varchar(10),#local.thisField.xmlattributes.dbObjectAlias#.#local.thisField.xmlattributes.dbField#,101) as [#local.thisField.xmlattributes.fieldCode#]
					<cfelseif local.fieldCodeSect eq "mpl">
						, #local.thisField.xmlattributes.dbObjectAlias#.#local.thisField.xmlattributes.dbField# as [#local.thisField.xmlattributes.fieldCode#]
					<cfelseif local.fieldCodeSect eq "mat">
						, ma#local.thisField.xmlattributes.dbObjectAlias#.#local.thisField.xmlattributes.dbField# as [#local.thisField.xmlattributes.fieldCode#]
					<cfelseif local.fieldCodeSect eq "mpt">
						, mp#local.thisField.xmlattributes.dbObjectAlias#.#local.thisField.xmlattributes.dbField# as [#local.thisField.xmlattributes.fieldCode#]
					<cfelseif local.fieldCodeSect eq "md" and local.thisField.xmlattributes.allowMultiple is 0 and local.thisField.xmlattributes.dataTypeCode eq "BIT">
						, mdcv#local.thisField.xmlattributes.dbObjectAlias#.valueID as [#local.thisField.xmlattributes.fieldCode#]
					<cfelseif local.fieldCodeSect eq "md" and local.thisField.xmlattributes.allowMultiple is 0 and local.thisField.xmlattributes.dataTypeCode eq "DATE" and local.thisField.xmlattributes.displayTypeCode eq "DATE">
						, convert(varchar(10),mdcv#local.thisField.xmlattributes.dbObjectAlias#.#local.thisField.xmlattributes.dbField#,101) as [#local.thisField.xmlattributes.fieldCode#]
					<cfelseif local.fieldCodeSect eq "md" and local.thisField.xmlattributes.allowMultiple is 0 and local.thisField.xmlattributes.dataTypeCode eq "DATE">
						, mdcv#local.thisField.xmlattributes.dbObjectAlias#.valueID as [#local.thisField.xmlattributes.fieldCode#]
					<cfelseif local.fieldCodeSect eq "md" and local.thisField.xmlattributes.allowMultiple is 0 and local.thisField.xmlattributes.dataTypeCode eq "DECIMAL2" and local.thisField.xmlattributes.displayTypeCode eq "TEXTBOX">
						, mdcv#local.thisField.xmlattributes.dbObjectAlias#.#local.thisField.xmlattributes.dbField# as [#local.thisField.xmlattributes.fieldCode#]
					<cfelseif local.fieldCodeSect eq "md" and local.thisField.xmlattributes.allowMultiple is 0 and local.thisField.xmlattributes.dataTypeCode eq "DECIMAL2">
						, mdcv#local.thisField.xmlattributes.dbObjectAlias#.valueID as [#local.thisField.xmlattributes.fieldCode#]
					<cfelseif local.fieldCodeSect eq "md" and local.thisField.xmlattributes.allowMultiple is 0 and local.thisField.xmlattributes.dataTypeCode eq "INTEGER" and local.thisField.xmlattributes.displayTypeCode eq "TEXTBOX">
						, mdcv#local.thisField.xmlattributes.dbObjectAlias#.#local.thisField.xmlattributes.dbField# as [#local.thisField.xmlattributes.fieldCode#]
					<cfelseif local.fieldCodeSect eq "md" and local.thisField.xmlattributes.allowMultiple is 0 and local.thisField.xmlattributes.dataTypeCode eq "INTEGER">
						, mdcv#local.thisField.xmlattributes.dbObjectAlias#.valueID as [#local.thisField.xmlattributes.fieldCode#]
					<cfelseif local.fieldCodeSect eq "md" and local.thisField.xmlattributes.allowMultiple is 0 and local.thisField.xmlattributes.dataTypeCode eq "STRING" and local.thisField.xmlattributes.displayTypeCode eq "TEXTBOX">
						, mdcv#local.thisField.xmlattributes.dbObjectAlias#.#local.thisField.xmlattributes.dbField# as [#local.thisField.xmlattributes.fieldCode#]
					<cfelseif local.fieldCodeSect eq "md" and local.thisField.xmlattributes.allowMultiple is 0 and local.thisField.xmlattributes.dataTypeCode eq "STRING">
						, mdcv#local.thisField.xmlattributes.dbObjectAlias#.valueID as [#local.thisField.xmlattributes.fieldCode#]
					<cfelseif local.thisField.xmlattributes.allowMultiple is 1>
						, replace(#local.thisField.xmlattributes.dbObjectAlias#.pipeValue,'|',',') as [#local.thisField.xmlattributes.fieldCode#]
					</cfif>
				</cfloop>
			from dbo.ams_members as m

			<cfset local.aliasesAlreadyCreated = "m">
			<cfloop array="#arguments.xmlFields.xmlRoot.xmlChildren#" index="local.thisField">
				<cfif not listFindNoCase(local.aliasesAlreadyCreated,local.thisField.xmlattributes.dbObjectAlias)>
					<cfset local.aliasesAlreadyCreated = listAppend(local.aliasesAlreadyCreated,local.thisField.xmlattributes.dbObjectAlias)>
					<cfswitch expression="#getToken(local.thisField.xmlattributes.fieldCode,1,"_")#">
						<cfcase value="me">
							inner join dbo.#local.thisField.xmlattributes.dbObject# as #local.thisField.xmlattributes.dbObjectAlias# on #local.thisField.xmlattributes.dbObjectAlias#.orgID = #local.orgID#
								and #local.thisField.xmlattributes.dbObjectAlias#.memberid = m.memberid 
								and #local.thisField.xmlattributes.dbObjectAlias#.emailTypeID = #local.thisField.xmlattributes.emailTypeID#
						</cfcase>
						<cfcase value="mw">
							inner join dbo.#local.thisField.xmlattributes.dbObject# as #local.thisField.xmlattributes.dbObjectAlias# on #local.thisField.xmlattributes.dbObjectAlias#.orgID = #local.orgID#
								and #local.thisField.xmlattributes.dbObjectAlias#.memberid = m.memberid 
								and #local.thisField.xmlattributes.dbObjectAlias#.websiteTypeID = #local.thisField.xmlattributes.websiteTypeID#
						</cfcase>
						<cfcase value="mpl">
							left outer join dbo.#local.thisField.xmlattributes.dbObject# as #local.thisField.xmlattributes.dbObjectAlias# on #local.thisField.xmlattributes.dbObjectAlias#.memberid = m.memberid 
								and #local.thisField.xmlattributes.dbObjectAlias#.PLTypeID = #local.thisField.xmlattributes.PLTypeID#
						</cfcase>
						<cfcase value="ma">
							left outer join dbo.#local.thisField.xmlattributes.dbObject# as #local.thisField.xmlattributes.dbObjectAlias# on #local.thisField.xmlattributes.dbObjectAlias#.orgID = #local.orgID#
								and #local.thisField.xmlattributes.dbObjectAlias#.memberid = m.memberid 
								and #local.thisField.xmlattributes.dbObjectAlias#.addressTypeID = #local.thisField.xmlattributes.addressTypeID#
						</cfcase>
						<cfcase value="mat">
							left outer join dbo.#local.thisField.xmlattributes.dbObject# as #local.thisField.xmlattributes.dbObjectAlias#
								inner join dbo.ams_memberAddresses as ma#local.thisField.xmlattributes.dbObjectAlias# on ma#local.thisField.xmlattributes.dbObjectAlias#.orgID = #local.orgID#
									and ma#local.thisField.xmlattributes.dbObjectAlias#.addressTypeID = #local.thisField.xmlattributes.dbObjectAlias#.addressTypeID 
									and ma#local.thisField.xmlattributes.dbObjectAlias#.memberID = #local.thisField.xmlattributes.dbObjectAlias#.memberID 
								inner join dbo.ams_memberAddressTypes as mat#local.thisField.xmlattributes.dbObjectAlias# on mat#local.thisField.xmlattributes.dbObjectAlias#.orgID = #local.orgID# 
									and mat#local.thisField.xmlattributes.dbObjectAlias#.addressTypeID = ma#local.thisField.xmlattributes.dbObjectAlias#.addressTypeID 
								on #local.thisField.xmlattributes.dbObjectAlias#.memberID = m.memberID 
								and #local.thisField.xmlattributes.dbObjectAlias#.addressTagTypeID = #local.thisField.xmlattributes.addressTagTypeID#
						</cfcase>
						<cfcase value="mp">
							left outer join dbo.ams_memberAddresses as ma#local.thisField.xmlattributes.dbObjectAlias# on ma#local.thisField.xmlattributes.dbObjectAlias#.orgID = #local.orgID#
								and ma#local.thisField.xmlattributes.dbObjectAlias#.memberid = m.memberid 
								and ma#local.thisField.xmlattributes.dbObjectAlias#.addressTypeID = #local.thisField.xmlattributes.addressTypeID#
							left outer join dbo.#local.thisField.xmlattributes.dbObject# as #local.thisField.xmlattributes.dbObjectAlias# on #local.thisField.xmlattributes.dbObjectAlias#.orgID = #local.orgID#
								and #local.thisField.xmlattributes.dbObjectAlias#.memberID = m.memberID
								and #local.thisField.xmlattributes.dbObjectAlias#.addressID = ma#local.thisField.xmlattributes.dbObjectAlias#.addressID 
								and #local.thisField.xmlattributes.dbObjectAlias#.phoneTypeID = #local.thisField.xmlattributes.phoneTypeID#
						</cfcase>
						<cfcase value="mpt">
							left outer join dbo.#local.thisField.xmlattributes.dbObject# as #local.thisField.xmlattributes.dbObjectAlias#
								inner join dbo.ams_memberAddresses as ma#local.thisField.xmlattributes.dbObjectAlias# on ma#local.thisField.xmlattributes.dbObjectAlias#.orgID = #local.orgID#
									and ma#local.thisField.xmlattributes.dbObjectAlias#.addressTypeID = #local.thisField.xmlattributes.dbObjectAlias#.addressTypeID 
									and ma#local.thisField.xmlattributes.dbObjectAlias#.memberID = #local.thisField.xmlattributes.dbObjectAlias#.memberID 
								inner join dbo.ams_memberAddressTypes as mat#local.thisField.xmlattributes.dbObjectAlias# on mat#local.thisField.xmlattributes.dbObjectAlias#.orgID = #local.orgID#
									and mat#local.thisField.xmlattributes.dbObjectAlias#.addressTypeID = ma#local.thisField.xmlattributes.dbObjectAlias#.addressTypeID 
								on #local.thisField.xmlattributes.dbObjectAlias#.memberID = m.memberID 
								and #local.thisField.xmlattributes.dbObjectAlias#.addressTagTypeID = #local.thisField.xmlattributes.addressTagTypeID#
							left outer join dbo.ams_memberPhones as mp#local.thisField.xmlattributes.dbObjectAlias# on mp#local.thisField.xmlattributes.dbObjectAlias#.orgID = #local.orgID#
								and mp#local.thisField.xmlattributes.dbObjectAlias#.memberID = m.memberID
								and mp#local.thisField.xmlattributes.dbObjectAlias#.phoneTypeID = #local.thisField.xmlattributes.phoneTypeID#
								and mp#local.thisField.xmlattributes.dbObjectAlias#.addressID = ma#local.thisField.xmlattributes.dbObjectAlias#.addressID 
						</cfcase>
						<cfcase value="md">
							<cfif local.thisField.xmlattributes.allowMultiple is 0>
								left outer join dbo.#local.thisField.xmlattributes.dbObject# as #local.thisField.xmlattributes.dbObjectAlias# 
									inner join dbo.ams_memberDataColumnValues as mdcv#local.thisField.xmlattributes.dbObjectAlias# on mdcv#local.thisField.xmlattributes.dbObjectAlias#.valueID = #local.thisField.xmlattributes.dbObjectAlias#.valueID 
										and mdcv#local.thisField.xmlattributes.dbObjectAlias#.columnID = #local.thisField.xmlattributes.mdColumnID#
									on #local.thisField.xmlattributes.dbObjectAlias#.memberid = m.memberid
							<cfelse>
								left outer join (
									select STRING_AGG(pipeValue,'|') as pipeValue,max(memberid) as memberid
									FROM 
									(select distinct mdcv#local.thisField.xmlattributes.dbObjectAlias#.valueID as pipeValue,md#local.thisField.xmlattributes.dbObjectAlias#.memberid as memberid
									from dbo.#local.thisField.xmlattributes.dbObject# as md#local.thisField.xmlattributes.dbObjectAlias# 
									inner join dbo.ams_memberDataColumnValues as mdcv#local.thisField.xmlattributes.dbObjectAlias# on mdcv#local.thisField.xmlattributes.dbObjectAlias#.valueID = md#local.thisField.xmlattributes.dbObjectAlias#.valueID and mdcv#local.thisField.xmlattributes.dbObjectAlias#.columnID = #local.thisField.xmlattributes.mdColumnID#
									where md#local.thisField.xmlattributes.dbObjectAlias#.memberID = <cfqueryparam value="#arguments.memberID#" cfsqltype="CF_SQL_INTEGER">) as tmp
								) as #local.thisField.xmlattributes.dbObjectAlias# on #local.thisField.xmlattributes.dbObjectAlias#.memberid = m.memberid
							</cfif>
						</cfcase>
					</cfswitch>
				</cfif>
			</cfloop>
			where m.memberID = <cfqueryparam value="#arguments.memberID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
				
		<cfset local.columnLabels = QueryColumnArray(local.qryMemberData)>
		<cfloop array="#local.columnLabels#" index="local.colName">
			<cfif local.colName neq "memberid">
				<cfset StructInsert(local.strReturn, local.colName, local.qryMemberData[local.colName][1])>
			</cfif>
		</cfloop>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="mem_DepoMemberData" access="public" output="false" returntype="Query">
		<cfargument name="memberNumber" type="string" required="true">
		<cfargument name="orgID" type="numeric" required="true">		

		<cfset var local = structNew()>

		<cfquery name="local.qryGetDepoMemberData" datasource="#application.dsn.membercentral.dsn#">
			select 
				distinct np.depomemberdataid
			from 
			dbo.ams_memberNetworkProfiles as mnp
			inner join dbo.ams_networkProfiles as np on 
				np.profileID = mnp.profileID 
				AND np.status = 'A'
			inner join dbo.ams_members as m on
				m.memberID = mnp.memberid 
				and m.activeMemberID = m.memberID
				and m.orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orgID#">
				and m.memberNumber = <cfqueryparam value="#arguments.memberNumber#" cfsqltype="cf_sql_varchar"> 
		</cfquery>

		<cfreturn local.qryGetDepoMemberData>
	</cffunction>	

	<cffunction name="mem_StoreMembershipApplication" access="public" returntype="void" output="false">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="strPDF" type="struct" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="docTitle" type="string" required="false" default="Membership Application - #DateFormat(dateTimeFormat(now()),'m/d/yyyy')#">
		<cfargument name="docDesc" type="string" required="false" default="Membership Application Confirmation">

		<cfset var local = structNew()>
		<cfset local.objMemberAdmin = CreateObject("component","model.admin.members.members")>
		<cfset local.objSection = CreateObject("component","model.system.platform.section")>

		<cfset local.newFile = { serverDirectory=arguments.strPDF.serverDirectory, clientFile=arguments.strPDF.serverFile, clientFileExt='pdf', 
									serverFile=arguments.strPDF.serverFile, serverFileExt='pdf' } >
		<cfset local.docSectionID = local.objSection.getSectionFromSectionCode(siteID=arguments.siteID, sectionCode="MCAMSMemberDocuments").sectionID>
		<cfset local.siteCode = application.objSiteInfo.getSiteCodeFromSiteID(siteID=arguments.siteID)>
		<cfset local.parentSiteResourceID = application.objSiteInfo.mc_siteInfo[local.siteCode].memberAdminSiteResourceID>

		<cfset local.insertResults = CreateObject("component","model.system.platform.document").insertDocument(siteID=arguments.siteID, 
			resourceType='ApplicationCreatedDocument', parentSiteResourceID=local.parentSiteResourceID, sectionID=local.docSectionID, 
			docTitle=arguments.docTitle, docDesc=arguments.docDesc, author='', fileData=local.newFile, isActive=1, isVisible=true, 
			contributorMemberID=arguments.memberid, recordedByMemberID=arguments.memberid, oldFileExt='pdf')>

		<cfset local.objMemberAdmin.saveMemberDocument(memberID=arguments.memberID, documentID=local.insertResults.documentID)>
	</cffunction>

	<cffunction name="mem_getSubscriptionRates" access="public" output="false" returntype="Query">
		<cfargument name="subUID" required="true" type="string">
		<cfargument name="memberID" required="true" type="numeric">
        
		<cfset var local = structNew()>
		<cfset local.qualifySubRateRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SubscriptionRate", functionName="qualify")>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryGetMemSubscriptionRates">
			SET NOCOUNT ON;

			declare @scheduleID int, @subscriptionID int, @FID int, @siteID int;
			select @FID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qualifySubRateRFID#">;

			select @scheduleID = scheduleID, @subscriptionID = subscriptionID, @siteID = t.siteID
			from dbo.sub_subscriptions as s
			inner join dbo.sub_types as t on t.typeID = s.typeID
			where uid = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.subUID#">;
			
			select subs.subscriptionName, sr.rateID, sr.uid,  sr.rateName, 
				sr.rateAFStartDate, sr.rateAFEndDate, sr.termAFStartDate, sr.termAFEndDate,
				sr.frontEndAllowChangePrice,rs.scheduleName,rf.rateAmt, f.frequencyShortName,rf.rfid,f.frequencyName
			from dbo.sub_subscriptions subs
			inner join dbo.sub_rateSchedules as rs on rs.scheduleID = subs.scheduleID
				and rs.status = 'A'
				and subs.subscriptionID = @subscriptionID
			inner join dbo.sub_rates as sr on sr.scheduleID = rs.scheduleID 
				and sr.status = 'A' 
				and sr.isRenewalRate = 0
				and month(sr.rateAFStartDate) = MONTH('#dateFormat(dateTimeFormat(now()),"YYYY/mm/dd")#')
				and month(sr.rateAFEndDate) = MONTH('#dateFormat(dateTimeFormat(now()),"YYYY/mm/dd")#')
			inner join dbo.sub_rateFrequencies rf on rf.rateID = sr.rateID and rf.status = 'A'
			inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID and f.status = 'A'
			inner join dbo.cache_perms_siteResourceFunctionRightPrints srfrp on srfrp.siteID = @siteID
				AND srfrp.siteResourceID = sr.siteResourceID 
				and srfrp.functionID = @FID
			inner join dbo.cache_perms_groupPrintsRightPrints gprp on gprp.siteID = @siteID
				and srfrp.rightPrintID = gprp.rightPrintID
			inner join dbo.ams_members m on m.memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">
				AND m.groupPrintID = gprp.groupPrintID;
		</cfquery>

		<cfreturn local.qryGetMemSubscriptionRates>
	</cffunction>

	<!--- --------------- --->
	<!--- Event functions --->
	<!--- --------------- --->
	<cffunction name="ev_objEventReg" access="public" output="false" returntype="struct">
		<cfargument name="eventID" type="numeric" required="true">
		<cfreturn createObject("component","model.system.platform.registerEventObj").init(eventID=arguments.eventID)>
	</cffunction>

	<cffunction name="ev_getInvoicesForRegistration" access="public" output="false" returntype="query">
		<cfargument name="registrantID" type="numeric" required="true">

		<cfset var qryData = "">

		<cfquery name="qryData" datasource="#application.dsn.membercentral.dsn#">
			select i.invoiceID, i.invoiceProfileID, ins.status, sum(it.cache_invoiceAmountAfterAdjustment - it.cache_activePaymentAllocatedAmount) as amtDue
			from dbo.fn_ev_registrantTransactions(<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.registrantID#">) as rt
			inner join dbo.tr_invoiceTransactions as it on it.orgID = rt.ownedByOrgID and it.transactionID = rt.transactionID
			inner join dbo.tr_invoices as i on i.orgID = rt.ownedByOrgID and i.invoiceID = it.invoiceID
			inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
			group by i.invoiceID, i.invoiceProfileID, ins.status
		</cfquery>

		<cfreturn qryData>
	</cffunction>

	<cffunction name="ev_getRatesForEvent" access="public" output="false" returntype="query">
		<cfargument name="eventID" type="numeric" required="yes">
		<cfargument name="memberID" type="numeric" required="yes">
		<cfargument name="showAllRates" type="boolean" required="no" default="0">

		<cfset var local = structNew()>
		<cfset local.QualifyRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="EventRate", functionName="Qualify")>

		<cfquery name="local.qryRegistration" datasource="#application.dsn.membercentral.dsn#">
			select registrationID
			from dbo.ev_registration
			where eventID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.eventID#">
			and status = 'A'
		</cfquery>

		<cfstoredproc procedure="ev_getRatesByRegistrationID" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#local.qryRegistration.registrationID#">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.memberID#">
			<cfprocparam type="In" cfsqltype="cf_sql_bit" value="#arguments.showAllRates#">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#local.QualifyRFID#">
			<cfprocresult name="local.qryEventRegRates" resultset="1">
		</cfstoredproc>

		<cfreturn local.qryEventRegRates>
	</cffunction>

	<cffunction name="ev_getCustomFieldsForEvent" access="public" output="false" returntype="xml">
		<cfargument name="eventID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cfquery name="local.qryEventSRID" datasource="#application.dsn.membercentral.dsn#">
			select siteID, siteResourceID
			from dbo.ev_events
			where eventID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.eventID#">
		</cfquery>

		<cfstoredproc procedure="cf_getFieldsXML" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryEventSRID.siteID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="Event"> 
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="Registrant">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryEventSRID.siteResourceID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="2">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
			<cfprocresult name="local.qryFields2">
		</cfstoredproc>

		<cfstoredproc procedure="cf_getFieldsXML" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryEventSRID.siteID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="Event"> 
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="Registrant">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryEventSRID.siteResourceID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="3">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
			<cfprocresult name="local.qryFields3">
		</cfstoredproc>

		<cfset local.tempXML2 = XMLParse("<allfields><end/></allfields>")>
		<cfset local.xmlReturn2 = XMLParse(local.qryFields2.returnXML)>
		<cfset local.xmlReturn2 = application.objCommon.combineXML(rootXML=local.tempXML2, secondaryXML=local.xmlReturn2)>
		<cfset local.xmlReturn3 = XMLParse(local.qryFields3.returnXML)>
		<cfset local.finalXML = application.objCommon.combineXML(rootXML=local.xmlReturn2, secondaryXML=local.xmlReturn3)>

		<cfreturn local.finalXML.xmlRoot>
	</cffunction>

	<cffunction name="ev_getCalendars" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="limitToCalendar" type="string" required="no" default="">
			
		<cfset var local = structNew()>

		<cfset local.qryCalendars = CreateObject("component","model.events.calendar").getCalendars(siteID=arguments.siteID)>

		<cfif len(arguments.limitToCalendar)>
			<cfquery name="local.qryCalendarsFiltered" dbtype="query">
				select calendarID, calendarName, siteResourceStatusID
				from [local].qryCalendars
				where lower(calendarName) = '#LCASE(arguments.limitToCalendar)#'
				order by calendarName
			</cfquery>

			<cfreturn local.qryCalendarsFiltered>
		<cfelse>
			<cfreturn local.qryCalendars>
		</cfif>
	</cffunction>

	<cffunction name="ev_getCategoriesOnCalendar" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="calendarID" type="numeric" required="yes">
		<cfargument name="limitToCategory" type="string" required="no" default="">
			
		<cfset var local = structNew()>

		<cfset local.qryCategories = CreateObject("component","model.events.calendar").getCategories(arguments.calendarID,1,arguments.siteID)>

		<cfif len(arguments.limitToCategory)>
			<cfquery name="local.qryCategoriesFiltered" dbtype="query">
				select categoryID, category, calColor, categoryShort, visibility
				from [local].qryCategories
				where lower(category) = '#LCASE(arguments.limitToCategory)#'
				order by category
			</cfquery>

			<cfreturn local.qryCategoriesFiltered>
		<cfelse>
			<cfreturn local.qryCategories>
		</cfif>
	</cffunction>

	<!--- this function is used by only 2 INDY custom pages. It should be removed should those pages be removed --->
	<cffunction name="ev_getUpcomingEvents" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="limitTop" type="numeric" required="yes">
		<cfargument name="limitCategoryIDList" type="string" required="yes">

		<cfset var qryData = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryData">
			set nocount on;

			declare @nowDate datetime;
			set @nowDate = getdate();

			-- get events on site
			IF OBJECT_ID('tempdb..##tmpEventsOnSite') IS NOT NULL 
				DROP TABLE ##tmpEventsOnSite;
			CREATE TABLE ##tmpEventsOnSite (calendarID int, eventID int, [status] char(1), isPastEvent bit,
				startTime datetime, endTime datetime, timeZoneID int, timeZoneCode varchar(25), timeZoneAbbr varchar(4),
				displayStartTime datetime, displayEndTime datetime, displayTimeZoneID int, displayTimeZoneCode varchar(25),
				displayTimeZoneAbbr varchar(4), siteResourceID int, isAllDayEvent bit, altRegistrationURL varchar(300),
				eventTitle varchar(200), eventSubTitle varchar(200), locationTitle varchar(200), 
				categoryIDList varchar(max));
			EXEC dbo.ev_getEventsOnSite @siteID=#arguments.siteID#, @startDate=@nowDate, @endDate=null, @categoryIDList='#arguments.limitCategoryIDList#';

			SELECT TOP #arguments.limitTop# e.eventID, tmp.eventTitle, tmp.startTime
			FROM dbo.ev_events e
			INNER JOIN ##tmpEventsOnSite as tmp on tmp.eventID = e.eventID
			WHERE tmp.status = 'A'
			AND isnull(e.hiddenFromCalendar,0) = 0
			ORDER BY tmp.startTime;

			IF OBJECT_ID('tempdb..##tmpEventsOnSite') IS NOT NULL 
				DROP TABLE ##tmpEventsOnSite;
		</cfquery>	

		<cfreturn qryData>
	</cffunction>


	<!--- ---------------------- --->
	<!--- Subscription functions --->
	<!--- ---------------------- --->
	<cffunction name="sub_getTypeFromUID" access="public" output="false" returntype="numeric">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="typeUID" type="string" required="true">

		<cfset var qryData = "">

		<cfquery name="qryData" datasource="#application.dsn.membercentral.dsn#">
			select typeID
			from dbo.sub_types
			where siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
			and UID = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.typeUID#">
		</cfquery>

		<cfreturn val(qryData.typeID)>
	</cffunction>

	<cffunction name="sub_getSubscriptionFromUID" access="public" output="false" returntype="numeric">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="uid" type="string" required="true">

		<cfset var qryData = "">

		<cfquery name="qryData" datasource="#application.dsn.membercentral.dsn#">
			select subscriptionID
			from dbo.sub_types t
			inner join sub_subscriptions subs
				on subs.typeID = t.typeID
				and subs.uid = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.uid#">
				and t.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
		</cfquery>

		<cfreturn val(qryData.subscriptionID)>
	</cffunction>

	<cffunction name="sub_hasSubsciptionInType" access="public" output="false" returntype="boolean">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="typeID" type="numeric" required="true">
		
		<cfscript>
			var local = structNew();
			local.hasSub = false;
			if (not structKeyExists(variables,"objsubscriptions"))
				variables.objsubscriptions = createObject("component", "model.admin.subscriptions.subscriptions");
			local.subList = DeserializeJSON(variables.objsubscriptions.getSubscriptionsForSubType(arguments.typeID),false);
		</cfscript>
		<cfloop query="local.subList">
			<cfif variables.objsubscriptions.checkMemberSub(mcproxy_orgID=arguments.mcproxy_orgID, mcproxy_siteID=arguments.mcproxy_siteID, subID=val(local.subList.subscriptionID), memberID=arguments.memberID).subscribed eq true>
				<cfset local.hasSub = true>
				<cfbreak>
			</cfif>
		</cfloop>
		
		<cfreturn local.hasSub>
	</cffunction>

	<cffunction name="sub_getSubscriptions" access="public" output="false" returntype="query">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="status" type="string" required="true">
		<cfargument name="distinct" type="boolean" required="false" default="false">
		<cfargument name="includeRate" type="boolean" required="false" default="false">

		<cfset var qryData = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryData">
			set nocount on

			declare @siteID int, @memberID int, @status char(1);
			set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">;
			set @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberid#">;
			set @status = <cfqueryparam cfsqltype="CF_SQL_CHAR" value="#arguments.status#">;
			
			select <cfif arguments.distinct>distinct<cfelse>s.subscriberID as rootSubscriberID, rms.subscriberID,</cfif> rms.thePathExpanded, rms.subscriptionName, 
				st.uid as typeUID, st.typeName, st.typeCode, s.subStartDate, s.subEndDate, s.offerRescindDate, rms.lastPrice, rms.modifiedRate, rms.linkedNonRenewalRateID, 
				rms.keepChangedPriceOnRenewal, rms.frontEndAllowChangePrice, rms.frontEndChangePriceMin, rms.frontEndChangePriceMax, rms.paymentStatus, 
				rms.paymentStatusName, s.directLinkCode, subs.uid, rms.subscriptionID
				<cfif arguments.includeRate>
					, r.UID AS rateUID, r.rateName,r.isRenewalRate
				</cfif>
			from dbo.sub_subscribers s
			inner join dbo.ams_members as m on m.memberID = s.memberID
			cross apply dbo.fn_getRecursiveMemberSubscriptions(@memberID, @siteID, s.subscriberID) as rms
			inner join dbo.sub_subscriptions subs on subs.subscriptionID = rms.subscriptionID
			inner join dbo.sub_types st on rms.typeName = st.typeName and st.siteID = @siteID
			<cfif arguments.includeRate>
				inner join dbo.sub_rateFrequencies as rf on rf.RFID = rms.RFID
				inner join dbo.sub_rates as r on r.rateID = rf.rateID
			</cfif>
			where m.activeMemberID = @memberID
			and rms.status = @status
			and s.parentSubscriberID is NULL;
		</cfquery>
		
		<cfreturn qryData>
	</cffunction>	

	<cffunction name="sub_getEligibleRates" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="subscriptionUID" type="string" required="true">
		<cfargument name="isRenewal" type="boolean" required="true">
		<cfargument name="rateUID" type="string" required="false" default="">
		<cfargument name="overridePerms" type="boolean" required="false" default="false">
		<cfargument name="frequencyShortName" type="string" required="false">
		<cfargument name="allowFrontEnd" type="boolean" required="false">

		<cfset var local = structNew()>

		<cfquery name="local.qrySubscription" datasource="#application.dsn.membercentral.dsn#">
			select subscriptionID
			from dbo.sub_subscriptions as s
			inner join dbo.sub_types as t on t.typeID = s.typeID
			where t.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
			and s.UID = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.subscriptionUID#">
		</cfquery>

		<cfset local.qryRates = CreateObject("component","model.admin.subscriptions.subscriptionReg").getRates(siteID=arguments.siteID, 
				memberID=arguments.memberID, subscriptionID=local.qrySubscription.subscriptionID, isRenewal=arguments.isRenewal,
				rateUID=arguments.rateUID, overridePerms=arguments.overridePerms)>

		<cfquery name="local.qryRatesReturn" dbtype="query">
			select rfid, rateAmt, numInstallments, allowFrontEnd, frequencyName, frequencyShortName, frequency, frequencyID, rateName, rateUID, frequencyUID
			from [local].qryRates
			where 1=1
			<cfif isDefined("arguments.frequencyShortName") and len(arguments.frequencyShortName)>
				and frequencyShortName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.frequencyShortName#">
			</cfif>
			<cfif isDefined("arguments.allowFrontEnd") and arguments.allowFrontEnd eq true>
				and allowFrontEnd = 1
			</cfif>
			order by rateAmt desc
		</cfquery>

		<cfreturn local.qryRatesReturn>
	</cffunction>

	<cffunction name="sub_getEligibleRatesAJX" access="public" output="false" returntype="query">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="subscriptionUID" type="string" required="true">
		<cfargument name="isRenewal" type="boolean" required="true">
		
		<cfreturn sub_getEligibleRates(siteID=arguments.mcproxy_siteID, memberID=arguments.memberID, subscriptionUID=arguments.subscriptionUID, isRenewal=arguments.isRenewal)>
	</cffunction>

	<cffunction name="sub_getRateSchedule" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="scheduleUID" type="string" required="true" hint="can be a list of UIDs">
		<cfargument name="rateUID" type="string" required="false" hint="can be a list of UIDs">
		<cfargument name="activeRatesOnly" type="string" required="false" default="false">
		<cfargument name="ignoreRenewalRates" type="string" required="false" default="false">
		<cfargument name="isFrequencyFull" type="string" required="false" default="false">
		<cfargument name="isAllowFrontEndOnly" type="boolean" required="false" default="false">
		
		<cfset var qryData = "">
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryData">
			select r.rateID, r.scheduleID, rs.scheduleName, r.rateName, r.UID as rateUID,
				f.frequencyID, f.frequencyName, rf.rateAmt, r.rateAFStartDate, r.rateAFEndDate, r.isRenewalRate, r.frontEndAllowChangePrice, rf.allowfrontend
			from dbo.sub_rates r
			inner join dbo.sub_rateSchedules rs on rs.scheduleID = r.scheduleID
			inner join dbo.sub_rateFrequencies rf on rf.rateID = r.rateID
			inner join dbo.sub_frequencies f on f.frequencyID = rf.frequencyID
			where rs.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
			and rs.uid in (<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.scheduleUID#" list="true">)
			and rs.status = 'A'
			and r.status = 'A'
			<cfif isDefined("arguments.ignoreRenewalRates") and arguments.ignoreRenewalRates eq true>
				and r.isRenewalRate = 0
			</cfif>			
			<cfif isDefined("arguments.activeRatesOnly") and arguments.activeRatesOnly eq true>
				and getDate() between r.rateAFStartDate and r.rateAFEndDate
			</cfif>			
			<cfif isDefined("arguments.rateUID") and listLen(arguments.rateUID)>
				and r.uid in (<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.rateUID#" list="true">)
			</cfif>			
			<cfif isDefined("arguments.isFrequencyFull") and arguments.isFrequencyFull eq true>
				and f.frequencyShortName = 'F'
				and f.isSystemRate = 1
			</cfif>
			<cfif isDefined("arguments.isAllowFrontEndOnly") and arguments.isAllowFrontEndOnly eq true>
				and rf.allowfrontend = 1
			</cfif>			
			order by rs.scheduleName, f.frequencyName, r.rateName						
		</cfquery>
	
		<cfreturn qryData>
	</cffunction>

	<cffunction name="sub_getRate" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="rateUID" type="string" required="true" hint="can be a list of UIDs">
		<cfargument name="activeRatesOnly" type="string" required="false" default="false">
		<cfargument name="ignoreRenewalRates" type="string" required="false" default="false">
		<cfargument name="isFrequencyFull" type="string" required="false" default="false">
		<cfargument name="isAllowFrontEndOnly" type="boolean" required="false" default="false">
		
		<cfset var local = structNew()>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryData">
			select r.rateID, r.scheduleID, rs.scheduleName, rs.UID as scheduleUID, r.rateName, r.UID as rateUID,
				f.frequencyID, f.frequencyName, rf.rateAmt, r.rateAFStartDate, r.rateAFEndDate, r.isRenewalRate, r.frontEndAllowChangePrice, rf.allowfrontend
			from dbo.sub_rates r
			inner join dbo.sub_rateSchedules rs on rs.scheduleID = r.scheduleID
			inner join dbo.sub_rateFrequencies rf on rf.rateID = r.rateID
			inner join dbo.sub_frequencies f on f.frequencyID = rf.frequencyID
			where rs.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
			and r.uid in (<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.rateUID#" list="true">)
			and rs.status = 'A'
			and r.status = 'A'
			<cfif arguments.ignoreRenewalRates eq true>
				and r.isRenewalRate = 0
			</cfif>			
			<cfif arguments.activeRatesOnly eq true>
				and getDate() between r.rateAFStartDate and r.rateAFEndDate
			</cfif>		
			<cfif arguments.isFrequencyFull eq true>
				and f.frequencyShortName = 'F'
				and f.isSystemRate = 1
			</cfif>
			<cfif arguments.isAllowFrontEndOnly eq true>
				and rf.allowfrontend = 1
			</cfif>
			
			order by rs.scheduleName, f.frequencyName, r.rateName						
		</cfquery>
	
		<cfreturn local.qryData>
	</cffunction>

	<cffunction name="sub_isbilledSubscription" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="typeName" type="string" required="yes">
		<cfargument name="memberID" type="numeric" required="yes">

		<cfset var local = StructNew()>

		<cfquery name="local.qrySubscription" datasource="#application.dsn.memberCentral.dsn#">
			select s.subscriberID, s.directLinkCode, ss.statusCode as status, count(s2.subscriberID) as currAccepted
			from dbo.sub_subscribers s
			inner join dbo.sub_subscriptions subs on subs.subscriptionID = s.subscriptionID
			inner join dbo.sub_types t on t.typeID = subs.typeID
				and t.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
				and t.typeName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.typeName#">
			inner join dbo.sub_statuses ss on ss.statusID = s.statusID
			left outer join dbo.sub_subscribers s2 
				inner join dbo.sub_subscriptions subs2 on subs2.subscriptionID = s2.subscriptionID
				inner join dbo.sub_types t2 on t2.typeID = subs2.typeID 
					and t2.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
					and t2.typeName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.typeName#">
				inner join dbo.sub_statuses ss2 on ss2.statusID = s2.statusID on s2.memberID = s.memberID
					and ss2.statusCode = 'P'
					and s2.parentSubscriberID is null
			where s.memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
			and ss.statusCode = 'O'
			and s.parentSubscriberID is null
			group by s.subscriberID, s.directLinkCode, ss.statusCode
		</cfquery>

		<cfset local.data.success = local.qrySubscription.recordcount eq 1>
		<cfset local.data.directLinkCode = local.qrySubscription.recordcount eq 1 ? local.qrySubscription.directLinkCode : "">

		<cfreturn local.data>
	</cffunction>

	<cffunction name="sub_getInvoicesForSubscription" access="public" output="false" returntype="query">
		<cfargument name="rootSubscriberID" type="numeric" required="true">

		<cfset var qryData = "">

		<cfquery name="qryData" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			declare @rootSubscriberID int, @siteID int, @orgID int;
			set @rootSubscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rootSubscriberID#">;

			select @siteID = t.siteID, @orgID = sub.orgID
			from dbo.sub_types as t
			inner join dbo.sub_subscriptions as s on s.typeID = t.typeID
			inner join dbo.sub_subscribers as sub on sub.subscriptionID = s.subscriptionID
			where sub.subscriberID = @rootSubscriberID;

			IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
				DROP TABLE ##mcSubscribersForAcct;
			IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
				DROP TABLE ##mcSubscriberTransactions;

			CREATE TABLE ##mcSubscribersForAcct (subscriberID int PRIMARY KEY);
			CREATE TABLE ##mcSubscriberTransactions (subscriberID int, invoiceID int, dateDue date, invoiceStatusID int, payProfileID int,
				invoiceProfileID int, mainTransactionID int, transactionID int, statusID int, detail varchar(500), parentTransactionID int,
				amount decimal(18,2), amountDue decimal(18,2), amountToConsider decimal(18,2), dateRecorded datetime, transactionDate datetime, 
				assignedToMemberID int, recordedByMemberID int, statsSessionID int, typeID int, debitGLAccountID int,
				creditGLAccountID int, INDEX IDX_mcSubscriberTransactions_invoiceID_typeID (invoiceID, typeID));

			insert into ##mcSubscribersForAcct (subscriberID)
			select grs.subscriberID
			from dbo.fn_getRecursiveSubscriptionsByID(@siteID,@rootSubscriberID) as grs
			where grs.status <> 'D';

			EXEC dbo.sub_getSubscriberTransactions @orgID=@orgID;

			select i.invoiceID, i.invoiceProfileID, i.dateDue, inv.sumRevTotal, inv.sumTaxTotal, inv.sumRevTotal + inv.sumTaxTotal as sumTotal, 
				i.fullInvoiceNumber as invoiceNumber, i.invoiceCode
			from (
				select st.invoiceID, sum(case when st.typeID <> 7 then st.amount else 0 end) as sumRevTotal, sum(case when st.typeID = 7 then st.amount else 0 end) as sumTaxTotal
				from ##mcSubscriberTransactions as st
				group by st.invoiceID
				having sum(case when st.typeID <> 7 then st.amount else 0 end) > 0
			) as inv			
			inner join dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = inv.invoiceID
			inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = i.assignedToMemberID
			inner join dbo.organizations as o on o.orgID = m.orgID
			order by i.dateDue, i.invoiceID;

			IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
				DROP TABLE ##mcSubscribersForAcct;
			IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
				DROP TABLE ##mcSubscriberTransactions;
		</cfquery>

		<cfreturn qryData>
	</cffunction>

	<cffunction name="sub_getInvoicesDuesForSubscription" access="public" output="false" returntype="query">
		<cfargument name="rootSubscriberID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="orgID" type="numeric" required="true">

		<cfset var qryInvoicesDues = "">

		<cfquery name="qryInvoicesDues" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @orgID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orgID#">, @t_Tax int, @tr_SalesTaxTrans int;
			set @t_Tax = dbo.fn_tr_getTypeID('Sales Tax');
			set @tr_SalesTaxTrans = dbo.fn_tr_getRelationshipTypeID('SalesTaxTrans');

			select nv.invoiceID, nv.invoiceProfileID,
				totalAmount = sum(it.cache_invoiceAmountAfterAdjustment) + sum(isnull(tax.totalAmt,0)),
				dueNow = case when nv.dateDue < getdate() then 1 else 0 end
			from dbo.sub_subscribers s
			inner join dbo.sub_subscriptions subs on subs.subscriptionID = s.subscriptionID and s.rootSubscriberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.rootSubscriberID#">
			inner join dbo.sub_types t on t.typeID = subs.typeID and t.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
			inner join dbo.tr_applications ta on ta.orgID = @orgID and ta.itemID = s.subscriberID and ta.itemType = 'Dues'
			inner join dbo.cms_applicationTypes cat on cat.applicationTypeID=ta.applicationTypeID and cat.applicationTypeName = 'Admin'
			inner join dbo.tr_invoiceTransactions it on it.orgID = @orgID and it.transactionID = ta.transactionID
			inner join dbo.tr_invoices nv on nv.orgID = @orgID and nv.invoiceID = it.invoiceID
			outer apply (
				select sum(ts.cache_amountAfterAdjustment) as totalAmt
				from dbo.tr_relationships as tr
				inner join dbo.tr_transactions as tx on tx.ownedByOrgID = @orgID
					and tx.typeID = @t_Tax
					and tx.transactionID = tr.transactionID
				inner join dbo.tr_transactionSales as ts on ts.orgID = @orgID
					and ts.transactionID = tx.transactionID
				where tr.orgID = @orgID
				and tr.appliedToTransactionID = it.transactionID
				and tr.typeID = @tr_SalesTaxTrans
			) tax
			group by nv.invoiceID, nv.invoiceProfileID, nv.dateDue
			order by nv.dateDue;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryInvoicesDues>
	</cffunction>

	<cffunction name="sub_associateCardOnFile" access="public" returntype="void" output="false">
		<cfargument name="subscriberID" type="numeric" required="true">
		<cfargument name="MPProfileID" type="numeric" required="true">
		<cfargument name="mppID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgID)>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdate">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @orgID int, @siteID int, @subscriberID int, @MPProfileID int, @payProfileID int, @existingPayProfileID int, @payProfileIDList varchar(max),
					@recordedByMemberID int, @detail varchar(max);
				SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgID#">;
				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).siteID#">;
				SET @subscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subscriberID#">;
				SET @MPProfileID = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.MPProfileID#">,0);
				SET @payProfileID = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mppID#">,0);
				<cfif local.useMID>
					SET @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.useMID#">;
				<cfelse>
					SELECT @recordedByMemberID = m.activeMemberID
					FROM dbo.sub_subscribers AS s
					INNER JOIN dbo.ams_members AS m ON m.orgID = @orgID AND m.memberID = s.memberID
					WHERE s.subscriberID = @subscriberID;
				</cfif>

				SELECT @existingPayProfileID = payProfileID
				FROM dbo.sub_subscribers
				WHERE subscriberID = @subscriberID;

				IF @payProfileID IS NOT NULL
					SELECT @detail = detail
					FROM dbo.ams_memberPaymentProfiles
					WHERE payProfileID = @payProfileID;
				ELSE
					SELECT @detail = mpp.detail
					FROM dbo.sub_subscribers as s
					INNER JOIN dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = s.payProfileID
					INNER JOIN dbo.sub_subscriptions as ss on ss.subscriptionID = s.subscriptionID
					WHERE s.subscriberID = @subscriberID;

				IF @detail IS NOT NULL
					SELECT @detail = 'Pay Profile ' + @detail + CASE WHEN @payProfileID IS NULL THEN ' removed from ' ELSE ' associated to ' END + 'Subscription [' + ss.subscriptionName + '] (SubscriberID: ' + CAST(@subscriberID AS varchar(10)) + ')'
					FROM dbo.sub_subscribers as s
					INNER JOIN dbo.sub_subscriptions as ss on ss.subscriptionID = s.subscriptionID
					WHERE s.subscriberID = @subscriberID;

				BEGIN TRAN;
					UPDATE dbo.sub_subscribers
					SET payProfileID = @payProfileID,
						MPProfileID = @MPProfileID
					WHERE subscriberID = @subscriberID;

					-- trigger reprocessing of credit card expiration conditions (if limiting to subscriptions)
					IF ISNULL(@existingPayProfileID,0) <> ISNULL(@payProfileID,0) BEGIN
						SET @payProfileIDList = CAST(ISNULL(@existingPayProfileID,0) as varchar(20)) + ',' + CAST(ISNULL(@payProfileID,0) as varchar(20));
						EXEC dbo.tr_reprocessCCExpConditions @orgID=@orgID, @payProfileIDList=@payProfileIDList, @lookupMode='limittosub';
					END

					IF @detail IS NOT NULL
						INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
						VALUES ('{ "c":"auditLog", "d": {
							"AUDITCODE":"SUBS",
							"ORGID":' + cast(@orgID as varchar(10)) + ',
							"SITEID":' + cast(@siteID as varchar(10)) + ',
							"ACTORMEMBERID":' + cast(@recordedByMemberID as varchar(20)) + ',
							"ACTIONDATE":"' + convert(varchar(20),GETDATE(),120) + '",
							"MESSAGE":"' + replace(memberCentral.dbo.fn_cleanInvalidXMLChars(@detail),'"','\"') + '" } }');
				COMMIT TRAN;

			END TRY
			BEGIN CATCH
				IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="sub_getMostExclusiveRateInfo" access="public" returntype="query" output="false" hint="Get most exclusive rate">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="subscriptionUID" type="string" required="true">
		<cfargument name="isRenewalRate" type="boolean" required="true">
		<cfargument name="frequencyShortName" type="string" required="false">

		<cfset var local = structNew()>
		<cfset local.qualifySubRateRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SubscriptionRate", functionName="qualify")>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRateInfo">
			set nocount on;

			declare @subscriptionID int, @memberID int, @isRenewalRate bit, @frequencyShortName varchar(10), @qualifyFID int;
			set @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">;
			set @isRenewalRate =  <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.isRenewalRate#">;
			<cfif isDefined("arguments.frequencyShortName")>
				set @frequencyShortName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.frequencyShortName#">;
			</cfif>
			set @qualifyFID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qualifySubRateRFID#">;

			select @subscriptionID = subscriptionID 
			from dbo.sub_subscriptions 
			where uid = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.subscriptionUID#">;

			select rateTbl.rateID,	rateTbl.rateName, rateTbl.UID as rateUID, f.frequencyID, f.frequencyName, f.frequencyShortName, f.uid as freqUID, rf.rfID, rf.rateAmt
			from dbo.fn_sub_getMostExclusiveRateInfo(@memberID, @subscriptionID, @isRenewalRate,@qualifyFID) as rateTbl
			inner join dbo.sub_rateFrequencies rf on rf.rateID = rateTbl.rateID
			inner join dbo.sub_frequencies f on f.frequencyID = rf.frequencyID
			<cfif isDefined("arguments.frequencyShortName")>
				and f.frequencyShortName = @frequencyShortName
			</cfif>;			
		</cfquery>

		<cfreturn local.qryRateInfo>
	</cffunction>

	<cffunction name="sub_getSetSubscriptions" access="public" output="false" returntype="query">
		<cfargument name="setUID" type="string" required="true">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var qryData = "">

		<cfquery name="qryData" datasource="#application.dsn.membercentral.dsn#">
			select s.subscriptionName, s.uid as subUID, r.rateName, r.uid as rateUID, subSets.setName, subSets.uid as setUID, s.subscriptionID, rf.rfid, rf.rateAmt
			from dbo.sub_subscriptions s
			inner join dbo.sub_rateSchedules as sch on sch.scheduleID = s.scheduleID 
				and sch.status = 'A'
			inner join dbo.sub_rates r on r.scheduleID = sch.scheduleID
				and r.isRenewalRate=0
				and r.status = 'A'
			inner join dbo.sub_rateFrequencies rf on rf.rateID = r.rateID 
				and rf.status = 'A'
			inner join dbo.sub_frequencies f on rf.frequencyID = f.frequencyID
				and f.isSystemRate = 1
				and f.frequencyShortName = 'F'
			inner join dbo.sub_subscriptionSets ss on ss.subscriptionID = s.subscriptionID
			inner join dbo.sub_sets subSets on subSets.setID = ss.setID
				and subSets.uid = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.setUID#">		
			inner join dbo.sub_types t on t.typeID = s.typeID
				and t.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">					 
			where s.status = 'A'	
			order by s.subscriptionName
		</cfquery>

		<cfreturn qryData>
	</cffunction>	

	<cffunction name="sub_getSRateInfo" access="public" output="false" returntype="query">
		<cfargument name="rateUID" type="string" required="false">
		<cfargument name="rateID" type="numeric" required="false">
		<cfargument name="frequencyShortName" type="string" required="false">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var qryData = "">

		<cfquery name="qryData" datasource="#application.dsn.membercentral.dsn#">
			select r.rateID, r.rateName, r.UID as rateUID, rf.rfID, rf.rateAmt, f.frequencyName, f.frequencyShortName
			from dbo.sub_rates r 
			inner join dbo.sub_rateFrequencies rf on rf.rateID = r.rateID
			inner join dbo.sub_frequencies f on f.frequencyID = rf.frequencyID
			where f.siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="cf_sql_integer">
			and r.status = 'A'
			<cfif isDefined("arguments.rateUID")>
				and r.uid = <cfqueryparam value="#arguments.rateUID#" cfsqltype="cf_sql_varchar">	
			</cfif>
			<cfif isDefined("arguments.rateID")>
				and r.rateID = <cfqueryparam value="#arguments.rateID#" cfsqltype="cf_sql_integer">	
			</cfif>
			<cfif isDefined("arguments.frequencyShortName")>
				and f.frequencyShortName = <cfqueryparam value="#arguments.frequencyShortName#" cfsqltype="cf_sql_varchar">	
			</cfif>			
		</cfquery>

		<cfreturn qryData>
	</cffunction>	

	<cffunction name="sub_getSubscriptionTreeStruct" access="public" output="false" returntype="struct">
		<cfargument name="subscriptionID" type="string" required="false">
		<cfargument name="memberID" type="numeric" required="false">
		<cfargument name="isRenewalRate" type="boolean" required="true">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.subxml">
			select dbo.fn_sub_getSubscriptionStructureXML(<cfqueryparam value="#arguments.subscriptionID#" cfsqltype="cf_sql_integer">,1) as subxml
		</cfquery>

		<cfscript>
			local.subStructureXML = xmlparse(local.subxml.subxml);
			local.rateSchedules = XMLSearch(local.subStructureXML, '//subscription/@rateScheduleID');
			local.distinctRateScheduleIDs = "";

			for (local.i=1;local.i<=arrayLen(local.rateSchedules);local.i++) {
				if (not listFind(local.distinctRateScheduleIDs, local.rateSchedules[local.i].xmlvalue))
					local.distinctRateScheduleIDs = listAppend(local.distinctRateScheduleIDs, trim(local.rateSchedules[local.i].xmlvalue));
			}
			local.rateScheduleInfo = structNew();
			for(local.i=1;local.i<=listLen(local.distinctRateScheduleIDs);local.i++) {
				local.thisScheduleID = trim(listGetAt(local.distinctRateScheduleIDs, local.i));

				// get qualified rates
				local.qualRatesXML = getCustomPageQualifiedRatesXML(scheduleID=local.thisScheduleID, memberID=arguments.memberID, isRenewalRate=arguments.isRenewalRate);
				local.thisScheduleInfo = application.objCommon.xmlToStruct(local.qualRatesXML);
				normalizeRateScheduleStruct(local.thisScheduleInfo);
				local.rateScheduleInfo[local.thisScheduleID] = local.thisScheduleInfo.rates;
			}

			local.result = application.objCommon.xmlToStruct(local.subStructureXML).subscription;
			normalizeSubStruct(local.result,local.rateScheduleInfo,arguments.memberID);
		</cfscript>

		<cfreturn local.result>
	</cffunction>

	<cffunction name="getCustomPageQualifiedRatesXML" access="private" output="false" returntype="xml">
		<cfargument name="scheduleID" type="numeric" required="yes">
		<cfargument name="memberID" type="numeric" required="yes">
		<cfargument name="isRenewalRate" type="boolean" required="yes">
		<cfscript>
			var local = structNew();
			if (not structKeyExists(variables,"objsubscriptions"))
				variables.objsubscriptions = createObject("component", "model.admin.subscriptions.subscriptions");

				local.qualRatesXML = variables.objsubscriptions.getQualifiedRatesXML(scheduleID=arguments.scheduleID, memberID=arguments.memberID, isRenewalRate=arguments.isRenewalRate);

			// remove all frequencies that are not available on the front end
			local.arrNotFE = XmlSearch(local.qualRatesXML,"/rates/rate/frequencies[@allowFrontEnd='0']");
			application.objCommon.XmlDeleteNodes(local.qualRatesXML, local.arrNotFE);

			// remove rates frequencies that are deleted
			local.arrNotFE = XmlSearch(local.qualRatesXML,"/rates/rate/frequencies[@status='D']");
			application.objCommon.XmlDeleteNodes(local.qualRatesXML, local.arrNotFE);

			// remove rates that are deleted
			local.arrNotFE = XmlSearch(local.qualRatesXML,"/rates/rate[@status='D']");
			application.objCommon.XmlDeleteNodes(local.qualRatesXML, local.arrNotFE);				

			// remove rates that are inactive
			local.arrNotFE = XmlSearch(local.qualRatesXML,"/rates/rate[@status='I']");
			application.objCommon.XmlDeleteNodes(local.qualRatesXML, local.arrNotFE);	

			// remove rates that now have no frequencies available
			local.arrNotFE = XmlSearch(local.qualRatesXML,"/rates/rate[count(frequencies)=0]");
			application.objCommon.XmlDeleteNodes(local.qualRatesXML, local.arrNotFE);
		</cfscript>

		<cfreturn xmlParse(local.qualRatesXML)>
	</cffunction>	

	<cffunction name="normalizeRateScheduleStruct" access="private" returntype="void" hint="remove double nested structs, convert single subs">
		<cfargument name="rateScheduleInfo" type="struct" required="true">

		<cfscript>
			var local = structNew();
			local.now = now();
			local.rateLookupStruct = structNew();

			if (structKeyExists(arguments.rateScheduleInfo.rates, "rate")) {
				local.rates = arguments.rateScheduleInfo.rates.rate;

				//rates should always be an array, even if there is only one
				if (isArray(arguments.rateScheduleInfo.rates.rate)) 
					arguments.rateScheduleInfo.rates = local.rates;
				else if (isStruct(arguments.rateScheduleInfo.rates.rate))
					arguments.rateScheduleInfo.rates = [local.rates];
				else
					arguments.rateScheduleInfo.rates = arrayNew(1);
			} else
				arguments.rateScheduleInfo.rates = arrayNew(1);

			local.ratesToRemove = "";
			for (local.j=1;local.j<=arrayLen(arguments.rateScheduleInfo.rates);local.j++) {

				//frequencies should always be an array, even if there is only one
				if (isStruct(arguments.rateScheduleInfo.rates[local.j].frequencies)) {
					local.thisFreqElement = arguments.rateScheduleInfo.rates[local.j]["frequencies"];
					arguments.rateScheduleInfo.rates[local.j]["frequencies"] = [local.thisFreqElement];
				}

				//test to see if this rate was already processed for another frequency
				local.rateAlreadyProcessed = structKeyExists(local.rateLookupStruct, arguments.rateScheduleInfo.rates[local.j].rateID);
				// if previous processed, merge the frequency array for current element into the previously processed element
				if (local.rateAlreadyProcessed) {
					local.preexistingRateArrayPosition = local.rateLookupStruct[arguments.rateScheduleInfo.rates[local.j].rateID];
					for (local.k=1;local.k<=arrayLen(arguments.rateScheduleInfo.rates[local.j].frequencies);local.k++) {
						arrayAppend(arguments.rateScheduleInfo.rates[local.preexistingRateArrayPosition].frequencies, arguments.rateScheduleInfo.rates[local.j].frequencies[local.k]);
					}
					local.ratesToRemove = listPrepend(local.ratesToRemove, local.j);
				} else {

					/*
					commenting this line out to disable frequency collapsing until we have true join form support for frequency changing

					local.rateLookupStruct[arguments.rateScheduleInfo.rates[local.j].rateID] = local.j;
					*/
					//rates that aren't currently available should be added to deletion list
					local.thisRateStartDate = listFirst(arguments.rateScheduleInfo.rates[local.j].rateAFStartDate,"T");
					local.thisRateEndDate = dateadd("d",1,listFirst(arguments.rateScheduleInfo.rates[local.j].rateAFEndDate,"T"));
					if (local.now lt local.thisRateStartDate or local.now gt local.thisRateEndDate) {
						local.ratesToRemove = listPrepend(local.ratesToRemove, local.j);
					}
				}

			}
			// delete rates that were added to deletion list, due to not being currently available
			for(local.j=1;local.j<=listLen(local.ratesToRemove);local.j++) {
				arrayDeleteAt(arguments.rateScheduleInfo.rates, listGetAt(local.ratesToRemove,local.j));
			}
		</cfscript>
	</cffunction>

	<cffunction name="normalizeSubStruct" access="private" returntype="void" hint="remove double nested structs, convert single subs">
		<cfargument name="subDefinitionStruct" type="struct" required="true">
		<cfargument name="rateScheduleInfo" type="struct" required="true">
		<cfargument name="memberID" type="numeric" required="true">

		<cfscript>
			var local = structNew();

			local.returnStruct = arguments.subDefinitionStruct;
			local.returnStruct["rateSchedule"] = arguments.rateScheduleInfo[local.returnStruct.rateScheduleID];

			if (structKeyExists(local.returnStruct, "addons")) {
				//remove double nested struct
				local.returnStruct.addons = local.returnStruct.addons.addon;
				//addons should always be an array
				if (isStruct(local.returnStruct.addons)) {
					local.tempAddons = arrayNew(1);
					arrayAppend(local.tempAddons, local.returnStruct.addons);
					local.returnStruct.addons = local.tempAddons;
				}
				local.addonsWithNoEligibleSubs = "";
				for (local.addonIndex=1;local.addonIndex<=arrayLen(local.returnStruct.addons);local.addonIndex++) {
					if (structKeyExists(local.returnStruct.addons[local.addonIndex], "subscriptions")){
						//remove double nested struct
						local.returnStruct.addons[local.addonIndex].subscriptions = local.returnStruct.addons[local.addonIndex].subscriptions.subscription;
					} else {
						// no active subscriptions in the set
						local.returnStruct.addons[local.addonIndex].subscriptions = arrayNew(1);
					}
					
					//addon subscriptions should always be an array
					if (isStruct(local.returnStruct.addons[local.addonIndex].subscriptions)) {
						local.temp = local.returnStruct.addons[local.addonIndex].subscriptions;
						local.returnStruct.addons[local.addonIndex].subscriptions = [local.temp];
					}
					local.nonqualifiedAddonSubs = "";

					local.returnStruct.addons[local.addonIndex]["frontEndContent"] = application.objCMS.getStaticContent(contentID=local.returnStruct.addons[local.addonIndex].frontEndContentID,languageID=1,skipMerge=1);
					for (local.addonSubIndex=1;local.addonSubIndex<=arrayLen(local.returnStruct.addons[local.addonIndex].subscriptions);local.addonSubIndex++) {
						local.thisAddonSub = local.returnStruct.addons[local.addonIndex].subscriptions[local.addonSubIndex];

						//recurse through addon subs if the member is eligible for at least one rate, otherwise add element to deletion list
						if (structKeyExists(arguments.rateScheduleInfo,local.thisAddonSub.rateScheduleID) and arrayLen(arguments.rateScheduleInfo[local.thisAddonSub.rateScheduleID])) {
							normalizeSubStruct(local.returnStruct.addons[local.addonIndex].subscriptions[local.addonSubIndex],arguments.rateScheduleInfo,arguments.memberID);
						} else {
							local.nonqualifiedAddonSubs = listPrepend(local.nonqualifiedAddonSubs, local.addonSubIndex);
						}
					}
					// delete addon subs that were added to deletion list, due to having no eligible rates
					for(local.addonSubIndex=1;local.addonSubIndex<=listLen(local.nonqualifiedAddonSubs);local.addonSubIndex++) {
						arrayDeleteAt(local.returnStruct.addons[local.addonIndex].subscriptions, listGetAt(local.nonqualifiedAddonSubs,local.addonSubIndex));
					}

					//add this addon to deletion list if all subs in the subscription array have been deleted
					if (not arrayLen(local.returnStruct.addons[local.addonIndex].subscriptions))
						local.addonsWithNoEligibleSubs = listPrepend(local.addonsWithNoEligibleSubs, local.addonIndex);
				}
				// delete addons that were added to deletion list, due to having empty subscription arrays
				for(local.addonIndex=1;local.addonIndex<=listLen(local.addonsWithNoEligibleSubs);local.addonIndex++) {
					arrayDeleteAt(local.returnStruct.addons, listGetAt(local.addonsWithNoEligibleSubs,local.addonIndex));
				}
			}
		</cfscript>
	</cffunction>

	<cffunction name="sub_getRateScheduleFrequencyInfo" access="public" output="false" returntype="query">
		<cfargument name="scheduleID" type="numeric" required="false">
		<cfargument name="frequencyName" type="string" required="true">

		<cfset var qryData = "">

		<cfquery name="qryData" datasource="#application.dsn.membercentral.dsn#">
			select r.rateID,r.scheduleID,r.ratename, r.isRenewalRate, r.forceUpfront, r.frontEndAllowChangePrice, f.frequencyName, rf.rateAmt
			from sub_rates r
			inner join sub_rateFrequencies rf
				on r.rateID = rf.rateID
				and r.scheduleID = <cfqueryparam value="#arguments.scheduleID#" cfsqltype="cf_sql_integer">
				and r.status = 'A'
				and r.isRenewalRate=0
			inner join sub_frequencies f
				on f.frequencyID = rf.frequencyID
				and f.frequencyName = <cfqueryparam value="#arguments.frequencyName#" cfsqltype="cf_sql_varchar">
				and f.status = 'A'
			order by rf.rateAmt
		</cfquery>

		<cfreturn qryData>
	</cffunction>

	<cffunction name="sub_getSubscriptionsDetailsFromSubsetUID" access="public" output="false" returntype="query">
		<cfargument name="setUID" type="string" required="true" hint="can be a list of UIDs">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="status" type="string" required="false" default="A" hint="can be a list of UIDs">
		<cfargument name="activeRatesOnly" type="boolean" required="false" default="false">
		<cfargument name="ignoreRenewalRates" type="boolean" required="false" default="false">
		<cfargument name="isFrequencyFull" type="boolean" required="false" default="false">
		<cfargument name="isAllowFrontEndOnly" type="boolean" required="false" default="false">
		<cfargument name="rateUID" type="string" required="false" default="" hint="can be a list of UIDs">
		<cfargument name="subUID" type="string" required="false" hint="can be a list of UIDs">

		<cfset var local = structNew()>
		<cfset local.getRateData = false/>
		<cfif arguments.ignoreRenewalRates EQ true OR arguments.activeRatesOnly EQ true 
			OR arguments.isFrequencyFull EQ true OR arguments.isAllowFrontEndOnly EQ true OR listLen(arguments.rateUID)>
			<cfset local.getRateData = true/>
		</cfif>
		
		<cfquery name="local.qrySubscriptionsFromSubsetUID" datasource="#application.dsn.membercentral.dsn#">
			select s.subscriptionID, s.subscriptionName, s.UID, s.reportCode, s.GLAccountID, st.typeID, st.TypeName, st.UID, subSets.setName, subSets.uid
			<cfif local.getRateData EQ true>
				,r.rateID, r.scheduleID, r.rateName, r.UID as rateUID,
				f.frequencyID, f.frequencyName, rf.rateAmt, r.rateAFStartDate, r.rateAFEndDate, 
				r.isRenewalRate, r.frontEndAllowChangePrice, rf.allowfrontend
			</cfif>
			from sub_subscriptions s
			inner join sub_subscriptionSets ss on ss.subscriptionID = s.subscriptionID
			inner join sub_types st on st.typeid = s.typeid
			and st.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
			inner join sub_sets subSets on subSets.setID = ss.setID 
			and subSets.uid in (<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.setUID#" list="true">)
			<cfif local.getRateData EQ true>
				inner join dbo.sub_rates r on r.scheduleID = s.scheduleID and r.status = 'A'
				<cfif listLen(arguments.rateUID)>
					and r.uid in (<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.rateUID#" list="true">)
				</cfif>
				inner join dbo.sub_rateSchedules rs on rs.scheduleID = r.scheduleID and rs.status = 'A'
				inner join dbo.sub_rateFrequencies rf on rf.rateID = r.rateID
				inner join dbo.sub_frequencies f on f.frequencyID = rf.frequencyID				
			</cfif>
			where s.status in (<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.status#" list="true">)
			<cfif arguments.ignoreRenewalRates eq true>
				and r.isRenewalRate = 0
			</cfif>			
			<cfif arguments.activeRatesOnly eq true>
				and getDate() between r.rateAFStartDate and r.rateAFEndDate
			</cfif>
			<cfif arguments.isFrequencyFull eq true>
				and f.frequencyShortName = 'F'
				and f.isSystemRate = 1
			</cfif>
			<cfif arguments.isAllowFrontEndOnly eq true>
				and rf.allowfrontend = 1
			</cfif>
			<cfif listLen(arguments.subUID)>
				and s.uid in (<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.subUID#" list="true">)
			</cfif>
			order by s.subscriptionName
		</cfquery>

		<cfreturn local.qrySubscriptionsFromSubsetUID>
	</cffunction>

	<cffunction name="sub_getSubscriptionsDetailsFromTypeUID" access="public" output="false" returntype="query">
		<cfargument name="subTypeUID" type="string" required="true" hint="can be a list of UIDs">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="status" type="string" required="false" default="A" hint="can be a list of UIDs">
		<cfargument name="activeRatesOnly" type="boolean" required="false" default="false">
		<cfargument name="ignoreRenewalRates" type="boolean" required="false" default="false">
		<cfargument name="isFrequencyFull" type="boolean" required="false" default="false">
		<cfargument name="isAllowFrontEndOnly" type="boolean" required="false" default="false">
		<cfargument name="rateUID" type="string" required="false" default="" hint="can be a list of UIDs">

		<cfset var local = structNew()>
		<cfset local.getRateData = false/>
		<cfif arguments.ignoreRenewalRates EQ true OR arguments.activeRatesOnly EQ true 
			OR arguments.isFrequencyFull EQ true OR arguments.isAllowFrontEndOnly EQ true OR listLen(arguments.rateUID)>
			<cfset local.getRateData = true/>
		</cfif>
		
		<cfquery name="local.qrySubscriptionsFromTypeUID" datasource="#application.dsn.membercentral.dsn#">
			select s.subscriptionID, s.subscriptionName, s.UID, s.reportCode, s.GLAccountID, st.typeID, st.TypeName, st.UID
			<cfif local.getRateData EQ true>
				,r.rateID, r.scheduleID, r.rateName, r.UID as rateUID,
				f.frequencyID, f.frequencyName, rf.rateAmt, r.rateAFStartDate, r.rateAFEndDate, 
				r.isRenewalRate, r.frontEndAllowChangePrice, rf.allowfrontend
			</cfif>
			from sub_subscriptions s			
			inner join sub_types st on st.typeid = s.typeid 
			and st.uid in (<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.subTypeUID#" list="true">)
			and st.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
			<cfif local.getRateData EQ true>
				inner join dbo.sub_rates r on r.scheduleID = s.scheduleID and r.status = 'A'
				<cfif listLen(arguments.rateUID)>
					and r.uid in (<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.rateUID#" list="true">)
				</cfif>
				inner join dbo.sub_rateSchedules rs on rs.scheduleID = r.scheduleID and rs.status = 'A'
				inner join dbo.sub_rateFrequencies rf on rf.rateID = r.rateID
				inner join dbo.sub_frequencies f on f.frequencyID = rf.frequencyID				
			</cfif>
			where s.status in (<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.status#" list="true">)
			<cfif arguments.ignoreRenewalRates eq true>
				and r.isRenewalRate = 0
			</cfif>			
			<cfif arguments.activeRatesOnly eq true>
				and getDate() between r.rateAFStartDate and r.rateAFEndDate
			</cfif>
			<cfif arguments.isFrequencyFull eq true>
				and f.frequencyShortName = 'F'
				and f.isSystemRate = 1
			</cfif>
			<cfif arguments.isAllowFrontEndOnly eq true>
				and rf.allowfrontend = 1
			</cfif>
			order by s.subscriptionName
		</cfquery>

		<cfreturn local.qrySubscriptionsFromTypeUID>
	</cffunction>

	<cffunction name="sub_getSubscriptionDetailsFromUID" access="public" output="false" returntype="query">
		<cfargument name="subUID" type="string" required="true" hint="can be a list of UIDs">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="status" type="string" required="false" default="A" hint="can be a list of UIDs">
		<cfargument name="activeRatesOnly" type="boolean" required="false" default="false">
		<cfargument name="ignoreRenewalRates" type="boolean" required="false" default="false">
		<cfargument name="isFrequencyFull" type="boolean" required="false" default="false">
		<cfargument name="isAllowFrontEndOnly" type="boolean" required="false" default="false">
		<cfargument name="rateUID" type="string" required="false" default="" hint="can be a list of UIDs">

		<cfset var local = structNew()>

		<cfset local.getRateData = false/>
		<cfif arguments.ignoreRenewalRates EQ true OR arguments.activeRatesOnly EQ true 
			OR arguments.isFrequencyFull EQ true OR arguments.isAllowFrontEndOnly EQ true OR listLen(arguments.rateUID)>
			<cfset local.getRateData = true/>
		</cfif>
		
		<cfquery name="local.qrySubscriptionFromUID" datasource="#application.dsn.membercentral.dsn#">
			select s.subscriptionID, s.subscriptionName, s.UID, s.reportCode, s.GLAccountID
			<cfif local.getRateData EQ true>
				,r.rateID, r.scheduleID, r.rateName, r.UID as rateUID,
				f.frequencyID, f.frequencyName, rf.rateAmt, r.rateAFStartDate, r.rateAFEndDate, 
				r.isRenewalRate, r.frontEndAllowChangePrice, rf.allowfrontend
			</cfif>
			from sub_subscriptions s
			inner join sub_types st on st.typeid = s.typeid 
			and st.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
			<cfif local.getRateData EQ true>
				inner join dbo.sub_rates r on r.scheduleID = s.scheduleID and r.status = 'A'
				<cfif listLen(arguments.rateUID)>
					and r.uid in (<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.rateUID#" list="true">)
				</cfif>
				inner join dbo.sub_rateSchedules rs on rs.scheduleID = r.scheduleID and rs.status = 'A'
				inner join dbo.sub_rateFrequencies rf on rf.rateID = r.rateID
				inner join dbo.sub_frequencies f on f.frequencyID = rf.frequencyID				
			</cfif>
			where s.uid in (<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.subUID#" list="true">)
			and s.status in (<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.status#" list="true">)
			<cfif arguments.ignoreRenewalRates eq true>
				and r.isRenewalRate = 0
			</cfif>			
			<cfif arguments.activeRatesOnly eq true>
				and getDate() between r.rateAFStartDate and r.rateAFEndDate
			</cfif>
			<cfif arguments.isFrequencyFull eq true>
				and f.frequencyShortName = 'F'
				and f.isSystemRate = 1
			</cfif>
			<cfif arguments.isAllowFrontEndOnly eq true>
				and rf.allowfrontend = 1
			</cfif>
			order by s.subscriptionName
		</cfquery>
			
		<cfreturn local.qrySubscriptionFromUID>
	</cffunction>

	<cffunction name="sub_checkActivationsByMember" access="public" output="false" returntype="void">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="subscriberID" type="numeric" required="true">
		<cfargument name="bypassQueue" type="boolean" required="true">

		<cfset var local = structNew()>

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="sub_checkActivationsByMember">
			<cfprocparam type="in" cfsqltype="cf_sql_integer" value="#arguments.orgID#">
			<cfprocparam type="in" cfsqltype="cf_sql_integer" value="#arguments.memberID#">
			<cfif arguments.subscriberID gt 0>
				<cfprocparam type="in" cfsqltype="cf_sql_integer" value="#arguments.subscriberID#">
			<cfelse>
				<cfprocparam type="in" cfsqltype="cf_sql_integer" null="true">
			</cfif>
			<cfprocparam type="in" cfsqltype="cf_sql_bit" value="#arguments.bypassQueue#">
		</cfstoredproc>						
	</cffunction>

	<!--- ------------------------ --->
	<!--- Member History functions --->
	<!--- ------------------------ --->
	<cffunction name="mh_getCategory" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="parentCode" type="string" required="true">
		<cfargument name="subName" type="string" required="false">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryData" datasource="#application.dsn.membercentral.dsn#">
			select cP.categoryID, cP.categoryCode, cp.categoryName, cp.categoryDesc,
				<cfif isDefined("arguments.subName") and len(arguments.subName)>c.categoryID<cfelse>0</cfif> as subCategoryID
			from dbo.cms_categoryTrees as ct
			inner join dbo.cms_categories as cP on ct.categoryTreeID = cP.categoryTreeID
			<cfif isDefined("arguments.subName") and len(arguments.subName)>
				inner join dbo.cms_categories as c on c.parentCategoryID = cP.categoryID
					and c.categoryName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.subName#">
					and c.isActive = 1
			</cfif>
			where ct.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteid#">
			and ct.categoryTreeName = 'MemberHistoryTypes'
			and cP.categoryCode in (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.parentCode#" list="true">)
			and cP.isActive = 1
		</cfquery>

		<!--- if not found, trigger email to support by looking at tag context --->
		<!--- TagContext array: 1 will be this function's cfthrow. 2 will be the code calling this function. --->
		<cfif NOT local.qryData.recordcount>
			<cftry>
				<cfthrow message="Forced Stack Trace" type="stackTrace">
			<cfcatch type="stackTrace">
				<cfset local.callingPageName = replace(cfcatch.tagContext[2].template,"/app/membercentral/sitecomponents/","")>
				<cfset local.siteCode = session.mcstruct.siteCode>

				<cfsavecontent variable="local.supportEmailContent">
					<cfoutput>
					<p>The following custom page requires a Member History category that is not defined. The page may fail until this category is defined.</p>
					<p>Environment: <b>#application.MCEnvironment#</b></p>
					<p>SiteCode: <b>#local.siteCode#</b></p>
					<p>Custom Page: <b>#local.callingPageName#</b></p>
					<cfif isDefined("arguments.subName") and len(arguments.subName)>
						<p>Parent Category Code: <b>#arguments.parentCode#</b></p>
						<p>Sub Category Name: <b>#arguments.subName#</b></p>
					<cfelse>
						<p>Category Code: <b>#arguments.parentCode#</b></p>
					</cfif>
					</cfoutput>
				</cfsavecontent>

				<cfset local.emailTitle = "#local.siteCode# - Required Member History Category Missing">

				<cfset local.strReturn = application.objEmailWrapper.sendMailESQ(
					emailfrom={ name="MemberCentral", email="<EMAIL>" },
					emailto=[{ name='', email="<EMAIL>" }],
					emailreplyto="",
					emailsubject="#application.MCEnvironment# - #local.emailTitle#",
					emailtitle=local.emailTitle,
					emailhtmlcontent=local.supportEmailContent,
					siteID=application.objSiteInfo.getSiteIDFromSiteCode(siteCode='MC'),
					memberID=application.objCommon.getMCSystemMemberID(),
					messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
					sendingSiteResourceID=application.objSiteInfo.getSiteInfo(local.siteCode).siteSiteResourceID
				)>
			</cfcatch>
			</cftry>
		</cfif>

		<cfreturn local.qryData>
	</cffunction>

	<cffunction name="mh_getCategoryAJX" access="public" output="false" returntype="query">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="parentCode" type="string" required="true">
		<cfargument name="subName" type="string" required="false" default="">
		
		<cfreturn mh_getCategory(siteID=arguments.mcproxy_siteID, parentCode=arguments.parentCode, subName=arguments.subName)>
	</cffunction>

	<cffunction name="mh_addHistory" access="public" output="false" returntype="numeric">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="categoryID" type="string" required="true"> <!--- string here in case history category is not defined --->
		<cfargument name="subCategoryID" type="numeric" required="true">
		<cfargument name="description" type="string" required="true">
		<cfargument name="linkMemberID" type="numeric" required="false">
		<cfargument name="enteredByMemberID" type="numeric" required="true">
		<cfargument name="newAccountsOnly" type="boolean" required="true">
		<cfargument name="qty" type="numeric" required="false">
		<cfargument name="dollarAmt" type="numeric" required="false">
		<cfargument name="startDate" type="string" default="#dateformat(now(),'m/d/yyyy')#" required="false">
		<cfargument name="endDate" type="string" default="" required="false">
	
		<cfset var local = StructNew()>
		<cfset local.memHistoryID = 0>
		<cfset local.newMemIdArr = application.mcCacheManager.sessionGetValue(keyname='newMemIdArr', defaultValue=arrayNew(1))>
		<cfif arguments.newAccountsOnly eq false OR (IsArray("local.newMemIdArr") AND listFind(ArrayToList(local.newMemIdArr),arguments.memberid))>
			<cftry>
				<cfset arguments.description = trim(arguments.description)>
				<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_addMemberHistory">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="1">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.categoryID#">
					<cfif val(arguments.subCategoryID)>
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.subCategoryID#">
					<cfelse>
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
					</cfif>
					<cfif isDefined("arguments.startDate") and len(arguments.startDate)>
						<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.startDate#">
					<cfelse>
						<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" null="yes">
					</cfif>
					<cfif isDefined("arguments.endDate") and len(arguments.endDate)>
						<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.endDate#">
					<cfelse>
						<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" null="yes">
					</cfif>
					<cfif isDefined("arguments.qty") and val(arguments.qty)>
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.qty#">
					<cfelse>
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
					</cfif>
					<cfif isDefined("arguments.dollarAmt") and val(arguments.dollarAmt)>
						<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" value="#arguments.dollarAmt#">
					<cfelse>
						<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" null="yes">
					</cfif>
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.description#">
					<cfif isDefined("arguments.linkMemberID") and val(arguments.linkMemberID)>
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.linkMemberID#">
					<cfelse>
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
					</cfif>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="yes">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="yes">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.enteredByMemberID#">
					<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.memHistoryID">	
				</cfstoredproc>
				<cfif local.memHistoryID is 0>
					<cfthrow message="Member History entry was not saved.">
				</cfif>
				<cfcatch type="Any">
					<cfset local.tmpCatch = { type="", message="Unable to record member history entry.", detail="#cfcatch.message# #cfcatch.detail#", tagContext=cfcatch.tagContext } >
					<cfset application.objError.sendError(cfcatch=local.tmpCatch, objectToDump=arguments)>
				</cfcatch>
			</cftry>
		</cfif>
		
		<cfreturn local.memHistoryID>
	</cffunction>

	<cffunction name="mh_updateHistory" access="public" output="false" returntype="boolean">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="historyID" type="numeric" required="true">
		<cfargument name="subCategoryID" type="numeric" required="true">
		<cfargument name="description" type="string" required="true">
		<cfargument name="newAccountsOnly" type="boolean" required="true">
		<cfargument name="qty" type="numeric" required="false">
		<cfargument name="dollarAmt" type="numeric" required="false">		
		
		<cfset var local = StructNew()>

		<cftry>
			<cfset local.newMemIdArr = application.mcCacheManager.sessionGetValue(keyname='newMemIdArr', defaultValue=arrayNew(1))>
			<cfif arguments.newAccountsOnly eq false OR (IsArray(local.newMemIdArr) AND listFind(ArrayToList(local.newMemIdArr),arguments.memberid))>
				<cfif not structKeyExists(arguments,"qty")>
					<cfset arguments.qty = 0>
				</cfif>
				<cfif not structKeyExists(arguments,"dollarAmt")>
					<cfset arguments.dollarAmt = "">
				</cfif>

				<cfset createObject("component","model.admin.memberHistory.memberHistory").updateMemberHistory(historyID=arguments.historyID, memberID=arguments.memberID, categoryID=0, 
													subCategoryID=arguments.subCategoryID, userDate='', userEndDate='', description=arguments.description, quantity=arguments.qty, 
													dollarAmt=arguments.dollarAmt, linkMemberID=0)>
			</cfif>
			<cfcatch type="Any">
				<cfset local.tmpCatch = { type="", message="Unable to update member history entry.", detail="#cfcatch.message# #cfcatch.detail#", tagContext=cfcatch.tagContext } >
				<cfset application.objError.sendError(cfcatch=local.tmpCatch, objectToDump=arguments)>
			</cfcatch>
		</cftry>
		
		<cfreturn true>
	</cffunction>
	

	<!--- ---------------------- --->
	<!--- Member Notes functions --->
	<!--- ---------------------- --->
	<cffunction name="mn_addNote" access="public" output="false" returntype="boolean">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="categoryID" type="numeric" required="true">
		<cfargument name="subCategoryID" type="numeric" required="true">
		<cfargument name="description" type="string" required="true">
		<cfargument name="linkMemberID" type="numeric" required="false">
		<cfargument name="enteredByMemberID" type="numeric" required="true">
		<cfargument name="newAccountsOnly" type="boolean" required="true">
	
		<cfset var local = StructNew()>
		<cfset local.newMemIdArr = application.mcCacheManager.sessionGetValue(keyname='newMemIdArr', defaultValue=arrayNew(1))>
		<cfif arguments.newAccountsOnly eq false OR (IsArray(local.newMemIdArr) AND listFind(ArrayToList(local.newMemIdArr),arguments.memberid))>
			<cftry>
				<cfset arguments.description = trim(arguments.description)>
				<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_addMemberHistory">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="3">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.categoryID#">
					<cfif val(arguments.subCategoryID)>
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.subCategoryID#">
					<cfelse>
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
					</cfif>				
					<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#dateformat(now(),'m/d/yyyy')#">
					<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" null="yes">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
					<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" null="yes">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.description#">
					<cfif isDefined("arguments.linkMemberID") and val(arguments.linkMemberID)>
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.linkMemberID#">
					<cfelse>
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
					</cfif>					
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="yes">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="yes">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.enteredByMemberID#">
					<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.memHistoryID">	
				</cfstoredproc>
				<cfif local.memHistoryID is 0>
					<cfthrow message="Member History entry was not saved.">
				</cfif>
				<cfcatch type="Any">
					<cfset local.tmpCatch = { type="", message="Unable to record member history entry.", detail="#cfcatch.message# #cfcatch.detail#", tagContext=cfcatch.tagContext } >
					<cfset application.objError.sendError(cfcatch=local.tmpCatch, objectToDump=arguments)>
				</cfcatch>
			</cftry>
		</cfif>
		
		<cfreturn true>
	</cffunction>

	<cffunction name="nt_getCategoriesByCode" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="typeCode" type="string" required="true">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryData" datasource="#application.dsn.membercentral.dsn#">
			select cC.categoryID, cC.categoryName
			from dbo.cms_categoryTrees t
			inner join dbo.cms_categories c on c.categoryTreeID = t.categoryTreeID
				and c.isActive = 1
				and c.categoryCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.typeCode#">
			inner join dbo.cms_categories cC on cC.parentCategoryID = c.categoryID
				and cC.isActive = 1
			where t.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			and t.categoryTreeName = 'HistoryTypes'
			order by cC.categoryName
		</cfquery>
		
		<!--- if not found, trigger email to support by looking at tag context --->
		<!--- TagContext array: 1 will be this function's cfthrow. 2 will be the code calling this function. --->
		<cfif NOT local.qryData.recordcount>
			<cftry>
				<cfthrow message="Forced Stack Trace" type="stackTrace">
			<cfcatch type="stackTrace">
				<cfset local.callingPageName = replace(cfcatch.tagContext[2].template,"/app/membercentral/sitecomponents/","")>
				<cfset local.siteCode = session.mcstruct.siteCode>

				<cfsavecontent variable="local.supportEmailContent">
					<cfoutput>
					<p>The following custom page requires Notes categories that are not defined. The page may fail until these categories are defined.</p>
					<p>Environment: <b>#application.MCEnvironment#</b></p>
					<p>SiteCode: <b>#local.siteCode#</b></p>
					<p>Custom Page: <b>#local.callingPageName#</b></p>
					<p>Category Code: <b>#arguments.typeCode#</b></p>
					</cfoutput>
				</cfsavecontent>

				<cfset local.emailTitle = "#local.siteCode# - Required Notes Categories Missing">

				<cfset local.strReturn = application.objEmailWrapper.sendMailESQ(
					emailfrom={ name="MemberCentral", email="<EMAIL>" },
					emailto=[{ name='', email="<EMAIL>" }],
					emailreplyto="",
					emailsubject="#application.MCEnvironment# - #local.emailTitle#",
					emailtitle=local.emailTitle,
					emailhtmlcontent=local.supportEmailContent,
					siteID=application.objSiteInfo.getSiteIDFromSiteCode(siteCode='MC'),
					memberID=application.objCommon.getMCSystemMemberID(),
					messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
					sendingSiteResourceID=application.objSiteInfo.getSiteInfo(local.siteCode).siteSiteResourceID
				)>				
			</cfcatch>
			</cftry>
		</cfif>

		<cfreturn local.qryData>
	</cffunction>
	
	<cffunction name="nt_addNote" access="public" output="false" returntype="boolean">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="subCategoryID" type="numeric" required="true">
		<cfargument name="description" type="string" required="true">
		
		<cfset var local = StructNew()>
	
		<cfquery name="local.qryGetParentCategory" datasource="#application.dsn.membercentral.dsn#">
			select parentCategoryID
			from cms_categories
			where categoryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subCategoryID#">
			and isActive = 1
		</cfquery>
		
		<cftry>
			<cfset arguments.description = trim(arguments.description)>
			<cfset local.memHistoryID = 0>
			<cfif local.qryGetParentCategory.recordCount and arguments.memberID>
				<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_addMemberHistory">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="3">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryGetParentCategory.parentCategoryID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.subCategoryID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#dateformat(now(),'m/d/yyyy')#">
					<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" null="yes">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
					<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" null="yes">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.description#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="yes">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="yes">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
					<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.memHistoryID">	
				</cfstoredproc>
			</cfif>
			<cfif local.memHistoryID is 0>
				<cfthrow message="Member History entry was not saved.">
			</cfif>
			<cfcatch type="Any">
				<cfset local.tmpCatch = { type="", message="Unable to record member history entry.", detail="#cfcatch.message# #cfcatch.detail#", tagContext=cfcatch.tagContext } >
				<cfset application.objError.sendError(cfcatch=local.tmpCatch, objectToDump=arguments)>
			</cfcatch>
		</cftry>
		
		<cfreturn true>
	</cffunction>


	<!--- ------------------------ --->
	<!--- Content object functions --->
	<!--- ------------------------ --->
	<cffunction name="content_getRawContentFromTitle" access="public" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="contentTitle" type="string" required="true">

		<cfset var qry = "">
	
		<cfquery name="qry" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">;

			select cv.rawContent
			from dbo.cms_content as c
			inner join dbo.cms_siteResources as sr on sr.siteID = @siteID
				and sr.siteResourceID = c.siteResourceID 
				and sr.siteResourceStatusID = 1
			inner join dbo.cms_contentLanguages as cl on cl.siteID = @siteID
				and c.contentID = cl.contentID
				and cl.languageID = 1
				and cl.contentTitle = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.contentTitle#">
			inner join dbo.cms_contentVersions as cv on cv.siteID = @siteID
				and cv.contentID = cl.contentID
				and cv.contentLanguageID = cl.contentLanguageID 
				and cv.isActive = 1
			where c.siteID = @siteID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn qry.rawContent>
	</cffunction>

	<cffunction name="content_getContentLanguageID" access="public" output="false" returntype="numeric">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="contentTitle" type="string" required="true">

		<cfset var local = StructNew()>
	
		<cfquery name="local.qryGetContentLanguageID" datasource="#application.dsn.membercentral.dsn#">
			select cl.contentLanguageID
			from dbo.cms_content as c
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = c.siteResourceID 
			inner join dbo.sites as s on s.siteID = sr.siteID
			inner join dbo.cms_contentLanguages as cl on c.contentID = cl.contentID
			inner join dbo.cms_languages as l on l.languageID = cl.languageID
			where s.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
			and sr.siteResourceStatusID = 1
			and l.languageID = 1
			and cl.contentTitle = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.contentTitle#">	
		</cfquery>
		
		<cfreturn val(local.qryGetContentLanguageID.contentLanguageID)>
	</cffunction>

	<cffunction name="content_createContentVersion" access="public" output="false" returntype="numeric">
		<cfargument name="contentLanguageId" type="numeric" required="true">
		<cfargument name="content" type="string" required="true">
		<cfargument name="performedByMemberID" type="numeric" required="true">

		<cfset var local = StructNew()>
		
		<cfstoredproc procedure="cms_createContentVersion" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.contentLanguageId#">
			<cfprocparam type="In" cfsqltype="CF_SQL_longvarchar" value="#arguments.content#">
			<cfprocparam type="In" cfsqltype="cf_sql_bit" value="1">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.performedByMemberID#">
			<cfprocparam type="Out" cfsqltype="cf_sql_integer" variable="local.contentVersionID">
		</cfstoredproc>

		<cfreturn local.contentVersionID>
	</cffunction>

	<cffunction name="content_getRawContent" access="public" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="contentVersionId" type="numeric" required="yes">

		<cfset var local = StructNew()>

		<cfquery name="local.qryContent" datasource="#application.dsn.memberCentral.dsn#">
			select rawContent
			from dbo.cms_contentVersions
			where siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
			and contentVersionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.contentVersionId#">
		</cfquery>

		<cfreturn local.qryContent.rawContent>
	</cffunction>


	<!--- -------------- --->
	<!--- Misc functions --->
	<!--- -------------- --->
	<cffunction name="setFormCustomFields" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="siteResourceID" type="numeric" required="true">
		<cfargument name="arrCustomFields" type="array" required="false" default="#arrayNew(1)#">

		<cfset var local = StructNew()>
		<cfset local.objSiteResourceFields = createObject("component","model.admin.common.modules.siteResourceFields.siteResourceFields")>
		<cfset local.strFields = structNew()>

		<!--- arrCustomFields has the fields this form thinks should be there. so make sure they are --->
		<cfif arrayLen(arguments.arrCustomFields)>
			<cfset local.objSiteResourceFields.initCustomFields(siteID=arguments.siteID, siteResourceID=arguments.siteResourceID, arrCustomFields=arguments.arrCustomFields)>
		</cfif>

		<cfset local.qryFields = local.objSiteResourceFields.getSiteResourceFieldsAndValues(siteID=arguments.siteID, siteResourceID=arguments.siteResourceID)>
		<cfloop query="local.qryFields">
			<cfset structInsert(local.strFields, local.qryFields.columnName, local.qryFields.columnValue, true)>
		</cfloop>

		<cfreturn local.strFields>
	</cffunction>	

	<cffunction name="getFieldSetDetails" access="public" output="false" returntype="struct">
		<cfargument name="uid" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.fsDetails = { fieldSetTitle='', showHelp='', descriptionPosition='', descriptionContentID='', descriptionContent=''}>

		<cfset local.xmlFieldset = CreateObject("component","model.system.platform.memberFieldsets").getMemberFieldsXMLByUID(uid=arguments.uid, usage="CustomPage")>
		<cfset local.fsDetails.showHelp = xmlSearch(local.xmlFieldset,"string(/fields/@showHelp)")>
		<cfset local.fsDetails.descriptionPosition = xmlSearch(local.xmlFieldset,"string(/fields/@descriptionPosition)")>
		<cfset local.fsDetails.descriptionContentID = xmlSearch(local.xmlFieldset,"string(/fields/@descriptionContentID)")>		
		<cfquery name="local.qryGetFSContent" datasource="#application.dsn.membercentral.dsn#">
			select rawContent
			from dbo.fn_getContent(<cfqueryparam value="#local.fsDetails.descriptionContentID#" cfsqltype="CF_SQL_INTEGER">,1)			
		</cfquery>
		<cfset local.fsDetails.descriptionContent = local.qryGetFSContent.rawContent>

		<cfreturn local.fsDetails>
	</cffunction>

	<cffunction name="renderFieldSet" access="public" output="false" returntype="struct">
		<cfargument name="uid" type="string" required="true">
		<cfargument name="mode" type="string" required="true" hint="collection or confirmation">
		<cfargument name="strData" type="struct" required="false" default="#structNew()#">

		<cfset var local = structNew()>
		<cfset local.strReturn = { strFields=structNew(), jsValidation='', fieldSetContent='', fieldSetTitle='' }>

		<cfset local.xmlFieldset = CreateObject("component","model.system.platform.memberFieldsets").getMemberFieldsXMLByUID(uid=arguments.uid, usage="CustomPage")>
		<cfset local.currShowHelp = xmlSearch(local.xmlFieldset,"string(/fields/@showHelp)")>
		<cfset local.fsDescriptionPos = xmlSearch(local.xmlFieldset,"string(/fields/@descriptionPosition)")>
		<cfset local.fsDescriptionContentID = xmlSearch(local.xmlFieldset,"string(/fields/@descriptionContentID)")>
		<cfif arguments.mode eq "collection">
			<cfquery name="local.qryGetFSContent" datasource="#application.dsn.membercentral.dsn#">
				select rawContent
				from dbo.fn_getContent(<cfqueryparam value="#local.fsDescriptionContentID#" cfsqltype="CF_SQL_INTEGER">,1)			
			</cfquery>
		</cfif>

		<cfset local.strReturn.fieldSetTitle = htmlEditFormat(xmlSearch(local.xmlFieldset,"string(/fields/@fieldsetName)"))>

		<cfset local.qryStates = application.objCommon.getStates()>

		<cfif arguments.mode eq "collection">
			<cfsavecontent variable="local.stateDropdownOptions">
				<cfoutput><option value=""></option></cfoutput>
				<cfoutput query="local.qryStates" group="countryID">
					<optgroup label="#local.qryStates.country#">
					<cfoutput>
						<option value="#local.qryStates.stateID#">#local.qryStates.stateName# &nbsp;</option>
					</cfoutput>
					</optgroup>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfif arguments.mode eq "collection">
			<cfsavecontent variable="local.strReturn.fieldSetContent">
				<cfoutput>
				<table cellpadding="3" border="0" cellspacing="0">
				<cfif len(local.qryGetFSContent.rawContent) and local.fsDescriptionPos is "above">
					<tr id="MC_fieldSetDesc_#local.fsDescriptionContentID#">
						<td colspan="4" class="tsAppBodyText">#local.qryGetFSContent.rawContent#</td>
					</tr>
				</cfif>
				<cfloop array="#local.xmlFieldset.xmlRoot.xmlChildren#" index="local.thisfield">
					<cfset local.strReturn.strFields[local.thisfield.xmlattributes.fieldCode] = local.thisfield.xmlattributes.fieldLabel>

					<tr valign="top">
						<td class="tsAppBodyText" width="10"><cfif local.thisfield.xmlattributes.isRequired is 1>*</cfif>&nbsp;</td>
						<td class="tsAppBodyText" nowrap>#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#</td>
						<td class="tsAppBodyText">
							<cfif local.currShowHelp AND len(trim(local.thisfield.xmlattributes.fieldDescription))>
								<img src="/assets/common/images/help-icon.jpg" onMouseOver="Tip('#JSStringFormat(replace(trim(local.thisfield.xmlattributes.fieldDescription),chr(34),"'","ALL"))#');" onMouseOut="UnTip();" />
							<cfelse>
								&nbsp;
							</cfif>
						</td>
						<td class="tsAppBodyText">
							<cfset local.thisValue = "">
							<cfif structKeyExists(arguments.strData, local.thisfield.xmlattributes.fieldCode)>
								<cfset local.thisValue = arguments.strData[local.thisfield.xmlattributes.fieldCode]>
							</cfif>								
							<cfswitch expression="#local.thisfield.xmlAttributes.displayTypeCode#">
							<cfcase value="TEXTBOX">
								<cfswitch expression="#local.thisfield.xmlAttributes.dataTypeCode#">
									<cfcase value="DECIMAL2">
										<input type="text" name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" class="tsAppBodyText" size="8" value="#local.thisValue#" autocomplete="off">
										<cfsavecontent variable="local.strReturn.jsValidation">
											<cfoutput>
											#local.strReturn.jsValidation#
											if(_CF_hasValue(_CF_this['#local.thisfield.xmlattributes.fieldCode#'], "TEXT", false)) {
												var decRegEx = new RegExp("^[0-9]{0,7}\.{0,1}[0-9]{0,2}$", "gi");
												if(!(decRegEx.test($('###local.thisfield.xmlattributes.fieldCode#').val()))) arrReq[arrReq.length] = "#local.thisfield.xmlAttributes.fieldLabel# must be a decimal (two decimal places max).";
											}
											<cfif isdefined("local.thisfield.xmlattributes.minValueDecimal2") and isdefined("local.thisfield.xmlattributes.maxValueDecimal2")
												and len(local.thisfield.xmlattributes.minValueDecimal2) and len(local.thisfield.xmlattributes.maxValueDecimal2)>
												if (!mc_validrange_decimal2($('###local.thisfield.xmlattributes.fieldCode#'),#local.thisfield.xmlattributes.minValueDecimal2#,#local.thisfield.xmlattributes.maxValueDecimal2#)) {
													arrReq[arrReq.length] = "#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)# must be between #local.thisfield.xmlattributes.minValueDecimal2# and #local.thisfield.xmlattributes.maxValueDecimal2#.";
												}
											</cfif>
											</cfoutput>
										</cfsavecontent>											
									</cfcase>
									<cfcase value="INTEGER">
										<input type="text" name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" class="tsAppBodyText" size="8" value="#local.thisValue#" autocomplete="off">
										<cfsavecontent variable="local.strReturn.jsValidation">
											<cfoutput>
											#local.strReturn.jsValidation#
											if(_CF_hasValue(_CF_this['#local.thisfield.xmlattributes.fieldCode#'], "TEXT", false)) {
												if(!(mc_valid_int($('###local.thisfield.xmlattributes.fieldCode#').val()))) arrReq[arrReq.length] = "#local.thisfield.xmlAttributes.fieldLabel# must be a whole number.";
											}
											<cfif isdefined("local.thisfield.xmlattributes.minValueInt") and isdefined("local.thisfield.xmlattributes.maxValueInt")
												and len(local.thisfield.xmlattributes.minValueInt) and len(local.thisfield.xmlattributes.maxValueInt)>
												if (!mc_validrange_int($('###local.thisfield.xmlattributes.fieldCode#'),#local.thisfield.xmlattributes.minValueInt#,#local.thisfield.xmlattributes.maxValueInt#)) {
													arrReq[arrReq.length] = "#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)# must be between #local.thisfield.xmlattributes.minValueInt# and #local.thisfield.xmlattributes.maxValueInt#.";
												}
											</cfif>
											</cfoutput>
										</cfsavecontent>											
									</cfcase>
									<cfcase value="STRING">
										<input type="text" name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" class="tsAppBodyText" size="60" value="#local.thisValue#" autocomplete="off">
										<cfif isdefined("local.thisfield.xmlattributes.minChars") and isdefined("local.thisfield.xmlattributes.maxChars")
											and local.thisfield.xmlattributes.minChars gt 0 and local.thisfield.xmlattributes.maxChars gt 0>
											<cfsavecontent variable="local.strReturn.jsValidation">
												<cfoutput>
												#local.strReturn.jsValidation#
												if (!mc_validrange_string($('###local.thisfield.xmlattributes.fieldCode#'),#local.thisfield.xmlattributes.minChars#,#local.thisfield.xmlattributes.maxChars#)) {
													arrReq[arrReq.length] = "#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)# must be between #local.thisfield.xmlattributes.minChars# and #local.thisfield.xmlattributes.maxChars# characters.";
												}
												</cfoutput>
											</cfsavecontent>											
										</cfif>
									</cfcase>
									<cfdefaultcase>
										<input type="text" name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" class="tsAppBodyText" size="24" value="#local.thisValue#" autocomplete="off">
									</cfdefaultcase>
								</cfswitch>
							</cfcase>									
							<cfcase value="RADIO">
								<cfif local.thisfield.xmlattributes.allowNull is 1>
									<input type="radio" name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#_0" checked value="">None selected<br/>
								</cfif>

								<cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt"> 
									<cfswitch expression="#local.thisfield.xmlAttributes.dataTypeCode#">
									<cfcase value="STRING">
										<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueString>
									</cfcase>
									<cfcase value="DECIMAL2">
										<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueDecimal2>
									</cfcase>
									<cfcase value="INTEGER">
										<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueInteger>
									</cfcase>
									<cfcase value="DATE">
										<cfset local.thisOptColValue = dateformat(replaceNoCase(local.thisOpt.xmlAttributes.columnValueDate,'T',' '),"m/d/yyyy")>
									</cfcase>
									<cfcase value="BIT">
										<cfset local.thisOptColValue = YesNoFormat(local.thisOpt.xmlAttributes.columnValueBit)>
									</cfcase>
									<cfdefaultcase>
										<cfset local.thisOptColValue = "">
									</cfdefaultcase>
									</cfswitch>
									<input type="radio" name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#_#local.thisOpt.xmlAttributes.valueID#" value="#local.thisOpt.xmlAttributes.valueID#" <cfif listFind(local.thisValue,local.thisOpt.xmlAttributes.valueID)>checked="checked"</cfif>>#local.thisOptColValue#<br/>
								</cfloop>
							</cfcase>
							<cfcase value="CHECKBOX">
								<cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt"> 
									<cfswitch expression="#local.thisfield.xmlAttributes.dataTypeCode#">
									<cfcase value="STRING">
										<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueString>
									</cfcase>
									<cfcase value="DECIMAL2">
										<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueDecimal2>
									</cfcase>
									<cfcase value="INTEGER">
										<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueInteger>
									</cfcase>
									<cfdefaultcase>
										<cfset local.thisOptColValue = "">
									</cfdefaultcase>
									</cfswitch>
									<input type="checkbox" name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#_#local.thisOpt.xmlAttributes.valueID#" value="#local.thisOpt.xmlAttributes.valueID#" <cfif listFind(local.thisValue,local.thisOpt.xmlAttributes.valueID)>checked="checked"</cfif>>#local.thisOptColValue#<br/>
								</cfloop>
							</cfcase>
							<cfcase value="SELECT">
								<cfif local.thisfield.xmlattributes.allowMultiple>
									<cfsavecontent variable="local.jQueryMultiselect">
										<cfoutput>
										<script type="text/javascript">
										function multiSelectLoad#local.thisfield.xmlattributes.fieldCode#(){
											$("###local.thisfield.xmlattributes.fieldCode#").multiselect({ header:"Choose options below", selectedList:10, minWidth:400 });
										}
										$(function(){											
											multiSelectLoad#local.thisfield.xmlattributes.fieldCode#();										
										});
										</script>	
										</cfoutput>									
									</cfsavecontent>
									<cfhtmlhead text="#application.objCommon.minText(local.jQueryMultiselect)#">

									<cfif isdefined("local.thisfield.xmlattributes.minSelected") and isdefined("local.thisfield.xmlattributes.maxSelected")
										and local.thisfield.xmlattributes.minSelected gt 0 and local.thisfield.xmlattributes.maxSelected gt 0>
										<cfsavecontent variable="local.strReturn.jsValidation">
											<cfoutput>
											#local.strReturn.jsValidation#
											if (!mc_validrange_select($('###local.thisfield.xmlattributes.fieldCode# :selected'),#local.thisfield.xmlattributes.minSelected#,#local.thisfield.xmlattributes.maxSelected#)) {
												arrReq[arrReq.length] = "#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)# must have between #local.thisfield.xmlattributes.minSelected# and #local.thisfield.xmlattributes.maxSelected# selections.";
											}
											</cfoutput>
										</cfsavecontent>											
									</cfif>
								</cfif>

								<cfif ReFindNoCase('ma_[0-9]+_stateprov',local.thisfield.xmlattributes.fieldCode)>
									<select class="tsAppBodyText" name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#">
										#replaceNoCase(local.stateDropdownOptions, 'value="#local.thisValue#"', 'value="#local.thisValue#" selected', "all" )#
									</select>
								<cfelse>
									<select name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" class="tsAppBodyText" <cfif local.thisfield.xmlattributes.allowMultiple>multiple data-function="multiSelectLoad#local.thisfield.xmlattributes.fieldCode#"</cfif>>
									<cfif NOT local.thisfield.xmlattributes.allowMultiple and local.thisfield.xmlattributes.allowNull is 1>
										<option value=""></option>
									</cfif>
									<cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt"> 
										<cfswitch expression="#local.thisfield.xmlAttributes.dataTypeCode#">
										<cfcase value="STRING">
											<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueString>
										</cfcase>
										<cfcase value="DECIMAL2">
											<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueDecimal2>
										</cfcase>
										<cfcase value="INTEGER">
											<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueInteger>
										</cfcase>
										<cfcase value="DATE">
											<cfset local.thisOptColValue = dateformat(replaceNoCase(local.thisOpt.xmlAttributes.columnValueDate,'T',' '),"m/d/yyyy")>
										</cfcase>
										<cfcase value="BIT">
											<cfset local.thisOptColValue = YesNoFormat(local.thisOpt.xmlAttributes.columnValueBit)>
										</cfcase>
										<cfdefaultcase>
											<cfset local.thisOptColValue = "">
										</cfdefaultcase>
										</cfswitch>
										<option value="#local.thisOpt.xmlAttributes.valueID#" 
											<cfif listFind(local.thisValue, local.thisOpt.xmlAttributes.valueID)>
												selected
											<cfelseif structKeyExists(local.thisOpt.xmlAttributes, "isDefault") AND val(local.thisOpt.xmlAttributes.isDefault)>
												selected
											</cfif>
										>#local.thisOptColValue#</option>
									</cfloop>
									</select>
								</cfif>
							</cfcase>
							<cfcase value="DATE">
								<cfif isdefined("local.thisfield.xmlattributes.minValueDate") and isdefined("local.thisfield.xmlattributes.maxValueDate") 
									and len(local.thisfield.xmlattributes.minValueDate) and len(local.thisfield.xmlattributes.maxValueDate)>
									<cfset local.thisfield.xmlattributes.minValueDate = replaceNoCase(local.thisfield.xmlattributes.minValueDate,'T',' ')>
									<cfset local.thisfield.xmlattributes.maxValueDate = replaceNoCase(local.thisfield.xmlattributes.maxValueDate,'T',' ')>
								<cfelse>
									<cfset local.thisfield.xmlattributes.minValueDate = '1/1/1753'>
									<cfset local.thisfield.xmlattributes.maxValueDate = '12/31/9999'>
								</cfif>	

								<nobr>
									<input class="tsAppBodyText" type="text" name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" value="#local.thisValue#" autocomplete="off" size="16" data-function="readyPicker#local.thisfield.xmlattributes.fieldCode#">
									<button type="button" class="tsAppBodyButton" name="btnClear#local.thisfield.xmlattributes.fieldCode#" id="btnClear#local.thisfield.xmlattributes.fieldCode#" onclick="clearField#local.thisfield.xmlattributes.fieldCode#();">clear</button>
								</nobr>
								<cfsavecontent variable="local.datejs">
									<cfoutput>
									<script language="javascript">
										function readyPicker#local.thisfield.xmlattributes.fieldCode#(){ 
											mca_setupDatePickerField('#local.thisfield.xmlattributes.fieldCode#','#DateFormat(local.thisfield.xmlattributes.minValueDate,"m/d/yyyy")#','#DateFormat(local.thisfield.xmlattributes.maxValueDate,"m/d/yyyy")#');
										}
										function clearField#local.thisfield.xmlattributes.fieldCode#(){
											mca_clearDateRangeField('#local.thisfield.xmlattributes.fieldCode#');return false;
										}
										$(function() {
											readyPicker#local.thisfield.xmlattributes.fieldCode#();
											$('##btnClear#local.thisfield.xmlattributes.fieldCode#').on('click', function(e) { mca_clearDateRangeField('#local.thisfield.xmlattributes.fieldCode#');return false; } );
										});
									</script>
									<style type="text/css">
									###local.thisfield.xmlattributes.fieldCode# { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; cursor: pointer !important;  }
									</style>
									</cfoutput>
								</cfsavecontent>
								<cfhtmlhead text="#application.objCommon.minText(local.datejs)#">	
							</cfcase>
							<cfcase value="HTMLCONTENT">

								<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryGetValueID" result="local.qryGetValueIDResult">
									select mdcv.columnValueSiteResourceID as valueID
									from dbo.ams_memberData as md
									inner join dbo.ams_memberDataColumnValues as mdcv on 
										mdcv.valueID = md.valueID
									inner join dbo.ams_memberDataColumns mdc on
										mdc.columnID = mdcv.columnID
										and mdc.columnName = '#local.thisfield.xmlattributes.dbfield#'
									where md.memberid = <cfqueryparam value="#arguments.strData.memberID#" cfsqltype="CF_SQL_INTEGER">
								</cfquery>
									
								<cfset local.valueID = 0>
								<cfif val(local.qryGetValueID.valueID)>
									<cfset local.valueID = local.qryGetValueID.valueID>	
									<cfif not structKeyExists(arguments.strData, "p1Visited") OR not val(arguments.strData.p1Visited)>									
										<cfquery name="local.qryGetOldContent" datasource="#application.dsn.membercentral.dsn#">
											SET NOCOUNT ON;

											DECLARE @defaultSiteID int, @contentID int, @languageID int;
											SELECT @defaultSiteID = dbo.fn_getDefaultSiteIDFromOrgID(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.strData.orgID#">);
											SET @languageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.mcstruct.languageID#">;

											SELECT @contentID = contentID 
											FROM dbo.cms_content 
											WHERE siteID = @defaultSiteID
											AND siteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(local.valueID)#">;

											SELECT rawContent FROM dbo.fn_getContent(@contentID,@languageID);
										</cfquery>	
										<cfset local.thisValue = local.qryGetOldContent.rawContent>
									</cfif>																					
								</cfif>

								<cfset local.currColumnID = val(replace(local.thisfield.xmlattributes.fieldCode, 'md_', ''))>
								<cfoutput>
									<input type="hidden" name="#local.thisfield.xmlattributes.fieldCode#_old" id="#local.thisfield.xmlattributes.fieldCode#_old" value="#local.valueID#">
								<div id="frmmd_#local.currColumnID#">
									<div style="text-align:right;margin-bottom:3px;"><i>Showing text only. <a href="javascript:editContentBlock(#local.currColumnID#,#local.valueID#,'FrontEndUser');">Click to view/edit this text</a></i></div>
									<textarea name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" style="width:100%" rows="5" class="tsAppBodyText" readonly="true">#ReReplace(local.thisValue, "<[^<|>]+?>", "","ALL")#</textarea>
								</div>
								</cfoutput>

									<cfsavecontent variable="local.strReturn.jsValidation">
										<cfoutput>
										#local.strReturn.jsValidation#
										for (instance in CKEDITOR.instances) CKEDITOR.instances[instance].updateElement();
										<cfif isdefined("local.thisfield.xmlattributes.minChars") and isdefined("local.thisfield.xmlattributes.maxChars") 
											and local.thisfield.xmlAttributes.minChars gt 0 and local.thisfield.xmlAttributes.maxChars gt 0>
												if (!mc_validrange_string($('###local.thisfield.xmlattributes.fieldCode#'),#local.thisfield.xmlattributes.minChars#,#local.thisfield.xmlattributes.maxChars#)) {
													locateErr += "#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)# must be between #local.thisfield.xmlattributes.minChars# and #local.thisfield.xmlattributes.maxChars# characters.\n";
												}
										</cfif>
										</cfoutput>
									</cfsavecontent>											
								
							</cfcase>	
							<cfcase value="TEXTAREA">
								<cfoutput><input type="hidden" name="#local.thisfield.xmlattributes.fieldCode#_old" id="#local.thisfield.xmlattributes.fieldCode#_old" value="#local.thisValue#"></cfoutput>
								<cfoutput><textarea name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" style="width:100%" rows="5" class="tsAppBodyText">#HTMLEditFormat(local.thisValue)#</textarea></cfoutput>
								<cfif local.thisfield.xmlAttributes.dataTypeCode eq "CONTENTOBJ"
									and isdefined("local.thisfield.xmlattributes.minChars") and isdefined("local.thisfield.xmlattributes.maxChars")
									and local.thisfield.xmlAttributes.minChars gt 0 and local.thisfield.xmlAttributes.maxChars gt 0>
									<cfsavecontent variable="local.strReturn.jsValidation">
										<cfoutput>
										#local.strReturn.jsValidation#
										if (!mc_validrange_string($('###local.thisfield.xmlattributes.fieldCode#'),#local.thisfield.xmlattributes.minChars#,#local.thisfield.xmlattributes.maxChars#)) {
											locateErr += "#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)# must be between #local.thisfield.xmlattributes.minChars# and #local.thisfield.xmlattributes.maxChars# characters.\n";
										}
										</cfoutput>
									</cfsavecontent>											
								</cfif>										
							</cfcase>	
							<cfcase value="DOCUMENT">
								<cfoutput>								
									<input type="button" class="tsAppBodyText" name="#local.thisfield.xmlattributes.fieldCode#_btn" id="#local.thisfield.xmlattributes.fieldCode#_btn" value="Select file" data-function="uploader#local.thisfield.xmlattributes.fieldCode#">
									<input type="hidden" name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" class="documentField" style="white-space: nowrap!important;">
									<span id="#local.thisfield.xmlattributes.fieldCode#_newFileDetails" class="tsAppBodyText"></span>
								</cfoutput>	
								<cfsavecontent variable="local.datecssjs">
									<cfoutput>
									<script type="text/javascript" src="/assets/common/javascript/jQueryAddons/plupload/3.1.2/plupload.full.min.js"></script>
									<script language="javascript">
                                        function uploader#local.thisfield.xmlattributes.fieldCode#(){ 
                                            if (typeof arrUploaders == 'undefined') {
                                                arrUploaders = [];												
                                                if($("###local.thisfield.xmlattributes.fieldCode#").parents('form').length){
                                                    $($("###local.thisfield.xmlattributes.fieldCode#").parents('form')[0]).attr('enctype','multipart/form-data');
                                                }
                                            }

                                            var el = $("###local.thisfield.xmlattributes.fieldCode#_btn");
                                            var mcFileSelBtnID = el.attr('id');
                                            var mcFileFieldCode = "#local.thisfield.xmlattributes.fieldCode#";
                                            var mcFileColumnID = "#local.thisfield.xmlattributes.mdColumnID#";
                                            var mcma_link_savedoc ="/?event=proxy.ts_json&c=CUSTOM_FORM_UTILITIES&m=saveMemberCustomSingleDocument";
                                            var uploadURL = mcma_link_savedoc+'&cid='+mcFileColumnID+'&memberid=';

                                            var uploader = new plupload.Uploader({
                                                runtimes:'html5',
                                                browse_button:mcFileSelBtnID,
                                                url:uploadURL,
                                                file_data_name:mcFileFieldCode,
                                                multi_selection:false
                                            });

                                            uploader.bind('FilesAdded', function(up, files) {
                                                plupload.each(files, function(file) {
                                                    var fileName = file.name + ' (' + plupload.formatSize(file.size) + ')';
                                                    $('##'+mcFileFieldCode).val(fileName);
                                                    $('##'+mcFileFieldCode+'_newFileDetails').html(fileName);
                                                });
                                            });
                                            
                                            uploader.bind('FileUploaded', function () {
                                                arrUploaders.find(function (f) {
                                                    return f.columnID == mcFileColumnID;
                                                }).fileUploaded = 1;
                                            });

                                            uploader.init();
                                            arrUploaders.push({ uploader:uploader, columnID:mcFileColumnID, fileUploaded:0 });                                        
                                        }
										$(function() {
                                            uploader#local.thisfield.xmlattributes.fieldCode#();
										});
									</script>
									</cfoutput>
								</cfsavecontent>
								<cfhtmlhead text="#application.objCommon.minText(local.datecssjs)#">																					
							</cfcase>										
							</cfswitch>
						</td>
					</tr>

					<cfif local.thisfield.xmlattributes.isRequired is 1>
						<cfswitch expression="#local.thisfield.xmlAttributes.displayTypeCode#">
						<cfcase value="TEXTBOX,DATE,DOCUMENT">
							<cfsavecontent variable="local.strReturn.jsValidation">
								<cfoutput>
								#local.strReturn.jsValidation#
								if(!_CF_hasValue(_CF_this['#local.thisfield.xmlattributes.fieldCode#'], "TEXT", false)) arrReq[arrReq.length] = "#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)# is required.";
								</cfoutput>
							</cfsavecontent>
						</cfcase>
						<cfcase value="RADIO">
							<cfsavecontent variable="local.strReturn.jsValidation">
								<cfoutput>
								#local.strReturn.jsValidation#
								if(!_CF_hasValue(_CF_this['#local.thisfield.xmlattributes.fieldCode#'], "RADIO", false)) arrReq[arrReq.length] = '#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)# is required.';
								</cfoutput>
							</cfsavecontent>
						</cfcase>
						<cfcase value="CHECKBOX">
							<cfsavecontent variable="local.strReturn.jsValidation">
								<cfoutput>
								#local.strReturn.jsValidation#
								if(!_CF_hasValue(_CF_this['#local.thisfield.xmlattributes.fieldCode#'], "CHECKBOX", false)) arrReq[arrReq.length] = '#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)# is required.';
								</cfoutput>
							</cfsavecontent>
						</cfcase>
						<cfcase value="SELECT">
							<cfsavecontent variable="local.strReturn.jsValidation">
								<cfoutput>
								#local.strReturn.jsValidation#
								<cfif local.thisfield.xmlattributes.allowMultiple is 1>
									if( !_CF_hasValue(_CF_this['#local.thisfield.xmlattributes.fieldCode#'], "SELECT", false )) arrReq[arrReq.length] = "#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)# is required.";
								<cfelse>
									if (_CF_this['#local.thisfield.xmlattributes.fieldCode#'].options[_CF_this['#local.thisfield.xmlattributes.fieldCode#'].selectedIndex].value.length == 0) arrReq[arrReq.length] = "#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)# is required.";
								</cfif>
								</cfoutput>
							</cfsavecontent>
						</cfcase>
						<cfcase value="TEXTAREA">
							<cfsavecontent variable="local.strReturn.jsValidation">
								<cfoutput>
								#local.strReturn.jsValidation#
								if(!_CF_hasValue(_CF_this['#local.thisfield.xmlattributes.fieldCode#'], "TEXTAREA", false)) arrReq[arrReq.length] = "#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)# is required.";
								</cfoutput>
							</cfsavecontent>
						</cfcase>
						</cfswitch>
					</cfif>
					<cfif ReFindNoCase('me_[0-9]+_email',local.thisfield.xmlattributes.fieldCode)>
						<cfsavecontent variable="local.strReturn.jsValidation">
							<cfoutput>
							#local.strReturn.jsValidation#
							if(_CF_hasValue(_CF_this['#local.thisfield.xmlattributes.fieldCode#'], "TEXT", false)) {
								var urlRegEx = new RegExp("#application.regEx.email#", "gi");
								if(!(urlRegEx.test($('###local.thisfield.xmlattributes.fieldCode#').val()))) arrReq[arrReq.length] = "#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)# is not a valid e-mail address.";
							}
							</cfoutput>
						</cfsavecontent>
					<cfelseif ReFindNoCase('mw_[0-9]+_website',local.thisfield.xmlattributes.fieldCode)>
						<cfsavecontent variable="local.strReturn.jsValidation">
							<cfoutput>
							#local.strReturn.jsValidation#
							if(_CF_hasValue(_CF_this['#local.thisfield.xmlattributes.fieldCode#'], "TEXT", false)) {
								var mw = $('###local.thisfield.xmlattributes.fieldCode#');
								if (mw.val().substring(0,4) != 'http') mw.val('http://' + mw.val());
								var urlRegEx = new RegExp("#application.regEx.url#", "gi");
								if(!(urlRegEx.test($('###local.thisfield.xmlattributes.fieldCode#').val()))) arrReq[arrReq.length] = "#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)# is not a valid URL.";
							}
							</cfoutput>
						</cfsavecontent>					
					</cfif>
				</cfloop>
				<cfif len(local.qryGetFSContent.rawContent) and local.fsDescriptionPos is "below">
					<tr>
						<td colspan="4" class="tsAppBodyText">#local.qryGetFSContent.rawContent#</td>
					</tr>
				</cfif>			
				</table>
				</cfoutput>				
			</cfsavecontent>
		<cfelse>
			<cfsavecontent variable="local.strReturn.fieldSetContent">
				<cfoutput>
				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
				<tr>
					<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">#local.strReturn.fieldSetTitle#</td>
				</tr>
				<tr>
					<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
						<table cellpadding="3" border="0" cellspacing="0">
						<cfloop array="#local.xmlFieldset.xmlRoot.xmlChildren#" index="local.thisfield">
							<tr valign="top">
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;" nowrap>#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#: &nbsp;</td>
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;"> 
									<cfif structKeyExists(arguments.strData,local.thisfield.xmlattributes.fieldCode)>
										<cfset local.memberData = arguments.strData[local.thisfield.xmlattributes.fieldCode]>
										<cfif len(local.memberData)>
											<cfswitch expression="#local.thisfield.xmlAttributes.displayTypeCode#">
											<cfcase value="RADIO">
												<cfswitch expression="#local.thisfield.xmlAttributes.dataTypeCode#">
												<cfcase value="STRING">
													<cfset local.memberColDataActualValue = XMLSearch(local.thisfield,"string(opt[@valueID=#local.memberData#]/@columnValueString)")>
												</cfcase>
												<cfcase value="DECIMAL2">
													<cfset local.memberColDataActualValue = XMLSearch(local.thisfield,"string(opt[@valueID=#local.memberData#]/@columnValueDecimal2)")>
												</cfcase>
												<cfcase value="INTEGER">
													<cfset local.memberColDataActualValue = XMLSearch(local.thisfield,"string(opt[@valueID=#local.memberData#]/@columnValueInteger)")>
												</cfcase>
												<cfcase value="DATE">
													<cfset local.memberColDataActualValue = dateformat(replaceNoCase(XMLSearch(local.thisfield,"string(opt[@valueID=#local.memberData#]/@columnValueDate)"),'T',' '),"m/d/yyyy")>
												</cfcase>
												<cfcase value="BIT">
													<cfset local.memberColDataActualValue = YesNoFormat(XMLSearch(local.thisfield,"string(opt[@valueID=#local.memberData#]/@columnValueBit)"))>
												</cfcase>
												<cfdefaultcase>
													<cfset local.memberColDataActualValue = "">
												</cfdefaultcase>
												</cfswitch>

												<cfif local.thisfield.xmlAttributes.dataTypeCode eq "BIT" and len(local.memberColDataActualValue)>
													#YesNoFormat(local.memberColDataActualValue)#
												<cfelseif local.thisfield.xmlAttributes.dataTypeCode eq "BIT" and NOT len(local.memberColDataActualValue) and local.thisfield.xmlattributes.allowNull is 1>
													&nbsp;
												<cfelseif local.thisfield.xmlAttributes.dataTypeCode eq "DATE" and len(local.memberColDataActualValue)>
													#DateFormat(local.memberColDataActualValue,"m/d/yyyy")#
												<cfelseif local.thisfield.xmlAttributes.dataTypeCode eq "DATE" and NOT len(local.memberColDataActualValue) and local.thisfield.xmlattributes.allowNull is 1>
													&nbsp;
												<cfelseif len(local.memberColDataActualValue)>
													#local.memberColDataActualValue#
												<cfelseif NOT len(local.memberColDataActualValue) and local.thisfield.xmlattributes.allowNull is 1>
													&nbsp;
												</cfif>
											</cfcase>
											<cfcase value="CHECKBOX">
												<cfset local.memberColDataActualValue = "">
												<cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt"> 
													<cfif listFind(local.memberData,local.thisOpt.xmlAttributes.valueID)>
														<cfswitch expression="#local.thisfield.xmlAttributes.dataTypeCode#">
														<cfcase value="STRING">
															<cfset local.memberColDataActualValue = listAppend(local.memberColDataActualValue,local.thisOpt.xmlAttributes.columnValueString,chr(7))>
														</cfcase>
														<cfcase value="DECIMAL2">
															<cfset local.memberColDataActualValue = listAppend(local.memberColDataActualValue,local.thisOpt.xmlAttributes.columnValueDecimal2,chr(7))>
														</cfcase>
														<cfcase value="INTEGER">
															<cfset local.memberColDataActualValue = listAppend(local.memberColDataActualValue,local.thisOpt.xmlAttributes.columnValueInteger,chr(7))>
														</cfcase>
														</cfswitch>
													</cfif>
												</cfloop>
												#listChangeDelims(local.memberColDataActualValue,"<br/>",chr(7))#
											</cfcase>
											<cfcase value="SELECT">
												<cfif ReFindNoCase('ma_[0-9]+_stateprov',local.thisfield.xmlattributes.fieldCode)>
													<cfquery name="local.qryStatesSelected" dbtype="query">
														select StateName
														from [local].qryStates
														where stateID = #val(local.memberData)#
													</cfquery>
													#local.qryStatesSelected.StateName#
												<cfelse>
													<cfset local.memberColDataActualValue = "">
													<cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt"> 
														<cfif listFind(local.memberData,local.thisOpt.xmlAttributes.valueID)>
															<cfswitch expression="#local.thisfield.xmlAttributes.dataTypeCode#">
															<cfcase value="STRING">
																<cfset local.memberColDataActualValue = listAppend(local.memberColDataActualValue,local.thisOpt.xmlAttributes.columnValueString,chr(7))>
															</cfcase>
															<cfcase value="DECIMAL2">
																<cfset local.memberColDataActualValue = listAppend(local.memberColDataActualValue,local.thisOpt.xmlAttributes.columnValueDecimal2,chr(7))>
															</cfcase>
															<cfcase value="INTEGER">
																<cfset local.memberColDataActualValue = listAppend(local.memberColDataActualValue,local.thisOpt.xmlAttributes.columnValueInteger,chr(7))>
															</cfcase>
															<cfcase value="DATE">
																<cfset local.memberColDataActualValue = listAppend(local.memberColDataActualValue,dateformat(replaceNoCase(local.thisOpt.xmlAttributes.columnValueDate,'T',' '),"m/d/yyyy"),chr(7))>
															</cfcase>
															<cfcase value="BIT">
																<cfset local.memberColDataActualValue = listAppend(local.memberColDataActualValue,YesNoFormat(local.thisOpt.xmlAttributes.columnValueBit),chr(7))>
															</cfcase>
															</cfswitch>
														</cfif>
													</cfloop>
													#listChangeDelims(local.memberColDataActualValue,"<br/>",chr(7))#
												</cfif>
											</cfcase>
											<cfcase value="DOCUMENT">
												<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryGetValueID" result="local.qryGetValueIDResult">
													select mdcv.columnValueSiteResourceID as valueID
													from dbo.ams_memberData as md
													inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
													inner join dbo.ams_memberDataColumns mdc on mdc.columnID = mdcv.columnID
													inner join dbo.ams_memberDataColumnDataTypes as mdt on mdt.dataTypeID = mdc.dataTypeID
													where mdt.dataTypeCode = 'DOCUMENTOBJ'
													and md.memberid = <cfqueryparam value="#val(arguments.strData.memberID)#" cfsqltype="CF_SQL_INTEGER">
													and mdcv.columnID = <cfqueryparam value="#val(local.thisfield.xmlattributes.mdColumnID)#" cfsqltype="CF_SQL_INTEGER">
												</cfquery>
												<cfif local.qryGetValueID.recordCount>
													<cfset local.getDocument = createObject("model.system.platform.document").getDocumentDataBySiteResourceID(local.qryGetValueID.valueID)>
													<cfif val(local.getDocument.DOCUMENTID)>
														<a href="#application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).scheme#://#application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).mainhostname#/docdownload/#local.getDocument.documentID#&vid=#local.getDocument.documentVersionID#">#local.memberData#</a>
													<cfelse>
														#local.memberData#
													</cfif>
												<cfelse>
													#local.memberData#
												</cfif>
											</cfcase>
											<cfdefaultcase>
												#local.memberData#
											</cfdefaultcase>
											</cfswitch>
										<cfelse>
											&nbsp;
										</cfif>
									<cfelse>
										&nbsp;
									</cfif>
								</td>
							</tr>
						</cfloop>
						</table>
					</td>
				</tr>
				</table>
				<br/>
				</cfoutput>				
			</cfsavecontent>
		</cfif>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="renderSubscriptionForm" access="public" output="false" returntype="struct">
		<cfargument name="subscriptionID" type="string" required="false">
		<cfargument name="memberID" type="numeric" required="false">
		<cfargument name="isRenewalRate" type="boolean" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="strData" type="struct" required="true" hint="struct of form field default values">

		<cfset var local = structNew()>

		<cfset local.subDefinitionStruct = sub_getSubscriptionTreeStruct(
			subscriptionID = arguments.subscriptionID,
			memberID = arguments.memberID,
			isRenewalRate = arguments.isRenewalRate,
			siteID = arguments.siteID)>
		<cfreturn getSubscriptionForm(subDefinitionStruct=local.subDefinitionStruct,strData=arguments.strData)>
	</cffunction>

	<cffunction name="getSubscriptionForm" access="private" output="false" returntype="struct">
		<cfargument name="subDefinitionStruct" type="struct" required="false">
		<cfargument name="recursionLevel" type="numeric" required="false" default="1">
		<cfargument name="strData" type="struct" required="true" hint="struct of form field default values">

		<cfset var local = structNew()>
		<cfset local.strReturn = { jsValidation='', formContent='', formTitle='' }>

		<cfif arguments.recursionLevel eq 1>
			<cfset local.subdivclass = "well">
		<cfelse>
			<cfset local.subdivclass = "">			
		</cfif>
		<cfset arguments.subDefinitionStruct.rateSchedule = sortArrayOfStructs(arguments.subDefinitionStruct.rateSchedule,'rateName')>
		
		<cfsavecontent variable="local.strReturn.formContent">
			<cfoutput>
				<div class="subLabelHolder">
					<div class="#local.subdivclass#">
						<cfif arguments.recursionLevel eq 1>
							<legend>#arguments.subDefinitionStruct.typename#</legend>
						</cfif>

						<cfset local.hasSingleEditableRate = (arrayLen(arguments.subDefinitionStruct.rateSchedule) eq 1 and val(arguments.subDefinitionStruct.rateSchedule[1].frontEndAllowChangePrice))>
						<cfset local.skipRateSelection = (arraylen(arguments.subDefinitionStruct.rateSchedule) eq 1 and (arraylen(arguments.subDefinitionStruct.rateSchedule[1].frequencies) eq 1 OR arguments.recursionLevel gt 1))>
						<label class="checkbox subLabel" for="sub#arguments.subDefinitionStruct.subscriptionID#">

							<cfif arguments.recursionLevel eq 1>
								<input class="subCheckbox" type="hidden" name="sub#arguments.subDefinitionStruct.subscriptionID#" id="sub#arguments.subDefinitionStruct.subscriptionID#" value="sub#arguments.subDefinitionStruct.subscriptionID#">
							<cfelse>
								<cfset local.thisSubSelected = "">
								<cfif structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#")>
									<cfset local.thisSubSelected = 'checked="checked"'>
								</cfif>	
								<input class="subCheckbox" type="checkbox" #local.thisSubSelected# name="sub#arguments.subDefinitionStruct.subscriptionID#" id="sub#arguments.subDefinitionStruct.subscriptionID#" value="sub#arguments.subDefinitionStruct.subscriptionID#"> 
							</cfif>
							#arguments.subDefinitionStruct.subscriptionName#
							<span class="selectedRate" id="sub#arguments.subDefinitionStruct.subscriptionID#_selectedRate">
								<cfif local.skipRateSelection>
									- 
									<cfloop array="#arguments.subDefinitionStruct.rateSchedule#" index="local.thisRate">
										<input type="hidden" class="subRateCheckbox" name="sub#arguments.subDefinitionStruct.subscriptionID#_rate" id="sub#arguments.subDefinitionStruct.subscriptionID#_rate#local.thisRate.rateID#_#local.thisRate.frequencies[1].frequencyShortName#" value="#local.thisRate.rateID#" data-frequencyuid="#local.thisRate.frequencies[1].uid#" data-rfid="#local.thisRate.frequencies[1].rfid#" data-subscriptionuid="#arguments.subDefinitionStruct.uid#" data-selectedfrequencyid="sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"> 
										<input type="hidden" class="subRateFrequencySelected" name="sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected" id="sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected" value="#local.thisRate.frequencies[1].uid#"> 
										#local.thisRate.rateName# 
										<cfloop array="#local.thisRate.frequencies#" index="local.thisRateFrequency">
											<span id="sub#arguments.subDefinitionStruct.subscriptionID#_selectedRate_rate" class="joinFrequency joinFrequency_#local.thisRateFrequency.frequencyShortName# sub#arguments.subDefinitionStruct.subscriptionID#_selectedRate_rate" data-rate="#dollarFormat(local.thisRateFrequency.rateAmt)#">(#dollarFormat(local.thisRateFrequency.rateAmt)# #local.thisRateFrequency.frequencyName#)</span>
										</cfloop>
									</cfloop>
								</cfif>
							</span>
						</label>
						<cfif not local.skipRateSelection or local.hasSingleEditableRate>
							<div style="margin-left:15px;" class="subAvailableRates subRatesDisabled">
								<input type="hidden" class="subRateFrequencySelected" name="sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected" id="sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"> 
								<cfloop array="#arguments.subDefinitionStruct.rateSchedule#" index="local.thisRate">

									<cfset local.thisRateSelected = "">
									<cfif structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rate") and structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected") and arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rate"] eq local.thisRate.rateID and arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"] eq local.thisRate.frequencies[1].uid>
										<cfset local.thisRateSelected = 'checked="checked"'>
									</cfif>	
									<label class="radio subRateLabel" for="sub#arguments.subDefinitionStruct.subscriptionID#_rate#local.thisRate.rateID#_#local.thisRate.frequencies[1].frequencyShortName#">
										<cfif not local.hasSingleEditableRate>
											<input type="radio" data-frequencyFname="#local.thisRate.frequencies[1].frequencyShortName#" data-rateinstallments="#local.thisRate.frequencies[1].numInstallments#" class="subRateCheckbox" #local.thisRateSelected# name="sub#arguments.subDefinitionStruct.subscriptionID#_rate" id="sub#arguments.subDefinitionStruct.subscriptionID#_rate#local.thisRate.rateID#_#local.thisRate.frequencies[1].frequencyShortName#" value="#local.thisRate.rateID#" data-frequencyuid="#local.thisRate.frequencies[1].uid#" data-rfid="#local.thisRate.frequencies[1].rfid#" data-subscriptionuid="#arguments.subDefinitionStruct.uid#" data-selectedfrequencyid="sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected">
										</cfif>
										<span class="labelText">
											#local.thisRate.rateName#
											<cfif local.thisRate.frontEndAllowChangePrice>												
												<cfif structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rate") and structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected") and arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rate"] eq local.thisRate.rateID and arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"] eq local.thisRate.frequencies[1].uid and structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#") and arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#"] gt 0>
													<cfset local.editablePrice = arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#"]>
												<cfelse>
													<cfloop array="#local.thisRate.frequencies#" index="local.thisRateFrequency">                                                       
														<cfset local.editablePrice = local.thisRateFrequency.rateAmt />
													</cfloop>
												</cfif>
												<cfif arguments.recursionLevel eq 1>
													($ <input type="text" class="subRateOverrideBox" data-ratemin="#local.thisRate.frontEndChangePriceMin#" data-ratemax="#local.thisRate.frontEndChangePriceMax#" name="sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#" id="sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#" value="#trim(numberFormat(local.editablePrice,'__.00'))#" size="10"> #local.thisRate.frequencies[1].frequencyName#)
												<cfelse>
													$ <input type="text" class="subRateOverrideBox" data-ratemin="#local.thisRate.frontEndChangePriceMin#" data-ratemax="#local.thisRate.frontEndChangePriceMax#"  name="sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#" id="sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#" value="#trim(numberFormat(local.editablePrice,'__.00'))#" size="10">
												</cfif>
												<cfsavecontent variable="local.strReturn.jsValidation">
													#local.strReturn.jsValidation#
													<cfif not findNoCase("amountRegex", local.strReturn.jsValidation)>
														var amountRegex =  /(?:^\d{1,3}(?:\.?\d{3})*(?:,\d{2})?$)|(?:^\d{1,3}(?:,?\d{3})*(?:\.\d{2})?$)/;
													</cfif>													
													if(($('##sub#arguments.subDefinitionStruct.subscriptionID#:checked').length > 0)&&	($("##sub#arguments.subDefinitionStruct.subscriptionID#_rate#local.thisRate.rateID#_#local.thisRate.frequencies[1].frequencyShortName#").is(':checked') || $("##sub#arguments.subDefinitionStruct.subscriptionID#_rate#local.thisRate.rateID#_#local.thisRate.frequencies[1].frequencyShortName#").attr('type') == 'hidden') && (($.trim($('##sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#').val()).length == 0) || ($.trim($('##sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#').val()).length > 0 && (!amountRegex.test($.trim($('##sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#').val())) || $('##sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#').val() <= 0  ))) ){
														arrReq[arrReq.length] = " Enter a valid amount for rate #local.thisRate.rateName#.";
													}
												</cfsavecontent>
											<cfelse>
												<cfloop array="#local.thisRate.frequencies#" index="local.thisRateFrequency">
													<span id="sub#arguments.subDefinitionStruct.subscriptionID#_selectedRate_rate" class="joinFrequency joinFrequency_#local.thisRateFrequency.frequencyShortName# sub#arguments.subDefinitionStruct.subscriptionID#_selectedRate_rate" data-rate="#dollarFormat(local.thisRateFrequency.rateAmt)#">(#dollarFormat(local.thisRateFrequency.rateAmt)# #local.thisRateFrequency.frequencyName#)</span>
												</cfloop>
											</cfif>
										</span>
									</label>
									<cfif arguments.recursionLevel gt 1>
										<cfsavecontent variable="local.strReturn.jsValidation">
											#local.strReturn.jsValidation#
											if($("##sub#arguments.subDefinitionStruct.subscriptionID#_rate#local.thisRate.rateID#_#local.thisRate.frequencies[1].frequencyShortName#").is(':checked') && $('##sub#arguments.subDefinitionStruct.subscriptionID#').is(':checkbox') && !$("##sub#arguments.subDefinitionStruct.subscriptionID#").is(':checked') )
												arrReq[arrReq.length] = " Select subscription for rate #local.thisRate.rateName#.";
										</cfsavecontent>	
									</cfif>									
								</cfloop>
								<cfif arguments.recursionLevel gt 1>
									<cfsavecontent variable="local.strReturn.jsValidation">
										#local.strReturn.jsValidation#
										if( $('##sub#arguments.subDefinitionStruct.subscriptionID#').is(':checkbox') && $("##sub#arguments.subDefinitionStruct.subscriptionID#").is(':checked') &&
										$("input[name='sub#arguments.subDefinitionStruct.subscriptionID#_rate']:checked").val() != undefined &&
										!$("input[name='sub#arguments.subDefinitionStruct.subscriptionID#_rate']:checked").val() ){
											arrReq[arrReq.length] = " Select rate for subscription #arguments.subDefinitionStruct.subscriptionName#.";
										}											
									</cfsavecontent>	
								</cfif>									
							</div>
						</cfif>
					</div>
				</div>
				<cfif structKeyExists(arguments.subDefinitionStruct, "addons")>
					<div class="subAddonsArrayWrapper" id="sub#arguments.subDefinitionStruct.subscriptionID#_addons">
						<cfloop array="#arguments.subDefinitionStruct.addons#" index="local.thisAddon">
							<div class="well subAddonWrapper" id="sub#arguments.subDefinitionStruct.subscriptionID#_addonID#local.thisAddon.addonID#" data-pcpctoffeach="#local.thisAddon.PCPctOffEach#" data-pcnum="#local.thisAddon.PCnum#" data-addonid="#local.thisAddon.addOnID#" data-frontendaddadditional="#local.thisAddon.frontEndAddAdditional#" data-frontendallowchangeprice="#local.thisAddon.frontEndAllowChangePrice#" data-frontendallowselect="#local.thisAddon.frontEndAllowSelect#" data-maxallowed="#local.thisAddon.maxAllowed#" data-minallowed="#local.thisAddon.minAllowed#" data-setid="#local.thisAddon.setID#" data-setname="#local.thisAddon.setName#" data-setuid="#local.thisAddon.setUID#">
								<legend>#local.thisAddon.setName#</legend>
								<cfif len(local.thisAddon.frontEndContent.rawcontent)>
									<p>#local.thisAddon.frontEndContent.rawcontent#</p>
								</cfif>
								<div class="addonMessageArea"></div>
								<cfloop array="#local.thisAddon.subscriptions#" index="local.thisAddonSub">
									<cfset local.thisAddonSubForm = getSubscriptionForm(subDefinitionStruct=local.thisAddonSub,recursionLevel=arguments.recursionLevel+1,strData=arguments.strData)>
									<cfset local.strReturn.jsValidation = local.strReturn.jsValidation & chr(13) & chr(10) & local.thisAddonSubForm.jsValidation>
									#local.thisAddonSubForm.formContent#
								</cfloop>
							</div>
						</cfloop>
					</div>
				</cfif>
			</cfoutput>				
		</cfsavecontent>
		<cfif arguments.recursionLevel eq 1>
			<cfsavecontent variable="local.strReturn.jsValidation">
				<cfoutput>
					<!--- copy frequency UID to hidden form field for each subscription --->
					$('.subRateCheckbox:hidden').each(function(thisIndex,thisElement){
						var selectedFrequencyField = $('##' + $(thisElement).data('selectedfrequencyid'))[0];
						selectedFrequencyField.value = $(thisElement).data('frequencyuid');
					});
					$('.subRateCheckbox:checked').each(function(thisIndex,thisElement){
						var selectedFrequencyField = $('##' + $(thisElement).data('selectedfrequencyid'))[0];
						selectedFrequencyField.value = $(thisElement).data('frequencyuid');
					});
					#local.strReturn.jsValidation#
				</cfoutput>
			</cfsavecontent>
			<cfsavecontent variable="local.strReturn.jsAddonValidation">
				<cfoutput>
					$(document).ready(function(){	
                        $('.subAddonWrapper .subCheckbox').change(function() {                            
                            var thisPcNum =  $(this).closest('[data-pcnum]').data('pcnum');
                            var thisDivID =  $(this).parent().closest('div[id]').attr("id");
                            if (thisPcNum > 0){
                                $('##' + thisDivID + '> .subLabelHolder input:checkbox:not(:checked)').each(function(thisIndex,thisElement) { 
                                    var thisLabelElement = $('.' + $(thisElement).val() +'_selectedRate_rate');
									thisLabelElement.each(function(){
										var thisRate =  $(this).data('rate');  
										$(this).text($(this).text().replace('$0.00', thisRate));
									});
                                }); 
                                
                                $('##' + thisDivID + '> .subLabelHolder input:checkbox:checked').each(function(thisIndex,thisElement) {
                                    if(thisPcNum >= (thisIndex+1)){
                                       	var thisLabelElement = $('.' + $(thisElement).val() +'_selectedRate_rate');
										thisLabelElement.each(function(){
											var thisRate =  $(this).data('rate');  
											$(this).text($(this).text().replace(thisRate, '$0.00'));
										});
                                    }
                                    else{   
										var thisLabelElement = $('.' + $(thisElement).val() +'_selectedRate_rate');
										thisLabelElement.each(function(){
											var thisRate =  $(this).data('rate');  
											$(this).text($(this).text().replace('$0.00', thisRate));
										});                                    
                                    }                                    
                                }); 
                            }                           
                        });
					});	
				</cfoutput>
			</cfsavecontent>			
		</cfif>	
		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="showSubscriptionFormSelections" access="public" output="false" returntype="struct">
		<cfargument name="subscriptionID" type="string" required="false">
		<cfargument name="memberID" type="numeric" required="false">
		<cfargument name="isRenewalRate" type="boolean" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="strData" type="struct" required="true" hint="struct of form field default values">

		<cfset var local = structNew()>

		<cfset local.subDefinitionStruct = sub_getSubscriptionTreeStruct(subscriptionID=arguments.subscriptionID, memberID=arguments.memberID, isRenewalRate=arguments.isRenewalRate, siteID=arguments.siteID)>
		<cfset local.strReturn = getSubscriptionFormSelections(subDefinitionStruct=local.subDefinitionStruct, strData=arguments.strData, memberID=arguments.memberID)>
		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="getSubscriptionFormSelections" access="private" output="false" returntype="struct">
		<cfargument name="subDefinitionStruct" type="struct" required="false">
		<cfargument name="recursionLevel" type="numeric" required="false" default="1">
		<cfargument name="strData" type="struct" required="true" hint="struct of form field default values">
		<cfargument name="isFree" type="boolean" required="false" default="false" hint="subscription is free">	
		<cfargument name="memberID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { jsValidation='', formContent='', formTitle='', totalFullPrice = 0, subPrices={}, strTax={ totalTaxAmt = 0 }}>

		<cfif arguments.recursionLevel eq 1>
			<cfset local.subdivclass = "">
		<cfelse>
			<cfset local.subdivclass = "">
		</cfif>
		<cfset local.qryMemberBillingAddress = application.objMember.getMemberAddressByBillingAddressType(orgID=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgid, memberID=arguments.memberID)>

		<cfsavecontent variable="local.strReturn.formContent">
			<cfoutput>
				<cfif structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#") and structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rate")>
					<div>
						<div class="#local.subdivclass#">
							<cfif arguments.recursionLevel eq 1>
								<div>
									<strong>#arguments.subDefinitionStruct.typename#</strong>
								</div>
							</cfif>
							#arguments.subDefinitionStruct.subscriptionName#
							<span class="selectedRate" id="sub#arguments.subDefinitionStruct.subscriptionID#_selectedRate">
								-
								<cfloop array="#arguments.subDefinitionStruct.rateSchedule#" index="local.thisRate">
									<cfif structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rate") and arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rate"] eq local.thisRate.rateID>
										<!--- #local.thisRate.rateName#  --->
										<cfif local.thisRate.frontEndAllowChangePrice and structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#") and arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#"] gt 0>
											<cfloop array="#local.thisRate.frequencies#" index="local.thisRateFrequency">
												<cfif structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected") and (arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"] eq local.thisRateFrequency.uid or trim(arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"]) eq "")>
													#local.thisRate.rateName#  <span class="joinFrequency joinFrequency_#local.thisRateFrequency.frequencyShortName#">(<cfif not arguments.isFree>#dollarFormat(arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#"])#<cfelse>$0</cfif> #local.thisRateFrequency.frequencyName#)</span>
												</cfif>
											</cfloop>
											<cfset local.strReturn.totalFullPrice = arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.thisRate.rateID#"] />
											<cfset local.strReturn.subPrices['sub#arguments.subDefinitionStruct.subscriptionID#'] = local.strReturn.totalFullPrice>
										<cfelse>
											<cfloop array="#local.thisRate.frequencies#" index="local.thisRateFrequency">
												<cfif arguments.recursionLevel eq 1 and structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected") and (arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"] eq local.thisRateFrequency.uid or trim(arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"]) eq "")>
													#local.thisRate.rateName#  <span class="joinFrequency joinFrequency_#local.thisRateFrequency.frequencyShortName#">(<cfif not arguments.isFree>#dollarFormat(local.thisRateFrequency.rateAmt)#<cfelse>$0</cfif> #local.thisRateFrequency.frequencyName#)</span>
												<cfelseif arguments.recursionLevel gt 1 and structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected") and (arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"] eq local.thisRateFrequency.uid or trim(arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"]) eq "")>
													#local.thisRate.rateName#  <span class="joinFrequency joinFrequency_#local.thisRateFrequency.frequencyShortName#">(<cfif not arguments.isFree>#dollarFormat(local.thisRateFrequency.rateAmt)#<cfelse>$0</cfif> #local.thisRateFrequency.frequencyName#)</span>
												</cfif>
												<cfif structKeyExists(arguments.strData, "sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected") AND arguments.strData["sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"] EQ local.thisRateFrequency.uid>
													<cfset local.strReturn.totalFullPrice = local.thisRateFrequency.rateAmt * local.thisRateFrequency.numInstallments/>
													<cfset local.strReturn.subPrices['sub#arguments.subDefinitionStruct.subscriptionID#'] = local.thisRateFrequency.rateAmt>
												<cfelseif local.thisRateFrequency.frequencyName eq "Full">
													<cfset local.strReturn.totalFullPrice = local.thisRateFrequency.rateAmt />
													<cfset local.strReturn.subPrices['sub#arguments.subDefinitionStruct.subscriptionID#'] = local.strReturn.totalFullPrice>
												</cfif>
											</cfloop>
										</cfif>
										<cfset local.strReturn.strTax = createObject("component","model.system.platform.accounting").getTaxForUncommittedSale(saleGLAccountID=arguments.subDefinitionStruct.GLAccountID, saleAmount=local.strReturn.totalFullPrice, transactionDate=now(), stateIDForTax=val(local.qryMemberBillingAddress.stateID), zipForTax=local.qryMemberBillingAddress.postalCode)>
									</cfif>
								</cfloop>
							</span>
						</div>
					</div>
					<cfif structKeyExists(arguments.subDefinitionStruct, "addons")>
						<div class="subAddonsArrayWrapper" id="sub#arguments.subDefinitionStruct.subscriptionID#_addons">
							<cfloop array="#arguments.subDefinitionStruct.addons#" index="local.thisAddon">
								<div class="subAddonWrapper" style="margin-top: 10px;" id="sub#arguments.subDefinitionStruct.subscriptionID#_addonID#local.thisAddon.addonID#" data-pcpctoffeach="#local.thisAddon.PCPctOffEach#" data-pcnum="#local.thisAddon.PCnum#" data-addonid="#local.thisAddon.addOnID#" data-frontendaddadditional="#local.thisAddon.frontEndAddAdditional#" data-frontendallowchangeprice="#local.thisAddon.frontEndAllowChangePrice#" data-frontendallowselect="#local.thisAddon.frontEndAllowSelect#" data-maxallowed="#local.thisAddon.maxAllowed#" data-minallowed="#local.thisAddon.minAllowed#" data-setid="#local.thisAddon.setID#" data-setname="#local.thisAddon.setName#" data-setuid="#local.thisAddon.setUID#">
									<strong>#local.thisAddon.setName#</strong>
									<div class="addonMessageArea"></div>
									<cfset local.selectionFound = false>
									<cfset local.pcNumCounter = 1>
									<cfloop array="#local.thisAddon.subscriptions#" index="local.thisAddonSub">
										<cfset local.isFree = false>
										<cfif local.thisAddon.PCnum gte local.pcNumCounter>
											<cfset local.isFree = true>
										</cfif>
										<cfset local.thisAddonSubForm = getSubscriptionFormSelections(subDefinitionStruct=local.thisAddonSub,recursionLevel=arguments.recursionLevel+1,strData=arguments.strData, isFree=local.isFree, memberID=arguments.memberID)>
										<cfset local.strReturn.jsValidation = local.strReturn.jsValidation & chr(13) & chr(10) & local.thisAddonSubForm.jsValidation>
										<cfif len(trim(local.thisAddonSubForm.formContent))>
											<cfset local.selectionFound = true>
											<cfif local.pcNumCounter gt local.thisAddon.PCnum>
												<cfset local.strReturn.totalFullPrice = local.strReturn.totalFullPrice + local.thisAddonSubForm.totalFullPrice>
												<cfset StructAppend(local.strReturn.subPrices,local.thisAddonSubForm.subPrices)>
												<cfset local.strReturn.strTax.totalTaxAmt = local.strReturn.strTax.totalTaxAmt + local.thisAddonSubForm.strTax.totalTaxAmt>
											</cfif>
											<cfset local.pcNumCounter = local.pcNumCounter + 1>
											#local.thisAddonSubForm.formContent#
										</cfif>
									</cfloop>
									<cfif local.selectionFound eq false>
										No Selections Made
									</cfif>
								</div>
							</cfloop>
						</div>
					</cfif>
				</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="getSubscriptionStructFromForm" access="public" output="false" returntype="struct" hint="get substruct of user form selections">
		<cfargument name="subscriptionID" type="string" required="false">
		<cfargument name="memberID" type="numeric" required="false">
		<cfargument name="isRenewalRate" type="boolean" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.subDefinitionStruct = sub_getSubscriptionTreeStruct(
			subscriptionID = arguments.subscriptionID,
			memberID = arguments.memberID,
			isRenewalRate = arguments.isRenewalRate,
			siteID = arguments.siteID)>

		<cfreturn generateSubscriptionStructFromForm(subDefinitionStruct=local.subDefinitionStruct,rc=arguments.rc)>
	</cffunction>

	<cffunction name="generateSubscriptionStructFromForm" access="private" output="false" returntype="struct" hint="recursively generate substruct of user form selections">
		<cfargument name="subDefinitionStruct" type="struct" required="false">
		<cfargument name="rc" type="struct" required="false">
		<cfargument name="recursionLevel" type="numeric" required="false" default="1">

		<cfscript>
			var local = structNew();
			var objToDump = structNew();
			local.returnStruct = structNew();
			local.returnStruct.subscription = structNew();
			local.returnStruct.success = true;
			try {
				//test if susbcription was selected by user on form
				if (structKeyExists(arguments.rc, "sub#arguments.subDefinitionStruct.subscriptionID#") and structKeyExists(arguments.rc, "sub#arguments.subDefinitionStruct.subscriptionID#_rate")){
					local.returnStruct.subscription.uid = arguments.subDefinitionStruct.uid;

					//look through rates to find chosen rate
					local.selectedRateID = arguments.rc["sub#arguments.subDefinitionStruct.subscriptionID#_rate"];

					if (arguments.recursionLevel eq 1 and structKeyExists(arguments.rc, "sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"))
						local.returnStruct.subscription.freqUID = arguments.rc["sub#arguments.subDefinitionStruct.subscriptionID#_rateFrequencySelected"];


					for (local.rateIndex=1;local.rateIndex<=arrayLen(arguments.subDefinitionStruct.rateSchedule);local.rateIndex++) {
						if (arguments.subDefinitionStruct.rateSchedule[local.rateIndex].rateID eq local.selectedRateID) {
							local.returnStruct.subscription.rateUID = arguments.subDefinitionStruct.rateSchedule[local.rateIndex].uid;
							break;
						}
					}
					//check for price override for selected rate
					if (structKeyExists(arguments.rc, "sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.selectedRateID#") and len(arguments.rc["sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.selectedRateID#"])) {
						local.returnStruct.subscription.rateOverride = arguments.rc["sub#arguments.subDefinitionStruct.subscriptionID#_rateOverride#local.selectedRateID#"];
					}

					//process addons
					if (structKeyExists(arguments.subDefinitionStruct, "addons")) {
						local.returnStruct.subscription.children = arrayNew(1);

						for (local.addonIndex=1;local.addonIndex<=arrayLen(arguments.subDefinitionStruct.addons);local.addonIndex++) {
							for (local.addonSubIndex=1;local.addonSubIndex<=arrayLen(arguments.subDefinitionStruct.addons[local.addonIndex].subscriptions);local.addonSubIndex++) {
								//recursive call to generate struct for this addon
								local.thisAddonStruct = generateSubscriptionStructFromForm (subDefinitionStruct=arguments.subDefinitionStruct.addons[local.addonIndex].subscriptions[local.addonSubIndex],rc=arguments.rc,recursionLevel=recursionLevel+1);
								if (not local.thisAddonStruct.success) {
									//exit if addon generated a failure
									local.returnStruct.success = false;
									return local.returnStruct;
								} else if (structCount(local.thisAddonStruct.subscription)) {
									arrayAppend(local.returnStruct.subscription.children, local.thisAddonStruct.subscription);
								}
							}
						}
						//no need for children array if none were selected by user on form
						if (not arrayLen(local.returnStruct.subscription.children)) {
							structDelete(local.returnStruct.subscription,"children");
						}
					}
				}
			} catch (any e){
				objToDump.arguments = arguments;
				objToDump.local = local;
				application.objError.sendError(cfcatch=e, objectToDump=objToDump, customMessage="Error building subscription struct from form submission");
				local.returnStruct.success = false;
			}

			return local.returnStruct;
		</cfscript>
	</cffunction>

	<cffunction name="renderPaymentForm" access="public" output="false" returntype="struct">
		<cfargument name="arrPayMethods" type="array" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="title" type="string" required="true">
		<cfargument name="formName" type="string" required="true">
		<cfargument name="backStep" type="string" required="true">
		<cfargument name="paymentFeaturesAndChargeInfo" type="struct" required="false">

		<cfset var local = structNew()>
		<cfset local.strReturn = { headCode='', paymentHTML='' }>
		<cfset local.arrPayMethods = []>

		<cfloop from="1" to="#arrayLen(arguments.arrPayMethods)#" index="local.thisPayID">
			<cfset local.thisProfileCode = arguments.arrPayMethods[local.thisPayID]>
			<cfset local.payBtnText = "Continue">

			<cfset local.args = {
				"siteid"=arguments.siteID,
				"profilecode"=local.thisProfileCode,
				"pmid"=arguments.memberID,
				"showCOF"=arguments.memberID EQ session.cfcUser.memberData.memberID,
				"usePopup"=false,
				"usePopupDIVName"='mccfProfile#local.thisPayID#Form',
				"autoShowForm"=1
			}>
			<cfif arguments.keyExists("paymentFeaturesAndChargeInfo")>
				<cfset local.args["paymentFeatures"] = arguments.paymentFeaturesAndChargeInfo.paymentFeatures>
				<cfset local.args["chargeInfo"] = arguments.paymentFeaturesAndChargeInfo.chargeInfo>
				<cfset local.payBtnText = arguments.paymentFeaturesAndChargeInfo.keyExists("payBtnText") AND arguments.paymentFeaturesAndChargeInfo.payBtnText.keyExists(local.thisProfileCode) 
											? arguments.paymentFeaturesAndChargeInfo.payBtnText[local.thisProfileCode] 
											: "Continue">
			</cfif>
			
			<cfset local.arrPayMethods[local.thisPayID] = application.objPayments.showGatewayInputForm(argumentcollection=local.args)>

			<cfif len(local.arrPayMethods[local.thisPayID].headCode)>
				<cfsavecontent variable="local.strReturn.headCode">
					<cfoutput>
					#local.strReturn.headCode#
					#local.arrPayMethods[local.thisPayID].headCode#
					</cfoutput>
				</cfsavecontent>
			</cfif>

			<cfset local.arrPayMethods[local.thisPayID]['payBtnText'] = local.payBtnText>
		</cfloop>

		<cfset local.preventPayBtnTextUpdate = arguments.keyExists("paymentFeaturesAndChargeInfo") AND arguments.paymentFeaturesAndChargeInfo.keyExists("preventPayBtnTextUpdate") 
												? arguments.paymentFeaturesAndChargeInfo.preventPayBtnTextUpdate
												: false>

		<cfsavecontent variable="local.strReturn.headCode">
			<cfoutput>
			#local.strReturn.headCode#
			<script language="javascript">
				<cfif arrayLen(local.arrPayMethods) gt 1>
					function getPayMethod() {
						return ($('###arguments.formName# input[name="mccf_payMeth"]:checked').length)?$('###arguments.formName# input[name="mccf_payMeth"]:checked').val():'';
					}
					function showPaymentDetail(pid) {
						$('div.mccfpayinfodiv').hide();
						var payMeth = getPayMethod();
						if (payMeth != '') {
							$('div##mccfdiv_' + payMeth.replace(/\s/g, '')).css('overflow','unset');
							$('div##mccfdiv_' + payMeth.replace(/\s/g, '')).css('height','auto');
							$('div##mccfdiv_' + payMeth.replace(/\s/g, '')).show();
						}
						var tt = $('##mccfProfile'+pid+'TabTitle').text();
						$('###arguments.formName# input[name="mccf_payMethID"]').val(pid);
						$('###arguments.formName# input[name="mccf_payMethTitle"]').val(tt);
					}
				<cfelse>
					$('##mccfdiv_#local.arrPayMethods[1].profileCode#').removeAttr('style').show();
					function getPayMethod() {
						return $('###arguments.formName# input[name="mccf_payMeth"]').val();
					}
				</cfif>
				function mccf_validatePPForm() {
					var payMeth = getPayMethod();
					var thisForm = document.forms['#arguments.formName#'];
					var arrReq = new Array();
					if (payMeth == '') arrReq[arrReq.length] = 'Select a method of payment.';
					<cfloop array="#local.arrPayMethods#" index="local.thisPayMethod">
						<cfif len(local.thisPayMethod.jsvalidation)>
							if (payMeth == '#local.thisPayMethod.profileCode#') {
								#local.thisPayMethod.jsvalidation#
								
								if(!$('###arguments.formName# input[name=p_#local.thisPayMethod.profileID#_mppid]').is(':checked'))
									arrReq[arrReq.length] = 'There is no credit card selected. Please select a credit card.';						
							}
						</cfif>
					</cfloop>
					return arrReq;
				}
				$(document).ready(function () {
					$('##btnContinue').removeAttr('disabled');
					$("[name='#arguments.formName#']").submit(function () {
						$("[name='btnContinue']").attr("disabled", true);
						return true;
					});
					window.addEventListener('pageshow', function( e ){
						$("[name='btnContinue']").removeAttr("disabled");
					});			
				});
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfsavecontent variable="local.strReturn.paymentHTML">
			<cfoutput>
			<cfif arrayLen(local.arrPayMethods) gt 1>
				<div class="tsAppSectionHeading" id="paymentMethodContainer">#arguments.title#</div>
				<div class="tsAppSectionContentContainer">
					<table cellpadding="3" border="0" cellspacing="0">
					<tr>
						<td colspan="2" class="tsAppBodyText">Select your method of payment from the following options:</td>
					</tr>
					<cfloop from="1" to="#arrayLen(local.arrPayMethods)#" index="local.thisPayMethodInd">
						<tr>
							<td class="tsAppBodyText" width="30">
								<input value="#local.arrPayMethods[local.thisPayMethodInd].profileCode#" name="mccf_payMeth" id="mccf_payMeth_#local.thisPayMethodInd#" type="radio" onClick="showPaymentDetail(#local.arrPayMethods[local.thisPayMethodInd].profileID#);">
							</td>
							<td class="tsAppBodyText" id="mccfProfile#local.arrPayMethods[local.thisPayMethodInd].profileID#TabTitle">#local.arrPayMethods[local.thisPayMethodInd].tabTitle#</td>
						</tr>
					</cfloop>
					</table>
				</div>
				<input type="hidden" name="mccf_payMethTitle" id="mccf_payMethTitle" value="">
				<input type="hidden" name="mccf_payMethID" id="mccf_payMethID" value="">
			<cfelseif arrayLen(local.arrPayMethods) is 1>
				<input type="hidden" name="mccf_payMethTitle" id="mccf_payMethTitle" value="#local.arrPayMethods[1].tabTitle#">
				<input type="hidden" name="mccf_payMeth" id="mccf_payMeth" value="#local.arrPayMethods[1].profileCode#">
				<input type="hidden" name="mccf_payMethID" id="mccf_payMethID" value="#local.arrPayMethods[1].profileID#">
			</cfif>

			<cfloop from="1" to="#arrayLen(local.arrPayMethods)#" index="local.thisPayID">
				<cfset local.thisPayMethod = local.arrPayMethods[local.thisPayID]>
				<div id="mccfdiv_#Replace(local.thisPayMethod.profileCode, " ", "", "ALL")#" class="mccfpayinfodiv" style="height:0px;overflow:scroll;">
					<div class="tsAppSectionHeading">#local.thisPayMethod.tabTitle#</div>
					<div class="tsAppSectionContentContainer">
						<div id="mccfProfile#local.thisPayID#Form">
							<div>#local.thisPayMethod.inputForm#</div>
							<div>
								<button name="btnContinue" type="submit" class="tsAppBodyButton" onClick="hideAlert();"<cfif local.preventPayBtnTextUpdate> data-prevent-text-update="true"</cfif>>#local.thisPayMethod.payBtnText#</button>
								<button name="btnBack" type="button" style="float:right;" class="tsAppBodyButton" onClick="hideAlert();mc_goBackForm($('###arguments.formName#'),'#arguments.backStep#');">&lt;&lt; Back</button>
							</div>
						</div>
					</div>
				</div>
			</cfloop>

			</cfoutput>
		</cfsavecontent>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="sendConfirmationEmail" access="public" output="false" returntype="struct">
		<cfargument name="sitecode" type="string" required="true">
		<cfargument name="strEmail" type="struct" required="true">
		<cfargument name="emailTitle" type="string" required="true">
		<cfargument name="emailContent" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { success=false, err="" }>
		
		<cfif len(arguments.strEmail.TO)>
			<cfscript>
				local.arrEmailTo = [];
				local.strEmail.to = replace(arguments.strEmail.TO,",",";","all");
				local.toEmailArr = listToArray(local.strEmail.to,';');
				for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
					local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
				}
			</cfscript>
			<cfset local.strReturn = application.objEmailWrapper.sendMailESQ(
				emailfrom={ name="", email=arguments.strEmail.FROM },
				emailto=local.arrEmailTo,
				emailreplyto=arguments.strEmail.FROM,
				emailsubject=arguments.strEmail.SUBJECT,
				emailtitle=arguments.emailTitle,
				emailhtmlcontent=arguments.emailContent,
				siteID=application.objSiteInfo.getSiteInfo(arguments.siteCode).siteID,
				memberID=val(application.objSiteInfo.getSiteInfo(arguments.siteCode).sysMemberID),
				messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
				sendingSiteResourceID=application.objSiteInfo.getSiteInfo(arguments.siteCode).siteSiteResourceID
			)>
		</cfif>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="createSessionStructure" access="public" output="false" returntype="struct">
		<cfargument name="rc" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStr = structNew()>

		<cftry>
			<cfloop collection="#arguments.rc#" item="local.key">
				<cfif structKeyExists(arguments.rc, "fieldNames")  and listFindNoCase(arguments.rc.fieldNames,local.key) 
					and structKeyExists(arguments.rc, "doNotIncludeList") and NOT listFindNoCase(arguments.rc.doNotIncludeList,local.key)>
					<cfset local.returnStr[local.key] = arguments.rc[local.key]>
				</cfif>
			</cfloop>
		<cfcatch type="Any">
			<cfset local.returnStr = {}>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStr>
	</cffunction>

	<cffunction name="upperFirst" access="public" returntype="string" output="false" hint="I convert the first letter of a string to upper case, while leaving the rest of the string alone.">
		<cfargument name="name" type="string" required="true">
		<cfreturn uCase(left(arguments.name,1)) & right(arguments.name,len(arguments.name)-1)>
	</cffunction>	
	
	<cffunction name="sortArrayOfStructs" access="private" output="false" returntype="array" >
		<cfargument name="arrData" type="array" required="true">
		<cfargument name="key" type="string" required="true" >
		
		<cfscript>
			local.sortOrder = "asc";        
			local.sortType = "textnocase";
			local.delim = ".";
			local.sortArray = arraynew(1);
			local.returnArray = arraynew(1);
			local.count = arrayLen(arguments.arrData);
			
			for(local.index = 1; local.index lte local.count; local.index = local.index + 1){
				local.sortArray[local.index] = arguments.arrData[local.index][arguments.key] & local.delim &  local.index;
			}
			arraySort(local.sortArray,local.sortType,local.sortOrder);
			
			for(local.index = 1; local.index lte local.count; local.index = local.index + 1){
				local.returnArray[local.index] = arguments.arrData[listLast(local.sortArray[local.index],local.delim)];
			}
		</cfscript>
		
		<cfreturn local.returnArray>
	</cffunction>

	<cffunction name="getRSSFeedCategories" returntype="query" output="false" access="public">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var qryCategories = "">

		<cfquery name="qryCategories" datasource="#application.dsn.customApps.dsn#">
			SET NOCOUNT ON;

			DECLARE @siteID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">;

			SELECT rssCategoryID, rssCategoryDescription
			FROM dbo.rss_categories
			WHERE siteID = @siteID
			ORDER BY sortOrder;
		</cfquery>

		<cfreturn qryCategories>
	</cffunction>

	<cffunction name="getRSSEntriesBySite" returntype="query" output="false" access="public">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var qryEntries = "">

		<cfquery name="qryEntries" datasource="#application.dsn.customApps.dsn#">
			SET NOCOUNT ON;

			DECLARE @siteID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">;

			SELECT c.rssCategoryID, c.rssCategoryDescription, f.rssID, f.rssDescription, e.pubDate, e.link, e.title, e.shortDesc
			FROM dbo.rss_categories as c
			INNER JOIN dbo.rss_feeds as f on f.rssCategoryID = c.rssCategoryID
			INNER JOIN dbo.rss_entries as e on e.rssID = f.rssID
			WHERE c.siteID = @siteID
			ORDER BY c.sortOrder, f.sortOrder, e.pubDate DESC;
		</cfquery>

		<cfreturn qryEntries>
	</cffunction>

	<cffunction name="getMessageTypeID" access="public" returnType="numeric" output="no">		
		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.platformMail.dsn#" name="local.qryMessageTypeID">
			select messageTypeID
			from dbo.email_messageTypes 
			where messageTypeCode =  'CUSTOMFORM';
		</cfquery>
		
		<cfreturn val(local.qryMessageTypeID.messageTypeID)>
	</cffunction> 

	<cffunction name="displayCaptcha" access="public" returntype="struct" output="false" hint="I display the captcha and an error message, if appropriate">
		<cfargument name="length" type="numeric" default="4" />
		<cfargument name="width" type="string" default="200" hint="width of captcha image in pixels" />
		<cfargument name="height" type="string" default="50" hint="height of captcha image in pixels" />
		<cfargument name="fonts" type="string" default="verdana,arial,times new roman,courier" hint="fonts to use for characters in captcha image" />
		<cfargument name="message" type="string" default="Please enter the correct code shown in the graphic." hint="Message to display below captcha if validate method failed.">
		<cfargument name="formname" required="true" type="string">
		<cfset var local = structNew()>
		<cfset var local.returnStruct = structNew() />

		<cfsavecontent variable="local.returnStruct.htmlContent">
		<cfoutput>
			<div id="captcha-wrapper"></div>
			</cfoutput>
		</cfsavecontent>
		<cfsavecontent variable="local.jscode">
			<cfoutput>
			<script>
				var er_change = function(r) {
					if (r.response && r.response == 'success') {
						$('##captcha-wrapper').html(r.htmlcontent);
					} else {
						$('##captcha-wrapper').html('Could not load captcha. Try again later!');
					}
				};
				function showCaptcha(){
					var objParams = { formname:'#arguments.formname#',length:#arguments.length#,width:#arguments.width#,height:#arguments.height#,fonts:'#arguments.fonts#'};
					TS_AJX('CUSTOM_FORM_UTILITIES','showCaptcha',objParams,er_change,er_change,30000,er_change);
				}
				function readWord(word) {
					if ('speechSynthesis' in window && 'SpeechSynthesisUtterance' in window) {
						let delay = 0;
						for (let i = 0; i < word.length; i++) {
							const letter = word[i];
							const utterance = new SpeechSynthesisUtterance(letter);
							utterance.rate = 0.6;
							setTimeout(() => {
								speechSynthesis.speak(utterance);
							}, delay);
							delay += 100;
						}
					} else {
						console.log("Speech synthesis is not supported in this browser.");
					}
				}
			</script>	
			</cfoutput>
		</cfsavecontent>
		<cfsavecontent variable="local.returnStruct.jsvalidationcode">
			<cfoutput>
				var objParams = { code:$('##captcha').val(),captcha:$('##captcha_check').val()};
				TS_AJX_SYNC('CUSTOM_FORM_UTILITIES','validateCaptcha',objParams,captcha_callback,captcha_callback,30000,captcha_callback);
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.jscode#">
		<cfreturn local.returnStruct />
	</cffunction>

	<cffunction name="showCaptcha" access="public" returntype="struct" output="false" hint="I display the captcha and an error message, if appropriate">
		<cfargument name="length" type="numeric" default="4" />
		<cfargument name="text" type="string" default="c#randRange(100,1000)#R" />
		<cfargument name="width" type="string" default="200" hint="width of captcha image in pixels" />
		<cfargument name="height" type="string" default="50" hint="height of captcha image in pixels" />
		<cfargument name="fonts" type="string" default="verdana,arial,times new roman,courier" hint="fonts to use for characters in captcha image" />
		<cfargument name="message" type="string" default="Please enter the correct code shown in the graphic." hint="Message to display below captcha if validate method failed.">
		<cfargument name="formname" required="true" type="string">

		<cfset var local = structNew()>
		<cfset var local.returnStruct = structNew() />
		<cfset local.validateCaptchaURL = CreateObject("component","model.admin.admin").buildLinkToTool(toolType='captcha',mca_ta='validateCaptcha')>
		
		<cfset setCaptchaCode(arguments.text) />
		<cfset local.returnStruct.response = 'fail'>
		<cfsavecontent variable="local.returnStruct.htmlContent">
		<cfoutput>
		
			<div class="tsAppBodyText">
				<table width="100%" style="text-align:center;">
					<tr><td><div>Please enter the code shown in the graphic.</div></td></tr>
					<tr><td>
						<input type="hidden" name="captcha_check" id="captcha_check" value="#encrypt(lcase(arguments.text),"TRiaL_SMiTH", "CFMX_COMPAT", "Hex")#">
						<cfimage action="captcha" 
								text="#arguments.text#"
								width="#arguments.width#" 
								height="#arguments.height#"
								fonts="#arguments.fonts#"  
								difficulty="medium" base64="true"/>
					</td></tr>
					<tr><td style="padding: 5px;">
					<a href="javascript:showCaptcha();" name="captcha_reload" id="captcha_reload">Reload</a>&nbsp;
					<a href="javascript:readWord('#arguments.text#');" id="captcha_speaker"><img src="/assets/common/images/speaker-icon.png" width="16" alt="Speaker Icon"/></a>
					</td></tr>
					<tr><td><input type="text" name="captcha" id="captcha" value="" autocomplete="off"></td></tr>

					<tr><td><span class="cb_captchamessage" style="color:red;font-family:Arial,san-serif;"></span></td></tr>
				</table>		
			</div>
			</cfoutput>
		</cfsavecontent>
		
		<cfset local.returnStruct.response = 'success'>
		<!--- after it's decided whether to display the error message,
		clear the validation flag in case user just navigates to another page and comes back --->
		<cfset setValidated(true) />
		<cfreturn local.returnStruct />
	</cffunction>

	<cffunction name="validateCaptcha" access="public" returntype="struct" output="false" hint="I validate the passed in string against the captcha code">
		<cfargument name="code" type="string" required="true" />
		<cfargument name="captcha" type="string" required="true" />
		<cfset var local = structNew()>
		<cfset local.responseStruct = structNew()>
		<cfset local.responseStruct.response = 'fail'>
		<cfset local.encryptedCode = encrypt(lcase(arguments.code),"TRiaL_SMiTH", "CFMX_COMPAT", "Hex")>
		<cfif local.encryptedCode eq arguments.captcha>
			<cfset clearCaptcha() /><!--- delete the captcha struct --->
			<cfset local.responseStruct.response = 'success'>
			<cfreturn local.responseStruct />
		<cfelse>
			<cfset setValidated(false) />
			<!---<cfset isValidated() /> do not remove. For future expansion--->
			<cfreturn local.responseStruct />
		</cfif>
	</cffunction>

	<cffunction name="getCaptchaStorage" access="private" returntype="any" output="false">		
		<cfset var local = structNew()>
		<cfset local.captcha = {captchaCode = "", validated = true}>

		<cfif NOT application.mcCacheManager.sessionValueExists(keyname='cb_captcha')>
			<cfset local.cb_captcha = application.mcCacheManager.sessionSetValue(keyname='cb_captcha', value=local.captcha)>
		</cfif>

		<cfreturn application.mcCacheManager.sessionGetValue(keyname='cb_captcha')>
	</cffunction>

	<cffunction name="setCaptchaCode" access="public" returntype="void" output="false">
		<cfargument name="captchastring" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.captcha = {captchaCode = encrypt(lcase(arguments.captchastring),"TRiaL_SMiTH", "CFMX_COMPAT", "Hex"), validated = true}>
		
		<cfset application.mcCacheManager.sessionSetValue(keyname='cb_captcha', value=local.captcha)>
	</cffunction>
	
	<cffunction name="getCaptchaCode" access="public" returntype="string" output="false">
		<cfreturn getCaptchaStorage().captchaCode>
	</cffunction>
	
	<cffunction name="setValidated" access="public" returntype="void" output="false">
		<cfargument name="validated" type="boolean" required="true" />

		<cfset var local = structNew()>

		<cfif application.mcCacheManager.sessionValueExists('cb_captcha')>
			<cfset local.cb_captcha = application.mcCacheManager.sessionGetValue('cb_captcha','')>
			<cfset local.cb_captcha.validated = arguments.validated>
			<cfset application.mcCacheManager.sessionSetValue(keyname='cb_captcha', value=local.cb_captcha)>
		</cfif>		
	</cffunction>
	
	<cffunction name="isValidated" access="public" returntype="boolean" output="false">
		<cfreturn getCaptchaStorage().validated>
	</cffunction>
	
	<cffunction name="clearCaptcha" access="public" returntype="void" output="false">
		<cfset application.mcCacheManager.sessionDeleteValue(keyname="cb_captcha")>
	</cffunction>
	
	<cffunction name="saveMemberCustomSingleDocument" access="public" output="false" returntype="boolean">
		<cfargument name="cid" type="string" required="true">
		<cfargument name="memberid" type="numeric" required="true">	
		
		<cfset var local = structNew()>
		<cfset local.columnID = arguments.cid>
		<cfset local.newVal = form['md_'&'#local.columnID#']>
		<cfif len(local.newVal)>
			<cfset local.fileUploaded = true>
			<cfset local.objDocument = CreateObject("component","model.system.platform.document")>
			<cfset local.objSection = CreateObject("component","model.system.platform.section")>
			<cfset local.orgDefaultSiteID = application.objOrgInfo.getOrgDefaultSiteID(orgID=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgid)>
			<cfset local.orgDefaultSiteCode = application.objSiteInfo.getSiteCodeFromSiteID(siteID=local.orgDefaultSiteID)>
			<cfset local.memberAdminSiteResourceID = application.objSiteInfo.mc_siteInfo[local.orgDefaultSiteCode].memberAdminSiteResourceID>
			<cfset local.oldVal = "">

			<cftry>
				<cfset local.newFile = local.objDocument.uploadFile('md_#local.columnID#')>				
				<cfcatch type="any">
					<cfset local.fileUploaded = false>
				</cfcatch> 
			</cftry>
			<cfif local.fileUploaded>
				<cfset local.objDocument.forceFileExtentionIfBlank(local.newFile)>
				<cfset local.uploadedFileName = local.newFile.clientFile>
				<cfset local.uploadedFileExt = local.newFile.clientFileExt>

				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryGetValueID" result="local.qryGetValueIDResult">
					select mdcv.columnValueSiteResourceID as valueID
					from dbo.ams_memberData as md
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
					inner join dbo.ams_memberDataColumns mdc on	mdc.columnID = mdcv.columnID
					inner join dbo.ams_memberDataColumnDataTypes as mdt on mdt.dataTypeID = mdc.dataTypeID
					where mdt.dataTypeCode = 'DOCUMENTOBJ'
					and md.memberid = <cfqueryparam value="#arguments.memberid#" cfsqltype="CF_SQL_INTEGER">
					and mdcv.columnID = <cfqueryparam value="#local.columnID#" cfsqltype="CF_SQL_INTEGER">
				</cfquery>

				<cfif local.qryGetValueID.recordCount>
					<cfset local.oldVal = local.qryGetValueID.valueID/>
				</cfif>
				
				<!--- if there's an existing document, insert new version --->
				<cfif val(local.oldVal) gt 0>
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.getDocumentLanguageID">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						SELECT dbo.fn_getDocumentLanguageIDFromSiteResourceID(<cfqueryparam value="#val(local.oldVal)#" cfsqltype="CF_SQL_INTEGER">,1) as documentLanguageID;

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>

					<cfset local.documentVersionID = local.objDocument.insertVersion(orgcode=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgcode, 
								sitecode=local.orgDefaultSiteCode, fileData=local.newFile, documentLanguageID=local.getDocumentLanguageID.documentLanguageID,
								fileData=local.newFile, author='', contributorMemberID=arguments.memberid, recordedByMemberID=arguments.memberid, oldFileExt=local.uploadedFileExt)>

				<!--- otherwise add new document --->
				<cfelse>
					<cfset local.rootSectionID = local.objSection.getSectionFromSectionCode(siteID=local.orgDefaultSiteID, sectionCode="MCAMSMemberDocuments").sectionID>
					<cfset local.insertResults = local.objDocument.insertDocument(siteID=local.orgDefaultSiteID, 
								resourceType='ApplicationCreatedDocument', parentSiteResourceID=local.memberAdminSiteResourceID,
								sectionID=local.rootSectionID, docTitle='', docDesc='', author='', fileData=local.newFile, isVisible=false, 
								contributorMemberID=arguments.memberid, recordedByMemberID=arguments.memberid, fromCustomFields=true, oldFileExt=local.uploadedFileExt)>

					<!--- get column name from columnID, since custom tab uses fieldset column names --->
					<cfquery name="local.qryColumn" datasource="#application.dsn.membercentral.dsn#">
						select mdc.columnName
						from dbo.ams_memberDataColumns as mdc
						inner join dbo.ams_memberDataColumnDataTypes as mdt on mdt.dataTypeID = mdc.dataTypeID
						where mdc.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgid#">
						and mdc.columnID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.columnID#">
						and mdt.dataTypeCode = 'DOCUMENTOBJ'
					</cfquery>

					<!--- using setMemberData here since PMI does not support DOCUMENTOBJ --->
					<cfstoredproc procedure="ams_setMemberData" datasource="#application.dsn.membercentral.dsn#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.memberid#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgid#">
						<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.qryColumn.columnName#">
						<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.insertResults.documentSiteResourceID#">
						<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
					</cfstoredproc>
				</cfif>									
			</cfif>
		</cfif>
		<cfreturn local.fileUploaded>
	</cffunction>

	<cffunction name="getGroups" access="public" output="false" returntype="query">
		<cfargument name="groupUID" type="string" required="true" hint="can be a list of UIDs">
		<cfargument name="orgID" type="numeric" required="true">
	
		<cfset var local = structNew()>

		<cfquery name="local.qryGroups" datasource="#application.dsn.membercentral.dsn#">
			select g.groupID, g.groupName, g.uid, g.groupCode 
			from dbo.ams_groups g
			where g.uid in (<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.groupUID#" list="true">)
			and g.orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orgID#">
		</cfquery>
	
		<cfreturn local.qryGroups>
	</cffunction>
	
	<cffunction name="getOrgMemberFields" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="true">
	
		<cfset var local = structNew()>

		<cfquery name="local.qryOrgMemberFields" datasource="#application.dsn.membercentral.dsn#">
			select o.hasPrefix, o.usePrefixList, o.hasMiddleName, o.hasSuffix, o.hasProfessionalSuffix
			from dbo.organizations as o
			where o.orgID = <cfqueryparam value="#arguments.orgID#" cfsqltype="cf_sql_integer">			
		</cfquery>
	
		<cfreturn local.qryOrgMemberFields>
	</cffunction>

	<cffunction name="getCustomFieldData" access="public" output="false" returntype="Query">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="columnName" type="string" required="true">
		<cfargument name="columnValueString" type="string" required="false">
		<cfargument name="columnValueList" type="string" required="false">
		
		<cfset var local = structNew()>
		
		<cfquery name="local.getColumnValues" datasource="#application.dsn.membercentral.dsn#">
			select mdcv.valueID, mdcv.columnID, mdcv.columnValueString
			from dbo.ams_memberDataColumns mdc
			inner join dbo.ams_memberdatacolumnvalues mdcv on mdcv.columnID = mdc.columnID and mdc.columnName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.columnName#">
			<cfif isDefined("arguments.columnValueString") and len(trim(arguments.columnValueString))>
				and mdcv.columnValueString = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.columnValueString#">
			</cfif>
			<cfif isDefined("arguments.columnValueList") and len(trim(arguments.columnValueList))>
				and mdcv.columnValueString in (<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.columnValueList#" list="yes">)
			</cfif>
			where mdc.orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orgID#">
			order by mdcv.columnValueString;
		</cfquery>

		<cfreturn local.getColumnValues>
	</cffunction>

	<cffunction name="getMembershipByStatus" access="public" output="false" returntype="query">
		<cfargument name="statusCodeList" type="string" required="true">
    	<cfargument name="subUID" type="string" required="false" default="">
		<cfargument name="subscriptionID" type="string" required="false" default="">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="orderby" type="string" required="false" default="status">
		<cfargument name="orderdir" type="string" required="false" default="desc">

		<cfset var local = structNew()>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubscriber">
			select distinct sb.subscriberID, sb.subscriptionID, f.frequencyShortName,sb.subEndDate, sb.subStartDate, sb.graceEndDate, st.statusCode, sb.GLAccountID, s.uid
			from dbo.sub_subscribers as sb
			inner join dbo.sub_statuses as st on st.statusID = sb.statusID 
				AND st.statusCode in (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.statusCodeList#" list="true">)
			inner join dbo.sub_subscriptions as s on s.subscriptionID = sb.subscriptionID
			inner join dbo.sub_types as t on t.typeID = s.typeID AND t.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
            inner join dbo.sub_rateSchedules as sch on sch.scheduleID = s.scheduleID 
            inner join dbo.sub_rates r on r.scheduleID = sch.scheduleID
            inner join dbo.sub_rateFrequencies rf on rf.rateID = r.rateID and rf.rfid = sb.rfid 
            inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID  
			inner join dbo.ams_members as m on m.memberID = sb.memberID and m.activeMemberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">
	    where 
			<cfif listLen(subscriptionID)>
				sb.parentSubscriberID is not null and sb.subscriptionID in (<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.subscriptionID#" list="true">)
			<cfelse>
				sb.parentSubscriberID is null <cfif len(arguments.subUID)>and s.uid in (<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.subUID#" list="true">)</cfif>
			</cfif>
			order by
			<cfif arguments.orderby eq "enddate">
				sb.subEndDate
			<cfelse>
				st.statusCode
			</cfif>
			#arguments.orderdir#
		</cfquery>

		<cfreturn local.qrySubscriber>
	</cffunction>
	
	<cffunction name="getSubscriberExpiredFromParentSubscriptions" access="public" output="false" returntype="query">
		<cfargument name="memberID" type="numeric" required="true">
        <cfargument name="parentSubscriberID" type="string" required="true">

		<cfset var qrySubscriberExpired = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qrySubscriberExpired">
			select distinct ssbq.subscriberID, ssbq.subscriptionID 
			from dbo.sub_subscribers ssbq
			inner join dbo.sub_subscriptions sspq on sspq.subscriptionID = ssbq.subscriptionID
			inner join dbo.sub_statuses sttu on sttu.statusID = ssbq.statusID and sttu.statusCode='E'
			where ssbq.memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">
			and ssbq.parentSubscriberID in (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.parentSubscriberID#" list="true">)
		</cfquery>
        
		<cfreturn qrySubscriberExpired>
	</cffunction>

	<cffunction name="getSubscriberFromParentSubscriptions" access="public" output="false" returntype="query">
		<cfargument name="memberID" type="numeric" required="true">
        <cfargument name="parentStatusCodeList" type="string" required="true">
        <cfargument name="subTypeUID" type="string" required="false" default="">
		<cfargument name="subSetUID" type="string" required="false" default="">
        <cfargument name="subUID" type="string" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="subscriberIDList" type="string" required="false" default="">

		<cfset var qryCurrentSections = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryCurrentSections">
			select sb.subscriberID, sttu.statusCode,stp.uid as addonuid 
			from dbo.sub_subscriptionSets st 
			inner join dbo.sub_sets s2 on st.setId = s2.setID 
			inner join dbo.sub_subscriptions s on s.subscriptionID = st.subscriptionID 
			inner join (
				select distinct sspq.uid 
				from dbo.sub_subscribers ssbq 
				inner join dbo.sub_subscriptions sspq on sspq.subscriptionID = ssbq.subscriptionID 
				where ssbq.subscriberID in (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.subscriberIDList#" list="true">)
			) scr on scr.uid = s.uid
			inner join dbo.sub_Types stp on stp.typeID = s.typeID
			inner join dbo.sub_rateSchedules as sch on sch.scheduleID = s.scheduleID
			inner join dbo.sub_rates r on r.scheduleID = sch.scheduleID
			inner join dbo.sub_rateFrequencies rf on rf.rateID = r.rateID
			inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID           
			inner join dbo.sub_subscribers sb on sb.subscriptionID = s.subscriptionID and sb.rfid = rf.rfid
			inner join dbo.ams_members m on m.memberID = sb.memberID and m.activeMemberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">
			inner join dbo.sub_statuses stt on stt.statusID = sb.statusID 
			inner join dbo.sub_subscribers ssb on ssb.subscriberID  = sb.parentSubscriberID 
			inner join dbo.sub_subscriptions ssp on ssp.subscriptionID = ssb.subscriptionID 
			inner join dbo.sub_statuses sttu on sttu.statusID = ssb.statusID and sttu.statusCode in (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.parentStatusCodeList#" list="true">)        
			where s2.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
			and ssp.uid = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.subUID#">
			<cfif listlen(arguments.subTypeUID)>
                and stp.uid in (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.subTypeUID#" list="true">)
			</cfif>
			<cfif listlen(arguments.subSetUID)>
                and s2.uid in (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.subSetUID#" list="true">)
			</cfif>
		</cfquery>
        
		<cfreturn qryCurrentSections>
	</cffunction>

    <cffunction name="getCurrentSubscriptions" access="public" output="false" returntype="query">
		<cfargument name="memberID" type="numeric" required="true">
        <cfargument name="subsToExpire" type="string" required="false" default="">		
        <cfargument name="subTypeUID" type="string" required="false" default="">
		<cfargument name="subSetUID" type="string" required="false" default="">
        <cfargument name="subUID" type="string" required="false" default="">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCurrentSections">
			select sttu.statusCode as parentStatusCode,stt.statusCode as childStatusCode, sb.parentSubscriberID,sb.subscriberID, r.rateID, r.rateName, s.uid, stp.uid as addonuid,
            rf.rateAmt, s.subscriptionName, s.subscriptionID, rf.rfid, r.frontEndAllowChangePrice, cast(s.subscriptionID as varchar(10)) + '_' + cast(r.rateID as varchar(10)) as subIDrateID, s2.uid as setUID
            from dbo.sub_subscriptionSets st
            inner join dbo.sub_sets s2 on st.setId = s2.setID
            inner join dbo.sub_subscriptions s on s.subscriptionID = st.subscriptionID
            inner join dbo.sub_subscribers sb on sb.subscriptionID = s.subscriptionID 
            inner join dbo.sub_rateFrequencies rf on rf.rfid = sb.RFID 
            inner join dbo.sub_rates r on r.rateID = rf.rateID
            inner join dbo.sub_Types stp on stp.typeID = s.typeID
            inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID
            inner join dbo.ams_members m on m.memberID = sb.memberID and m.activeMemberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">
            inner join dbo.sub_statuses stt on stt.statusID = sb.statusID and stt.statusCode in ('A','P','R','O')
            inner join  dbo.sub_subscribers ssb on ssb.subscriberID  = sb.parentSubscriberID 
            inner join dbo.sub_subscriptions ssp on ssp.subscriptionID = ssb.subscriptionID 
            inner join dbo.sub_statuses sttu on sttu.statusID = ssb.statusID and sttu.statusCode in ('A','P','R','O')        
            where 
                s2.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#"> 
			<cfif listlen(arguments.subTypeUID)>
                and stp.uid in (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.subTypeUID#" list="true">)
			</cfif>
			<cfif listlen(arguments.subSetUID)>
                and s2.uid in (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.subSetUID#" list="true">)
			</cfif>
            <cfif listlen(arguments.subUID)>
				and ssp.uid in (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.subUID#" list="true">)
			</cfif>
            <cfif listlen(arguments.subsToExpire)>
				and sb.subscriberID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subsToExpire#" list="true">)
			</cfif>
            order by subscriptionName, subscriptionID, rateName
		</cfquery>

        <cfquery name="local.qryGetSubActive" dbtype="query">
            select * from [local].qryCurrentSections where parentStatusCode = 'A' 
        </cfquery>

        <cfquery name="local.qryGetSubAccepted" dbtype="query">
            select * from [local].qryCurrentSections where parentStatusCode = 'P' 
        </cfquery>

        <cfquery name="local.qryGetSubBilled" dbtype="query">
            select * from [local].qryCurrentSections where parentStatusCode in ('R','O') 
        </cfquery>

        <cfif local.qryGetSubBilled.recordcount>
            <cfset local.qryCurrentSections = local.qryGetSubActive/>
        <cfelseif local.qryGetSubAccepted.recordcount>
            <cfset local.qryCurrentSections = local.qryGetSubAccepted/>
        </cfif>

		<cfreturn local.qryCurrentSections>
	</cffunction>

    <cffunction name="getAvailableSections" access="public" output="false" returntype="query">
        <cfargument name="memberID" type="numeric" required="true">		
		<cfargument name="subTypeUID" type="string" required="false" default="">
		<cfargument name="subSetUID" type="string" required="false" default="">		
		<cfargument name="isSubParent" type="boolean" required="false" default="false">
		<cfargument name="subParentUID" type="string" required="false" default="">
		<cfargument name="siteID" type="numeric" required="true">
        <cfargument name="existingSubsList" type="string" required="false" default="">
        <cfargument name="newSubs" type="string" required="false" default="">

		<cfset var local = structNew()>
		<cfset local.qualifySubRateRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SubscriptionRate", functionName="qualify")>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAvailableSections" result="local.qryAvailableSectionsResult">
			SET NOCOUNT ON;

			declare @FID int, @memberid int, @siteID int;
			SET @FID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qualifySubRateRFID#">;
			SET @memberid = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">;
			SET @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">;

			select distinct r.rateID, r.rateName, r.frontEndAllowChangePrice, s.uid, rf.rateAmt, s.subscriptionName, s.subscriptionID, rf.rfid, stp.uid as addonuid,
				cast(s.subscriptionID as varchar(10)) + '_' + cast(r.rateID as varchar(10)) as subIDrateID, s2.uid as setUID
				<cfif arguments.isSubParent>
					, ao.subscriptionID as parentSubscriptionID
				</cfif>
			from dbo.sub_subscriptionSets st
			inner join dbo.sub_sets s2 on st.setId = s2.setID
			inner join dbo.sub_subscriptions s on s.subscriptionID = st.subscriptionID and s.status = 'A'
			inner join dbo.sub_Types stp on stp.typeID = s.typeID and stp.status = 'A'
			inner join dbo.sub_rateSchedules as sch on sch.scheduleID = s.scheduleID and sch.status = 'A'
			inner join dbo.sub_rates r on r.scheduleID = sch.scheduleID and r.status = 'A' and r.isRenewalRate = 0 and getdate() between r.rateAFStartDate and dateadd(day, datediff(day, 0, r.rateAFEndDate)+1, 0)
			inner join dbo.sub_rateFrequencies rf on rf.rateID = r.rateID and rf.status = 'A' and rf.allowfrontend = 1 and r.isRenewalRate = 0
			inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID and f.frequencyShortName = 'F' and f.status = 'A'
			inner join dbo.cache_perms_siteResourceFunctionRightPrints srfrp ON srfrp.siteID = @siteID
				AND srfrp.siteResourceID = r.siteResourceID 
				and srfrp.functionID = @FID
			inner join dbo.cache_perms_groupPrintsRightPrints gprp on gprp.siteID = @siteID
				AND srfrp.rightPrintID = gprp.rightPrintID
			inner join dbo.ams_members m on m.memberID = @memberID
				AND m.groupPrintID = gprp.groupPrintID
			<cfif arguments.isSubParent>
				left join dbo.sub_addons ao  
				inner join dbo.sub_subscriptions ss on ss.subscriptionID = ao.subscriptionID 
				on ao.childSetID = s2.setID
			</cfif>
			where s2.siteID = @siteID
			<cfif listlen(arguments.subTypeUID)>
			and stp.uid in (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.subTypeUID#" list="true">)
			</cfif>
			<cfif listlen(arguments.subSetUID)>
			and s2.uid in (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.subSetUID#" list="true">)
			</cfif>
			<cfif listlen(arguments.existingSubsList)>
			and s.subscriptionID not in (<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.existingSubsList#" list="true">)
			</cfif>
			<cfif listlen(arguments.newSubs)>
			and cast(s.subscriptionID as varchar(10)) + '_' + cast(r.rateID as varchar(10)) in (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.newSubs#" list="true">)
			</cfif>
			<cfif arguments.isSubParent AND listlen(arguments.subParentUID)>
			and ss.uid in (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.subParentUID#" list="true">)
			</cfif>
			order by subscriptionName, subscriptionID, rateName;
		</cfquery>

		<cfreturn local.qryAvailableSections>
	</cffunction>
	
	<cffunction name="getAddonsForSub" access="public" output="false" returntype="query">
        <cfargument name="subID" type="string" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAddons">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select ao.addonID, ao.subscriptionID, ao.childSetID, ao.orderNum, ao.minAllowed, ao.maxAllowed, ao.useAcctCodeInSet, ao.useTermEndDateInSet, 
				ao.PCnum, ao.PCPctOffEach, ao.frontEndAllowSelect, ao.frontEndAllowChangePrice, sets.setName, sets.uid as setUID, sets.status
			from dbo.sub_addons ao 
			inner join dbo.sub_sets sets on sets.setID = ao.childSetID 
				and sets.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
			where 1=1
			 <cfif listlen(arguments.subID)>
				and ao.subscriptionID in (<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.subID#" list="true">)
			</cfif>
			order by ao.orderNum, sets.setName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
	
		<cfreturn local.qryAddons>
	</cffunction>

	<cffunction name="getMemberDataByUID" access="public" output="false" returntype="string">
        <cfargument name="orgID" type="string" required="true">
		<cfargument name="columnUID" type="string" required="true">
		<cfargument name="memberID" type="numeric" required="true">

		<cfset var local = structNew() />
		<cfset local.columnValue = false />

		<cfquery name="local.qryMemberData" datasource="#application.dsn.membercentral.dsn#" cachedwithin="#createTimeSpan(0,0,5,0)#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT TOP 1 CASE mdata.dataTypeCode
					WHEN 'STRING' THEN mdcv.columnValueString
					WHEN 'DECIMAL2' THEN convert(varchar(255), mdcv.columnValueDecimal2)
					WHEN 'INTEGER' THEN convert(varchar(255), mdcv.columnValueInteger)
					WHEN 'DATE' THEN convert(varchar(10), mdcv.columnValueDate, 101)
					WHEN 'BIT' THEN convert(varchar(255), mdcv.columnValueBit)
					ELSE NULL
				END AS columnValue
			FROM dbo.ams_memberDataColumns AS f
			INNER JOIN dbo.ams_memberDataColumnDataTypes AS mdata ON mdata.dataTypeID = f.dataTypeID
			INNER JOIN dbo.ams_memberDataColumnDisplayTypes AS mdisp ON mdisp.displayTypeID = f.displayTypeID
			INNER JOIN dbo.ams_memberDataColumnValues AS mdcv ON mdcv.columnID = f.columnID
			INNER JOIN dbo.ams_memberData AS md ON md.valueID = mdcv.valueID
				AND md.memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">
			WHERE f.orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orgID#">
			AND f.[uid] = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.columnUID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
	
		<cfreturn local.qryMemberData.columnValue>
	</cffunction>

	<cffunction name="isMemberInGroups" access="public" returntype="boolean">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="groupIDList" type="string" required="true">
		
		<cfset var local = structNew() />
		<cfset local.returnValue = false />	
		<cfset local.qryData = application.objMember.getMemberGroups(arguments.memberID,arguments.orgID) />
		<cfquery dbtype="query" name="local.data">
			SELECT 1
			FROM [local].qryData
			WHERE groupID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.groupIDList#" list="yes" >)
		</cfquery>
		<cfif local.data.recordCount>
			<cfset local.returnValue = true />
		</cfif>
		<cfreturn local.returnValue />
	</cffunction>

	<cffunction name="getPaymentProcessingFeesInfo" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="mppid" type="numeric" required="true">
		<cfargument name="profileID" type="numeric" required="true">
		<cfargument name="amt" type="numeric" required="yes">
		<cfargument name="stateIDForTax" type="numeric" required="no" default="0">
		<cfargument name="zipForTax" type="string" required="no" default="">
		<cfargument name="processingFeeOpted" type="boolean" required="no" default="0">

		<cfset var local = structNew()>

		<cfquery name="local.qryMerchantProfile" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

			SELECT pr.profileID, s.siteCode, pr.enableProcessingFeeDonation, pr.processFeeDonationFeePercent, pr.processingFeeLabel, pfm.message as processFeeDonationFEMsg, 
				pr.processFeeDonationRenevueGLAccountID, pr.processFeeDonationRevTransDesc, pr.enableSurcharge, pr.surchargePercent, pr.surchargeRevenueGLAccountID, pr.enableMCPay
			FROM dbo.mp_profiles as pr
			INNER JOIN dbo.sites AS s ON s.siteID = pr.siteID
			LEFT OUTER JOIN dbo.tr_solicitationMessages AS pfm ON pfm.siteID = @siteID
				AND pfm.messageID = pr.solicitationMessageID
			WHERE pr.profileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.profileID#">
			AND pr.siteID = @siteID
			AND pr.status = 'A';

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.qrySavedInfoOnFile = application.objPayments.getSavedInfoOnFile(mppid=arguments.mppid, memberID=arguments.memberID, profileID=arguments.profileID)>

		<cfset local.processingFeesInfo = application.objPayments.getAdditionalFeesInfo(qryMerchantProfile=local.qryMerchantProfile, amt=arguments.amt, 
											stateIDForTax=arguments.stateIDForTax, zipForTax=arguments.zipForTax, processingFeeOpted=arguments.processingFeeOpted, 
											surchargeEligibleCard=val(local.qrySavedInfoOnFile.surchargeEligible))>

		<cfset local.processingFeesInfo['surchargeStatement'] = local.qryMerchantProfile.enableSurcharge EQ 1 AND val(local.qryMerchantProfile.surchargePercent)
						? replace(application.objPayments.getDefaultSurchargeMsg(orgName=application.objSiteInfo.getSiteInfo(local.qryMerchantProfile.siteCode).orgName),"{{PERCENT}}","#local.qryMerchantProfile.surchargePercent#%")
						: "">
		
		<cfreturn local.processingFeesInfo>
	</cffunction>

	<cffunction name="getMemberPaymentProfileInfoForDisplay" access="public" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="payProfileID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.payProfileInfo = "">
		<cfset local.strMPP = application.objPayments.getMemberPaymentProfileInfo(mcproxy_siteID=arguments.siteID, mcproxy_orgID=arguments.orgID, memberID=arguments.memberID, payProfileID=arguments.payProfileID).data>

		<cfif structCount(local.strMPP)>
			<cfif local.strMPP.gatewayclass EQ 'creditcard'>
				<cfset local.payProfileInfo = "#len(local.strMPP.cardtype) ? '#local.strMPP.cardtype# ' : ''##local.strMPP.detail#">
				<cfif len(local.strMPP.expiration)>
					<cfset local.payProfileInfo = "#local.payProfileInfo# Exp #local.strMPP.expiration#">
				</cfif>
				<cfif len(local.strMPP.nickname)>
					<cfset local.payProfileInfo = "#local.payProfileInfo#<br/>#local.strMPP.nickname#">
				</cfif>
				<cfif local.strMPP.enablesurcharge>
					<cfif local.strMPP.surchargeeligible>
						<cfset local.payProfileInfo = "#local.payProfileInfo#<br/>#local.strMPP.surchargepercent#% surcharge applies">
					<cfelse>
						<cfset local.payProfileInfo = "#local.payProfileInfo#<br/>No surcharge applies">
					</cfif>
				</cfif>
			<cfelseif local.strMPP.gatewayclass EQ 'bankdraft'>
				<cfset local.payProfileInfo = local.strMPP.detail>
				<cfset local.payProfileInfo = "#local.payProfileInfo#<br/>#local.strMPP.bankAccountType#">
				<cfif len(local.strMPP.nickname)>
					<cfset local.payProfileInfo = "#local.payProfileInfo#<br/>#local.strMPP.nickname#">
				</cfif>
			<cfelse>
				<cfset local.payProfileInfo = local.strMPP.detail>
				<cfif len(local.strMPP.nickname)>
					<cfset local.payProfileInfo = "#local.payProfileInfo#<br/>#local.strMPP.nickname#">
				</cfif>
			</cfif>
		</cfif>

		<cfreturn local.payProfileInfo>
	</cffunction>

	<cffunction name="getMerchantProfileDetails" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="profileID" type="numeric" required="true">

		<cfset var qryMerchantProfile = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryMerchantProfile">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

			SELECT pr.profileID, s.siteCode, pr.enableProcessingFeeDonation, pr.processFeeDonationFeePercent, pr.processingFeeLabel, pr.processFeeDonationRenevueGLAccountID, pr.processFeeDonationRevTransDesc,
				pr.processFeeDonationDefaultSelect, pr.processFeeOtherPaymentsFELabel, pr.processFeeOtherPaymentsFEDenyLabel, pfm.title AS processFeeDonationFETitle, 
				pfm.message as processFeeDonationFEMsg, pr.enableSurcharge, pr.surchargePercent, pr.surchargeRevenueGLAccountID, pr.enableMCPay, g.gatewayType
			FROM dbo.mp_profiles as pr
			INNER JOIN dbo.sites AS s ON s.siteID = pr.siteID
			INNER JOIN dbo.mp_gateways as g on g.gatewayID = pr.gatewayID
			LEFT OUTER JOIN dbo.tr_solicitationMessages AS pfm ON pfm.siteID = @siteID
				AND pfm.messageID = pr.solicitationMessageID
			WHERE pr.profileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.profileID#">
			AND pr.siteID = @siteID
			AND pr.status = 'A';

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryMerchantProfile>
	</cffunction>

	<cffunction name="getBillingInfoFromFormData" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="formData" type="struct" required="true">
		<cfargument name="fieldSetUID" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.billingInfo = { "stateID":0, "zip":"" }>

		<cfquery name="local.qryFSFieldCodes" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT mf_state.fieldCode AS stateFieldCode, mf_zip.fieldCode AS zipFieldCode
			FROM dbo.ams_memberFieldSets AS mfs
			INNER JOIN dbo.ams_memberFields AS mf_state ON mf_state.fieldsetID = mfs.fieldsetID
				AND mf_state.fieldCode LIKE '%_stateProv'
			INNER JOIN dbo.ams_memberFields AS mf_zip ON mf_zip.fieldsetID = mfs.fieldsetID
				AND mf_zip.fieldCode LIKE '%_postalCode'
			WHERE mfs.[uid] = <cfqueryparam cfsqltype="CF_SQL_IDSTAMP" value="#arguments.fieldSetUID#">
			AND mfs.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif local.qryFSFieldCodes.recordCount>
			<cfset local.billingInfo.state = arguments.formData.keyExists(local.qryFSFieldCodes.stateFieldCode) ? arguments.formData[local.qryFSFieldCodes.stateFieldCode] : 0>
			<cfset local.billingInfo.zip = arguments.formData.keyExists(local.qryFSFieldCodes.zipFieldCode) ? arguments.formData[local.qryFSFieldCodes.zipFieldCode] : "">
		</cfif>

		<cfreturn local.billingInfo>
	</cffunction>
</cfcomponent>