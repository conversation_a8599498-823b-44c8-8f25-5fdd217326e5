<cfcomponent output="no">

	<cffunction name="createSWLProgram" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_sitecode" type="string" required="true">
		<cfargument name="seminarName" type="string" required="true">
		<cfargument name="dateStart" type="string" required="true">
		<cfargument name="dateEnd" type="string" required="true">

		<cfset var local = structnew()>

		<cfset local.siteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin',siteID=arguments.mcproxy_siteID)>
		<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>
		
		<cftry>
			<cfif not local.tmpRights.addSWLProgram>
				<cfthrow message="invalid request">
			</cfif>

			<cfset local.dateStart = ParseDateTime(replace(arguments.dateStart,' - ',' '))>
			<cfset local.dateEnd = ParseDateTime(replace(arguments.dateEnd,' - ',' '))>

			<cfset local.strTimeZones = getTimeinAllTimeZones(CentralStartTimeToConvert=local.dateStart, CentralEndTimeToConvert=local.dateEnd)>
			<cfwddx action="cfml2wddx" input="#local.strTimeZones#" output="local.wddxTimeZones">

			<cfstoredproc procedure="swl_createSeminar" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.mcproxy_sitecode#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.seminarName#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="true">
				<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#local.dateStart#">
				<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#local.dateEnd#">
				<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.wddxTimeZones#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="$0.00">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.seminarID">
			</cfstoredproc>

			<cfset local.returnStruct.seminarID = local.seminarID>
			<cfset local.returnStruct.success = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="updateSWLProgram" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="mcproxy_orgID" type="string" required="yes">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="seminarName" type="string" required="yes">
		<cfargument name="seminarSubTitle" type="string" required="no">
		<cfargument name="programCode" type="string" required="yes">
		<cfargument name="seminarDesc" type="string" required="no">
		<cfargument name="isPublished" type="string" required="no">
		<cfargument name="dateStart" type="string" required="no">
		<cfargument name="dateEnd" type="string" required="yes">
		<cfargument name="lockSWLProgramSettings" type="boolean" required="no">
		<cfargument name="webinarHost" type="string" required="no">
		<cfargument name="sendConfirmationEmail" type="boolean" required="no">
		<cfargument name="onDemandDetails" type="string" required="no">
		<cfargument name="createSWLWebinar" type="numeric" required="no">
		<cfargument name="agenda" type="string" required="no">
		<cfargument name="isOpen" type="string" required="no">
		<cfargument name="zoomHostID" type="string" required="no">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { success=false, errmsg='' }>
		<cfparam name="arguments.seminarName" default="">
		<cfparam name="arguments.seminarSubTitle" default="0">
		<cfparam name="arguments.programCode" default="0">
		<cfparam name="arguments.seminarDesc" default="0">
		<cfparam name="arguments.isPublished" default="0">
		<cfparam name="arguments.dateStart" default="">
		<cfparam name="arguments.dateEnd" default="">
		<cfparam name="arguments.createSWLWebinar" default="0">
		<cfparam name="arguments.lockSWODProgramSettings" default="0">
		<cfparam name="arguments.webinarHost" default="">
		<cfparam name="arguments.sendConfirmationEmail" default="0">
		<cfparam name="arguments.onDemandDetails" default="">
		<cfparam name="arguments.agenda" default="">
		<cfparam name="arguments.isOpen" default="0">
		<cfparam name="arguments.zoomHostID" default="">
		<cfset local.objSWL = CreateObject("component","model.seminarweb.SWLiveSeminars")>

		<cfsetting requesttimeout="600">
		<cftry>
			<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qrySeminar">
				select s.seminarName, s.seminarSubTitle, s.isPublished, s.lockSettings, swl.dateStart, swl.dateEnd, swl.ACLicenseID, swl.ACSeminarSCOID, 
					swl.ACSessionSCOID, swl.ZoomWebinarID, swl.ZoomWebinarHostID, swl.ZoomWebinarPwd, swl.ZoomWebinarPhoneNum, 
					p.orgcode as publisherOrgCode
				from dbo.tblSeminars as s
				inner join dbo.tblParticipants as p on p.participantID = s.participantID
				inner join dbo.tblSeminarsSWLive as swl on swl.seminarID = s.seminarID
				where s.seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
				and s.isDeleted = 0
			</cfquery>

			<!--- security --->
			<cfset local.SeminarWebAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin',siteID=arguments.mcproxy_siteid)>
			<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.SeminarWebAdminSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteid)>

			<cfset local.hasEditSWLProgramAllRights = local.tmpRights.editSWLProgramAll is 1>
			<cfset local.hasEditSWLProgramPublishRights = local.tmpRights.editSWLProgramPublish is 1>
			<cfset local.hasLockSWProgramRights = local.tmpRights.lockSWProgram is 1>
			<cfset local.hasUpdateSWLProgramRights = (local.hasEditSWLProgramAllRights OR local.hasEditSWLProgramPublishRights) AND local.qrySeminar.publisherOrgCode eq arguments.mcproxy_siteCode>

			<cfif local.hasUpdateSWLProgramRights AND NOT local.qrySeminar.lockSettings>
				<cfif local.hasEditSWLProgramAllRights>
					<cfset local.dateStart = ParseDateTime(replace(arguments.dateStart,' - ',' '))>
					<cfset local.dateEnd = ParseDateTime(replace(arguments.dateEnd,' - ',' '))>
					<cfset local.durationInMins = DateDiff('n',local.dateStart,local.dateEnd)>

					<cfset local.strTimeZones = getTimeinAllTimeZones(CentralStartTimeToConvert=local.dateStart, CentralEndTimeToConvert=local.dateEnd)>
					<cfwddx action="cfml2wddx" input="#local.strTimeZones#" output="local.wddxTimeZones">

					<!--- get for updates at the end --->
					<cfif arguments.createSWLWebinar neq 1>
						<cfset local.prevStartTimeComp = DateFormat(local.qrySeminar.dateStart,"m/d/yyyy") & " " & TimeFormat(local.qrySeminar.dateStart,"h:mm tt")>
						<cfset local.prevEndTimeComp = DateFormat(local.qrySeminar.dateEnd,"m/d/yyyy") & " " & TimeFormat(local.qrySeminar.dateEnd,"h:mm tt")>
						<cfset local.newStartTimeComp = DateFormat(local.dateStart,"m/d/yyyy") & " " & TimeFormat(local.dateStart,"h:mm tt")>
						<cfset local.newEndTimeComp = DateFormat(local.dateEnd,"m/d/yyyy") & " " & TimeFormat(local.dateEnd,"h:mm tt")>
						<cfset local.seminarNameChanged = compare(local.qrySeminar.seminarName,trim(arguments.seminarName))>
						<cfset local.timeChanged = compare(local.prevStartTimeComp,local.newStartTimeComp) or compare(local.prevEndTimeComp,local.newEndTimeComp)>
					</cfif>
				</cfif>
				
				<!--- update SWL Program --->
				<cfstoredproc procedure="swl_updateSeminar" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.mcproxy_siteCode#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.seminarName#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.seminarSubTitle#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.programCode#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.seminarDesc#">
					<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.agenda#">
					<cfif local.hasEditSWLProgramAllRights>
						<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.isPublished#">
						<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#local.dateStart#">
						<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#local.dateEnd#">
						<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.wddxTimeZones#">
						<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.isOpen#">
						<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.webinarHost#">
					<cfelse>
						<cfprocparam type="In" cfsqltype="CF_SQL_BIT" null="true">
						<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" null="true">
						<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" null="true">
						<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" null="true">
						<cfprocparam type="In" cfsqltype="CF_SQL_BIT" null="true">
						<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="true">
					</cfif>
					<cfif local.hasLockSWProgramRights>
						<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.lockSWLProgramSettings#">
					<cfelse>
						<cfprocparam type="In" cfsqltype="CF_SQL_BIT" null="true">
					</cfif>
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#local.hasEditSWLProgramAllRights#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
				</cfstoredproc>

				<!--- stream provider updates --->
				<cfif application.MCEnvironment eq "Production" AND local.hasEditSWLProgramAllRights>
					<cfswitch expression="#arguments.webinarHost#">
						<!--- Zoom Webinar --->
						<cfcase value="SemWeb">
							<!--- create Zoom Webinar if needed --->
							<cfif arguments.createSWLWebinar is 1>
								<cfset local.strCreateZoomWebinar = CreateObject("component","model.seminarWeb.SWZoomWebinar").createWebinar(
									hostUserID=arguments.zoomHostID, 
									webinarName="SWL-#arguments.seminarID# - #arguments.seminarName#", 
									startTime=local.dateStart, durationInMinutes=local.durationInMins)>

								<cfif local.strCreateZoomWebinar.success>
									<cfset local.arrZoomWebinarLicenses = createObject("component","model.seminarWeb.SWZoomWebinar").getWebinarLicenses()>
									<cfset var zoomHostID = arguments.zoomHostID>
									<cfset local.zoomHostIndex = local.arrZoomWebinarLicenses.find(function(item) { return arguments.item.id eq zoomHostID; })>

									<cfquery name="local.updateSeminarSWL" datasource="#application.dsn.tlasites_seminarweb.dsn#">
										DECLARE @seminarID INT, @orgID INT, @siteID INT, @crlf VARCHAR(10), @msgjson VARCHAR(MAX), @recordedByMemberID int;
										SET @seminarID = <cfqueryparam value="#arguments.seminarID#" cfsqltype="CF_SQL_INTEGER">;
										SET @orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">;
										SET @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_siteID#">;
										SET @recordedByMemberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#session.cfcUser.memberData.memberID#">;
										SET @crlf = CHAR(13) + CHAR(10);

										UPDATE dbo.tblSeminarsSWLive 
										SET providerID = 3,
											ZoomWebinarID = <cfqueryparam value="#local.strCreateZoomWebinar.seminar.id#" cfsqltype="CF_SQL_VARCHAR">,
											ZoomWebinarHostID = <cfqueryparam value="#arguments.zoomHostID#" cfsqltype="CF_SQL_VARCHAR">,
											ZoomWebinarPwd = <cfqueryparam value="#local.strCreateZoomWebinar.seminar.password#" cfsqltype="CF_SQL_CHAR">,
											ZoomWebinarPhoneNum = <cfqueryparam value="#local.strCreateZoomWebinar.seminar.phone#" cfsqltype="CF_SQL_VARCHAR">
										WHERE seminarID = @seminarID;

										SELECT @msgjson = 'Stream Provider Settings for SWL-' + CAST(@seminarID AS VARCHAR(10)) + ' has been updated.' + @crlf
											+ 'Stream Provider : ' + sp.provider + @crlf
											+ 'Webinar ID : ' + s.ZoomWebinarID + @crlf
											<cfif val(local.zoomHostIndex) gt 0>
												+ 'Host : #local.arrZoomWebinarLicenses[local.zoomHostIndex].firstname#' + @crlf
											</cfif>
											+ 'Webinar Password : ' + s.ZoomWebinarPwd + @crlf
											+ 'Webinar Phone : ' + s.ZoomWebinarPhoneNum + @crlf
										FROM dbo.tblSeminarsSWLive s
										LEFT JOIN dbo.tblSeminarsSWLiveProviders sp ON sp.providerID = s.providerID
											AND sp.isActive = 1
										WHERE seminarID = @seminarID;

										INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
										VALUES('{ "c":"auditLog", "d": {
											"AUDITCODE":"SW",
											"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
											"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
											"ACTORMEMBERID":' + CAST(@recordedByMemberID AS VARCHAR(20)) + ',
											"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
											"MESSAGE":"' + REPLACE(@msgjson,'"','\"') + '" } }');
									</cfquery>

									<cfset local.returnStruct["zoomwebinarcreated"] = true>

									<!--- auto add speakers if there are any --->
									<cfset local.qryLinkedAuthors = CreateObject("component","model.seminarWeb.SWAuthors").getAuthorsBySeminarID(seminarID=arguments.seminarID, authorType='Speaker')>
									<cfif local.qryLinkedAuthors.recordcount>
										<cfset local.objSWAuthors = CreateObject("component","seminarWebAuthors")>
										<cfloop query="local.qryLinkedAuthors">
											<cfif NOT len(local.qryLinkedAuthors.SWLCode)>
												<cfset local.objSWAuthors.addSeminarAuthorSWLCode(seminarId=arguments.seminarID, authorID=local.qryLinkedAuthors.authorID)>
											</cfif>
											<cfset local.strAdd = local.objSWAuthors.addSeminarAuthorZoomWebinarPanelistID(seminarId=arguments.seminarID, authorID=local.qryLinkedAuthors.authorID)>
											<cfif not local.strAdd.success>
												<cfset local.returnStruct.success = false>
												<cfset local.returnStruct.errmsg = local.strAdd.errmsg>
												<cfbreak>
											</cfif>
										</cfloop>
									</cfif>

									<!--- auto add enrollments if there are any --->
									<cfset local.qryEnrollments = getEnrollments(seminarID=arguments.seminarID)>
									<cfif local.qryEnrollments.recordcount>
										<cfloop query="local.qryEnrollments">
											<cfif NOT len(local.qryEnrollments.ZoomWebinarRegistrantID)>
												<cfset local.strRegAdd = addSeminarRegistrantToZoomWebinar(seminarID=arguments.seminarID, registrantID=local.qryEnrollments.enrollmentID)>
												<cfif NOT local.strRegAdd.success>
													<cftry>
														<cfthrow message="Problem Saving Enrollment." detail="Unable to auto enroll registrant swlive_l#arguments.seminarID#_r#local.qryEnrollments.enrollmentID#@seminarweblive.com.">
													<cfcatch type="Any">
														<cfset local.returnStruct.success = false>
														<cfset local.returnStruct.errmsg = local.returnStruct.errmsg & cfcatch.detail & '<br/>'>
														<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local.strRegAdd)>
													</cfcatch>
													</cftry>
												</cfif>
											</cfif>
										</cfloop>
									</cfif>

									<cfif len(local.returnStruct.errmsg)>
										<cfreturn local.returnStruct>
									</cfif>

								<cfelse>
									<cftry>
										<cfthrow message="Problem Saving Seminar." detail="The seminar was saved locally but we could not complete Zoom Webinar setup.">
									<cfcatch type="Any">
										<cfset local.returnStruct.success = false>
										<cfset local.returnStruct.errmsg = "Problem Saving Seminar. The seminar was saved locally but we could not complete Zoom Webinar setup.">
										<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local.strCreateZoomWebinar)>
									</cfcatch>
									</cftry>
								</cfif>

							<!--- update Zoom Webinar if needed --->
							<cfelseif len(local.qrySeminar.ZoomWebinarID) AND (local.seminarNameChanged or local.timeChanged)>
								<cfset local.strUpdateZoomWebinar = CreateObject("component","model.seminarWeb.SWZoomWebinar").updateWebinar(
									webinarID=local.qrySeminar.ZoomWebinarID, 
									webinarName="SWL-#arguments.seminarID# - #arguments.seminarName#",
									startTime=local.dateStart, durationInMinutes=local.durationInMins)>

								<cfif NOT local.strUpdateZoomWebinar.success>
									<cftry>
										<cfthrow message="Problem Saving Seminar." detail="The seminar was saved locally but we could not update the Zoom Webinar.">
									<cfcatch type="Any">
										<cfset local.returnStruct.success = false>
										<cfset local.returnStruct.errmsg = "Problem Saving Seminar. The seminar was saved locally but we could not update the Zoom Webinar.">
										<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local.strUpdateZoomWebinar)>
										<cfreturn local.returnStruct>
									</cfcatch>
									</cftry>
								</cfif>
							</cfif>
						</cfcase>
					</cfswitch>
				</cfif>

				<!--- get seminar data after update --->
				<cfset local.qrySeminar = local.objSWL.getSeminarBySeminarID(arguments.seminarID)>

				<cfif arguments.sendConfirmationEmail and not application.objUser.isSuperUser(cfcuser=session.cfcuser)>
					<cfset local.sendingSiteInfo = application.objSiteInfo.getSiteInfo(local.qrySeminar.publisherOrgCode)>
					<cfset local.siteInfo = application.objSiteInfo.getSiteInfo(local.qrySeminar.publisherOrgCode)>
					<cfset local.programLink = "#local.siteInfo.scheme#://#local.siteInfo.mainhostname##CreateObject('component','model.admin.admin').buildLinkToTool(toolType='SeminarWebAdmin',mca_ta='editSWLProgram')#&pid=#local.qrySeminar.seminarID#">
					<cfset local.parsedTime = local.objSWL.parseTimesFromWDDX(local.qrySeminar.wddxTimeZones,local.qrySeminar.orgWddxTimeZones,local.qrySeminar.dateStart,local.qrySeminar.dateEnd)>
					<!--- confirmation email to submitter --->
					<cfif len(local.qrySeminar.submittedByEmail)>
						<cfsavecontent variable="local.emailHTMLContent">
							<cfoutput>
								Hi #local.qrySeminar.submitterFirstName#,<br/><br/>
								<b>#local.qrySeminar.seminarName#</b><cfif arguments.webinarHost EQ "Assoc"> has been successfully converted to a webinar.<cfelse> has been successfully submitted to SeminarWeb. We will follow up on your submission within 24-48 hours.</cfif> To review your program, <a href="#local.programLink#">click here.</a><br/>
								<hr>
								Below is a summary of your program: <br/><br/>
								Program Status: <cfif local.qrySeminar.isPublished>Active <cfelse>Inactive</cfif><br/><br/>
								Host: <cfif arguments.webinarHost eq "SemWeb">SeminarWeb<cfelse>#local.qrySeminar.publisherOrgCode#</cfif><br/><br/>
								Webinar Date: #dateFormat(local.parsedTime.StartDate,'m/d/yyyy')#<br/><br/>
								Webinar Time: #replace(timeFormat(local.parsedTime.StartDate,'h:mm tt'),":00","")# #uCase(local.parsedTime.TimeZone)#<br/><br/>
								Webinar Title: #encodeForHTML(local.qrySeminar.seminarName)#
								<cfif len(local.qrySeminar.seminarSubTitle)>
									<br/><br/>Webinar Subtitle: #encodeForHTML(local.qrySeminar.seminarSubTitle)#
								</cfif>
								<cfif LEN(local.qrySeminar.dateCatalogStart)>
									<br/><br/>Catalog Sale Dates: #DateFormat(local.qrySeminar.dateCatalogStart,'mm/dd/yyyy')# - #DateFormat(local.qrySeminar.dateCatalogEnd,'mm/dd/yyyy')#
								</cfif>
								<hr><br/>
								<p style="margin-top:2px;margin-bottom:2px;">SeminarWeb</p>
								737-201-2059
							</cfoutput>
						</cfsavecontent>
						<cfset local.emailHTMLContent = trim(replace(replace(replace(local.emailHTMLContent,chr(9),"","ALL"),chr(13),"","ALL"),chr(10),"","ALL"))>
						<cfif arguments.webinarHost EQ "SemWeb">
							<cfset local.emailsubject = "Webinar Submitted to SeminarWeb - #local.qrySeminar.seminarName#">
						<cfelse>
							<cfset local.emailsubject = "Webinar Created -  #local.qrySeminar.seminarName#">
						</cfif>

						<cfset application.objEmailWrapper.sendMailESQ(
							emailfrom={ name="SeminarWeb", email="<EMAIL>" },
							emailto=[{ name=local.qrySeminar.submittedByMember, email=local.qrySeminar.submittedByEmail }],
							emailreplyto="",
							emailsubject=local.emailsubject,
							emailtitle= arguments.webinarHost EQ "SemWeb" ? "Webinar Submitted to SeminarWeb": "Webinar Created",
							emailhtmlcontent=local.emailHTMLContent,
							siteID=local.qrySeminar.participantSiteID,
							memberID=local.qrySeminar.submittedByMemberID,
							messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SWLCREATE"),
							sendingSiteResourceID=local.sendingSiteInfo.siteSiteResourceID
						)>
					</cfif>

					<cfif arguments.webinarHost EQ "SemWeb">
						<cftry>
							<cfsavecontent variable="local.emailHTMLContent">
								<cfoutput>
									The following webinar was submitted:<hr/>
									<a href="#local.programLink#">Click here</a> to review this submission.<hr/>
									<b>Submitted By:</b><br/><br/>
									#local.qrySeminar.submittedByMember#<br/><br/>
									#local.qrySeminar.submittedByEmail#
									<hr/>
									<b>Webinar Details:</b><br/><br/>
									Publisher: #local.qrySeminar.publisherOrgCode#<br/><br/>
									Webinar Title: #encodeForHTML(local.qrySeminar.seminarName)#<br/><br/>
									Webinar Date: #dateFormat(local.qrySeminar.dateStart,'m/d/yyyy')# <br/><br/>
									Webinar Time: #replace(timeFormat(local.qrySeminar.dateStart,'h:mm tt'),":00","")# CENTRAL
									<cfif LEN(arguments.onDemandDetails)>
										<hr/>
										<b>OnDemand Details:</b><br/><br/>
										#arguments.onDemandDetails#
									</cfif>
								</cfoutput>
							</cfsavecontent>
							<cfset local.emailHTMLContent = trim(replace(replace(replace(local.emailHTMLContent,chr(9),"","ALL"),chr(13),"","ALL"),chr(10),"","ALL"))>
							<cfset local.emailsubject = "#local.sendingSiteInfo.sitename# Webinar Submission: #local.qrySeminar.seminarName#">

							<!--- create ticket via API --->						
							<cfsavecontent variable="local.apiPayload"><cfoutput>{"ticket": { "subject": #serializeJSON(local.emailSubject)#, "comment": { "html_body": #serializeJSON(local.emailHTMLContent)#, "public": false }, "requester": { "name": "#local.qrySeminar.submittedByMember#", "email": "#local.qrySeminar.submittedByEmail#" }, "tags": [ "no_csat","no_solve_confirmation","no_initial_email"], "group_id": 360000404992, "email_ccs": [{ "user_email": "#local.qrySeminar.submittedByEmail#", "user_name": "#local.qrySeminar.submittedByMember#", "action": "put"}] }}</cfoutput></cfsavecontent>

							<cfhttp method="POST" url="https://trialsmith.zendesk.com/api/v2/tickets.json?async=true" useragent="MemberCentral.com" result="local.apiResult" throwonerror="true">
								<cfhttpparam type="header" name="accept" value="application/json">
								<cfhttpparam type="header" name="content-type" value="application/json">
								<cfhttpparam type="header" name="authorization" value="Basic #ToBase64("#application.strPlatformAPIKeys.zendesk.email#/token:#application.strPlatformAPIKeys.zendesk.token#")#">
								<cfhttpparam type="body" value="#trim(local.apiPayload)#">
							</cfhttp>
						<cfcatch type="Any">
							<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
						</cfcatch>
						</cftry>
					</cfif>
				</cfif>

				<cfif not len(local.returnStruct.errmsg)>
					<cfset local.returnStruct.success = true>
				</cfif>

				<!--- include the updated display start time in ajax response --->
				<cfset local.qryAssociation = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(arguments.mcproxy_siteCode).qryAssociation>
				<cfset local.parsedTime = local.objSWL.parseTimesFromWDDX(local.qrySeminar.wddxTimeZones,local.qryAssociation.wddxTimeZones,local.qrySeminar.dateStart,local.qrySeminar.dateEnd)>
				<cfset local.returnStruct["dspstarttime"] = "#DateFormat(local.parsedTime.StartDate, "ddd, mmmm d, yyyy")# #replace(TimeFormat(local.parsedTime.StartDate,"h:mm TT"),":00 ","")#">

			<cfelseif local.hasUpdateSWLProgramRights AND local.hasLockSWProgramRights>
				<cfset CreateObject("seminarWebSWCommon").updateProgramLockSettings(orgID=arguments.mcproxy_orgID,
					siteID=arguments.mcproxy_siteID, programType="SWL", programID=arguments.seminarID,
					lockSWProgramSettings=arguments.lockSWLProgramSettings)>

				<cfset local.returnStruct.success = true>
			<cfelse>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.errmsg = "You do not have rights to this section.">
			</cfif>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="updateSWLProgramRecording" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="mcproxy_orgID" type="string" required="yes">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="offerReplay" type="numeric" required="yes">
		<cfargument name="hasReplayVideoUploaded" type="string" required="no" default="0">
		<cfargument name="canResellSeminar" type="numeric" required="yes">
		<cfargument name="replayExpirationDate" type="string" required="yes">
		<cfargument name="replayAvailability" type="string" required="yes">
	
		<cfset var local = structNew()>
		<cfset local.returnStruct = { success=false, errmsg='' }>
	
		<cfsetting requesttimeout="600">
	
		<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qrySeminar">
			select s.lockSettings,	p.orgcode as publisherOrgCode
			from dbo.tblSeminars as s
			inner join dbo.tblParticipants as p on p.participantID = s.participantID
			where s.seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
			and s.isDeleted = 0
		</cfquery>
	
		<!--- security --->
		<cfset local.SeminarWebAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin',siteID=arguments.mcproxy_siteID)>
		<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.SeminarWebAdminSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>
	
		<cfset local.hasEditSWLProgramAllRights = local.tmpRights.editSWLProgramAll is 1>
		<cfset local.hasEditSWLProgramPublishRights = local.tmpRights.editSWLProgramPublish is 1>
		<cfset local.hasLockSWProgramRights = local.tmpRights.lockSWProgram is 1>
		<cfset local.hasUpdateSWLProgramRights = (local.hasEditSWLProgramAllRights OR local.hasEditSWLProgramPublishRights) AND local.qrySeminar.publisherOrgCode eq arguments.mcproxy_siteCode>
	
		<cfif local.hasUpdateSWLProgramRights AND NOT local.qrySeminar.lockSettings>
			<cfif arguments.offerReplay eq 0 and arguments.hasReplayVideoUploaded eq 1>
				<cfset deleteSWLReplayVideo(mcproxy_siteID=arguments.mcproxy_siteID, seminarID=arguments.seminarID)>
			</cfif>
			<cfstoredproc procedure="sw_updateSeminarRecording" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.offerReplay#">
				<cfif arguments.offerReplay>
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.replayExpirationDate#">
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.canResellSeminar#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.replayAvailability#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="true">
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" null="true">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="registrants">
				</cfif>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>
	
			<cfif not len(local.returnStruct.errmsg)>
				<cfset local.returnStruct.success = true>
			</cfif>
		<cfelse>
			<cfset local.returnStruct.success = false>
			<cfset local.returnStruct.errmsg = "You do not have rights to this section.">
		</cfif>
	
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="saveSeminarRateOptions" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="isPriceBasedOnActual" type="boolean" required="yes">
		<cfargument name="revenueGLAccountID" type="numeric" required="yes">
		<cfargument name="allowCatalog" type="boolean" required="yes">
		<cfargument name="dateCatalogStart" type="string" required="yes">
		<cfargument name="dateCatalogEnd" type="string" required="yes">
		<cfargument name="freeRateDisplay" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { success=false, err='' }>

		<cftry>
			<cfif not hasUpdateSeminarRights(siteID=arguments.mcproxy_siteID, seminarID=arguments.seminarID, action="Edit", checkLockSettings=true)>
				<cfthrow message="You do not have rights to this section.">
			</cfif>

			<cfstoredproc procedure="sw_updateSeminarPricing" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.allowCatalog#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.isPriceBasedOnActual#">
				<cfif arguments.allowCatalog is 1 and len(arguments.dateCatalogStart) and isDate(arguments.dateCatalogStart)>
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.dateCatalogStart#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="true">
				</cfif>
				<cfif arguments.allowCatalog is 1 and len(arguments.dateCatalogEnd) and isDate(arguments.dateCatalogEnd)>
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.dateCatalogEnd#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="true">
				</cfif>
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.freeRateDisplay#">
				<cfif val(arguments.revenueGLAccountID) GT 0>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.revenueGLAccountID#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
				</cfif>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>

			<cfset local.returnStruct.success = true>
		<cfcatch type="Any">
			<cfset local.returnStruct.success = false>
			<cfset local.returnStruct.err = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="saveSWLFilter" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			getSWLFilter();
			local.SWLFilter = application.mcCacheManager.sessionGetValue(keyname='SWLFilter',defaultValue={});
			local.SWLFilter_listFilter_hash = hash(serializeJSON(local.SWLFilter.listFilter), "SHA", "UTF-8");
			local.fromDt 		= dateFormat(dateAdd("d",-180,now()), "m/d/yyyy");
			local.toDt 			= dateFormat(dateAdd("d",30,now()), "m/d/yyyy");
			
			local.SWLFilter.listFilter.fDateFrom = arguments.event.getValue('fDateFrom',local.fromDt);
			local.SWLFilter.listFilter.fDateTo = arguments.event.getValue('fDateTo',local.toDt);
			local.SWLFilter.listFilter.fKeyword = arguments.event.getValue('fKeyword','');
			local.SWLFilter.listFilter.fProgramCode = arguments.event.getValue('fProgramCode','');
			local.SWLFilter.listFilter.fPubType = arguments.event.getValue('fPubType','PO');
			local.SWLFilter.listFilter.fStatus = arguments.event.getValue('fStatus',1);
			local.SWLFilter.listFilter.fFeaturedOnly = arguments.event.getValue('fFeaturedOnly',0);
			local.SWLFilter.listFilter.fSyndicatedOnly = arguments.event.getValue('fSyndicatedOnly',0);
			
			if (local.SWLFilter_listFilter_hash NEQ hash(serializeJSON(local.SWLFilter.listFilter), "SHA", "UTF-8")) 
				application.mcCacheManager.sessionSetValue(keyname='SWLFilter', value=local.SWLFilter);
				
			local.data.success = true;
		</cfscript>
		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="getSWLFilter" access="public" output="false" returntype="struct">
		<cfscript>
			var local = structNew();
			local.fromDt 		= dateFormat(dateAdd("d",-180,now()), "m/d/yyyy");
			local.toDt 			= dateFormat(dateAdd("d",30,now()), "m/d/yyyy");
			local.tmpStr = { fDateFrom=local.fromDt, fDateTo=local.toDt, fKeyword='', fProgramCode='', fPubType='PO', fStatus=1, fFeaturedOnly=0, fSyndicatedOnly=0 };

			local.SWLFilter = application.mcCacheManager.sessionGetValue(keyname='SWLFilter',defaultValue={});
			local.SWLFilter_hash = hash(serializeJSON(local.SWLFilter), "SHA", "UTF-8");

			if (NOT structKeyExists(local.SWLFilter,"listFilter"))
				local.SWLFilter.listFilter = duplicate(local.tmpStr);

			
			for (local.thiskey in local.tmpStr) {
				if (not structKeyExists(local.SWLFilter.listFilter, local.thiskey))
					structInsert(local.SWLFilter.listFilter, local.thiskey, local.tmpStr[local.thiskey], true);
			}
			if (local.SWLFilter_hash NEQ hash(serializeJSON(local.SWLFilter), "SHA", "UTF-8")) 
				application.mcCacheManager.sessionSetValue(keyname='SWLFilter', value=local.SWLFilter);
		</cfscript>

		<cfreturn local.SWLFilter>
	</cffunction>


	<cffunction name="getPrograms" access="public" output="false" returntype="Any">
		<cfargument name="sitecode" type="string" required="true">
		<cfargument name="mode" type="string" required="true">
		<cfargument name="dateFrom" type="string" required="true">
		<cfargument name="dateTo" type="string" required="true">
		<cfargument name="keyword" type="string" required="true">
		<cfargument name="programCode" type="string" required="true">
		<cfargument name="publisherType" type="string" required="true">
		<cfargument name="hideInactive" type="boolean" required="true">
		<cfargument name="featuredOnly" type="boolean" required="false" default="0">
		<cfargument name="orderby" type="numeric" required="false" default="0">
		<cfargument name="direct" type="string" required="false" default="asc">
		<cfargument name="posstart" type="numeric" required="false" default="0">
		<cfargument name="count" type="numeric" required="false" default="50">
		<cfargument name="sidList" type="string" required="false" default="">
		<cfargument name="xsidList" type="string" required="false" default="">
		<cfargument name="folderPathUNC" type="string" required="false" default="">
		<cfargument name="reportFileName" type="string" required="false" default="">
		<cfargument name="pid" type="numeric" required="false" default="0">
		<cfargument name="publisherIDList" type="string" required="false" default="">
		<cfargument name="syndicatedOnly" type="boolean" required="false" default="0">
		
		<cfset var local = structNew()>
		
		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"tmp.dateStart")>
		<cfif listFindNoCase('copyRatesGrid,couponsDatatable',arguments.mode)>
			<cfset arrayAppend(local.arrCols,"tmp.dateStart")>
		</cfif>
		<cfset arrayAppend(local.arrCols,"tmp.seminarName")>
		<cfif arguments.direct eq "DES"><cfset arguments.direct = 'DESC'></cfif>
		<cfset local.orderby = local.arrcols[arguments.orderby+1]>

		<cfquery name="local.qrySeminars" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				-- create temp table to store all seminars in catalog
				DECLARE @tmpTable TABLE (autoID int IDENTITY(1,1), seminarID int, programCode varchar(15), seminarName varchar(250), seminarSubTitle varchar(250), 
					publisherOrgCode varchar(10), isFeatured bit, dateStart datetime, dateEnd datetime, isPublished BIT, allowSyndication BIT, lockSettings bit, wddxTimeZones VARCHAR(MAX), row int);

				declare @participantID int, @sd date, @ed date, @pn varchar(200), @rc varchar(15), @siteCode varchar(10), @totalCount int, @posStart int, @posStartAndCount int;
				SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.posStart#">;
				SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.count#">;
				SET @siteCode = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.sitecode#">;
				select @participantID = dbo.fn_getParticipantIDFromOrgcode(@siteCode);
				SET @sd = <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.dateFrom#">;
				SET @ed = <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.dateTo#">;
				SET @pn = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.keyword#">;
				SET @rc = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.programCode#">;
				
				INSERT INTO @tmpTable (seminarID, programCode, seminarName, seminarSubTitle, publisherOrgCode, dateStart, dateEnd, isPublished, isFeatured, allowSyndication, lockSettings, wddxTimeZones, row)
				select tmp.seminarID, tmp.programCode, tmp.seminarName, tmp.seminarSubTitle, tmp.publisherOrgCode, tmp.dateStart, tmp.dateEnd, tmp.isPublished, 
					case when fp.featuredID is not null then 1 else 0 end as isFeatured, tmp.allowSyndication, tmp.lockSettings, tmp.wddxTimeZones,
					ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)# #arguments.direct#) as row
				from (
					select s.seminarID, s.programCode, s.seminarName, s.seminarSubTitle, p.participantID, p.orgcode as publisherOrgCode, swl.dateStart, swl.dateEnd, s.isPublished, swl.allowSyndication, s.lockSettings, swl.wddxTimeZones
					FROM dbo.tblSeminars as s
					INNER JOIN dbo.tblSeminarsSWLive as swl on swl.seminarID = s.seminarID
					INNER JOIN dbo.tblParticipants as p on p.participantID = s.participantID
					WHERE p.participantID = @participantID
					and s.isDeleted = 0
					<cfif arguments.hideInactive is 1>
						and s.isPublished = 1
					</cfif>
						UNION
					SELECT s.seminarID, s.programCode, s.seminarName, s.seminarSubTitle, p2.participantID, p2.orgcode as publisherOrgCode, swl.dateStart, swl.dateEnd, s.isPublished, swl.allowSyndication, s.lockSettings, swl.wddxTimeZones
					FROM dbo.tblParticipants AS p 
					INNER JOIN dbo.tblSeminarsOptIn AS soi ON p.participantID = soi.participantID AND soi.IsActive = 1
					INNER JOIN dbo.tblSeminars AS s ON soi.seminarID = s.seminarID 
					INNER JOIN dbo.tblSeminarsSWLive AS swl ON s.seminarID = swl.seminarID
					INNER JOIN dbo.tblParticipants AS p2 ON p2.participantID = s.participantID
					WHERE p.participantID = @participantID
					and s.isDeleted = 0
					<cfif arguments.hideInactive is 1>
						and s.isPublished = 1
					</cfif>
				) as tmp
				<cfif listLen(arguments.publisherIDList)>
					inner join memberCentral.dbo.fn_intListToTableInline(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.publisherIDList#">,',') as tmpP on tmpP.listitem = tmp.participantID
				</cfif>
				<cfif listLen(arguments.sidList)>
					inner join memberCentral.dbo.fn_intListToTableInline('0#arguments.sidList#',',') as tbl on tbl.listitem = tmp.seminarID
				</cfif>
				<cfif listLen(arguments.xsidList)>
					left outer join memberCentral.dbo.fn_intListToTableInline('0#arguments.xsidList#',',') as tblExc on tblExc.listitem = tmp.seminarID
				</cfif>
				left outer join dbo.tblFeaturedPrograms as fp on fp.participantID = @participantID
					and fp.seminarID = tmp.seminarID
				where 1 = 1
				<cfif len(arguments.keyword)>
					and tmp.seminarName + ISNULL(' ' + tmp.seminarSubTitle,'') LIKE '%' + @pn + '%'
				</cfif>
				<cfif len(arguments.programCode)>
					and tmp.programCode = @rc
				</cfif>
				<cfif len(arguments.dateFrom) and len(arguments.dateTo)>
					and tmp.dateStart between @sd and @ed 
				<cfelseif len(arguments.dateFrom)>
					and tmp.dateStart >= @sd
				<cfelseif len(arguments.dateTo)>
					and tmp.dateStart <= @ed
				</cfif>
				<cfif arguments.publisherType EQ 'P'>
					and tmp.publisherOrgCode = @siteCode
				<cfelseif arguments.publisherType EQ 'O'>
					and tmp.publisherOrgCode <> @siteCode
				</cfif>
				<cfif arguments.featuredOnly eq 1>
					and fp.featuredID is not null
				</cfif>
				<cfif listLen(arguments.xsidList)>
					and tblExc.listitem is null
				</cfif>
				<cfif arguments.mode EQ "copyRatesGrid">
					and tmp.seminarID <> <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.pid#">
				</cfif>
				<cfif arguments.syndicatedOnly eq 1>
					and tmp.allowSyndication = 1 
					and tmp.publisherOrgCode = @siteCode
				</cfif>;

				SET @totalCount = @@ROWCOUNT;

				<cfif arguments.mode EQ "export">
					IF OBJECT_ID('tempdb..##tmpSWL') IS NOT NULL
						DROP TABLE ##tmpSWL;
					
					SELECT 'SWL-' + cast(tmp.seminarId as varchar(10)) as [Program ID], tmp.seminarName as [Program], 
						tmp.ProgramCode, tmp.seminarSubTitle as [ProgramSubTitle],
						case when tmp.publisherOrgCode = @siteCode and tmp.allowSyndication = 1 then 'Publisher/Syndicated' 
						when tmp.publisherOrgCode = @siteCode then 'Publisher' 
						else 'Opted-In' end as [Opt-In], 
						tmp.publisherOrgCode as Publisher, tmp.dateStart as [Program Date], 
						case when tmp.isPublished = 1 then 'Active' else 'Inactive' end as [Status],
						(
						SELECT count(e.enrollmentID)
						FROM dbo.tblEnrollments AS e 
						INNER JOIN dbo.tblSeminars as s on s.seminarid = e.seminarID
						INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID 
						INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID 
						WHERE e.seminarID = tmp.seminarID
						AND e.isActive = 1
						AND (s.participantID = @participantID OR e.participantID = @participantID)
						AND (d.adminflag2 is null or d.adminflag2 <> 'Y')
						) as Registrants
					INTO ##tmpSWL
					FROM @tmpTable as tmp
					ORDER BY tmp.row;
					
					DECLARE @selectsql varchar(max) = 'SELECT [Program ID], [ProgramCode], [Program Date], [Opt-In], [Publisher], [Program], [ProgramSubTitle], 
						[Status], [Registrants], ROW_NUMBER() OVER(order by [Program Date], [Program]) as mcCSVorder 
						*FROM* ##tmpSWL';
					EXEC membercentral.dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#arguments.folderPathUNC#\#arguments.reportFileName#', @returnColumns=0;
					
					IF OBJECT_ID('tempdb..##tmpSWL') IS NOT NULL
						DROP TABLE ##tmpSWL;

				<cfelseif listFindNoCase("grid,couponsDatatable,copyRatesGrid", arguments.mode)>
					SELECT tmp.seminarID, tmp.seminarName, tmp.seminarSubTitle, tmp.publisherOrgCode, tmp.dateStart, tmp.dateEnd, tmp.isPublished, tmp.isFeatured, 
					tmp.lockSettings, tmp.wddxTimeZones,
						@totalCount as totalCount, 
						(
							SELECT count(e.enrollmentID)
							FROM dbo.tblEnrollments AS e 
							INNER JOIN dbo.tblSeminars as s on s.seminarid = e.seminarID
							INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID 
							INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID 
							WHERE e.seminarID = tmp.seminarID
							AND e.isActive = 1
							AND (s.participantID = @participantID OR e.participantID = @participantID)
							AND (d.adminflag2 is null or d.adminflag2 <> 'Y')
						) as enrolledCount,
						tmp.allowSyndication
					FROM @tmpTable as tmp
					WHERE tmp.row > @posStart AND tmp.row <= @posStartAndCount
					ORDER BY tmp.row;
				</cfif>
			
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfif listFindNoCase("grid,couponsDatatable,copyRatesGrid", arguments.mode)>
			<cfreturn local.qrySeminars>
		</cfif>
	</cffunction>

	<cffunction name="getRegistrants" access="public" output="false" returntype="Any">
		<cfargument name="sitecode" type="string" required="yes">
		<cfargument name="mode" type="string" required="true">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="rAttended" type="string" required="yes">
		<cfargument name="rDateFrom" type="string" required="true">
		<cfargument name="rDateTo" type="string" required="true">
		<cfargument name="rHideDeleted" type="boolean" required="true">
		<cfargument name="rCredits" type="string" required="yes">
		<cfargument name="memberID" type="numeric" required="yes">
		<cfargument name="pDateFrom" type="string" required="false" default="">
		<cfargument name="pDateTo" type="string" required="false" default="">
		<cfargument name="pKeyword" type="string" required="false" default="">
		<cfargument name="pProgramCode" type="string" required="false" default="">
		<cfargument name="pPublisherType" type="string" required="false" default="">
		<cfargument name="pHideInactive" type="boolean" required="false" default="0">
		<cfargument name="emailTagTypeID" type="numeric" required="false" default="0">
		<cfargument name="orderby" type="numeric" required="false" default="1">
		<cfargument name="direct" type="string" required="false" default="desc">
		<cfargument name="posstart" type="numeric" required="false" default="0">
		<cfargument name="count" type="numeric" required="false" default="50">
		<cfargument name="folderPathUNC" type="string" required="false" default="">
		<cfargument name="reportFileName" type="string" required="false" default="">
		<cfargument name="fbExportType" type="string" required="false" default="">
		<cfargument name="fbFormID" type="numeric" required="false" default="0">
		<cfargument name="fieldSetID" type="numeric" required="false" default="0">
		<cfargument name="exportFieldIDList" type="string" required="false" default="">

		<cfset var local = StructNew()>

		<!---
		// GRIDMODES:
		// regsearchgrid = registrant search grid
		// exportregsearch = export from registrant search
		// reggrid = seminarweb tab on the members record
		// grid = registrants tab on a program
		// export = export from registrants tab on a program
		// manageAttendance = attendance list from a program
		// massEmailInstr,massEmailMaterials,massEmailReplayLink,massEmailRegGrid,massEmailReg,massEmailCert,regForSWOD,formResponses = mass actions from a program
		--->

		<cfset local.arrCols = arrayNew(1)>		
		<cfif listFindNoCase("reggrid", arguments.mode)>
			<cfset arrayAppend(local.arrCols,"tmp.seminarName")>
		<cfelse>		
			<cfset arrayAppend(local.arrCols,"m2.LastName + ', ' + m2.FirstName")>
		</cfif>
		<cfset arrayAppend(local.arrCols,"tmp.dateEnrolled")>
		<cfif arguments.direct eq "DES"><cfset arguments.direct = 'DESC'></cfif>
		<cfset local.orderby = local.arrcols[arguments.orderby+1]>

		<cfif listFindNoCase("export,exportregsearch",arguments.mode)>
			<cfset local.tmpSuffix = replace(createUUID(),'-','','ALL')>
		</cfif>
		
		<cfquery name="local.qryEnrollments" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @seminarID int, @siteCode varchar(10), @participantID INT, @rDateFrom smalldatetime, @rDateTo smalldatetime, 
					@pDateFrom smalldatetime, @pDateTo smalldatetime, @rAttended BIT, @rCredits BIT, @totalCount int, @memberID int, @siteID int, 
					@orgID int, @swlFieldList varchar(max) = '', @fullsql varchar(max);
				
				IF OBJECT_ID('tempdb..##tmpEnrollments') IS NOT NULL 
					DROP TABLE ##tmpEnrollments;
				IF OBJECT_ID('tempdb..##tmpEnrollments2') IS NOT NULL 
					DROP TABLE ##tmpEnrollments2;
				CREATE TABLE ##tmpEnrollments (userid int, enrollmentID int PRIMARY KEY, MCMemberID int, depoMemberDataID int INDEX IX_tmpEnrollments_depomemberdataID, 
					orgcode varchar(10), dateEnrolled smalldatetime, duration int, providerID int, ACUserID varchar(20), ZoomWebinarRegistrantID varchar(30), 
					passed bit, attended bit, attendedStart varchar(20), attendedEnd varchar(20), seminarID int, programCode varchar(15), seminarName varchar(250), 
					seminarSubTitle varchar(250), showReplay bit, showManageProgress bit, bundleOrderID int, handlesOwnPayment bit, isActive bit, isFeeExempt bit);
				CREATE TABLE ##tmpEnrollments2 (autoID int IDENTITY(1,1), enrollmentID int PRIMARY KEY, MCMemberID int, depomemberdataID int, firstName varchar(100), 
					lastName varchar(100), memberNumber varchar(50), company varchar(200), dateEnrolled datetime, CreditCount int, EmailCount int, 
					duration int, providerID int, ACUserID varchar(20), ZoomWebinarRegistrantID varchar(30), passed bit, attended bit, signUpOrgCode varchar(10), 
					showReplay bit, showManageProgress bit, bundleOrderID int,
					<cfif listFindNoCase("export,exportregsearch",arguments.mode)>
						attendedStart varchar(20), attendedEnd varchar(20), numReplays int, lastReplay datetime, 
					</cfif>
					seminarID int, programCode varchar(15), seminarName varchar(250), seminarSubTitle varchar(250), handlesOwnPayment bit, amountBilled decimal(14,2), 
					amountDue decimal(14,2), isActive bit, isFeeExempt bit, row int);

				SET @siteCode = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.sitecode#">;
				SELECT @siteID = siteID, @orgID = orgID from membercentral.dbo.sites where siteCode = @siteCode;
				select @participantID = dbo.fn_getParticipantIDFromOrgcode(@siteCode);

				<cfif arguments.rDateFrom NEQ ''>
					SET @rDateFrom = <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.rDateFrom#">;	
				</cfif>
				<cfif arguments.rDateTo NEQ ''>
					SET @rDateTo = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.rDateTo# 23:59">; <!--- smalldatetime - no seconds --->
				</cfif>
				<cfif arguments.rAttended NEQ ''>
					SET @rAttended = <cfqueryparam value="#arguments.rAttended#" cfsqltype="CF_SQL_BIT">;
				</cfif>
				<cfif arguments.rCredits NEQ ''>
					SET @rCredits = <cfqueryparam value="#arguments.rCredits#" cfsqltype="CF_SQL_BIT">;
				</cfif>
				<cfif arguments.pDateFrom NEQ ''>
					SET @pDateFrom = <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.pDateFrom#">;	
				</cfif>
				<cfif arguments.pDateTo NEQ ''>
					SET @pDateTo = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.pDateTo# 23:59">;
				</cfif>
				<cfif arguments.mode eq "reggrid">
					SET @memberID = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.memberID#">;
				<cfelseif listFindNoCase("grid,export,manageAttendance,massEmailInstr,massEmailMaterials,massEmailReplayLink,massEmailRegGrid,massEmailReg,massEmailCert,regForSWOD,formResponses",arguments.mode)>
					SET @seminarID = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.seminarID#">;
				</cfif>

				INSERT INTO ##tmpEnrollments (userid, enrollmentID, MCMemberID, depoMemberDataID, orgcode, dateEnrolled, duration, providerID, ACUserID, ZoomWebinarRegistrantID,
					passed, attended, attendedStart, attendedEnd, seminarID, programCode, seminarName, seminarSubTitle, showReplay, showManageProgress, bundleOrderID, 
					handlesOwnPayment, isActive, isFeeExempt)
				SELECT e.userid, e.enrollmentID, e.MCMemberID, d.depoMemberDataID, p.orgcode, e.dateEnrolled, eswl.duration, swl.providerID, eswl.ACUserID, eswl.ZoomWebinarRegistrantID,
					e.passed, eswl.attended, eswl.joinTime, eswl.exitTime, s.seminarID, s.programCode, s.seminarName, s.seminarSubTitle, 
					CASE WHEN swl.isOpen = 0 AND swl.offerReplay = 1 AND swl.isUploadedReplay = 1 AND swl.replayExpirationDate >= GETDATE() THEN 1 ELSE 0 END AS showReplay,
					CASE WHEN swl.isOpen = 0 THEN 1 ELSE 0 END as showManageProgress, e.bundleOrderID, e.handlesOwnPayment, e.isActive, e.isFeeExempt
				FROM dbo.tblEnrollments e
				INNER JOIN dbo.tblEnrollmentsSWLive eswl ON e.enrollmentID = eswl.enrollmentID 
				INNER JOIN dbo.tblSeminars s ON s.seminarID = e.seminarID
					AND s.isDeleted = 0
				INNER JOIN dbo.tblSeminarsSWLive as swl on swl.seminarID = s.seminarID
				INNER JOIN dbo.tblParticipants p ON e.participantID = p.participantID 
				INNER JOIN dbo.tblUsers u ON e.userID = u.userID
				INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID 
				WHERE 1=1
				<cfif arguments.rHideDeleted eq 1>
					AND e.isActive = 1
				</cfif>
				<cfif arguments.mode eq "reggrid">
					AND e.MCMemberID in (select memberID from membercentral.dbo.ams_members where activeMemberID = @memberID)
					AND e.participantID = @participantID
				<cfelseif listFindNoCase("grid,export,manageAttendance,massEmailInstr,massEmailMaterials,massEmailReplayLink,massEmailRegGrid,massEmailReg,massEmailCert,regForSWOD,formResponses",arguments.mode)>
					AND e.seminarID = @seminarID
				</cfif>
				<cfif arguments.rDateFrom NEQ '' and arguments.rDateTo NEQ ''>
					AND e.dateEnrolled BETWEEN @rDateFrom AND @rDateTo
				<cfelseif arguments.rDateFrom NEQ ''>
					AND e.dateEnrolled >= @rDateFrom
				<cfelseif arguments.rDateTo NEQ ''>
					AND e.dateEnrolled <= @rDateTo
				</cfif>
				<cfif arguments.mode EQ "massEmailReplayLink">
					AND (
						(swl.replayAvailability in ('attendees', 'nonattendees') AND eswl.attended = CASE WHEN swl.replayAvailability = 'attendees' THEN 1 ELSE 0 END) 
						OR 
						swl.replayAvailability = 'registrants' 
					)
				<cfelse>
					<cfif arguments.rAttended NEQ ''>
						AND eswl.attended = @rAttended
					</cfif>
				</cfif>	
				<cfif arguments.rCredits NEQ '' and arguments.rCredits eq 0>
					AND (SELECT COUNT(enrollmentID) FROM dbo.tblEnrollmentsAndCredit WHERE enrollmentID = e.enrollmentID) = @rCredits
				<cfelseif arguments.rCredits NEQ '' and arguments.rCredits eq 1>
					AND (SELECT COUNT(enrollmentID) FROM dbo.tblEnrollmentsAndCredit WHERE enrollmentID = e.enrollmentID) >= @rCredits
				</cfif>
				<cfif arguments.pDateFrom NEQ '' and arguments.pDateTo NEQ ''>
					AND swl.dateStart BETWEEN @pDateFrom AND @pDateTo
				<cfelseif arguments.pDateFrom NEQ ''>
					AND swl.dateStart >= @pDateFrom
				<cfelseif arguments.pDateTo NEQ ''>
					AND swl.dateStart <= @pDateTo
				</cfif>
				<cfif arguments.pPublisherType EQ 'P'>
					AND s.participantID = @participantID 
				<cfelseif arguments.pPublisherType EQ 'O'>
					AND s.participantID <> @participantID
					AND e.participantID = @participantID
				<cfelse>
					AND (s.participantID = @participantID OR e.participantID = @participantID)
				</cfif>
				<cfif len(arguments.pKeyword)>
					AND s.seminarName + ISNULL(' ' + s.seminarSubTitle,'') LIKE <cfqueryparam cfsqltype="cf_sql_varchar" value="%#arguments.pKeyword#%">
				</cfif>
				<cfif len(arguments.pProgramCode)>
					and s.programCode = <cfqueryparam value="#arguments.pProgramCode#" cfsqltype="cf_sql_varchar">
				</cfif>
				<cfif arguments.pHideInactive is 1>
					AND s.isPublished = 1
				</cfif>
				AND (d.adminflag2 is null or d.adminflag2 <> 'Y');

				<cfif listFindNoCase("reggrid,regsearchgrid,grid",arguments.mode)>
					IF OBJECT_ID('tempdb..##tblEnrollmentInvoices') IS NOT NULL 
						DROP TABLE ##tblEnrollmentInvoices;
					CREATE TABLE ##tblEnrollmentInvoices (enrollmentID int, invoiceID int);

					INSERT INTO ##tblEnrollmentInvoices (enrollmentID, invoiceID)
					select tmp.enrollmentID, it.invoiceID
					from ##tmpEnrollments as tmpOuter
					inner join ##tmpEnrollments as tmp on tmp.enrollmentID = tmpOuter.enrollmentID and tmp.handlesOwnPayment = 1
					cross apply memberCentral.dbo.fn_sw_enrollmentTransactions(tmp.enrollmentID,'SWL') as et
					inner join memberCentral.dbo.tr_invoiceTransactions as it on it.orgID = et.ownedByOrgID and it.transactionID = et.transactionID
						inner join memberCentral.dbo.tr_invoices as i on i.invoiceID = it.invoiceID
					inner join memberCentral.dbo.tr_invoiceStatuses as invs on invs.statusID = i.statusID
					where invs.status in ('Open','Closed','Delinquent')
					group by tmp.enrollmentID, it.invoiceID
					having sum(it.cache_invoiceAmountAfterAdjustment - it.cache_activePaymentAllocatedAmount - it.cache_pendingPaymentAllocatedAmount) > 0;
				</cfif>

				<cfif NOT listFindNoCase("reggrid,massEmailRegGrid,massEmailReg,massEmailCert",arguments.mode)>
					<cfif listFindNoCase("export,grid",arguments.mode)>
						-- get seminar fees
						IF OBJECT_ID('tempdb..##tmpSeminarsForFee') IS NOT NULL 
							DROP TABLE ##tmpSeminarsForFee; 
						IF OBJECT_ID('tempdb..##tmpSeminarsForFeeResult') IS NOT NULL 
							DROP TABLE ##tmpSeminarsForFeeResult; 
						IF OBJECT_ID('tempdb..##tblEnrollmentFees') IS NOT NULL 
							DROP TABLE ##tblEnrollmentFees;
						create table ##tmpSeminarsForFee (programID int PRIMARY KEY);
						create table ##tmpSeminarsForFeeResult (programID int, enrollmentID int, depoMemberDataID int, transactionID int, TransactionIDForRateAdjustment int);
						CREATE TABLE ##tblEnrollmentFees (enrollmentID int PRIMARY KEY, TransactionIDForRateAdjustment int, totalRegFee decimal(18,2), regFeePaid decimal(18,2));

						INSERT INTO ##tmpSeminarsForFee (programID)
						VALUES (@seminarID);

						EXEC memberCentral.dbo.sw_enrollmentTransactionsBySeminarBulk @swType='SWL';

						INSERT INTO ##tblEnrollmentFees (enrollmentID, TransactionIDForRateAdjustment, totalRegFee, regFeePaid)
						select fees.enrollmentID, max(fees.TransactionIDForRateAdjustment), sum(ts.cache_amountAfterAdjustment), sum(ts.cache_activePaymentAllocatedAmount)
						from ##tmpSeminarsForFeeResult as fees
						inner join memberCentral.dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.transactionID = fees.transactionid
						group by fees.enrollmentID;
					</cfif>
					
					INSERT INTO ##tmpEnrollments2 (enrollmentID, MCMemberID, depomemberdataID, firstName, lastName, memberNumber, company, dateEnrolled, 
						CreditCount, EmailCount, duration, providerID, ACUserID, ZoomWebinarRegistrantID, passed, attended, signUpOrgCode, showReplay, showManageProgress,
						<cfif listFindNoCase("export,exportregsearch",arguments.mode)>
							attendedStart, attendedEnd, numReplays, lastReplay, 
						</cfif>
						seminarID, programCode,<cfif listFindNoCase("regsearchgrid,exportregsearch",arguments.mode)>seminarName,seminarSubTitle,</cfif> 
						bundleOrderID, handlesOwnPayment, amountBilled, amountDue, isActive, isFeeExempt, row)
					SELECT tmp.enrollmentID, m2.memberID, tmp.depomemberdataid, m2.FirstName, m2.LastName, m2.membernumber, m2.Company, tmp.dateEnrolled,
						(SELECT COUNT(*) FROM dbo.tblEnrollmentsAndCredit WHERE enrollmentID = tmp.enrollmentID) AS CreditCount,
						(SELECT COUNT(*) FROM dbo.tblLogSWLive WHERE enrollmentID = tmp.enrollmentID AND seminarID = tmp.seminarID AND contact LIKE '%@%') AS EmailCount, 
						tmp.duration, tmp.providerID, tmp.ACUserID, tmp.ZoomWebinarRegistrantID, tmp.passed, tmp.attended, tmp.orgCode, tmp.showReplay, tmp.showManageProgress, 
						<cfif listFindNoCase("export,exportregsearch",arguments.mode)>
							tmp.attendedStart, tmp.attendedEnd, eswl.numReplays, eswl.lastReplay,
						</cfif>
						tmp.seminarID, tmp.programCode,
						<cfif listFindNoCase("regsearchgrid,exportregsearch",arguments.mode)>
							tmp.seminarName, tmp.seminarSubTitle,
						</cfif>
						tmp.bundleOrderID, tmp.handlesOwnPayment, 
						CASE WHEN tmp.handlesOwnPayment = 0 
							THEN ISNULL((SELECT SUM(dt.amountBilled + dt.salesTaxAmount) 
										FROM trialsmith.dbo.depoTransactionsApplications as dta 
										INNER JOIN trialsmith.dbo.depoTransactions as dt on dt.TransactionID = dta.transactionID
										WHERE dta.itemID = tmp.enrollmentID
										AND dta.itemType = 'SWE'),0)
							ELSE regFee.totalRegFee END as amountBilled,
						CASE WHEN tmp.handlesOwnPayment = 1 THEN regFee.totalRegFee-regFee.regFeePaid ELSE 0 END as amountDue, tmp.isActive, tmp.isFeeExempt, 
						ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)# #arguments.direct#) AS row
					FROM ##tmpEnrollments AS tmp
					INNER JOIN dbo.tblEnrollmentsSWLive eswl ON eswl.enrollmentID = tmp.enrollmentID 
					INNER JOIN memberCentral.dbo.ams_members as m on m.memberID = tmp.MCMemberID
					INNER JOIN memberCentral.dbo.ams_members as m2 on m2.memberID = m.activeMemberID
					<cfif listFindNoCase("export,grid",arguments.mode)>
						LEFT OUTER JOIN ##tblEnrollmentFees as regFee on regFee.enrollmentID = tmp.enrollmentID
					<cfelse>
						OUTER APPLY memberCentral.dbo.fn_sw_totalRegFeeAndPaid(tmp.enrollmentID,'SWL') as regFee
					</cfif>;

					SET @totalCount = @@ROWCOUNT;

					<cfif listFindNoCase("export,exportregsearch",arguments.mode)>
						IF OBJECT_ID('tempdb..####tmpSWExport#local.tmpSuffix#') IS NOT NULL
							DROP TABLE ####tmpSWExport#local.tmpSuffix#;
						IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL 
							DROP TABLE ##tmpMembers;
						IF OBJECT_ID('tempdb..##tmp_membersForFS') IS NOT NULL
							DROP TABLE ##tmp_membersForFS;
						IF OBJECT_ID('tempdb..##tmp_CF_ItemIDs') IS NOT NULL
							DROP TABLE ##tmp_CF_ItemIDs;
						IF OBJECT_ID('tempdb..##tmp_CF_FieldData') IS NOT NULL
							DROP TABLE ##tmp_CF_FieldData;
						IF OBJECT_ID('tempdb..##tmpSWLRegFieldData') IS NOT NULL
							DROP TABLE ##tmpSWLRegFieldData;
						IF OBJECT_ID('tempdb..####tmpSWLRegFieldData#local.tmpSuffix#') IS NOT NULL
							DROP TABLE ####tmpSWLRegFieldData#local.tmpSuffix#;
						CREATE TABLE ##tmpMembers (MFSAutoID int IDENTITY(1,1) not null);
						CREATE TABLE ##tmp_membersForFS (memberID int PRIMARY KEY, FirstName varchar(75), LastName varchar(75));
						CREATE TABLE ##tmp_CF_ItemIDs (itemID int, itemType varchar(20));
						CREATE TABLE ##tmp_CF_FieldData (fieldID int, fieldValue varchar(max), amount decimal(14,2), itemID int);

						DECLARE @fieldsetID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldSetID#">,
							@outputFieldsXML xml;

						-- get fieldset data
						INSERT INTO ##tmp_membersForFS (memberID, FirstName, LastName)
						SELECT DISTINCT MCMemberID, firstName, lastName
						FROM ##tmpEnrollments2
						WHERE MCMemberID IS NOT NULL;

						EXEC memberCentral.dbo.ams_getMemberDataByFieldSets @orgID=@orgID, @fieldsetIDList=@fieldsetID, @existingFields='',
							@ovNameFormat=NULL, @ovMaskEmails=NULL, @membersTableName='##tmp_membersForFS', @membersResultTableName='##tmpMembers',
							@linkedMembers=0, @mode='export', @outputFieldsXML=@outputFieldsXML OUTPUT;

						<cfif arguments.fbFormID>
							DECLARE @SWFormID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fbFormID#">,
								@finalResponseTable varchar(60) = '####SWFBFinalResp' + replace(NEWID(),'-','');

							EXEC dbo.sw_getRegistrantFormResponses @formID=@SWFormID, @onlyFormData=1, @finalResponseTable=@finalResponseTable;
						</cfif>

						-- swl program custom fields
						INSERT INTO ##tmp_CF_ItemIDs (itemID, itemType)
						SELECT DISTINCT enrollmentID, 'SWLRegCustom'
						FROM ##tmpEnrollments2;

						EXEC memberCentral.dbo.cf_getFieldData;

						SELECT fd.itemID AS enrollmentID, replace(f.fieldReference,',','') as titleOnInvoice, fd.fieldValue as answer
						INTO ##tmpSWLRegFieldData
						FROM ##tmp_CF_FieldData AS fd
						INNER JOIN memberCentral.dbo.cf_fields as f on f.fieldID = fd.fieldID
						<cfif listLen(arguments.exportFieldIDList)>
							WHERE f.fieldID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="yes" value="#arguments.exportFieldIDList#">)
						</cfif>;

						-- swl program custom fields pivoted
						select @swlFieldList = COALESCE(@swlFieldList + ',', '') + quoteName(titleonInvoice) from ##tmpSWLRegFieldData group by titleOnInvoice;
						IF left(@swlFieldList,1) = ','
							select @swlFieldList = right(@swlFieldList,len(@swlFieldList)-1);
						IF len(@swlFieldList) > 0 BEGIN
							set @fullsql = '';
							select @fullsql = @fullsql + '
								select * 
								into ####tmpSWLRegFieldData#local.tmpSuffix#
								from (
									select enrollmentID, titleOnInvoice, answer
									from ##tmpSWLRegFieldData
								) as reg
								PIVOT (min(answer) for titleonInvoice in (' + @swlFieldList + ')) as p ';
							EXEC(@fullsql);
						END
						ELSE
							SELECT enrollmentID 
							INTO ####tmpSWLRegFieldData#local.tmpSuffix# 
							FROM ##tmpEnrollments2 
							WHERE 0=1;

						SET @fullsql = 'SELECT tmp.dateEnrolled as Registered, tmp.FirstName, tmp.LastName,
							<cfif arguments.mode EQ "exportregsearch">
								tmp.ProgramCode, tmp.SeminarName as ProgramName, tmp.seminarSubTitle as ProgramSubTitle, 
							</cfif>
							m.*, tmp.CreditCount as [NumberOfCredits], tmp.EmailCount as [NumberOfCommunications],
							case when tmp.attended = 1 then ''Yes'' else ''No'' end as [Attended], tmp.AttendedStart, tmp.AttendedEnd,
							tmp.duration as AttendedMinutes, 
							case when tmp.passed = 1 then ''Yes'' else ''No'' end as [Passed], tla.Description as EnrolledOnSite, 
							case when tmp.isActive = 1 then ''Yes'' else ''No'' end as [isActive],
							case when tmp.isFeeExempt = 1 then ''Yes'' else ''No'' end as [isFeeExempt],
							case when exists(select top 1 transactionID from trialsmith.dbo.depotransactions 
								where depoMemberDataID = tmp.depoMemberDataID and AccountCode = ''7002'' and [Description] like ''SWL-'' + cast(tmp.seminarID as varchar(5)) + ''-CD%'') 
								then ''Yes'' else ''No'' end AS [PurchasedCD], isnull(tmp.amountBilled,0) as [AmountBilled], isnull(tmp.amountDue,0) as [AmountDue],
							tmp.numReplays as [Number of Replays], tmp.lastReplay as [Last Replay]';
						<cfif arguments.fbFormID>
							SET @fullsql = @fullsql + ', SWFR.*';
						</cfif>
						SET @fullsql = @fullsql + case when len(@swlFieldList)>0 then ', swfd.' + replace(@swlFieldList,',',',swfd.') else '' end;
						SET @fullsql = @fullsql + ' 
							INTO ####tmpSWExport#local.tmpSuffix#
							FROM ##tmpEnrollments2 as tmp
							LEFT OUTER JOIN trialsmith.dbo.depoTLA as tla on tla.[state] = tmp.SignUpOrgCode
							LEFT OUTER JOIN ##tmpMembers AS m ON m.memberID = tmp.MCMemberID
							LEFT OUTER JOIN ####tmpSWLRegFieldData#local.tmpSuffix# as swfd on swfd.enrollmentID = tmp.enrollmentID
							<cfif arguments.fbFormID>
								LEFT OUTER JOIN ' + @finalResponseTable + ' AS SWFR ON SWFR.SWFREnrollmentID = tmp.enrollmentID
							</cfif>
							ORDER BY tmp.row';

						EXEC(@fullsql);

						ALTER TABLE ####tmpSWExport#local.tmpSuffix# DROP COLUMN memberID;
						<cfif arguments.fbFormID>
							IF EXISTS(SELECT TOP 1 column_id FROM tempdb.sys.columns where [name] = 'SWFREnrollmentID' and object_id = object_id('tempdb..####tmpSWExport#local.tmpSuffix#')) 
								ALTER TABLE ####tmpSWExport#local.tmpSuffix# DROP COLUMN SWFREnrollmentID;
						</cfif>
						
						DECLARE @selectsql varchar(max) = 'SELECT *, ROW_NUMBER() OVER(order by Registered desc, LastName, FirstName) as mcCSVorder *FROM* ####tmpSWExport#local.tmpSuffix#';
						EXEC membercentral.dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#arguments.folderPathUNC#\#arguments.reportFileName#', @returnColumns=0;
						
						IF OBJECT_ID('tempdb..####tmpSWExport#local.tmpSuffix#') IS NOT NULL
						DROP TABLE ####tmpSWExport#local.tmpSuffix#;
						IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL 
							DROP TABLE ##tmpMembers;
						IF OBJECT_ID('tempdb..##tmp_membersForFS') IS NOT NULL
							DROP TABLE ##tmp_membersForFS;
						IF OBJECT_ID('tempdb..##tmp_CF_ItemIDs') IS NOT NULL
							DROP TABLE ##tmp_CF_ItemIDs;
						IF OBJECT_ID('tempdb..##tmp_CF_FieldData') IS NOT NULL
							DROP TABLE ##tmp_CF_FieldData;
						IF OBJECT_ID('tempdb..##tmpSWLRegFieldData') IS NOT NULL
							DROP TABLE ##tmpSWLRegFieldData;
						IF OBJECT_ID('tempdb..####tmpSWLRegFieldData#local.tmpSuffix#') IS NOT NULL
							DROP TABLE ####tmpSWLRegFieldData#local.tmpSuffix#;
						<cfif arguments.fbFormID>
							IF OBJECT_ID('tempdb..' + @finalResponseTable) IS NOT NULL
								EXEC('DROP TABLE ' + @finalResponseTable);
						</cfif>

					<cfelseif listFindNoCase("grid,regsearchgrid",arguments.mode)>
						DECLARE @posStart int, @posStartAndCount int;
						SET @posStart = <cfqueryparam value="#arguments.posStart#" cfsqltype="CF_SQL_INTEGER">;
						SET @posStartAndCount = @posStart + <cfqueryparam value="#arguments.count#" cfsqltype="CF_SQL_INTEGER">;

						SELECT 
							tmp.seminarName, tmp.seminarSubTitle, 
							tmp.enrollmentID, tmp.seminarID, tmp.depomemberdataID, tmp.firstName, tmp.lastName, tmp.memberNumber, tmp.company, 
							tmp.dateEnrolled, tmp.CreditCount, tmp.EmailCount, tmp.duration, tmp.signUpOrgCode, tmp.providerID, 
							tmp.ACUserID, tmp.ZoomWebinarRegistrantID, tmp.attended, tmp.passed, @totalCount as totalCount, tmp.showReplay, tmp.showManageProgress,
							case when exists (select 1 from ##tmpEnrollments2 where showReplay = 1) then 1 else 0 end as showReplayCol,
							case when exists (select 1 from ##tmpEnrollments2 where showManageProgress = 1) then 1 else 0 end as showManageProgressCol,
							tmp.amountBilled, tmp.amountDue, tla.Description as signUpOrgName, tmp.isActive, tmp.isFeeExempt, tmp.bundleOrderID, tmp.handlesOwnPayment, 
							case when tmp.handlesOwnPayment = 1 then case when tmp.amountDue > 0 
																			then (select substring((
																					select ','+ cast(invoiceID as varchar(10)) AS [text()]
																					from ##tblEnrollmentInvoices
																					where enrollmentID = tmp.enrollmentID
																					For XML PATH ('')
																					), 2, 2000)) 
																			else '' end
								else null end as invoicesDue, m.activeMemberID as memberID, swl.ACSeminarSCOID, swl.ZoomWebinarID
						FROM ##tmpEnrollments2 as tmp
						INNER JOIN dbo.tblSeminarsSWLive as swl on swl.seminarID = tmp.seminarID
						LEFT OUTER JOIN trialsmith.dbo.depoTLA as tla on tla.[state] = tmp.SignUpOrgCode
						LEFT OUTER JOIN memberCentral.dbo.ams_members as m on m.orgID = @orgID AND m.memberID = tmp.MCMemberID
						WHERE tmp.row > @posStart AND tmp.row <= @posStartAndCount
						ORDER BY tmp.row;
					<cfelseif arguments.mode eq 'manageAttendance'>
						SELECT tmp.enrollmentID, tmp.seminarID, tmp.depomemberdataID, tmp.firstName, tmp.lastName, tmp.memberNumber, 
							tmp.dateEnrolled, eswl.joinTime, eswl.exitTime, eswl.duration, eswl.completedPolling, tmp.signUpOrgCode
						FROM ##tmpEnrollments2 as tmp
						INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON eswl.enrollmentID = tmp.enrollmentID
						ORDER BY tmp.lastName, tmp.firstName, tmp.memberNumber;
					<cfelseif ListFindNoCase('massEmailInstr,massEmailMaterials,massEmailReplayLink',arguments.mode)>
						SELECT enrollmentID, depomemberdataID
						FROM ##tmpEnrollments2;
					<cfelseif arguments.mode eq 'regForSWOD'>
						SELECT tmp.enrollmentID, tmp.depomemberdataID, tmp.signUpOrgCode,
							CASE WHEN tmp.signUpOrgCode = @siteCode THEN 1 ELSE 0 END AS isPublisherSiteRegistrant,
							CASE WHEN ISNULL(e.enrollmentID,0) > 0 THEN 1 ELSE 0 END AS isRegisteredForSWOD,
							CASE WHEN ISNULL(soi.participantID,0) > 0 THEN 1 ELSE 0 END AS isOptedForSWOD,
							swod.allowSyndication
						FROM ##tmpEnrollments2 AS tmp 
						INNER JOIN dbo.tblSeminarsSWOD AS swod ON swod.convertedFromSeminarID = tmp.seminarID
						LEFT JOIN dbo.tblSeminarsOptIn AS soi ON soi.seminarID = swod.seminarID
							AND soi.participantID = dbo.fn_getParticipantIDFromOrgcode(tmp.signUpOrgCode) AND soi.IsActive = 1
						LEFT JOIN dbo.tblEnrollments AS e 
							INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID
							ON e.seminarID = swod.seminarID
							AND e.isActive = 1
							AND u.depoMemberDataID = tmp.depoMemberDataID;
					<cfelseif arguments.mode eq 'formResponses'>
						DECLARE @formID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fbFormID#">;
						<cfif arguments.fbExportType eq "csv">
							EXEC dbo.sw_exportRegistrantFormResponses @formID=@formID, @filename='#arguments.folderPathUNC#\#arguments.reportFileName#';
							SELECT 1 AS "success";
						<cfelseif arguments.fbExportType eq "pdf">
							DECLARE @xmlResult xml;
							EXEC dbo.sw_getRegistrantsFormResponseSummaryXML @formID=@formID, @xmlResult=@xmlResult OUTPUT;
							SELECT @xmlResult AS xmlResult;
						</cfif>
					</cfif>

				<cfelseif listFindNoCase("massEmailReg,massEmailRegGrid,massEmailCert",arguments.mode)>
					DECLARE @emailTagTypeID int;
					SET @emailTagTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.emailTagTypeID#">;
						
					<cfif arguments.mode eq 'massEmailRegGrid'>
						DECLARE @posStart int, @posStartAndCount int;
						SET @posStart = <cfqueryparam value="#arguments.posStart#" cfsqltype="CF_SQL_INTEGER">;
						SET @posStartAndCount = @posStart + <cfqueryparam value="#arguments.count#" cfsqltype="CF_SQL_INTEGER">;

						IF OBJECT_ID('tempdb..##tblRegistrants') IS NOT NULL 
							DROP TABLE ##tblRegistrants;
						CREATE TABLE ##tblRegistrants (enrollmentID int, depomemberdataID int, firstname varchar(75), lastname varchar(75), membernumber varchar(50),
							company varchar(200), email varchar(400), row int);

						INSERT INTO ##tblRegistrants
						SELECT e.enrollmentID, e.depomemberdataID, m2.firstname, m2.lastName, m2.memberNumber, m2.company, me.email,
							ROW_NUMBER() OVER (order by (m2.lastName + m2.firstname + m2.memberNumber) #arguments.direct#) as row
						FROM ##tmpEnrollments as e
						INNER JOIN memberCentral.dbo.ams_members as m on m.memberID = e.MCMemberID
						INNER JOIN memberCentral.dbo.ams_members as m2 on m2.memberID = m.activeMemberID
						INNER JOIN memberCentral.dbo.ams_memberEmails as me ON me.orgID = @orgID
							AND me.memberID = m2.memberID
						INNER JOIN memberCentral.dbo.ams_memberEmailTags as metag on metag.orgID = @orgID 
							and metag.memberID = me.memberID
							AND metag.emailTypeID = me.emailTypeID
						INNER JOIN memberCentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = @orgID
							AND metagt.emailTagTypeID = metag.emailTagTypeID
							AND metagt.emailTagTypeID = @emailTagTypeID;

						SELECT @totalCount = @@ROWCOUNT;

						SELECT enrollmentID, depomemberdataID, firstname, lastname, membernumber, company, email, @totalCount as totalCount
						FROM ##tblRegistrants
						WHERE row > @posStart
						AND row <= @posStartAndCount
						ORDER by row;

						IF OBJECT_ID('tempdb..##tblRegistrants') IS NOT NULL 
							DROP TABLE ##tblRegistrants;
					<cfelse>
						DECLARE @membersWithEmail int;

						SELECT @membersWithEmail = count(*)
						FROM ##tmpEnrollments as e
						INNER JOIN memberCentral.dbo.ams_members as m on m.memberID = e.MCMemberID
						INNER JOIN memberCentral.dbo.ams_members as m2 on m2.memberID = m.activeMemberID
						INNER JOIN memberCentral.dbo.ams_memberEmails as me on me.orgID = @orgID
							AND me.memberID = m2.memberID and me.email <> ''
						INNER JOIN memberCentral.dbo.ams_memberEmailTags as metag on metag.orgID = @orgID 
							and metag.memberID = me.memberID
							AND metag.emailTypeID = me.emailTypeID
						INNER JOIN memberCentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = @orgID
							AND metagt.emailTagTypeID = metag.emailTagTypeID
							AND metagt.emailTagTypeID = @emailTagTypeID;

						SELECT distinct e.enrollmentID, e.depomemberdataID, m2.membernumber, @membersWithEmail as membersWithEmail
						FROM ##tmpEnrollments as e
						INNER JOIN memberCentral.dbo.ams_members as m on m.memberID = e.MCMemberID
						INNER JOIN memberCentral.dbo.ams_members as m2 on m2.memberID = m.activeMemberID
						ORDER BY e.enrollmentID;
					</cfif>

				<cfelseif arguments.mode eq 'reggrid'>
					INSERT INTO ##tmpEnrollments2 (enrollmentID, depomemberdataID, dateEnrolled, CreditCount, EmailCount, duration, providerID, ACUserID, 
						ZoomWebinarRegistrantID, passed, attended, signUpOrgCode, showReplay, showManageProgress, seminarID, seminarName, seminarSubTitle, 
						bundleOrderID, handlesOwnPayment, amountBilled, amountDue, isActive, isFeeExempt, row)
					SELECT tmp.enrollmentID, tmp.depomemberdataid, tmp.dateEnrolled,
						(SELECT COUNT(*) FROM dbo.tblEnrollmentsAndCredit WHERE enrollmentID = tmp.enrollmentID) AS CreditCount,
						(SELECT COUNT(*) FROM dbo.tblLogSWLive WHERE enrollmentID = tmp.enrollmentID AND seminarID = tmp.seminarID AND contact LIKE '%@%') AS EmailCount, 
						tmp.duration, tmp.providerID, tmp.ACUserID, tmp.ZoomWebinarRegistrantID, tmp.passed, tmp.attended, tmp.orgCode, tmp.showReplay, tmp.showManageProgress,
						tmp.seminarID, tmp.seminarName, tmp.seminarSubTitle, tmp.bundleOrderID, tmp.handlesOwnPayment, 
						CASE WHEN tmp.handlesOwnPayment = 0 
							THEN ISNULL((SELECT SUM(dt.amountBilled + dt.salesTaxAmount) 
										FROM trialsmith.dbo.depoTransactionsApplications as dta 
										INNER JOIN trialsmith.dbo.depoTransactions as dt on dt.TransactionID = dta.transactionID
										WHERE dta.itemID = tmp.enrollmentID
										AND dta.itemType = 'SWE'),0) 
							ELSE regFee.totalRegFee END as amountBilled, 
						CASE WHEN tmp.handlesOwnPayment = 1 THEN regFee.totalRegFee-regFee.regFeePaid ELSE 0 END as amountDue, tmp.isActive, tmp.isFeeExempt, 
						ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)# #arguments.direct#) AS row
					FROM ##tmpEnrollments AS tmp
					OUTER APPLY memberCentral.dbo.fn_sw_totalRegFeeAndPaid(tmp.enrollmentID,'SWL') as regFee;

					SET @totalCount = @@ROWCOUNT;

					SELECT tmp.enrollmentID, tmp.seminarID, tmp.seminarName, tmp.seminarSubTitle, tmp.depomemberdataID, tmp.dateEnrolled, tmp.CreditCount, tmp.EmailCount, 
						tmp.duration, tmp.signUpOrgCode, tmp.providerID, tmp.ACUserID, tmp.ZoomWebinarRegistrantID, tmp.attended, tmp.passed, tmp.showReplay, tmp.showManageProgress, 
						@totalCount as totalCount,
						case when exists (select 1 from ##tmpEnrollments2 where showReplay = 1) then 1 else 0 end as showReplayCol,
						case when exists (select 1 from ##tmpEnrollments2 where showManageProgress = 1) then 1 else 0 end as showManageProgressCol,
						tmp.bundleOrderID, tmp.handlesOwnPayment, tmp.amountBilled, tmp.amountDue, tla.Description as signUpOrgName, tmp.isActive, tmp.isFeeExempt, 
						case when tmp.handlesOwnPayment = 1 THEN case when tmp.amountDue > 0 
																		then (select substring((
																				select ','+ cast(invoiceID as varchar(10)) AS [text()]
																				from ##tblEnrollmentInvoices
																				where enrollmentID = tmp.enrollmentID
																				For XML PATH ('')
																				), 2, 2000)) 
																		else '' end
							else null end as invoicesDue, swl.ACSeminarSCOID, swl.ZoomWebinarID
					FROM ##tmpEnrollments2 as tmp
					INNER JOIN dbo.tblSeminarsSWLive as swl on swl.seminarID = tmp.seminarID
					LEFT OUTER JOIN trialsmith.dbo.depoTLA as tla on tla.[state] = tmp.SignUpOrgCode
					WHERE tmp.row > #arguments.posStart# AND tmp.row <= #arguments.posStart + arguments.count#
					ORDER BY tmp.row;
				<cfelse>
					SELECT enrollmentID 
					FROM ##tmpEnrollments;
				</cfif>
				
				IF OBJECT_ID('tempdb..##tmpEnrollments') IS NOT NULL 
					DROP TABLE ##tmpEnrollments;
				IF OBJECT_ID('tempdb..##tmpEnrollments2') IS NOT NULL 
					DROP TABLE ##tmpEnrollments2;
				<cfif listFindNoCase("reggrid,regsearchgrid,grid",arguments.mode)>
					IF OBJECT_ID('tempdb..##tblEnrollmentInvoices') IS NOT NULL 
						DROP TABLE ##tblEnrollmentInvoices;
				</cfif>

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
		
		<cfif listFindNoCase("grid,reggrid,regsearchgrid,manageAttendance,massEmailInstr,massEmailMaterials,massEmailReplayLink,massEmailRegGrid,massEmailReg,massEmailCert,regForSWOD,formResponses", arguments.mode)>
			<cfreturn local.qryEnrollments>
		</cfif>		
	</cffunction>
	
	<cffunction name="getAllRegistrants" access="public" output="false" returntype="Any">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="folderPathUNC" type="string" required="false" default="">
		<cfargument name="reportFileName" type="string" required="false" default="">

		<cfset var local = StructNew()>

		<cfset local.tmpSuffix = replace(createUUID(),'-','','ALL')>
		<cfquery name="local.qryEnrollments" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @seminarID int = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.seminarID#">;

				IF OBJECT_ID('tempdb..####tmpSWExport#local.tmpSuffix#') IS NOT NULL
					DROP TABLE ####tmpSWExport#local.tmpSuffix#;

				SELECT o.orgcode, m.lastname, m.firstname, m.company, me.email, eswl.attended
				INTO ####tmpSWExport#local.tmpSuffix#
				FROM seminarweb.dbo.tblEnrollments AS e
				INNER JOIN seminarweb.dbo.tblEnrollmentsSWLive AS eswl ON eswl.enrollmentID = e.enrollmentID
				INNER JOIN membercentral.dbo.ams_members AS m ON m.memberID = e.MCMemberID
				INNER JOIN membercentral.dbo.organizations AS o ON o.orgID = m.orgID
				INNER JOIN membercentral.dbo.ams_memberEmailTags AS met ON met.memberID = m.memberID
				INNER JOIN membercentral.dbo.ams_memberEmailTagTypes AS mett ON mett.emailTagTypeID = met.emailTagTypeID AND mett.emailTagType = 'Primary'
				INNER JOIN membercentral.dbo.ams_memberEmails AS me ON me.orgID = o.orgID AND me.memberID = m.memberID AND me.emailTypeID = met.emailTypeID
				WHERE e.seminarID = @seminarID;

				DECLARE @selectsql varchar(max) = 'SELECT *, ROW_NUMBER() OVER(order by LastName, FirstName) as mcCSVorder *FROM* ####tmpSWExport#local.tmpSuffix#';
				EXEC membercentral.dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#arguments.folderPathUNC#\#arguments.reportFileName#', @returnColumns=0;

				IF OBJECT_ID('tempdb..####tmpSWExport#local.tmpSuffix#') IS NOT NULL
				DROP TABLE ####tmpSWExport#local.tmpSuffix#;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>
	
	<cffunction name="getSeminarsOptedInByOrgcode" access="public" returntype="query" output="no">
		<cfargument name="orgcode" type="string" required="yes">

		<cfset var local = structnew()>

		<cfstoredproc procedure="swl_getSeminarsOptedInByOrgcode" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.orgcode#" null="No">
			<cfprocresult name="local.qrySeminars" resultset="1">
		</cfstoredproc>

		<cfreturn local.qrySeminars>
	</cffunction>

	<cffunction name="getSeminarsOptedOutByOrgcode" access="public" returntype="query" output="no">
		<cfargument name="orgcode" type="string" required="yes">

		<cfset var local = structnew()>

		<cfstoredproc procedure="swl_getSeminarsOptedOutByOrgcode" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.orgcode#" null="No">
			<cfprocresult name="local.qrySeminars" resultset="1">
		</cfstoredproc>

		<cfreturn local.qrySeminars>
	</cffunction>

	<cffunction name="getTimeinAllTimeZones" access="private" returntype="struct" output="no">
		<cfargument name="CentralStartTimeToConvert" type="date" required="yes">
		<cfargument name="CentralEndTimeToConvert" type="date" required="yes">

		<cfset var local = structnew()>
		<cfset local.strMasterTimeZones = CreateObject("component","model.seminarweb.SWCommon").getMasterTimeZones()>

		<!--- length of time span --->
		<cfset local.ProgramLengthInMinutes = DateDiff("n",arguments.CentralStartTimeToConvert,arguments.CentralEndTimeToConvert)>

		<!--- Get time in diff timezones --->
		<!--- to get true time zone parsing need to use z in the formatter and adding the CDT/CST to the original start date parser --->
		<cfset local.returnStruct = { "START":{}, "END":{} }>
		<cfset local.timeFormatter = createObject('java','java.text.SimpleDateFormat').init("M/d/yyyy h:mm aa")>
		<cfset local.timeFormatter.setTimeZone("US/Central")>
		<cfset local.startingDate = local.timeFormatter.parse(datetimeformat(arguments.CentralStartTimeToConvert,"m/d/yyyy h:nn tt"))>
		<cfloop collection="#local.strMasterTimeZones#" item="local.tz">
			<cfset local.timeFormatter.setTimeZone(local.strMasterTimeZones[local.tz].id)>
			<cfset local.returnStruct.start[local.tz] = local.timeFormatter.format(local.startingDate)>
			<cfset local.returnStruct.end[local.tz] = DateTimeFormat(DateAdd("n",local.ProgramLengthInMinutes,local.returnStruct.start[local.tz]),"m/d/yyyy h:nn tt")>
		</cfloop>	

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="sendTestInvitationEmail" access="public" returntype="void" output="no">
		<cfargument name="seminarIDlist" type="string" required="yes">
		<cfargument name="testEmail" type="string" required="yes">
		<cfargument name="performedBy" type="numeric" required="yes">
		<cfargument name="outgoingType" type="string" required="yes">
		<cfargument name="orgcode" type="string" required="yes">
		<cfargument name="subject" type="string" required="yes">
		<cfargument name="preheaderText" type="string" required="true">
		<cfargument name="template" type="string" required="true">

		<cfset var local = structnew()>
		<cfset local.objSWL = CreateObject("component","model.seminarweb.SWLiveSeminars")>
		
		<cfif len(arguments.testEmail) is 0 or NOT IsValid("regex",arguments.testEmail,application.regEx.email)>
			<cfreturn>
		</cfif>

		<cfif ListLen(arguments.seminarIDlist) EQ 1>
			<cfset local.utm_campaign = local.objSWL.getSeminarBySeminarID(seminarID=val(arguments.seminarIDlist)).seminarName>
		<cfelse>
			<cfset local.utm_campaign = 'Multiple Programs'>
		</cfif>

		<cfset local.qrySend = generateInvitationTable(seminarIDlist=arguments.seminarIDlist,orgcode=arguments.orgcode)>
		<cfoutput query="local.qrySend" group="orgcode">
			<cfquery name="local.qrySendIndiv" dbtype="query">
				select seminarID
				from [local].qrySend
				where orgcode = '#local.qrySend.orgcode#'
			</cfquery>
			
			<cfif arguments.template eq '2021'>
				<cfset local.strEmailContent = generateInvitationEmailFor2021(seminarIDlist=valuelist(local.qrySendIndiv.seminarID), orgcode=local.qrySend.orgcode, subject=arguments.subject, preheaderText=arguments.preheaderText)>
			<cfelseif arguments.template eq '2020'>
				<cfset local.strEmailContent = generateInvitationEmailFor2020(seminarIDlist=valuelist(local.qrySendIndiv.seminarID), orgcode=local.qrySend.orgcode, subject=arguments.subject)>
			<cfelseif arguments.template eq '2017NONSAE'>
				<cfset local.strEmailContent = generateInvitationEmailFor2017NONSAE(seminarIDlist=valuelist(local.qrySendIndiv.seminarID), orgcode=local.qrySend.orgcode, subject=arguments.subject)>
			</cfif>

			<cfif application.MCEnvironment neq "production">
				<cfset arguments.testEmail = "<EMAIL>">
			</cfif>

			<cfset local.strEmailContent.html = application.objEmailWrapper.appendUTMCodesToLinks(htmlcontent=local.strEmailContent.html, utm_campaign=local.utm_campaign,
				utm_source="SWL Marketing", utm_medium="email", utm_content="#DateFormat(Now(),'YYYY-MM-DD')#-#local.strEmailContent.subject#")>

			<!--- send email --->
			<cfmail to="#arguments.testEmail#" from="<EMAIL> (""#local.strEmailContent.fromName#"")" subject="**TEST** #local.strEmailContent.subject#" mailerid="SeminarWeb" type="html" charset="utf-8">
				#local.strEmailContent.html#
			</cfmail>
			
			<cfoutput>
			<cfset local.objSWL.logAction(local.qrySend.seminarID,arguments.outgoingType,arguments.performedBy,arguments.testEmail,0,0)>
			</cfoutput>
		</cfoutput>
	</cffunction>

	<cffunction name="generateInvitationTable" access="private" returntype="query" output="no">
		<cfargument name="seminarIDlist" type="string" required="yes">
		<cfargument name="orgcode" type="string" required="yes">

		<cfset var local = structnew()>
		<cfset local.objSWPAdmin = CreateObject("component","model.admin.seminarweb.seminarWebParticipants")>

		<cfset local.tmpOptedIn = ArrayNew(1)>
		<cfset local.tmpArr = ListToArray(arguments.seminarIDlist)>
		<cfset ArraySet(local.tmpOptedIn,1,ArrayMax(local.tmpArr),'')>
		<cfloop list="#arguments.seminarIDlist#" index="local.thisSID">
			<cfset local.tmpOptedIn[local.thisSID] = local.objSWPAdmin.getParticipantsOptedIntoSeminar(seminarID=local.thisSID)>
		</cfloop>
		<cfset local.qrySend = QueryNew("orgcode,seminarID","varchar,integer")>

		<cfif len(arguments.orgcode)>
			<cfloop list="#arguments.seminarIDlist#" index="local.thisSID">
				<cfset local.tmpqry = local.tmpOptedIn[local.thisSID]>
				<cfif listFindNoCase(valuelist(local.tmpqry.orgcode),arguments.orgcode)>
					<cfset QueryAddRow(local.qrySend)>
					<cfset QuerySetCell(local.qrySend,"orgcode",UCASE(arguments.orgcode))>
					<cfset QuerySetCell(local.qrySend,"seminarID",local.thisSID)>
				</cfif>
			</cfloop>
		<cfelse>
			<cfset local.qryAssociations = local.objSWPAdmin.getParticipantsByFormat('SWL')>
			<cfloop query="local.qryAssociations">
				<cfset local.tmpOrgcode = UCASE(local.qryAssociations.orgcode)>
				<cfloop list="#arguments.seminarIDlist#" index="local.thisSID">
					<cfset local.tmpqry = local.tmpOptedIn[local.thisSID]>
					<cfif listFindNoCase(valuelist(local.tmpqry.orgcode),local.tmpOrgcode)>
						<cfset QueryAddRow(local.qrySend)>
						<cfset QuerySetCell(local.qrySend,"orgcode",local.tmpOrgcode)>
						<cfset QuerySetCell(local.qrySend,"seminarID",local.thisSID)>
					</cfif>
				</cfloop>
			</cfloop>
		</cfif>

		<cfreturn local.qrySend>
	</cffunction>

	<cffunction name="generateInvitationEmailFor2021" access="public" returntype="struct" output="no">
		<cfargument name="seminarIDlist" type="string" required="yes">
		<cfargument name="orgCode" type="string" required="yes">
		<cfargument name="subject" type="string" required="yes">
		<cfargument name="preheaderText" type="string" required="true">

		<cfset var local = structnew()>
		<cfset local.strEmailContent = StructNew()>
		<cfset local.objSWP = CreateObject("component","model.seminarweb.SWParticipants")>
		<cfset local.objSWL = CreateObject("component","model.seminarweb.SWLiveSeminars")>
		<cfset local.objAuthor = CreateObject("component","model.seminarweb.SWAuthors")>
		<cfset local.objWebsite = CreateObject("component","model.admin.website.website")>
		<cfset local.objSWCommon = CreateObject("component","seminarWebSWCommon")>
		<cfset local.objResourceTemplate = CreateObject("component","model.admin.common.modules.resourceTemplates.resourceTemplate")>
		<cfset local.strAssociation = local.objSWP.getAssociationDetails(arguments.orgCode)>
		<cfset local.qryProgramCredits = local.objSWCommon.getSWProgramCredits(seminarIDList=arguments.seminarIDlist)>
		<cfset local.qryOrgIdentity = application.objOrgInfo.getOrgIdentity(orgIdentityID=local.strAssociation.qryAssociation.orgIdentityID)>
		<cfset local.qrySWHostName = local.objSWP.getSWHostName()>
		<cfset local.qryPlatformFeaturedImageSetup = createObject("component","model.admin.common.modules.featuredImages.featuredImages").getPlatformFeaturedImagesSetup()>
		<cfset local.qryParticipantFeaturedImageSetup = local.strAssociation.qryParticipantFeaturedImageSetup>

		<cfset local.programsArr = ArrayNew(1)>
		<cfset local.programLevelFeaturedImageCount = 0>
		<cfloop list="#arguments.seminarIDlist#" index="local.thisSeminarID">
			<cfset local.tmpStr = StructNew()>
			<cfset local.tmpStr.seminarID = local.thisSeminarID>
			<cfset local.tmpStr.strSeminar = local.objSWL.getSeminarForCatalog(seminarID=local.thisSeminarID, catalogOrgCode=arguments.orgCode, billingState='', billingZip='', depoMemberDataID=0, memberID=0)>
			<cfset local.qrySWLSeminar = CreateObject("component","model.seminarweb.SWLiveSeminars").getSeminarBySeminarID(seminarID=local.thisSeminarID)>
			<cfif local.tmpStr.strSeminar.qrySeminar.recordCount>
				<cfset local.tmpStr.trimmedProgramDesc = local.objSWCommon.trimByWordsCount(inputString=local.tmpStr.strSeminar.qrySeminar.SeminarDesc, count=100)>

				<cfset local.creditDisplayInfoStr = local.objSWCommon.getSWCreditDisplayInfoForInvitationEmail(seminarID=local.thisSeminarID, qryProgramCredits=local.qryProgramCredits)>
				<cfset local.tmpStr.creditAuthorityCount = local.creditDisplayInfoStr.creditAuthorityCount>
				<cfset local.tmpStr.dspCredits = local.creditDisplayInfoStr.dspCredits>

				<!--- featured image paths --->
				<cfif local.strAssociation.qryAssociation.hasSWProgramImageConfiguration>
					<cfset local.programFeaturedImagesStr = local.objSWCommon.getProgramFeaturedImagePathsForInvitationEmail(publisherOrgCode=local.tmpStr.strSeminar.qrySeminar.publisherOrgCode, programType="SWL",
						programFeatureImageID=val(local.tmpStr.strSeminar.qrySeminar.featureImageID), programFeatureImageSizeID=val(local.tmpStr.strSeminar.qrySeminar.featureImageSizeID),
						programFeatureImageFileExtension=local.tmpStr.strSeminar.qrySeminar.featureImageFileExtension, qryPlatformFeaturedImageSetup=local.qryPlatformFeaturedImageSetup,
						qryParticipantFeaturedImageSetup=local.qryParticipantFeaturedImageSetup, qrySWHostName=local.qrySWHostName)>
					
					<cfset local.tmpStr.programLevelFeaturedImagePath = local.programFeaturedImagesStr.programLevelFeaturedImagePath>
					<cfif len(local.tmpStr.programLevelFeaturedImagePath)>
						<cfset local.programLevelFeaturedImageCount += 1>
					</cfif>
					<cfset local.tmpStr.featuredImagePath = local.programFeaturedImagesStr.featuredImagePath>
				<cfelse>
					<cfset local.tmpStr.programLevelFeaturedImagePath = "">
					<cfset local.tmpStr.featuredImagePath = "">
				</cfif>
				
				<cfset local.publisherSiteInfo = application.objSiteInfo.getSiteInfo(local.tmpStr.strSeminar.qrySeminar.publisherOrgCode)>
				<cfset local.publisherHostName = local.objWebsite.getMainHost(val(local.publisherSiteInfo.siteID)).mainHostname>
				<cfset local.tmpStr.browsePageLink = "#local.strAssociation.qryAssociation.CatalogURL#/?panel=browse&_swft=swl">
				<cfset local.tmpStr.programDetailPageLink = "#local.strAssociation.qryAssociation.CatalogURL#/?d=SWLive-#local.tmpStr.seminarID#">

				<cfset ArrayAppend(local.programsArr,local.tmpStr)>
			</cfif>
		</cfloop>
			
		<!--- set from/subject --->
		<cfif len(local.strAssociation.qryAssociation.emailFrom)>
			<cfset local.strEmailContent.fromName = local.strAssociation.qryAssociation.emailFrom>
		<cfelse>
			<cfset local.strEmailContent.fromName = "SeminarWeb">
		</cfif>
		<cfset local.strEmailContent.subject = arguments.subject>
		
		<cfsavecontent variable="local.strEmailContent.html">
			<cfoutput>
			<cfif arrayLen(local.programsArr) eq 1>
				<cfset local.thisProgram = local.programsArr[1]>
				<cfset local.strSeminar = local.thisProgram.strSeminar>
				<cfset local.upcomingProgramsArr = local.objSWCommon.getUpcomingProgramsForInvitationEmail(siteCode=arguments.orgCode, programID=local.thisProgram.seminarID, programType="SWL")>

				<cfset local.qryAuthorsAll = local.objAuthor.getAuthorsBySeminarID(seminarID=local.thisProgram.seminarID, authorType='Speaker')>
				<cfquery name="local.qrySpeakers" dbtype="query">
					select * from [local].qryAuthorsAll where displayOnWebsite = 1;
				</cfquery>

				<cfif local.qrySpeakers.recordcount GT 1>
					<cfset local.bioCount = 250>
				<cfelse>
					<cfset local.bioCount = 500>
				</cfif>
				<cfset local.speakerFeaturedImagesList = valueList(local.qrySpeakers.featureImageID)>
				<cfset local.speakersWithPhotosCount = listLen(local.speakerFeaturedImagesList.listFilter(function(id) { return arguments.id gt 0; } ))>

				<cfinclude template="dsp_programInvitationTemplate_swl_single.cfm">
			<cfelseif arrayLen(local.programsArr) gt 1>
				<cfinclude template="dsp_programInvitationTemplate_swl_multiple.cfm">
			</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfset local.strRenderedTemplate = local.objResourceTemplate.doRenderResourceTemplate(template=local.strEmailContent.html, model={}, templateFormat="MJML")>

		<!--- dont replace 13 and 10 with space -- it leads to screwed up messages in lyris all being on one line --->
		<cfset local.strEmailContent.html = trim(replace(local.strRenderedTemplate.content,chr(9),"","ALL"))>

		<!--- replacing chr(7) with CRLF to ensure that Lyris merge code is processed correctly. --->
		<cfset local.strEmailContent.html = replace(local.strEmailContent.html,chr(7),chr(13) & chr(10),"all")>

		<cfreturn local.strEmailContent>
	</cffunction>

	<cffunction name="generateInvitationEmailFor2020" access="public" returntype="struct" output="no">
		<cfargument name="seminarIDlist" type="string" required="yes">
		<cfargument name="orgcode" type="string" required="yes">
		<cfargument name="subject" type="string" required="yes">

		<cfset var local = structnew()>
		<cfset local.strEmailContent = StructNew()>
		<cfset local.objSWP = CreateObject("component","model.seminarweb.SWParticipants")>
		<cfset local.qryAssociationDetails = local.objSWP.getAssociationDetails(arguments.orgcode).qryAssociation>
		<cfset local.qrySWHostName = local.objSWP.getSWHostName()>
		<cfset local.qryCredit = CreateObject("component","model.seminarweb.SWCredits").getCreditsforSeminarList(arguments.seminarIDlist)>
		<cfset local.objAuthor = CreateObject("component","model.seminarweb.SWAuthors")>
		<cfset local.objSWL = CreateObject("component","model.seminarweb.SWLiveSeminars")>

		<cfset local.seminarIDs = ArrayNew(1)>
		<cfloop list="#arguments.seminarIDlist#" index="local.thisSID">
			<cfquery name="local.qryCreditDistinct" dbtype="query">
				select distinct Code
				from [local].qryCredit
				where seminarid = #local.thisSID#
				order by Code
			</cfquery>
			
			<cfset local.tmpStr = StructNew()>
			<cfset local.tmpStr.seminarID = local.thisSID>
			<cfset local.tmpStr.strSeminar = getSeminarForRegistrationByAdmin(seminarID=local.thisSID, catalogOrgCode=arguments.orgcode, billingState='', billingZip='', depoMemberDataID=0, memberID=0)>

			<cfset local.tmpStr.creditList = valueList(local.qryCreditDistinct.code)>
			<cfset local.tmpStr.qrySpeakers = local.objAuthor.getAuthorsBySeminarID(seminarID=local.thisSID, authorType='Speaker')>
			<cfset ArrayAppend(local.seminarIDs,local.tmpStr)>
		</cfloop>
	
		<!--- set from/subject --->
		<cfif len(local.qryAssociationDetails.emailFrom)>
			<cfset local.strEmailContent.fromName = local.qryAssociationDetails.emailFrom>
		<cfelse>
			<cfset local.strEmailContent.fromName = "SeminarWeb">
		</cfif>

		<cfset local.strEmailContent.subject = arguments.subject>
		
		<cfsavecontent variable="local.strEmailContent.html">
			<cfoutput>
			<html>
			<head></head>
			<body>
				<table style="background-color:##ffffff; font-family:Verdana,Arial,sans-serif; font-size:10pt; color:##666666; margin:0; padding:7px;" cellpadding="0" cellspacing="0" border="0">
					<tr>
						<td width="600">
							<cfloop from="1" to="#arraylen(local.seminarIDs)#" index="local.thisID">
								<table valign="top" style="background-color:##3b618e;" border="0">
									<tr>
										<td width="425" valign="top">
											<div style="padding:3px; font-family:Calibri; color:##ffffff; font-size:12pt; font-style:italic; line-height:93%;">
												#local.qryAssociationDetails.description# invites you to attend:
											</div>
											<div>
												<a style="font-size:16pt; line-height:119%; font-family:Calibri; color:##ffffff; font-weight:bold; text-decoration:none;" href="###local.seminarIDs[local.thisID].seminarID#">
													#encodeForHTML(local.seminarIDs[local.thisID].strSeminar.qrySeminar.seminarName)#
												</a>
											</div>
										</td>
										<td>
											&nbsp;
										</td>
										<td width="175">
											<table cellpadding="0" cellspacing="0" border="0">
												<tr>
													<td width="142" height="37" bgcolor="##983634" align="center">
														<div style="padding:2px;text-align:center;">
															<a style="text-align:center; font-size:12pt; line-height:100%; font-family:Calibri; color:##ffffff; font-weight:bold; text-decoration:none;" href="%%track {#local.qryAssociationDetails.CatalogURL#/?d=SWLive-#local.seminarIDs[local.thisID].seminarID#&linksource=PI&linkterms=SWML} -name {#local.qryAssociationDetails.shortname#} -group {#local.qryAssociationDetails.shortname#}%%">Register Now!</a>
														</div>
													</td>
												</tr>
											</table>
										</td>
									</tr>
								</table>
								<table cellpadding="0" cellspacing="0" border="0">
									<tr>
										<td width="600" height="23" valign="top" bgcolor="silver" style="background:silver;">
											<div width="600" style="line-height:93%; font-size:12pt; font-family:Calibri; color:##3b618e;">
												#DateFormat(local.seminarIDs[local.thisID].strSeminar.qrySeminar.dspStartDate,"dddd, mmmm d, yyyy")#&nbsp;|&nbsp;#replace(TimeFormat(local.seminarIDs[local.thisID].strSeminar.qrySeminar.dspStartDate,"h:mm tt"),":00","")# - #replace(TimeFormat(local.seminarIDs[local.thisID].strSeminar.qrySeminar.dspEndDate,"h:mm tt"),":00","")# #ucase(local.seminarIDs[local.thisID].strSeminar.qrySeminar.dsptz)#
											</div>
										</td>
									</tr>
								</table>
								<div style="height:20px;">&nbsp;</div>
								<table>
									<tr valign="top">
										<td valign="top">
											<table cellpadding="0" cellspacing="0" border="0">
												<tr valign="top">
													<td width="400" valign="top">
														<table cellpadding="0" cellspacing="0" border="0">
															<tr valign="top">
																<td width="400" valign="top">
																	<span style="font-size:12pt; line-height:110%; font-family:Georgia; color:##983634; font-weight:bold;">Summary</span><br />
																	<span style="font-size:10pt; line-height:110%; font-family:Calibri; color:##000000;">#application.objCommon.fullleft(local.seminarIDs[local.thisID].strSeminar.qrySeminar.seminarDesc,500)#</span>
																	<cfif len(local.seminarIDs[local.thisID].strSeminar.qrySeminar.seminarDesc) gt 500>
																		... [<a href="%%track {#local.qryAssociationDetails.CatalogURL#/?d=SWLive-#local.seminarIDs[local.thisID].seminarID#&linksource=PI&linkterms=SWML} -name {More} -group {#local.qryAssociationDetails.shortname#}%%">more</a>]
																	</cfif>
																</td>
															</tr>
															<tr><td height="7">&nbsp;</td></tr>
															<tr>
																<td>
																	<span style="font-size:12pt; line-height:110%; font-family:Georgia; color:##983634; font-weight:bold;">Credit</span><br />
																	<span style="font-size:10pt; line-height:110%; font-family:Calibri; color:##000000;">
																		<cfif listLen(local.seminarIDs[local.thisID].creditlist)>
																			#replace(local.seminarIDs[local.thisID].creditlist,",",", ","ALL")#<br/><br/>
																			Detailed credit information is available on the <a href="%%track {#local.qryAssociationDetails.CatalogURL#/?d=SWLive-#local.seminarIDs[local.thisID].seminarID#&linksource=PI&linkterms=SWML} -name {Credit} -group {#local.qryAssociationDetails.shortname#}%%">registration page</a>.
																		<cfelse>
																			Each paid registrant who attends the seminar receives a Certificate of Attendance; credit 
																			may be available in your state.
																		</cfif>
																	</span>
																</td>
															</tr>
															<tr><td height="7">&nbsp;</td></tr>
															<tr>
																<td>
																	<cfset local.qryAuthors = local.seminarIDs[local.thisID].qrySpeakers>
																	<span style="font-size:12pt; line-height:110%; font-family:Georgia; color:##983634; font-weight:bold;">Presenter<cfif local.qryAuthors.recordcount GT 1>s</cfif></span><br />
																	<table cellpadding="0">
																		<cfloop query="local.qryAuthors">
																			<cfif local.qryAuthors.displayOnWebsite EQ "1">
																				<tr valign="top">
																					<cfif val(local.qryAuthors.featureImageID) gt 0>
																						<td><img src="#local.qrySWHostName.scheme#://#local.qrySWHostName.mainHostName#/userassets/#LCASE(local.qryAuthors.featureImageOrgCode)#/#LCASE(local.qryAuthors.featureImageSiteCode)#/featuredimages/thumbnails/#local.qryAuthors.featureImageID#-#local.qryAuthors.featureImageSizeID#.#local.qryAuthors.fileExtension#" alt="#local.qryAuthors.firstname# #local.qryAuthors.middlename# #local.qryAuthors.lastname#" /></td>
																					<cfelse>
																						<td></td>
																					</cfif>
																					<td style="font-size:10pt; line-height:110%; font-family:Calibri; color:##000000;">
																						<b>#prefix# #firstname# #middlename# #lastname#<cfif len(suffix)>, #suffix#</cfif></b><br/>
																						#application.objCommon.fullleft(biography,175)#
																						<cfif len(biography) gt 175>... [<a href="%%track {#local.qryAssociationDetails.CatalogURL#/?d=SWLive-#local.seminarIDs[local.thisID].seminarID#&linksource=PI&linkterms=SWML} -name {More} -group {#local.qryAssociationDetails.shortname#}%%">more</a>]</cfif>
																					</td>
																				</tr>
																			</cfif>
																		</cfloop>
																	</table>
																</td>
															</tr>
															<tr><td height="7">&nbsp;</td></tr>
															<tr>
																<td>
																	<span style="font-size:12pt; line-height:110%; font-family:Georgia; color:##983634; font-weight:bold;">Questions?</span><br />
																	<span style="font-size:10pt; line-height:110%; font-family:Calibri; color:##000000;">
																		For immediate help please consult our 
																	<a href="%%track {#local.qryAssociationDetails.CatalogURL#/?d=FAQ-SWOD} -name {FAQ} -group {#local.qryAssociationDetails.shortname#}%%">FAQ page</a>.
																		<br/><br/>
																		If you're unable to find the answer you need, please call #local.qryAssociationDetails.supportPhone# (#local.qryAssociationDetails.supportHours#) or e-mail <a href="mailto:#local.qryAssociationDetails.supportEmail#" target="_blank">customer service</a>.
																	</span>
																</td>
															</tr>
														</table>
													</td>
												</tr>
											</table>
										</td>
										<td width="1px;" style="vertical-align:top; background:silver;"></td>
										<td width="210">
											<table cellpadding="0" cellspacing="0">
												<tr>
													<td width="210" style="border:1px solid ##A6A6A6; vertical-align:top; background:silver;">
														<div style="padding:4px; line-height:100%; font-size:12pt; font-family:Georgia; color:##3b618e; font-weight:bold;">Fee</div>
													</td>
												</tr>
												<tr>
													<td width="210" style="border:1px solid ##A6A6A6; background:##ffffff; vertical-align:top;">
														<div style="padding:4px;padding-left:5px; line-height:110%; font-size:10pt; font-family:Calibri; color:##000000;">
															<cfset local.tmpPrices = local.seminarIDs[local.thisID].strSeminar.qrySeminarPrices>
															<cfloop query="local.tmpPrices">
																<cfif local.tmpPrices.price gte 0>
																	<cfif local.tmpPrices.price is 0>
																		<strong>#replace(local.seminarIDs[local.thisID].strSeminar.qrySeminar.freeRateDisplay,".00","")#</strong>
																	<cfelseif local.tmpPrices.price gt 0>
																		<strong>#replace(dollarformat(local.tmpPrices.price),".00","")#</strong><cfif local.seminarIDs[local.thisID].strSeminar.qrySeminar.showUSD> USD</cfif>
																	</cfif>
																	<cfif len(local.tmpPrices.description)> <cfif local.tmpPrices.price gt 0 or len(local.seminarIDs[local.thisID].strSeminar.qrySeminar.freeRateDisplay)>for </cfif>#local.tmpPrices.description#</cfif><br/>
																</cfif>
															</cfloop>
														</div>
														<div align="center">
															<a style="font-size:12pt; line-height:100%; font-family:Calibri; color:##983634; text-decoration:underline; font-weight:bold;" href="%%track {#local.qryAssociationDetails.CatalogURL#/?d=SWLive-#local.seminarIDs[local.thisID].seminarID#&linksource=PI&linkterms=SWML} -name {#local.qryAssociationDetails.shortname#} -group {#local.qryAssociationDetails.shortname#}%%"><b><span style="font-size:12pt; line-height:100%; font-family:Calibri; color:##983634; text-decoration:underline; font-weight:bold;">Click here to register!</span></b></a>
														</div>
														<br/>
													</td>
												</tr>
												<tr><td>&nbsp;</td></tr>
												<tr>
													<td width="210" style="border:1px solid ##A6A6A6; vertical-align:top; background:silver;">
														<div style="padding:4px; line-height:100%; font-size:12pt; font-family:Georgia; color:##3b618e; font-weight:bold;">Upcoming Webinars</div>
													</td>
												</tr>
												<tr>
													<td width="210" style="border:1px solid ##A6A6A6; background:##ffffff; vertical-align:top;">
														<cfset local.orgWebinars = getSeminarsOptedInByOrgcode(arguments.orgCode)>
														<table cellpadding="0" cellspacing="0">
															<cfloop query="local.orgWebinars" endRow="6">
																<cfif local.orgWebinars.seminarID NEQ local.seminarIDs[local.thisID].seminarID AND local.orgWebinars.isPublished>
																	<tr>
																		<td width="45" style="background:##1F497D; padding:4px; border-right:solid ##777671 1.0pt; border-bottom:solid ##fff 1.0pt;">
																			<div style="font-size:15pt; line-height:119%; font-family:Calibri; color:##EEECE1; font-weight:bold; text-align:center;">
																				#dateFormat(local.orgWebinars.dateStart,'D')#
																			</div>
																			<div style="font-size:11pt; line-height:75%; font-family:Calibri; color:##EEECE1; font-weight:bold; text-align:center;">
																				#dateFormat(local.orgWebinars.dateStart,'MMM')#
																			</div>
																		</td>
																		<td style="<cfif local.orgWebinars.isLast()><cfelse>border-bottom:1px solid ##A6A6A6;</cfif>">
																			<div style="font-size:10pt; line-height:90%; font-family:Calibri; padding:3px;padding-left:5px;">
																				<a style="text-align:center; font-size:10pt; line-height:90%; font-family:Calibri; text-decoration:none;" href="%%track {#local.qryAssociationDetails.CatalogURL#/?d=SWLive-#local.orgWebinars.seminarID#&linksource=PI&linkterms=SWML} -name {Upcoming} -group {#local.qryAssociationDetails.shortname#}%%">#encodeForHTML(local.orgWebinars.seminarName)#</a>
																			</div>
																		</td>
																	</tr>
																</cfif>
															</cfloop>
														</table>
													</td>
												</tr>
												<tr><td>&nbsp;</td></tr>
												<tr>
													<td width="210" style="border:1px solid ##A6A6A6; vertical-align:top; background:silver;">
														<div style="padding:4px; line-height:100%; font-size:12pt; font-family:Georgia; color:##3b618e; font-weight:bold;">Tell a Colleague!</div>
													</td>
												</tr>
												<tr>
													<td width="210" align="center" style="border:1px solid ##A6A6A6; background:##ffffff; vertical-align:top;">
														<span class="st_facebook_large">
															<span style="padding:5px;text-decoration:none;color:##000000;display:inline-block;cursor:pointer;border=0">
																<a target="_blank" title="Share this on Facebook" href="https://www.facebook.com/sharer/sharer.php?u=#local.qryAssociationDetails.CatalogURL#/?d=SWLive-#local.seminarIDs[local.thisID].seminarID#" target="_blank">
																	<img width="32" height="32" alt="Share this on Facebook" src="http://ws.sharethis.com/images/facebook_32.png" border=0>
																</a>
															</span>
														</span>&nbsp;
														<span class="st_linkedin_large">
															<span style="padding:5px;text-decoration:none;color:##000000;display:inline-block;cursor:pointer;border=0">
																<a title="Share this on Linked In" href="http://www.linkedin.com/shareArticle?mini=true&url=#local.qryAssociationDetails.CatalogURL#/?d=SWLive-#local.seminarIDs[local.thisID].seminarID#" target="_blank">
																	<img src="http://ws.sharethis.com/images/linkedin_32.png" height="32" width="32" border=0>
																</a>
															</span>
														</span>&nbsp;
														<span class="st_email_large">
															<span style="padding:5px;text-decoration:none;color:##000000;display:inline-block;cursor:pointer;border=0">
																<a title="Email this to a colleague" href="mailto:?Subject=#URLEncodedFormat(local.seminarIDs[local.thisID].strSeminar.qrySeminar.seminarName)#&Body=Hello - %0A%0AI thought you would be interested in '#URLEncodedFormat(local.seminarIDs[local.thisID].strSeminar.qrySeminar.seminarName)#.' You can register for the seminar at #local.qryAssociationDetails.CatalogURL#/?d=SWLive-#local.seminarIDs[local.thisID].seminarID#." target="_blank">
																	<img src="http://ws.sharethis.com/images/email_32.png" height="32" width="32" border=0>
																</a>
															</span>
														</span>&nbsp;
														<span class="st_twitter_large">
															<span style="padding:5px;text-decoration:none;color:##000000;display:inline-block;cursor:pointer;border=0">
																<a title="Share this on Twitter" href="http://twitter.com/intent/tweet?source=webclient&text=#URLEncodedFormat(local.seminarIDs[local.thisID].strSeminar.qrySeminar.seminarName)#%20#local.qryAssociationDetails.CatalogURL#/?d=SWLive-#local.seminarIDs[local.thisID].seminarID#" target="_blank">
																	<img src="http://ws.sharethis.com/images/twitter_32.png" height="32" width="32" border=0>
																</a>
															</span>
														</span>
													</td>
												</tr>
											</table>
										</td>
									</tr>
								</table>
								<br /><br /><br />
							</cfloop>
						</td>
					</tr>
					<tr>
						<td>
							<p style="font-family:Verdana,Arial,sans-serif;font-size:7pt;color:##666;text-align:center;">
								<!--- chr(7) inserted before unsub address done on purpose to ensure that Lyris merge code is processed correctly. it is replaced with a CRLF after HTML is compacted --->
								This promotional email was sent by SeminarWeb on behalf of #local.qryAssociationDetails.description#.<br/>
								13359 N Hwy 183 ##406-1220, Austin, TX 78750<br/>
								To cease further e-mails regarding #local.qryAssociationDetails.shortname#'s Live SeminarWeb Webinars, click #chr(7)# <a href="mailto:$subst('Email.UnSub')?Subject=Unsubscribe%20Request&Body=Please%20unsubscribe%20me%20from%20this%20list." style="color:##000;">here</a>.
							</p>
							<IMG ALT="" SRC="http://lists.trialsmith.com/db/%%outmail.messageid%%/%%memberid%%/1.gif" WIDTH="1" HEIGHT="1">
						</td>
					</tr>
				</table>
				
			</body>
			</html>
			</cfoutput>
		</cfsavecontent>

		<!--- dont replace 13 and 10 with space -- it leads to screwed up messages in lyris all being on one line --->
		<cfset local.strEmailContent.html = trim(replace(local.strEmailContent.html,chr(9),"","ALL"))>

		<!--- replacing chr(7) with CRLF to ensure that Lyris merge code is processed correctly. --->
		<cfset local.strEmailContent.html = replace(local.strEmailContent.html,chr(7),chr(13) & chr(10),"all")>

		<cfreturn local.strEmailContent>
	</cffunction>

	<cffunction name="generateInvitationEmailFor2017NONSAE" access="public" returntype="struct" output="no">
		<cfargument name="seminarIDlist" type="string" required="yes">
		<cfargument name="orgcode" type="string" required="yes">
		<cfargument name="subject" type="string" required="yes">

		<cfset var local = structnew()>
		<cfset local.strEmailContent = StructNew()>
		<cfset local.objSWP = CreateObject("component","model.seminarweb.SWParticipants")>
		<cfset local.qryAssociationDetails = local.objSWP.getAssociationDetails(arguments.orgcode).qryAssociation>
		<cfset local.qrySWHostName = local.objSWP.getSWHostName()>
		<cfset local.qryCredit = CreateObject("component","model.seminarweb.SWCredits").getCreditsforSeminarList(arguments.seminarIDlist)>
		<cfset local.objAuthor = CreateObject("component","model.seminarweb.SWAuthors")>
		<cfset local.objSWL = CreateObject("component","model.seminarweb.SWLiveSeminars")>

		<cfset local.seminarIDs = ArrayNew(1)>
		<cfloop list="#arguments.seminarIDlist#" index="local.thisSID">
			<cfquery name="local.qryCreditDistinct" dbtype="query">
				select distinct Code
				from [local].qryCredit
				where seminarid = #local.thisSID#
				order by Code
			</cfquery>
			
			<cfset local.tmpStr = StructNew()>
			<cfset local.tmpStr.seminarID = local.thisSID>
			<cfset local.tmpStr.strSeminar = getSeminarForRegistrationByAdmin(seminarID=local.thisSID, catalogOrgCode=arguments.orgcode, billingState='', billingZip='', depoMemberDataID=0, memberID=0)>

			<cfset local.tmpStr.creditList = valueList(local.qryCreditDistinct.code)>
			<cfset local.tmpStr.qrySpeakers = local.objAuthor.getAuthorsBySeminarID(seminarID=local.thisSID, authorType='Speaker')>
			<cfset ArrayAppend(local.seminarIDs,local.tmpStr)>
		</cfloop>
	
		<!--- set from/subject --->
		<cfif len(local.qryAssociationDetails.emailFrom)>
			<cfset local.strEmailContent.fromName = local.qryAssociationDetails.emailFrom>
		<cfelse>
			<cfset local.strEmailContent.fromName = "SeminarWeb">
		</cfif>

		<cfset local.strEmailContent.subject = arguments.subject>
		
		<cfsavecontent variable="local.strEmailContent.html">
			<cfoutput>
			<html>
			<head></head>
			<body>
				<table style="background-color:##ffffff; font-family:Verdana,Arial,sans-serif; font-size:10pt; color:##666666; margin:0; padding:7px;" cellpadding="0" cellspacing="0" border="0">
					<tr>
						<td width="600">
							<cfloop from="1" to="#arraylen(local.seminarIDs)#" index="local.thisID">
								<table valign="top" style="background-color:##3b618e;" border="0">
									<tr>
										<td width="425" valign="top">
											<div style="padding:3px; font-family:Calibri; color:##ffffff; font-size:12pt; font-style:italic; line-height:93%;">
												#local.qryAssociationDetails.description# invites you to attend:
											</div>
											<div>
												<a style="font-size:16pt; line-height:119%; font-family:Calibri; color:##ffffff; font-weight:bold; text-decoration:none;" href="###local.seminarIDs[local.thisID].seminarID#">
													#encodeForHTML(local.seminarIDs[local.thisID].strSeminar.qrySeminar.seminarName)#
												</a>
											</div>
										</td>
										<td>
											&nbsp;
										</td>
										<td width="175">
											<table cellpadding="0" cellspacing="0" border="0">
												<tr>
													<td width="142" height="37" bgcolor="##983634" align="center">
														<div style="padding:2px;text-align:center;">
															<a style="text-align:center; font-size:12pt; line-height:100%; font-family:Calibri; color:##ffffff; font-weight:bold; text-decoration:none;" href="%%track {#local.qryAssociationDetails.CatalogURL#/?d=SWLive-#local.seminarIDs[local.thisID].seminarID#&linksource=PI&linkterms=SWML} -name {#local.qryAssociationDetails.shortname#} -group {#local.qryAssociationDetails.shortname#}%%">Register Now!</a>
														</div>
													</td>
												</tr>
											</table>
										</td>
									</tr>
								</table>
								<table cellpadding="0" cellspacing="0" border="0">
									<tr>
										<td width="600" height="23" valign="top" bgcolor="silver" style="background:silver;">
											<div width="600" style="line-height:93%; font-size:12pt; font-family:Calibri; color:##3b618e;">
												#DateFormat(local.seminarIDs[local.thisID].strSeminar.qrySeminar.dspStartDate,"dddd, mmmm d, yyyy")#&nbsp;|&nbsp;#replace(TimeFormat(local.seminarIDs[local.thisID].strSeminar.qrySeminar.dspStartDate,"h:mm tt"),":00","")# - #replace(TimeFormat(local.seminarIDs[local.thisID].strSeminar.qrySeminar.dspEndDate,"h:mm tt"),":00","")# #ucase(local.seminarIDs[local.thisID].strSeminar.qrySeminar.dsptz)#
											</div>
										</td>
									</tr>
								</table>
								<div style="height:20px;">&nbsp;</div>
								<table>
									<tr valign="top">
										<td valign="top">
											<table cellpadding="0" cellspacing="0" border="0">
												<tr valign="top">
													<td width="400" valign="top">
														<table cellpadding="0" cellspacing="0" border="0">
															<tr valign="top">
																<td width="400" valign="top">
																	<span style="font-size:12pt; line-height:110%; font-family:Georgia; color:##983634; font-weight:bold;">Summary</span><br />
																	<span style="font-size:10pt; line-height:110%; font-family:Calibri; color:##000000;">#application.objCommon.fullleft(local.seminarIDs[local.thisID].strSeminar.qrySeminar.seminarDesc,500)#</span>
																	<cfif len(local.seminarIDs[local.thisID].strSeminar.qrySeminar.seminarDesc) gt 500>
																		... [<a href="%%track {#local.qryAssociationDetails.CatalogURL#/?d=SWLive-#local.seminarIDs[local.thisID].seminarID#&linksource=PI&linkterms=SWML} -name {More} -group {#local.qryAssociationDetails.shortname#}%%">more</a>]
																	</cfif>
																</td>
															</tr>
															<tr><td height="7">&nbsp;</td></tr>
															<tr>
																<td>
																	<span style="font-size:12pt; line-height:110%; font-family:Georgia; color:##983634; font-weight:bold;">Credit</span><br />
																	<span style="font-size:10pt; line-height:110%; font-family:Calibri; color:##000000;">
																		<cfif listLen(local.seminarIDs[local.thisID].creditlist)>
																			#replace(local.seminarIDs[local.thisID].creditlist,",",", ","ALL")#<br/><br/>
																			Detailed credit information is available on the <a href="%%track {#local.qryAssociationDetails.CatalogURL#/?d=SWLive-#local.seminarIDs[local.thisID].seminarID#&linksource=PI&linkterms=SWML} -name {Credit} -group {#local.qryAssociationDetails.shortname#}%%">registration page</a>.
																		<cfelse>
																			Each paid registrant who attends the seminar receives a Certificate of Attendance; credit 
																			may be available in your state.
																		</cfif>
																	</span>
																</td>
															</tr>
															<tr><td height="7">&nbsp;</td></tr>
															<tr>
																<td>
																	<cfset local.qryAuthors = local.seminarIDs[local.thisID].qrySpeakers>
																	<span style="font-size:12pt; line-height:110%; font-family:Georgia; color:##983634; font-weight:bold;">Presenter<cfif local.qryAuthors.recordcount GT 1>s</cfif></span><br />
																	<table cellpadding="0">
																		<cfloop query="local.qryAuthors">
																			<cfif local.qryAuthors.displayOnWebsite EQ "1">
																				<tr valign="top">
																					<cfif val(local.qryAuthors.featureImageID) gt 0>
																						<td><img src="#local.qrySWHostName.scheme#://#local.qrySWHostName.mainHostName#/userassets/#LCASE(local.qryAuthors.featureImageOrgCode)#/#LCASE(local.qryAuthors.featureImageSiteCode)#/featuredimages/thumbnails/#local.qryAuthors.featureImageID#-#local.qryAuthors.featureImageSizeID#.#local.qryAuthors.fileExtension#" alt="#local.qryAuthors.firstname# #local.qryAuthors.middlename# #local.qryAuthors.lastname#" /></td>
																					<cfelse>
																						<td></td>
																					</cfif>
																					<td style="font-size:10pt; line-height:110%; font-family:Calibri; color:##000000;">
																						<b>#prefix# #firstname# #middlename# #lastname#<cfif len(suffix)>, #suffix#</cfif></b><br/>
																						#application.objCommon.fullleft(biography,175)#
																						<cfif len(biography) gt 175>... [<a href="%%track {#local.qryAssociationDetails.CatalogURL#/?d=SWLive-#local.seminarIDs[local.thisID].seminarID#&linksource=PI&linkterms=SWML} -name {More} -group {#local.qryAssociationDetails.shortname#}%%">more</a>]</cfif>
																					</td>
																				</tr>
																			</cfif>
																		</cfloop>
																	</table>
																</td>
															</tr>
															<tr><td height="7">&nbsp;</td></tr>
															<tr>
																<td>
																	<span style="font-size:12pt; line-height:110%; font-family:Georgia; color:##983634; font-weight:bold;">Questions?</span><br />
																	<span style="font-size:10pt; line-height:110%; font-family:Calibri; color:##000000;">
																		For immediate help please consult our 
																	<a href="%%track {#local.qryAssociationDetails.CatalogURL#/?d=FAQ-SWOD} -name {FAQ} -group {#local.qryAssociationDetails.shortname#}%%">FAQ page</a>.
																		<br/><br/>
																		If you're unable to find the answer you need, please call #local.qryAssociationDetails.supportPhone# (#local.qryAssociationDetails.supportHours#) or e-mail <a href="mailto:#local.qryAssociationDetails.supportEmail#" target="_blank">customer service</a>.
																	</span>
																</td>
															</tr>
														</table>
													</td>
												</tr>
											</table>
										</td>
										<td width="1px;" style="vertical-align:top; background:silver;"></td>
										<td width="210">
											<table cellpadding="0" cellspacing="0">
												<tr>
													<td width="210" style="border:1px solid ##A6A6A6; vertical-align:top; background:silver;">
														<div style="padding:4px; line-height:100%; font-size:12pt; font-family:Georgia; color:##3b618e; font-weight:bold;">Fee</div>
													</td>
												</tr>
												<tr>
													<td width="210" style="border:1px solid ##A6A6A6; background:##ffffff; vertical-align:top;">
														<div style="padding:4px;padding-left:5px; line-height:110%; font-size:10pt; font-family:Calibri; color:##000000;">
															<cfset local.tmpPrices = local.seminarIDs[local.thisID].strSeminar.qrySeminarPrices>
															<cfloop query="local.tmpPrices">
																<cfif local.tmpPrices.price gte 0>
																	<cfif local.tmpPrices.price is 0>
																		<strong>#replace(local.seminarIDs[local.thisID].strSeminar.qrySeminar.freeRateDisplay,".00","")#</strong>
																	<cfelseif local.tmpPrices.price gt 0>
																		<strong>#replace(dollarformat(local.tmpPrices.price),".00","")#</strong><cfif local.seminarIDs[local.thisID].strSeminar.qrySeminar.showUSD> USD</cfif>
																	</cfif>
																	<cfif len(local.tmpPrices.description)> <cfif local.tmpPrices.price gt 0 or len(local.seminarIDs[local.thisID].strSeminar.qrySeminar.freeRateDisplay)>for </cfif>#local.tmpPrices.description#</cfif><br/>
																</cfif>
															</cfloop>
														</div>
														<div align="center">
															<a style="font-size:12pt; line-height:100%; font-family:Calibri; color:##983634; text-decoration:underline; font-weight:bold;" href="%%track {#local.qryAssociationDetails.CatalogURL#/?d=SWLive-#local.seminarIDs[local.thisID].seminarID#&linksource=PI&linkterms=SWML} -name {#local.qryAssociationDetails.shortname#} -group {#local.qryAssociationDetails.shortname#}%%"><b><span style="font-size:12pt; line-height:100%; font-family:Calibri; color:##983634; text-decoration:underline; font-weight:bold;">Click here to register!</span></b></a>
														</div>
														<br/>
													</td>
												</tr>
												<tr><td>&nbsp;</td></tr>
												<tr>
													<td width="210" style="border:1px solid ##A6A6A6; vertical-align:top; background:silver;">
														<div style="padding:4px; line-height:100%; font-size:12pt; font-family:Georgia; color:##3b618e; font-weight:bold;">Upcoming Webinars</div>
													</td>
												</tr>
												<tr>
													<td width="210" style="border:1px solid ##A6A6A6; background:##ffffff; vertical-align:top;">
														<cfset local.orgWebinars = getSeminarsOptedInByOrgcode(arguments.orgCode)>
														<table cellpadding="0" cellspacing="0">
															<cfloop query="local.orgWebinars" endRow="6">
																<cfif local.orgWebinars.seminarID NEQ local.seminarIDs[local.thisID].seminarID AND local.orgWebinars.isPublished>
																	<tr>
																		<td width="45" style="background:##1F497D; padding:4px; border-right:solid ##777671 1.0pt; border-bottom:solid ##fff 1.0pt;">
																			<div style="font-size:15pt; line-height:119%; font-family:Calibri; color:##EEECE1; font-weight:bold; text-align:center;">
																				#dateFormat(local.orgWebinars.dateStart,'D')#
																			</div>
																			<div style="font-size:11pt; line-height:75%; font-family:Calibri; color:##EEECE1; font-weight:bold; text-align:center;">
																				#dateFormat(local.orgWebinars.dateStart,'MMM')#
																			</div>
																		</td>
																		<td style="<cfif local.orgWebinars.isLast()><cfelse>border-bottom:1px solid ##A6A6A6;</cfif>">
																			<div style="font-size:10pt; line-height:90%; font-family:Calibri; padding:3px;padding-left:5px;">
																				<a style="text-align:center; font-size:10pt; line-height:90%; font-family:Calibri; text-decoration:none;" href="%%track {#local.qryAssociationDetails.CatalogURL#/?d=SWLive-#local.orgWebinars.seminarID#&linksource=PI&linkterms=SWML} -name {Upcoming} -group {#local.qryAssociationDetails.shortname#}%%">#encodeForHTML(local.orgWebinars.seminarName)#</a>
																			</div>
																		</td>
																	</tr>
																</cfif>
															</cfloop>
														</table>
													</td>
												</tr>
												<tr><td>&nbsp;</td></tr>
												<tr>
													<td width="210" style="border:1px solid ##A6A6A6; vertical-align:top; background:silver;">
														<div style="padding:4px; line-height:100%; font-size:12pt; font-family:Georgia; color:##3b618e; font-weight:bold;">Tell a Colleague!</div>
													</td>
												</tr>
												<tr>
													<td width="210" align="center" style="border:1px solid ##A6A6A6; background:##ffffff; vertical-align:top;">
														<span class="st_facebook_large">
															<span style="padding:5px;text-decoration:none;color:##000000;display:inline-block;cursor:pointer;border=0">
																<a target="_blank" title="Share this on Facebook" href="https://www.facebook.com/sharer/sharer.php?u=#local.qryAssociationDetails.CatalogURL#/?d=SWLive-#local.seminarIDs[local.thisID].seminarID#" target="_blank">
																	<img width="32" height="32" alt="Share this on Facebook" src="http://ws.sharethis.com/images/facebook_32.png" border=0>
																</a>
															</span>
														</span>&nbsp;
														<span class="st_linkedin_large">
															<span style="padding:5px;text-decoration:none;color:##000000;display:inline-block;cursor:pointer;border=0">
																<a title="Share this on Linked In" href="http://www.linkedin.com/shareArticle?mini=true&url=#local.qryAssociationDetails.CatalogURL#/?d=SWLive-#local.seminarIDs[local.thisID].seminarID#" target="_blank">
																	<img src="http://ws.sharethis.com/images/linkedin_32.png" height="32" width="32" border=0>
																</a>
															</span>
														</span>&nbsp;
														<span class="st_email_large">
															<span style="padding:5px;text-decoration:none;color:##000000;display:inline-block;cursor:pointer;border=0">
																<a title="Email this to a colleague" href="mailto:?Subject=#URLEncodedFormat(local.seminarIDs[local.thisID].strSeminar.qrySeminar.seminarName)#&Body=Hello - %0A%0AI thought you would be interested in '#URLEncodedFormat(local.seminarIDs[local.thisID].strSeminar.qrySeminar.seminarName)#.' You can register for the seminar at #local.qryAssociationDetails.CatalogURL#/?d=SWLive-#local.seminarIDs[local.thisID].seminarID#." target="_blank">
																	<img src="http://ws.sharethis.com/images/email_32.png" height="32" width="32" border=0>
																</a>
															</span>
														</span>&nbsp;
														<span class="st_twitter_large">
															<span style="padding:5px;text-decoration:none;color:##000000;display:inline-block;cursor:pointer;border=0">
																<a title="Share this on Twitter" href="http://twitter.com/intent/tweet?source=webclient&text=#URLEncodedFormat(local.seminarIDs[local.thisID].strSeminar.qrySeminar.seminarName)#%20#local.qryAssociationDetails.CatalogURL#/?d=SWLive-#local.seminarIDs[local.thisID].seminarID#" target="_blank">
																	<img src="http://ws.sharethis.com/images/twitter_32.png" height="32" width="32" border=0>
																</a>
															</span>
														</span>
													</td>
												</tr>
											</table>
										</td>
									</tr>
								</table>
								<br /><br /><br />
							</cfloop>
						</td>
					</tr>
					<tr>
						<td>
							<p style="font-family:Verdana,Arial,sans-serif;font-size:7pt;color:##666;text-align:center;">
								<!--- chr(7) inserted before unsub address done on purpose to ensure that Lyris merge code is processed correctly. it is replaced with a CRLF after HTML is compacted --->
								This promotional email was sent by SeminarWeb on behalf of #local.qryAssociationDetails.description#.<br/>
								13359 N Hwy 183 ##406-1220, Austin, TX 78750<br/>
								To cease further e-mails regarding #local.qryAssociationDetails.shortname#'s Live SeminarWeb Webinars, click #chr(7)# <a href="mailto:$subst('Email.UnSub')?Subject=Unsubscribe%20Request&Body=Please%20unsubscribe%20me%20from%20this%20list." style="color:##000;">here</a>.
							</p>
							<IMG ALT="" SRC="http://lists.trialsmith.com/db/%%outmail.messageid%%/%%memberid%%/1.gif" WIDTH="1" HEIGHT="1">
						</td>
					</tr>
				</table>
				
			</body>
			</html>
			</cfoutput>
		</cfsavecontent>

		<!--- dont replace 13 and 10 with space -- it leads to screwed up messages in lyris all being on one line --->
		<cfset local.strEmailContent.html = trim(replace(local.strEmailContent.html,chr(9),"","ALL"))>

		<!--- replacing chr(7) with CRLF to ensure that Lyris merge code is processed correctly. --->
		<cfset local.strEmailContent.html = replace(local.strEmailContent.html,chr(7),chr(13) & chr(10),"all")>

		<cfreturn local.strEmailContent>
	</cffunction>

	<cffunction name="updateMailingName" access="public" returntype="void" output="no">
		<cfset var local = structnew()>

		<cfquery name="local.updateMailingName" datasource="#application.dsn.trialslyris1.dsn#">
			update mod
			set mod.title_ = left (upper(ss.name_) + ' | ' + left (mod.title_, charIndex (' ', mod.title_, 1)) + ' | ' + mod.hdrSubject_, 70)
			from dbo.moderate_ mod
			inner join subsets_ ss on mod.subsetID_ = ss.subsetID_
				and mod.list_ in ('seminarweblive', 'seminarweb_sae', 'seminarweb_rx', 'seminarweb_pt') 
				and mod.title_ not like '%|%'
		</cfquery>
	</cffunction>

	<cffunction name="sendInvitationEmail" access="public" returntype="void" output="no">
		<cfargument name="seminarIDlist" type="string" required="yes">
		<cfargument name="performedBy" type="numeric" required="yes">
		<cfargument name="outgoingType" type="string" required="yes">
		<cfargument name="orgcode" type="string" required="yes">
		<cfargument name="subject" type="string" required="yes">
		<cfargument name="preheaderText" type="string" required="true">
		<cfargument name="listchoice" type="string" required="yes">
		<cfargument name="template" type="string" required="true">

		<cfset var local = structnew()>
		<cfset local.objSWL = CreateObject("component","model.seminarweb.SWLiveSeminars")>

		<cfif ListLen(arguments.seminarIDlist) EQ 1>
			<cfset local.utm_campaign = local.objSWL.getSeminarBySeminarID(seminarID=val(arguments.seminarIDlist)).seminarName>
		<cfelse>
			<cfset local.utm_campaign = 'Multiple Programs'>
		</cfif>

		<cfset local.qrySend = generateInvitationTable(seminarIDlist=arguments.seminarIDlist, orgcode=arguments.orgcode)>
						
		<cfoutput query="local.qrySend" group="orgcode">
			<cfoutput>
			<cfset local.objSWL.logAction(local.qrySend.seminarID,"logInvitationToLyris",arguments.performedBy,'',0,0,2)>
			</cfoutput>
			
			<cfset local.thisOrgCode = UCASE(local.qrySend.orgcode)>

			<!--- get programs for orgcode --->
			<cfquery name="local.qrySendIndiv" dbtype="query">
				select seminarID
				from [local].qrySend
				where UPPER(orgcode) = '#local.thisOrgCode#'
			</cfquery>

			<cfif arguments.template eq '2021'>
				<cfset local.strEmailContent = generateInvitationEmailFor2021(seminarIDlist=valuelist(local.qrySendIndiv.seminarID), orgcode=local.thisOrgCode, subject=arguments.subject, preheaderText=arguments.preheaderText)>
			<cfelseif arguments.template eq '2020'>
				<cfset local.strEmailContent = generateInvitationEmailFor2020(seminarIDlist=valuelist(local.qrySendIndiv.seminarID), orgcode=local.thisOrgCode, subject=arguments.subject)>
			<cfelseif arguments.template eq '2017NONSAE'>
				<cfset local.strEmailContent = generateInvitationEmailFor2017NONSAE(seminarIDlist=valuelist(local.qrySendIndiv.seminarID), orgcode=local.thisOrgCode, subject=arguments.subject)>
			</cfif>

			<cfset local.strEmailContent.html = application.objEmailWrapper.appendUTMCodesToLinks(htmlcontent=local.strEmailContent.html, utm_campaign=local.utm_campaign,
				utm_source="SWL Marketing", utm_medium="email", utm_content="#DateFormat(Now(),'YYYY-MM-DD')#-#local.strEmailContent.subject#")>

			<!--- set list email address --->
			<cfswitch expression="#arguments.listChoice#">
				<cfcase value="SAE">
					<cfset local.emailList = "seminarweb_sae.#local.thisOrgCode#@lists.trialsmith.com">
				</cfcase>
				<cfcase value="NATLE">
					<cfset local.emailList = "seminarweblive.#local.thisOrgCode#@lists.trialsmith.com">
				</cfcase>
				<cfcase value="RX">
					<cfset local.emailList = "seminarweb_rx.#local.thisOrgCode#@lists.trialsmith.com">
				</cfcase>
				<cfcase value="PT">
					<cfset local.emailList = "seminarweb_pt.#local.thisOrgCode#@lists.trialsmith.com">
				</cfcase>
				<cfdefaultcase> 
					<cfset local.emailList = "seminarweb_#arguments.listChoice#.#arguments.listChoice#@lists.trialsmith.com">
				</cfdefaultcase> 
			</cfswitch>

			<cfif application.MCEnvironment neq "production">
				<cfset local.emailList = "<EMAIL>">
			</cfif>

			<cfmail to="#local.emailList#" from="<EMAIL> (""#local.strEmailContent.fromName#"")" subject="#local.strEmailContent.subject#" mailerid="SeminarWeb" type="html" charset="utf-8" server="#application.mailservers.lyris.server#" port="#application.mailservers.lyris.port#" username="#application.mailservers.lyris.username#" password="#application.mailservers.lyris.password#">
				#local.strEmailContent.html#
			</cfmail>
						
			<cfoutput>
			<cfset local.objSWL.logAction(local.qrySend.seminarID,arguments.outgoingType,arguments.performedBy,local.emailList,0,0,0)>
			<cfset local.objSWL.logAction(local.qrySend.seminarID,"logInvitationToLyris",arguments.performedBy,'',0,0,1)>
			</cfoutput>
		</cfoutput>
	</cffunction>

	<cffunction name="getEnrollments" access="public" output="false" returntype="query">
		<cfargument name="seminarID" type="numeric" required="yes">
		
		<cfset var qryEnrollments = "">
		
		<cfstoredproc procedure="swl_getEnrollments" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
			<cfprocresult name="qryEnrollments" resultset="1">
		</cfstoredproc>

		<cfreturn qryEnrollments>
	</cffunction>

	<cffunction name="convertSWLProgramToSWOD" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="seminarID" required="yes" type="numeric">
		<cfargument name="incWebinarSettings" type="boolean" required="false" default="true">
		<cfargument name="incCreditApprovals" type="boolean" required="false" default="true">

		<cfset var local = structnew()>
		<cfset local.returnStruct = structNew()>

		<cftry>
			<cfif not hasUpdateSeminarRights(siteID=arguments.mcproxy_siteID, seminarID=arguments.seminarID, action="convertProgramToSWOD", checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<cfstoredproc procedure="swl_convertToSWOD" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.incWebinarSettings#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.incCreditApprovals#">
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.convertErrCode">
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.newSeminarID">
			</cfstoredproc>

			<cfif local.convertErrCode gt 0>
				<cfset local.returnStruct.errmsg = "There was a problem converting this seminar.">
				<cfset local.returnStruct.success = false>
			<cfelse>
				<cfset local.returnStruct.newSeminarID = local.newSeminarID>
				<cfset local.returnStruct.success = true>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getNATLEOptInReportSummary" access="public" output="false" returntype="struct">
		<cfargument name="rptStart" type="string" required="yes">
		<cfargument name="rptEnd" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>

		<cfquery name="local.qrySeminars" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SELECT s.seminarID, COUNT(p.participantID) AS participantCount
			FROM dbo.tblSeminarsSWLive as swl
			INNER JOIN dbo.tblSeminars as s on s.seminarID = swl.seminarID
			INNER JOIN dbo.tblSeminarsOptIn as o on o.seminarID = s.seminarID AND o.IsActive = 1
			INNER JOIN dbo.tblParticipants as p on p.participantID = o.participantID			
			WHERE swl.dateStart BETWEEN <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.rptStart#"> AND <cfqueryparam cfsqltype="CF_SQL_timestamp" value="#arguments.rptEnd# 23:59">
			AND swl.isNATLE = 1
			AND s.isPublished = 1
			GROUP BY s.seminarID;
		</cfquery>
		<cfquery name="local.qryTotals" dbtype="query">
			SELECT COUNT(seminarID) AS totalCount, AVG(participantCount) AS avgOptIn, MIN(participantCount) AS minOptIn, MAX(participantCount) AS maxOptIn
			FROM [local].qrySeminars;
		</cfquery>

		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.totalcount = local.qryTotals.totalCount>
		<cfset local.returnStruct.avgoptin = local.qryTotals.avgOptIn>
		<cfset local.returnStruct.minoptin = local.qryTotals.minOptIn>
		<cfset local.returnStruct.maxoptin = local.qryTotals.maxOptIn>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getEnrollmentByEnrollmentID" access="public" returntype="query" output="no">
		<cfargument name="enrollmentID" type="numeric" required="yes">

		<cfset var local = StructNew()>
		
		<cfstoredproc procedure="swl_getEnrollmentByEnrollmentID" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.enrollmentID#">
			<cfprocresult name="local.qryEnrollment" resultset="1">
		</cfstoredproc>
		
		<cfreturn local.qryEnrollment>
	</cffunction>

	<cffunction name="removeEnrollment" access="public" returntype="struct" output="no">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		<cfargument name="AROption" type="string" required="yes">
		<cfargument name="emailRegistrant" type="boolean" required="yes">
		<cfargument name="customText" type="string" required="no" default="">
		<cfargument name="emailRefundRequest" type="boolean" required="false" default="true">

		<cfset var local = structnew()>
		<cfset local.data.success = false>
		<cfset local.data.errmsg = "">

		<cfset local.siteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin',siteID=arguments.mcproxy_siteID)>
		<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>
		<cfset local.siteCode = application.objSiteInfo.getSiteCodeFromSiteID(siteID=arguments.mcproxy_siteID)>

		<cfset local.qryEnrollment = getEnrollmentByEnrollmentID(enrollmentID=arguments.enrollmentID)>
		<cfif local.qryEnrollment.recordcount is 0>
			<cfset local.data.errmsg = "You do not have rights to this section.">
			<cfreturn local.data>
		</cfif>

		<cfset local.hasdeleteSWLRegistrantRights = (local.tmpRights.deleteSWLRegistrantSignUp is 1 AND local.qryEnrollment.signUpOrgCode EQ local.siteCode) OR local.tmpRights.deleteSWLRegistrantAll is 1>
		<cfif NOT local.hasdeleteSWLRegistrantRights>
			<cfset local.data.errmsg = "You do not have rights to perform this operation.">
			<cfreturn local.data>
		</cfif>

		<cfif local.qryEnrollment.recordcount is 1>
			<cfset local.enrollmentRemoved = false>
			<cfset local.qrySeminar = CreateObject("component","model.seminarweb.SWLiveSeminars").getSeminarBySeminarID(seminarID=local.qryEnrollment.seminarID)>
			
			<cfif NOT local.qryEnrollment.handlesOwnPayment>
				<cfset local.qryEnrollmentFees = CreateObject("component","seminarWebSWCommon").getEnrollmentFees(referenceID=arguments.enrollmentID, programType="SWL")>
			</cfif>

			<!--- TL 8/6/2020: since we no longer use Adobe Connect, we needed a way to remove enrollments that didnt rely on removing from AC. So those fall to the else condition now. --->
			<cfif application.MCEnvironment eq "production" and local.qrySeminar.providerID is 3 and len(local.qrySeminar.ZoomWebinarID) and len(local.qryEnrollment.ZoomWebinarRegistrantID)>
				<cfset local.zoomRegistrantNeedsDeletion = true>

				<cfset local.objSWZoomWebinar = CreateObject("component","model.seminarWeb.SWZoomWebinar")>
				
				<!--- Added to support removing registrant from past Webinars that have expired from Zoom's system. --->
				<cfset local.webinarExists = local.objSWZoomWebinar.webinarExists(webinarID=local.qrySeminar.ZoomWebinarID)>
				<cfif local.qrySeminar.dateend LT now() AND local.webinarExists.success and NOT local.webinarExists.exists>
					<cfset local.zoomRegistrantNeedsDeletion = false>
				<cfelse>
					<cfset local.strRemoveReg = local.objSWZoomWebinar.removeWebinarRegistrant(webinarID=local.qrySeminar.ZoomWebinarID, registrantID=local.qryEnrollment.ZoomWebinarRegistrantID)>
					<cfif local.strRemoveReg.success>
						<cfset local.zoomRegistrantNeedsDeletion = false>
					<cfelse>
						<cftry>
							<cfthrow message="Problem Removing Registrant." detail="Unable to remove registrant in Zoom.">
						<cfcatch type="Any">
							<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump={strRemoveReg: local.strRemoveReg, webinarExists: local.webinarExists})>
						</cfcatch>
						</cftry>
					</cfif>
				</cfif>

				<cfif NOT local.zoomRegistrantNeedsDeletion>
					<cfquery name="local.updateEnrollmentSWL" datasource="#application.dsn.tlasites_seminarweb.dsn#">
						UPDATE dbo.tblEnrollmentsSWLive 
						SET ZoomWebinarRegistrantID = NULL,
							ZoomWebinarJoinURL = NULL
						WHERE enrollmentID = <cfqueryparam value="#arguments.enrollmentID#" cfsqltype="CF_SQL_INTEGER">
					</cfquery>
					<cfstoredproc procedure="swl_removeEnrollment" datasource="#application.dsn.tlasites_seminarweb.dsn#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.enrollmentID#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(session.cfcuser.statsSessionID)#">
						<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.AROption#">
					</cfstoredproc>
					<cfset local.enrollmentRemoved = true>
				</cfif>
			<cfelse>
				<cfstoredproc procedure="swl_removeEnrollment" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.enrollmentID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(session.cfcuser.statsSessionID)#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.AROption#">
				</cfstoredproc>
				<cfset local.enrollmentRemoved = true>
			</cfif>

			<cfif local.enrollmentRemoved>
				<cfset local.qryAssociation = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(orgcode=local.qryEnrollment.orgcode).qryAssociation>
				<cfif NOT local.qryEnrollment.handlesOwnPayment>
					<cfset local.hasSWRegRefund = arguments.AROption EQ 'A' AND val(local.qryEnrollmentFees.total) GT 0>
					<cfif local.hasSWRegRefund>
						<cfset local.data.SWRegRefundAmount = dollarformat(val(local.qryEnrollmentFees.total)+val(local.qryEnrollmentFees.tax))>
					</cfif>
				<cfelse>
					<cfset local.hasSWRegRefund = false>
				</cfif>
				
				<cfif arguments.emailRegistrant>
					<cfset local.arrEmailTo = []>
					<cfif len(local.qryEnrollment.email)>
						<cfset local.arrEmailTo.append({ name="#local.qryEnrollment.firstName# #local.qryEnrollment.lastName#", email=local.qryEnrollment.email })>
					</cfif>
					<cfif len(local.qryEnrollment.overrideEmail) AND local.qryEnrollment.email NEQ local.qryEnrollment.overrideEmail>
						<cfset local.arrEmailTo.append({ name="#local.qryEnrollment.firstName# #local.qryEnrollment.lastName#", email=local.qryEnrollment.overrideEmail })>
					</cfif>

					<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(local.qryEnrollment.orgcode)>

					<cfif arrayLen(local.arrEmailTo)>
						<cfsavecontent variable="local.emailContent">
							<cfoutput>
								#local.qryEnrollment.firstName# #local.qryEnrollment.lastName#:<br/><br/>
								This email confirms that your registration for #encodeForHTML(local.qryEnrollment.seminarName)# has been cancelled and deactivated.<cfif local.hasSWRegRefund> A refund request for #local.data.SWRegRefundAmount# has been sent to our accounting department. Please allow 3 business days for your refund to be processed.</cfif><br/><br/>
								<cfif len(arguments.customText)>#arguments.customText#<br/><br/></cfif>
								If you have any questions about this cancellation, please contact us at #local.mc_siteInfo.supportProviderEmail#.<br/><br/>
								#local.qryAssociation.emailFrom#<br/>
								#local.qryAssociation.supportPhone#
							</cfoutput>
						</cfsavecontent>
						<cfset local.emailContent = trim(replace(replace(replace(local.emailContent,chr(9),"","ALL"),chr(13),"","ALL"),chr(10),"","ALL"))>

						<cfset local.memberID = CreateObject("component","model.seminarWeb.SWCommon").getMemberIDByDepoMemberDataID(siteCode=local.qryEnrollment.signUpOrgCode, depoMemberDataID=local.qryEnrollment.depomemberdataID)>

						<cfset local.SeminarWebAdminSRIDEnrollee = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin',siteID=local.mc_siteInfo.siteID)>

						<cfset local.strResult = application.objEmailWrapper.sendMailESQ(
								emailfrom={ name=local.qryAssociation.emailFrom, email=local.mc_siteInfo.networkEmailFrom },
								emailto=local.arrEmailTo,
								emailreplyto=local.mc_siteInfo.supportProviderEmail,
								emailsubject="Registration Cancelled: #encodeForHTML(local.qryEnrollment.seminarName)#",
								emailtitle="Registration Cancelled: #encodeForHTML(local.qryEnrollment.seminarName)#",
								emailhtmlcontent=local.emailContent,
								siteID=local.mc_siteInfo.siteID,
								memberID=local.memberID,
								messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SEMWEBREGCANCEL"),
								sendingSiteResourceID=local.SeminarWebAdminSRIDEnrollee
						)>
					</cfif>
				</cfif>
				
				<!--- email SW Support for Refunds --->
				<cfif local.hasSWRegRefund and arguments.emailRefundRequest>
					<cfsavecontent variable="local.refundEmailContent">
						<cfoutput>
							This registration has been cancelled and a refund may be applicable.<br/><br/>
							<b>Registration Details</b><br/><hr>
							Registrant Name: #local.qryEnrollment.firstName# #local.qryEnrollment.lastName#<br/><br/>
							Program: #encodeForHTML(local.qryEnrollment.seminarName)# (SWL-#local.qryEnrollment.seminarID#)<br/><br/>
							DepoMemberDataID: <a href="https://admin.trialsmith.com/TransactionView.cfm?depoMemberDataID=#local.qryEnrollment.depoMemberDataID#">#local.qryEnrollment.depoMemberDataID#</a><br/><br/>
							Refund Amount: #local.data.SWRegRefundAmount#
						</cfoutput>
					</cfsavecontent>
					<cfset local.refundEmailContent = trim(replace(replace(replace(local.refundEmailContent,chr(9),"","ALL"),chr(13),"","ALL"),chr(10),"","ALL"))>
					<cfset local.emailtitle = "Refund Request for #local.qryEnrollment.firstName# #local.qryEnrollment.lastName# on SWL-#local.qryEnrollment.seminarID#">

					<cfset local.SWSiteInfo = application.objSiteInfo.getSiteInfo('SW')>

					<cfset application.objEmailWrapper.sendMailESQ(
						emailfrom={ name='SeminarWeb', email='<EMAIL>' },
						emailto=[{ name="", email="<EMAIL>" }],
						emailreplyto="",
						emailsubject=local.emailtitle,
						emailtitle=local.emailtitle,
						emailhtmlcontent=local.refundEmailContent,
						siteID=local.SWSiteInfo.siteID,
						memberID=local.SWSiteInfo.sysMemberID,
						messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SEMWEBREFUNDREQ"),
						sendingSiteResourceID=local.SWSiteInfo.siteSiteResourceID
					)>
				</cfif>

				<cfset local.data.success = true>
				<cfset local.data.hasSWRegRefund = local.hasSWRegRefund>
			</cfif>
		</cfif>
		
		<cfreturn local.data>
	</cffunction>	

	<cffunction name="registerSWLUser" access="public" output="false" returntype="void">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="signUpOrgCode" type="string" required="yes">
		<cfargument name="performedBy" type="numeric" required="yes">
		<cfargument name="sendEmailConnectionInstructions" type="boolean" required="yes">
		<cfargument name="emailOverride" type="string" required="no" default="">		

		<cfset var local = structnew()>
		<cfset local.qrySeminar = CreateObject("component","model.seminarweb.SWLiveSeminars").getSeminarBySeminarID(seminarID=arguments.seminarID)>
		<cfset local.qrymemberInfo = CreateObject("component","model.seminarWeb.SWCommon").getMemberInfoForSemWeb(enrollmentID=arguments.enrollmentID)>

		<cfset local.sendEmailConfirmation = false>
		<cfset local.sendReplayEmailConfirmation = false>

		<!--- seminar is open --->
		<cfif local.qrySeminar.isOpen>
			<cfset local.strAdd = addSWLProviderRegistrantID(seminarID=arguments.seminarID, registrantID=arguments.enrollmentID)>
			<cfset local.sendEmailConfirmation = arguments.sendEmailConnectionInstructions AND local.strAdd.success>
		<cfelse>
			<!--- closed seminar and offer replays --->
			<cfquery name="local.qrySendReplayConfirmation" dbtype="query">
				SELECT seminarID
				FROM [local].qrySeminar
				WHERE isOpen = 0
				AND offerReplay = 1
				AND isUploadedReplay = 1
				AND canResellSeminar = 1
				AND replayExpirationDate >= <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#now()#">
				AND allowRegistrants = 1
			</cfquery>
			<cfset local.sendReplayEmailConfirmation = local.qrySendReplayConfirmation.recordCount IS 1>
		</cfif>

		<cfif local.sendEmailConfirmation>
			<cfset CreateObject("component","model.seminarWeb.SWLiveEmails").sendConfirmation(seminarID=arguments.seminarID, enrollmentID=arguments.enrollmentID, 
					performedBy=arguments.performedBy, outgoingType="registerInstructions", withMaterials=false, orgcode=arguments.signUpOrgCode, emailOverride=arguments.emailOverride)>
		<cfelseif local.sendReplayEmailConfirmation>
			<cfset CreateObject("component","model.seminarWeb.SWLiveEmails").sendReplayConfirmation(seminarID=arguments.seminarID, enrollmentID=arguments.enrollmentID, 
					performedBy=arguments.performedBy, outgoingType="replayInstructions", orgcode=arguments.signUpOrgCode, emailOverride=arguments.emailOverride, isRegistrationConfirmation=1)>
		</cfif>
	</cffunction>

	<cffunction name="addSWLProviderRegistrantID" access="public" output="false" returntype="struct">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="registrantID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":false, "errmsg":"" }>

		<cfset local.qrySeminar = CreateObject("component","model.seminarweb.SWLiveSeminars").getSeminarBySeminarID(seminarID=arguments.seminarID)>

		<cfif arguments.registrantID gt 0 and local.qrySeminar.isOpen is 1>
			<cfif local.qrySeminar.providerID is 3 AND len(local.qrySeminar.ZoomWebinarID)>
				<cfset local.returnStruct = addSeminarRegistrantToZoomWebinar(seminarID=arguments.seminarID, registrantID=arguments.registrantID)>
			</cfif>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="addSeminarRegistrantToZoomWebinar" access="public" output="false" returntype="struct">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="registrantID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":false, "errmsg":"" }>

		<cfif application.MCEnvironment eq "Production">
			<cfset local.qrySeminar = CreateObject("component","model.seminarweb.SWLiveSeminars").getSeminarBySeminarID(seminarID=arguments.seminarID)>

			<cfquery name="local.qryRegistrant" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				select u.depoMemberDataID, eswl.ZoomWebinarRegistrantID
				from dbo.tblEnrollments as e
				inner join dbo.tblEnrollmentsSWLive as eswl on eswl.enrollmentID = e.enrollmentID
				inner join dbo.tblUsers as u on u.userID = e.userID
				where e.enrollmentID = <cfqueryparam value="#arguments.registrantID#" cfsqltype="CF_SQL_INTEGER">
				and e.seminarID = <cfqueryparam value="#arguments.seminarID#" cfsqltype="CF_SQL_INTEGER">
			</cfquery>

			<cfif local.qrySeminar.providerID is 3 and len(local.qrySeminar.ZoomWebinarID) and NOT len(local.qryRegistrant.ZoomWebinarRegistrantID)>
				<!--- get first and last name from assn data --->
				<cfset local.qrymemberInfo = CreateObject("component","model.seminarWeb.SWCommon").getMemberInfoForSemWeb(enrollmentID=arguments.registrantID)>

				<cfset local.strAddRegistrant = CreateObject("component","model.seminarweb.SWZoomWebinar").addWebinarRegistrant(webinarID=local.qrySeminar.ZoomWebinarID,
					firstname=local.qrymemberInfo.firstname, lastname=local.qrymemberInfo.lastname, email="swlive_l#arguments.seminarID#_r#arguments.registrantID#@seminarweblive.com")>
				<cfif local.strAddRegistrant.success>
					<cfquery name="local.updateEnrollmentSWL" datasource="#application.dsn.tlasites_seminarweb.dsn#">
						UPDATE dbo.tblEnrollmentsSWLive 
						SET ZoomWebinarRegistrantID = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strAddRegistrant.registrant.id#">,
							ZoomWebinarJoinURL = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strAddRegistrant.registrant.join_url#">
						WHERE enrollmentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.registrantID#">
					</cfquery>
					<cfset local.returnStruct.success = true>
				<cfelse>
					<cfset local.returnStruct = { success=false, errmsg='Problem Saving Registrant. The registrant was saved locally but we could not complete Zoom Webinar registration.' }>
					<cfreturn local.returnStruct>
				</cfif>
			</cfif>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getSeminarForms" access="public" returntype="query" output="no">
		<cfargument name="seminarID" type="numeric" required="yes">

		<cfset var local = StructNew()>	

		<cfstoredproc procedure="sw_getSeminarForms" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
			<cfprocresult name="local.qryForms" resultset="1">
		</cfstoredproc>
		
		<cfreturn local.qryForms>
	</cffunction>

	<cffunction name="getSeminarFormDetailForRegistrants" access="public" returntype="query" output="no">
		<cfargument name="seminarID" type="numeric" required="yes">

		<cfset var local = StructNew()>	

		<cfquery name="local.qryFormDetails" datasource="#application.dsn.tlasites_formbuilder.dsn#">
			set nocount on

			;WITH tmp AS (
				SELECT saf.seminarFormID, f.formtypeid, r.responseID, r.depomemberdataid, r.dateDelivered, r.dateCompleted, 
					r.passingPct, r.isActive, rd.questionID, rd.isCorrect
				FROM seminarweb.dbo.tblSeminarsAndFormResponses as safr
				INNER JOIN seminarweb.dbo.tblSeminarsAndForms as saf on saf.seminarFormID = safr.seminarFormID
					AND saf.seminarID = <cfqueryparam value="#arguments.seminarID#" cfsqltype="CF_SQL_INTEGER"> 
				inner join formbuilder.dbo.tblForms as f on f.formid = saf.formid and f.isDeleted = 0
				INNER JOIN formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID
				INNER JOIN formbuilder.dbo.tblResponseDetails as rd on rd.responseid = r.responseid
			), tmpCorr AS (
				select responseID, count(questionID) as correctCount
				from tmp
				where isCorrect = 1
				group by responseID
			)
			select seminarFormID, tmp.responseID, depomemberdataid, isActive, dateDelivered, dateCompleted, 
				formtypeid, passingPct, count(questionID) as questionCount, tmpCorr.correctCount
			from tmp
			left outer join tmpCorr on tmpCorr.responseID = tmp.responseID
			group by seminarFormID, tmp.responseID, depomemberdataid, isActive, dateDelivered, dateCompleted, 
				formtypeid, passingPct, tmpCorr.correctCount
			order by 1, 2

			set nocount off
		</cfquery>
		
		<cfreturn local.qryFormDetails>
	</cffunction>

	<cffunction name="saveRegAttendance" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">
		<cfargument name="seminarID" type="numeric" required="yes">

		<cfset var local = structnew()>

		<cfquery name="local.qryEnrollments" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			select e.enrollmentID, d.depomemberdataID, coalesce(me.email,d.email) as email
			from dbo.tblEnrollments as e
			inner join dbo.tblEnrollmentsSWLive as eswl on eswl.enrollmentID = e.enrollmentID
			inner join dbo.tblSeminars as s ON s.seminarID = e.seminarID
			inner join dbo.tblSeminarsSWLive as swl on swl.seminarID = s.seminarID
			inner join dbo.tblParticipants as p ON e.participantID = p.participantID 
			inner join dbo.tblUsers as u ON e.userID = u.userID
			inner join trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID 
			left outer join membercentral.dbo.ams_members as m
				inner join membercentral.dbo.ams_members as m2 on m2.orgID = m.orgID and m2.memberID = m.activeMemberID
				inner join membercentral.dbo.ams_memberEmailTags as metag on metag.orgID = m2.orgID and metag.memberID = m2.memberID
				inner join membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = m2.orgID and metagt.emailTagTypeID = metag.emailTagTypeID and metagt.emailTagType = 'Primary'
				inner join membercentral.dbo.ams_memberEmails as me on me.orgID = m2.orgID and me.memberID = metag.memberID and me.emailTypeID = metag.emailTypeID
				on m.memberID = e.MCMemberID 
			where e.seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
			and e.enrollmentID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="yes" value="0#arguments.event.getValue('enrollmentIDListFiltered',0)#">)
			and e.isActive = 1;
		</cfquery>
		
		<cfif local.qryEnrollments.recordCount>
			<cfset local.objSWL = CreateObject("component","model.seminarweb.SWLiveSeminars")>
			<cfset local.objSWCert = CreateObject("component","model.seminarweb.SWCertificates")>

			<cfloop query="local.qryEnrollments">
				<cfset local.thisEnrollJoinTime = arguments.event.getTrimValue('joinTime_#local.qryEnrollments.enrollmentID#','')>
				<cfset local.thisEnrollExitTime = arguments.event.getTrimValue('exitTime_#local.qryEnrollments.enrollmentID#','')>
				<cfset local.thisEnrollDuration = arguments.event.getTrimValue('duration_#local.qryEnrollments.enrollmentID#','')>

				<cfif len(local.thisEnrollJoinTime) and len(local.thisEnrollExitTime)>
					<cfset local.attended = true>
					<cfif NOT len(local.thisEnrollDuration)>
						<cfset local.duration = DateDiff("n",parseDateTime("#local.thisEnrollJoinTime#"),parseDateTime("#local.thisEnrollExitTime#"))>
					<cfelse>
						<cfset local.duration = local.thisEnrollDuration>
					</cfif>
				<cfelse>
					<cfset local.attended = false>
					<cfset local.duration = 0>
				</cfif>
				
				<cfstoredproc procedure="swl_updateWebAttendance" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryEnrollments.enrollmentID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#local.attended#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#ucase(local.thisEnrollJoinTime)#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#ucase(local.thisEnrollExitTime)#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.duration#">
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('polling_#local.qryEnrollments.enrollmentID#',0)#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
				</cfstoredproc>
				
				<!--- Manual update of web attendance needs to check completion and autosend certificates ---->
				<cfstoredproc procedure="swl_completeWebinar" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryEnrollments.enrollmentID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="1">
					<cfprocresult name="local.qryCompleteResult" resultset="1">
				</cfstoredproc>

				<cfif len(local.qryEnrollments.email) and IsValid("regex",local.qryEnrollments.email,application.regEx.email)>
					<cfif local.qryCompleteResult.passed is 1 and local.qryCompleteResult.allowAutoSendOfCertificate is 1>
						<cfset local.objSWL.logAction(seminarID=arguments.seminarID, typeName="logCertificateToAttendees", performedBy=session.cfcuser.memberdata.depomemberdataid, contact='', 
									enrollmentID=0, authorID=0, pending=2)>
						
						<cfset local.objSWCert.sendCertificateByEmail(enrollmentID=local.qryEnrollments.enrollmentID, performedBy=session.cfcuser.memberdata.depomemberdataID, 
									outgoingType="autoCertificate", emailToUse=local.qryEnrollments.email)>

						<cfset local.objSWL.logAction(seminarID=arguments.seminarID, typeName="logCertificateToAttendees", performedBy=session.cfcuser.memberdata.depomemberdataid, contact='', 
									enrollmentID=0, authorID=0, pending=1)>
					</cfif>
				</cfif>
			</cfloop>
		</cfif>
	</cffunction>

	<cffunction name="getUploadSWLReplayFileSettings" access="public" output="false" returntype="struct">
		<cfargument name="seminarID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>

		<cfquery name="local.qryParticipant" datasource="#application.dsn.platformQueue.dsn#">
			SELECT s.seminarID, p.participantID, p.orgCode AS participantOrgCode
			FROM seminarWeb.dbo.tblSeminars as s
			INNER JOIN seminarWeb.dbo.tblSeminarsSWLive as swl on swl.seminarID = s.seminarID
			INNER JOIN seminarWeb.dbo.tblParticipants as p on p.participantID = s.participantID
			INNER JOIN membercentral.dbo.sites AS mcs ON mcs.siteCode = p.orgCode
			WHERE s.seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">;
		</cfquery>

		<cfset local.bucket = "seminarweb">
		<cfset local.returnStruct["accesskey"] = "********************">
		<cfset local.secretKey = "UJZ/JsekvjTNCJOK3UmUUPIAJwEwsoiqnq2jybdX">

		<cfset local.http = "http://">
		<cfif application.objPlatform.isRequestSecure()>
			<cfset local.http = "https://">
		</cfif>

		<cfset local.returnStruct["uploadurl"] = "#local.http##local.bucket#.s3.amazonaws.com">
		<cfset local.returnStruct["objectkey"] = lcase("swlreplay/#local.qryParticipant.participantOrgCode#/#local.qryParticipant.participantID#/#local.qryParticipant.seminarID#.mp4")>
		<cfset local.returnStruct["seminarid"] = arguments.seminarID>

		<!--- generating policy & signature here due to the difference in aws access & secret key @application.objS3 --->
		<cfset local.dateString = DateAdd("d",1,Now())>

		<cfsavecontent variable="local.stringPolicy">
			<cfoutput>
			{
				"expiration": "#DateFormat(local.dateString,"yyyy-mm-dd")#T12:00:00.000Z",
				"conditions": [
				{"bucket": "#local.bucket#" },
				{"acl": "public-read" },
				["starts-with", "$key", "#left(local.returnStruct.objectkey,len(local.returnStruct.objectkey)-1)#"],
				["starts-with", "$content-type", ""],
				["starts-with", "$name", ""],
				["starts-with", "$filename", ""]
				]
			}
			</cfoutput>
		</cfsavecontent>
		
		<!--- Replace "\n" with chr(10) to get a correct digest --->
		<cfset local.fixedData = replace(local.stringPolicy,"\n",chr(10),"all")>
		<cfset local.returnStruct["policy"] = ToBase64(local.fixedData)>

		<!--- Calculate the hash of the information --->
		<cfset local.signingKey = createObject("java","javax.crypto.spec.SecretKeySpec").init(local.secretKey.getBytes(),'HmacSHA1')>
		<cfset local.mac = createObject("java","javax.crypto.Mac").getInstance('HmacSHA1')>
		<cfset local.mac.init(local.signingKey)>
		<cfset local.returnStruct["policysignature"] = toBase64(mac.doFinal(local.returnStruct.policy.getBytes()))>

		<cfset local.returnStruct["success"] = true>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="setSWLReplayUploadedFlag" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="seminarID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasUpdateSeminarRights(siteID=arguments.mcproxy_siteID, seminarID=arguments.seminarID, action="Edit", checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery name="local.qryUpdateReplayUploadedFlag" datasource="#application.dsn.platformQueue.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					DECLARE @seminarID int, @orgID int, @siteID int, @recordedByMemberID int;

					SET @seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">;
					SET @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;

					SELECT @orgID = mcs.orgID, @siteID = mcs.siteID
					FROM seminarWeb.dbo.tblSeminars as s
					INNER JOIN seminarWeb.dbo.tblSeminarsSWLive as swl on swl.seminarID = s.seminarID
					INNER JOIN seminarWeb.dbo.tblParticipants as p on p.participantID = s.participantID
					INNER JOIN membercentral.dbo.sites AS mcs ON mcs.siteCode = p.orgCode
					WHERE s.seminarID = @seminarID
					AND s.isDeleted = 0;
					
					UPDATE seminarWeb.dbo.tblSeminarsSWLive
					SET isUploadedReplay = 1
					WHERE seminarID = @seminarID;
					
					INSERT INTO dbo.queue_mongo (msgjson)
					VALUES('{ "c":"auditLog", "d": {
						"AUDITCODE":"SW",
						"ORGID":' + CAST(@orgID AS varchar(10)) + ',
						"SITEID":' + CAST(@siteID AS varchar(10)) + ',
						"ACTORMEMBERID":' + CAST(@recordedByMemberID AS varchar(20)) + ',
						"ACTIONDATE":"' + CONVERT(varchar(20),GETDATE(),120) + '",
						"MESSAGE":"Replay video has been added to ' + 'SWL-' + cast(@seminarID as varchar(10)) + '" } }');
				END TRY
				BEGIN CATCH
					IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
					EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.data.success = true>
			<cfset local.data.replayvideolink = CreateObject("component","model.seminarweb.SWLiveSeminars").getSWLReplayVideoLinkFromSeminarID(seminarID=arguments.seminarID).replayVideoLink>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="deleteSWLReplayVideo" access="public" output="false" returntype="struct" hint="Delete video">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="seminarID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasUpdateSeminarRights(siteID=arguments.mcproxy_siteID, seminarID=arguments.seminarID, action="Edit", checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery name="local.qryRemoveSWLReplayVideo" datasource="#application.dsn.platformQueue.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					DECLARE @seminarID int, @objectkey VARCHAR(50), @s3DeleteReadyStatusID int, @orgID int, @siteID int,
					@msgjson varchar(max), @nowdate datetime = getdate(), @recordedByMemberID int, @participantID int, @participantOrgCode varchar(10);

					SET @seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">;
					SET @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;

					EXEC dbo.queue_getStatusIDbyType @queueType='s3Delete', @queueStatus='readyToProcess', @queueStatusID=@s3DeleteReadyStatusID OUTPUT;

					SELECT @participantID = p.participantID, @participantOrgCode = p.orgCode, @orgID = mcs.orgID, @siteID = mcs.siteID
					FROM seminarWeb.dbo.tblSeminars as s
					INNER JOIN seminarWeb.dbo.tblSeminarsSWLive as swl on swl.seminarID = s.seminarID
					INNER JOIN seminarWeb.dbo.tblParticipants as p on p.participantID = s.participantID
					INNER JOIN membercentral.dbo.sites AS mcs ON mcs.siteCode = p.orgCode
					WHERE s.seminarID = @seminarID
					AND s.isDeleted = 0;
					
					SET @objectkey = LOWER('swlreplay/'+ cast(@participantOrgCode as varchar(10)) +'/'+ cast(@participantID as varchar(10)) +'/'+ cast(@seminarID as varchar(10)) +'.mp4');

					BEGIN TRAN;
						INSERT INTO dbo.queue_S3Delete (statusID, s3bucketName, objectKey, dateAdded, dateUpdated)
						VALUES (@s3DeleteReadyStatusID, 'seminarweb', @objectkey, @nowdate, @nowdate);

						UPDATE seminarWeb.dbo.tblSeminarsSWLive
						SET isUploadedReplay = 0
						WHERE seminarID = @seminarID;
					COMMIT TRAN;

					SET @msgjson = 'Replay video has been deleted from ' + 'SWL-' + cast(@seminarID as varchar(10));
					
					INSERT INTO dbo.queue_mongo (msgjson)
					VALUES('{ "c":"auditLog", "d": {
						"AUDITCODE":"SW",
						"ORGID":' + CAST(@orgID AS varchar(10)) + ',
						"SITEID":' + CAST(@siteID AS varchar(10)) + ',
						"ACTORMEMBERID":' + CAST(@recordedByMemberID AS varchar(20)) + ',
						"ACTIONDATE":"' + CONVERT(varchar(20),GETDATE(),120) + '",
						"MESSAGE":"' + REPLACE(memberCentral.dbo.fn_cleanInvalidXMLChars(@msgjson),'"','\"') + '" } }');
				END TRY
				BEGIN CATCH
					IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
					EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="prepareWebAttendanceZoomWebinars" access="public" output="false" returntype="struct">
		<cfargument name="sitecode" type="string" required="yes">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="strWebinarReport" type="struct" required="yes">

		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.sitecode)>

		<cfset local.CTtargetZoneOffset = -6>
		<cfset local.TimeZoneInfo = GetTimeZoneInfo()>
		<cfif local.TimeZoneInfo.isDSTOn>
			<cfset local.CTtargetZoneOffset = local.CTtargetZoneOffset + 1>
		</cfif>

		<cftry>
			<cfquery name="local.qryZoomData" datasource="#application.dsn.tlasites_seminarweb.dsn#" result="local.qryZoomDataResult">
				SET NOCOUNT ON;

				DECLARE @success bit = 0, @xmlReport xml;

				IF OBJECT_ID('tempdb..##tmpParticipants') IS NOT NULL 
					DROP TABLE ##tmpParticipants;
				IF OBJECT_ID('tempdb..##tmpPolls') IS NOT NULL 
					DROP TABLE ##tmpPolls;
				IF OBJECT_ID('tempdb..##tmpAttendeeReportFlat') IS NOT NULL 
					DROP TABLE ##tmpAttendeeReportFlat;
				
				CREATE TABLE ##tmpParticipants (ZoomWebinarRegistrantID varchar(30), userEmail varchar(200), enterDate datetime, 
					exitDate datetime, numMinutes int, enrollmentID int);
				CREATE TABLE ##tmpPolls (userEmail varchar(200), numpolls int);
				CREATE TABLE ##tmpAttendeeReportFlat (ZoomWebinarRegistrantID varchar(30), userEmail varchar(200), enterDate varchar(25), 
					exitDate varchar(25), numMinutes int, enrollmentID int, polling varchar(3));

				<cfloop array="#arguments.strWebinarReport.arrParticipants#" index="local.thisRow">
					INSERT INTO ##tmpParticipants (ZoomWebinarRegistrantID, userEmail, enterDate, exitDate, numMinutes)
					VALUES ('#local.thisRow.id#', '#left(local.thisRow.user_email,200)#', #application.objCommon.DateConvertISO8601(local.thisRow.join_time, local.CTtargetZoneOffset)#, #application.objCommon.DateConvertISO8601(local.thisRow.leave_time, local.CTtargetZoneOffset)#, #int(local.thisRow.duration/60)#);
				</cfloop>
				<cfloop array="#arguments.strWebinarReport.arrPolls#" index="local.thisRow">
					INSERT INTO ##tmpPolls (userEmail, numpolls)
					VALUES ('#left(local.thisRow.email,200)#', #local.thisRow.numPolls#);
				</cfloop>

				EXEC dbo.swl_importWebAttendanceZoomWebinars_prepare @seminarID=#arguments.seminarID#;

				IF (select count(*) from ##tmpAttendeeReportFlat) > 0 BEGIN
					select @xmlReport = cast(isnull((
						select enrollmentID, enterDate, exitDate, numMinutes, polling 
						from ##tmpAttendeeReportFlat as row
						for xml auto, ROOT('report')
					),'<report/>') as xml);

					DECLARE @selectsql varchar(max) = '
						SELECT enrollmentID, enterDate, exitDate, numMinutes, polling, ROW_NUMBER() OVER(order by enrollmentID) as mcCSVorder 
						*FROM* ##tmpAttendeeReportFlat';
					EXEC membercentral.dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#local.strFolder.folderPathUNC#\#arguments.seminarID#_AttendanceReport.csv', @returnColumns=0;

					set @success = 1;
				END

				select @success as success, @xmlReport as xmlReport;

				IF OBJECT_ID('tempdb..##tmpParticipants') IS NOT NULL 
					DROP TABLE ##tmpParticipants;
				IF OBJECT_ID('tempdb..##tmpPolls') IS NOT NULL 
					DROP TABLE ##tmpPolls;
				IF OBJECT_ID('tempdb..##tmpAttendeeReportFlat') IS NOT NULL 
					DROP TABLE ##tmpAttendeeReportFlat;
			</cfquery>

			<cfset local.strReturn.success = local.qryZoomData.success>
			<cfset local.strReturn.xmlReport = local.qryZoomData.xmlReport>
			<cfset local.strReturn.csvFolder = local.strFolder.folderPathEnc>
			<cfset local.strReturn.csvFileName = "#arguments.seminarID#_AttendanceReport.csv">
			
		<cfcatch type="Any">
			<cfset local.strReturn.success = false>
			<cfset local.strReturn.xmlReport = "">
			<cfset local.strReturn.csvFolder = "">
			<cfset local.strReturn.csvFileName = "">
			<cfset local.strError = { argumentsscope:arguments, localscope:local }>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local.strError)>
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="saveWebAttendanceXML" access="public" output="false" returntype="boolean">
		<cfargument name="sitecode" type="string" required="yes">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="performedBy" type="numeric" required="yes">
		<cfargument name="xmlReport" type="string" required="yes">

		<cfset var local = StructNew()>
		<cfset local.success = false>
		<cfset local.arrAttendance = XMLSearch(arguments.xmlReport,"/report/row")>

		<cftry>
			<cfquery name="local.qryImportAttendance" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				SET NOCOUNT ON;

				IF OBJECT_ID('tempdb..##tmpAttendeeReport') IS NOT NULL
					DROP TABLE ##tmpAttendeeReport;
				CREATE TABLE ##tmpAttendeeReport (enrollmentID int, enterDate datetime, exitDate datetime, numMinutes int, polling varchar(3));

				DECLARE @seminarID int, @orgID INT, @siteID INT, @recordedByMemberID int, @provider varchar(40);
				SET @seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">;
				SET @recordedByMemberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#session.cfcUser.memberData.memberID#">;
				SELECT @orgID = orgID, @siteID = siteID FROM memberCentral.dbo.sites WHERE siteCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.siteCode#">;

				SELECT @provider = sp.provider 
				FROM dbo.tblSeminarsSWLive s
				LEFT JOIN dbo.tblSeminarsSWLiveProviders sp ON sp.providerID = s.providerID
				WHERE seminarID = @seminarID;

				<!--- had to remove cfqueryparam because it would result in "The incoming request has too many parameters" exception for large seminars --->
				<cfloop from="1" to="#ArrayLen(local.arrAttendance)#" index="local.thisRow">
					<cfset local.DCxmlDateNoMS = Replace(local.arrAttendance[local.thisRow].xmlAttributes.enterDate,'T',' ')>
					<cfset local.DExmlDateNoMS = Replace(local.arrAttendance[local.thisRow].xmlAttributes.exitDate,'T',' ')>
					
					INSERT INTO ##tmpAttendeeReport (enrollmentID, enterDate, exitDate, numMinutes, polling)
					VALUES (#local.arrAttendance[local.thisRow].xmlAttributes.enrollmentID#, '#local.DCxmlDateNoMS#', '#local.DExmlDateNoMS#', 
						#local.arrAttendance[local.thisRow].xmlAttributes.numMinutes#, '#local.arrAttendance[local.thisRow].xmlAttributes.polling#');
				</cfloop>

				EXEC dbo.swl_importWebAttendance @seminarID=@seminarID, @recordedByMemberID=@recordedByMemberID;

				INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
				VALUES('{ "c":"auditLog", "d": {
					"AUDITCODE":"SW",
					"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
					"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
					"ACTORMEMBERID":' + CAST(@recordedByMemberID AS VARCHAR(20)) + ',
					"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
					"MESSAGE":"' + REPLACE('Attendance has been imported from the pulled report (from '+ @provider +') for SWL-' + CAST(@seminarID AS VARCHAR(10)) + '.','"','\"') + '" } }');

				IF OBJECT_ID('tempdb..##tmpAttendeeReport') IS NOT NULL
					DROP TABLE ##tmpAttendeeReport;
			</cfquery>

			<cfset createObject("component","model.seminarweb.SWLiveSeminars").logAction(seminarID=arguments.seminarID, typeName="logWebAttendanceUpload", performedBy=arguments.performedBy,
				contact='', enrollmentID=0, authorID=0, pending=1)>

			<cfset local.success = true>
		<cfcatch type="Any">
			<cfset local.success = false>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>

	<cffunction name="saveWebAttendanceCSV" access="public" output="false" returntype="boolean">
		<cfargument name="sitecode" type="string" required="yes">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="performedBy" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.success = false>

		<!--- upload file --->
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.sitecode)>
		<cffile action="UPLOAD" filefield="frmReport" destination="#local.strFolder.folderPath#" result="local.fileUploadResult" nameconflict="OVERWRITE">
		<cfset local.tmpResultsFile = "#local.strFolder.folderPath#/#local.fileUploadResult.ServerFile#">

		<!--- ensure CSV --->
		<cfif local.fileUploadResult.serverfileExt neq "csv">
			<cffile action="DELETE" file="#local.tmpResultsFile#">
			<cfreturn false>
		</cfif>

		<!--- read in the CSV file and parse it --->
		<cfset local.fileReader = createobject("java","java.io.FileReader").init(local.tmpResultsFile)>
		<cfset local.csvReader = createObject("java","au.com.bytecode.opencsv.CSVReader").init(local.fileReader)>
		<cftry>
			<!--- read in first row (the headers) to start us off --->
			<cfset local.row = local.csvReader.readNext()>
			<cfset local.counter = 0>
			<cfset local.arrRowsCF = arrayNew(2)>
			
			<!--- loop while we have data --->
			<cfloop condition="#structKeyExists(local,"row")# and #isArray(local.row)#">
				<cfscript>
				// increase counter
				local.counter = local.counter + 1;
	
				// convert java array to cf array
				local.arrRow = ArrayNew(1);
				local.arrRow.AddAll(CreateObject("java","java.util.Arrays").AsList(local.row));

				// ensure arrays are all the same length. excel tends to drop empty elements at the end of rows
				if (local.counter is 1) local.masterDepth = arrayLen(local.arrRow);
				else if (arrayLen(local.arrRow) is not local.masterDepth) arraySet(local.arrRow,arrayLen(local.arrRow)+1,local.masterDepth,'');
				
				// append to master array
				local.arrRowsCF[arraylen(local.arrRowsCF) + 1] = local.arrRow;
				
				// get the next row
				local.row = local.csvReader.readNext();
				</cfscript>
			</cfloop>
		<cfcatch type="any">
			<cfset local.fileReader.close()>
			<cfreturn false>
		</cfcatch>
		</cftry>

		<!--- convert array to WDDX (xml) --->
		<cfwddx action="CFML2WDDX" input="#local.arrRowsCF#" output="local.wddxRows">

		<!--- transform to SQL statements --->
		<cfsavecontent variable="local.xsl">
			<cfoutput>
			<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform">
			<xsl:output method="text" omit-xml-declaration="yes" indent="no" encoding="ISO-8859-1" /> 
			<xsl:template match="/">
				<xsl:text>
				CREATE TABLE ##tmpAttendeeReport (</xsl:text>
				<xsl:for-each select="//data/array/array[position() = 1]/string">
					<xsl:text>[</xsl:text><xsl:value-of select="." /><xsl:text>] varchar(max)</xsl:text><xsl:if test="position()!= last()">,</xsl:if>
				</xsl:for-each>
				<xsl:text>)
				</xsl:text>
				<xsl:apply-templates select="//data/array"/>
			</xsl:template>
			<xsl:template match="//data/array">
				<xsl:for-each select="array[position() > 1]">
					<xsl:text>INSERT INTO ##tmpAttendeeReport (</xsl:text>
					<xsl:for-each select="../array[position() = 1]/string">
						<xsl:text>[</xsl:text><xsl:value-of select="." /><xsl:text>]</xsl:text><xsl:if test="position()!= last()">,</xsl:if>
					</xsl:for-each>
					<xsl:text>)
					</xsl:text>
					<xsl:text>VALUES (</xsl:text>
					<xsl:for-each select="string">
						<xsl:text>'</xsl:text><xsl:call-template name="cleanQuote"><xsl:with-param name="string"><xsl:value-of select="." /></xsl:with-param></xsl:call-template><xsl:text>'</xsl:text><xsl:if test="position()!= last()">,</xsl:if>
					</xsl:for-each>
					<xsl:text>)
					</xsl:text>
				</xsl:for-each>
			</xsl:template>
			<xsl:template name="cleanQuote">
				<xsl:param name="string" />
				<xsl:if test='contains($string, "&##x27;")'><xsl:value-of select='substring-before($string, "&##x27;")' />''<xsl:call-template name="cleanQuote">
					<xsl:with-param name="string"><xsl:value-of select='substring-after($string, "&##x27;")' /></xsl:with-param>
				</xsl:call-template>
				</xsl:if>
				<xsl:if test='not(contains($string, "&##x27;"))'><xsl:value-of select="$string" /></xsl:if>
			</xsl:template>
			</xsl:stylesheet>
			</cfoutput>
		</cfsavecontent>

		<cfset local.sqlText = XMLTransform(local.wddxRows,local.xsl)>

		<cftry>
			<cfquery name="local.qryImportAttendance" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					IF OBJECT_ID('tempdb..##tmpAttendeeReport') IS NOT NULL
						DROP TABLE ##tmpAttendeeReport;

					DECLARE @seminarID int, @orgID INT, @siteID INT, @recordedByMemberID int, @provider varchar(40);
					SET @seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">;
					SET @recordedByMemberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#session.cfcUser.memberData.memberID#">;
					SELECT @orgID = orgID, @siteID = siteID FROM memberCentral.dbo.sites WHERE siteCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.siteCode#">;

					SELECT @provider = sp.provider 
					FROM dbo.tblSeminarsSWLive s
					LEFT JOIN dbo.tblSeminarsSWLiveProviders sp ON sp.providerID = s.providerID
					WHERE seminarID = @seminarID;

					#PreserveSingleQuotes(local.sqlText)#

					<!--- this will get the columns that are required --->
					IF OBJECT_ID('tempdb..##tblReqCols') IS NOT NULL
						DROP TABLE ##tblReqCols;
					CREATE TABLE ##tblReqCols (ORDINAL_POSITION int, COLUMN_NAME sysname);

					insert into ##tblReqCols
					select 1, 'enrollmentID' union all
					select 2, 'enterDate' union all
					select 3, 'exitDate' union all
					select 4, 'numMinutes' union all
					select 5, 'polling';

					<!--- this will get the columns that are actually in the import --->
					IF OBJECT_ID('tempdb..##tblImpCols') IS NOT NULL
						DROP TABLE ##tblImpCols;
					CREATE TABLE ##tblImpCols (ORDINAL_POSITION int, COLUMN_NAME sysname);

					insert into ##tblImpCols
					select column_id, [name]
					from tempdb.sys.columns
					where object_id = object_id('tempdb..##tmpAttendeeReport');

					<!--- any required columns missing? --->
					IF EXISTS (
						select req.column_name
						from ##tblReqCols as req
						left outer join ##tblImpCols as imp on imp.column_name = req.column_name
						where imp.ORDINAL_POSITION is null
					) RAISERROR('missing columns',16,1);

					<!--- any extra columns? --->
					IF EXISTS (
						select imp.column_name
						from ##tblImpCols as imp
						left outer join ##tblReqCols as req on req.column_name = imp.column_name
						where req.ORDINAL_POSITION is null
					) RAISERROR('extra columns',16,1);

					<!--- column data checks --->
					ALTER TABLE ##tmpAttendeeReport ALTER COLUMN enrollmentID int not null;
					ALTER TABLE ##tmpAttendeeReport ALTER COLUMN enterDate datetime not null;
					ALTER TABLE ##tmpAttendeeReport ALTER COLUMN exitDate datetime not null;
					ALTER TABLE ##tmpAttendeeReport ALTER COLUMN numMinutes int not null;
					IF EXISTS (select top 1 enrollmentID from ##tmpAttendeeReport where polling not in ('Yes','No'))
	 					RAISERROR('bad polling',16,1);					

					EXEC dbo.swl_importWebAttendance @seminarID=@seminarID, @recordedByMemberID=@recordedByMemberID;

					INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
					VALUES('{ "c":"auditLog", "d": {
						"AUDITCODE":"SW",
						"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
						"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
						"ACTORMEMBERID":' + CAST(@recordedByMemberID AS VARCHAR(20)) + ',
						"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
						"MESSAGE":"' + REPLACE('Attendance has been imported from the updated version of the pulled report (from '+ @provider +') for SWL-' + CAST(@seminarID AS VARCHAR(10)) + '.','"','\"') + '" } }');
				
					IF OBJECT_ID('tempdb..##tblReqCols') IS NOT NULL
						DROP TABLE ##tblReqCols;
					IF OBJECT_ID('tempdb..##tblImpCols') IS NOT NULL
						DROP TABLE ##tblImpCols;
					IF OBJECT_ID('tempdb..##tmpAttendeeReport') IS NOT NULL
						DROP TABLE ##tmpAttendeeReport;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<!--- log it --->
			<cfset createObject("component","model.seminarweb.SWLiveSeminars").logAction(seminarID=arguments.seminarID, typeName="logWebAttendanceUpload",
				performedBy=arguments.performedBy, contact='', enrollmentID=0, authorID=0, pending=1)>
			
			<cfset local.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>

	<cffunction name="autoSendCertificateToAll" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="performedBy" type="numeric" required="yes">
		
		<cfset var local = structnew()>
		<cfset local.objSWL = CreateObject("component","model.seminarweb.SWLiveSeminars")>
		<cfset local.objSWCert = CreateObject("component","model.seminarweb.SWCertificates")>

		<cfset local.qryEnrollments = getEnrollments(seminarID=arguments.seminarID)>
		<cfset local.objSWL.logAction(seminarID=arguments.seminarID, typeName="logCertificateToAttendees", performedBy=arguments.performedBy, contact='', enrollmentID=0, authorID=0, pending=2)>
		
		<cfloop query="local.qryEnrollments">
			<cfstoredproc procedure="swl_completeWebinar" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryEnrollments.enrollmentID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="1">
				<cfprocresult name="local.qryCompleteResult" resultset="1">
			</cfstoredproc>

			<cfif local.qryCompleteResult.passed is 1 and local.qryCompleteResult.allowAutoSendOfCertificate is 1>			
				<cfset local.objSWCert.sendCertificateByEmail(enrollmentID=local.qryEnrollments.enrollmentID, performedBy=arguments.performedBy, outgoingType="autoCertificate", emailToUse=local.qryEnrollments.email)>
			</cfif>
		</cfloop>

		<cfset local.objSWL.logAction(seminarID=arguments.seminarID, typeName="logCertificateToAttendees", performedBy=arguments.performedBy, contact='', enrollmentID=0, authorID=0, pending=1)>
	</cffunction>

	<cffunction name="getEnrollmentDataForEmailing" access="public" output="false" returntype="struct">
		<cfargument name="enrollmentID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.retStruct = structNew()>

		<cfquery name="local.retStruct.qryData" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SELECT m.activeMemberID AS memberID, d.depomemberdataID
			FROM dbo.tblEnrollments AS e
			INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID
			INNER JOIN dbo.tblUsers u ON u.userID = e.userID
			INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID
			INNER JOIN membercentral.dbo.ams_members AS m ON m.memberID = e.MCMemberID
			WHERE e.enrollmentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.enrollmentID#">
			AND e.isActive = 1
		</cfquery>

		<cfset local.retStruct.extendedLinkedMergeCode = "">
		<cfset local.retStruct.arrResTypeMergeCodes = arrayNew(1)>

		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="getFilteredRegistrantsForEmailing" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any" required="true">

		<cfset var local = structNew()>
		<cfset local.retStruct = { itemIDList='', toolType='SeminarWebAdmin', catTreeCode='ETSEMWEB', extendedLinkedMergeCode='', extraMergeTagList='', errorCode='' }>

		<cfset local.qryRegistrants = getRegistrants(sitecode=arguments.event.getValue('mc_siteInfo.siteCode'), mode="massEmailReg", seminarID=arguments.event.getValue('pid',0), 
			rAttended=arguments.event.getValue('rAttended',''), rDateFrom=arguments.event.getValue('rDateFrom',''), rDateTo=arguments.event.getValue('rDateTo',''), 
			rHideDeleted=arguments.event.getValue('rHideDeleted',1), rCredits=arguments.event.getValue('rCredits',''), memberID=0, emailTagTypeID=arguments.event.getValue('emailTagType',0))>

		<cfif local.qryRegistrants.recordcount is 0>
			<cfset local.retStruct.errorCode = 'norecipient'>
			<cfreturn local.retStruct>
		
		<!--- no email ids defined --->
		<cfelseif val(local.qryRegistrants.membersWithEmail) eq 0>
			<cfset local.retStruct.errorCode = 'noemailrecipient'>
			<cfreturn local.retStruct>
		</cfif>

		<cfset local.retStruct.itemIDList = valueList(local.qryRegistrants.enrollmentID)>

		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="getSeminarForRegistrationByAdmin" access="public" returntype="struct" output="no">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="catalogOrgCode" type="string" required="yes">
		<cfargument name="billingState" type="string" required="yes">
		<cfargument name="billingZip" type="string" required="yes">
		<cfargument name="depoMemberDataID" type="numeric" required="yes">
		<cfargument name="memberID" type="numeric" required="yes">

		<cfset var local = structnew()>

		<cfstoredproc procedure="swl_getSeminarForRegistrationByAdmin" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.catalogOrgCode#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.billingState#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.billingZip#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.depoMemberDataID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
			<cfprocresult name="local.qrySeminar" resultset="1">
			<cfprocresult name="local.qrySeminarPrices" resultset="2">
		</cfstoredproc>
		
		<!--- Add timezone specific display to query --->
		<cfset local.qrySeminar = CreateObject("component","model.seminarweb.SWLiveSeminars").addParsedTimeZoneToSeminars(local.qrySeminar,arguments.catalogOrgCode)>

		<cfreturn local>
	</cffunction>

	<cffunction name="getFormBuilderEnrollments" access="private" returntype="query" output="no">
		<cfargument name="seminarID" type="numeric" required="yes">
		
		<cfset var local = StructNew()>
		
		<cfstoredproc procedure="swl_getFormBuilderEnrollments" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
			<cfprocresult name="local.qryEnrollments" resultset="1">
		</cfstoredproc>

		<cfreturn local.qryEnrollments>
	</cffunction>

	<cffunction name="getSeminarEnrollmentLogs" access="private" returntype="query" output="no">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="typeName" type="string" required="yes">

		<cfset var qryLogData = "">

		<cfquery name="qryLogData" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			select l.enrollmentID
			from dbo.tblLogSWLive as l
			inner join dbo.tblLogTypes as t on t.typeID = l.typeID
			where t.typeName = <cfqueryparam value="#arguments.typeName#" cfsqltype="CF_SQL_VARCHAR">
			and l.seminarID = <cfqueryparam value="#arguments.seminarID#" cfsqltype="CF_SQL_INTEGER">
			and l.enrollmentID is not null;
		</cfquery>

		<cfreturn qryLogData>
	</cffunction>

	<cffunction name="getSeminarAuthorsLogs" access="private" returntype="query" output="no">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="typeName" type="string" required="yes">

		<cfset var qryLogData = "">

		<cfquery name="qryLogData" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			select l.authorID
			from dbo.tblLogSWLive as l
			inner join dbo.tblLogTypes as t on t.typeID = l.typeID
			where t.typeName = <cfqueryparam value="#arguments.typeName#" cfsqltype="CF_SQL_VARCHAR">
			and l.seminarID = <cfqueryparam value="#arguments.seminarID#" cfsqltype="CF_SQL_INTEGER">
			and l.authorID is not null;
		</cfquery>

		<cfreturn qryLogData>
	</cffunction>

	<cffunction name="sendConfirmationToAllRegistrants" access="public" returntype="void" output="no">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="outgoingType" type="string" required="yes">
		
		<cfset var local = structnew()>
		<cfset local.objSWLEmail = CreateObject("component","model.seminarweb.SWLiveEmails")>
		<cfset local.qrySeminar = CreateObject("component","model.seminarweb.SWLiveSeminars").getSeminarBySeminarID(seminarID=arguments.seminarID)>

		<!--- hotfix-8413596 If the seminar has not happened we need to grab all enrollments to send the email to --->
		<cfif local.qrySeminar.isOpen is 1>
			<cfset local.qryEnrollments = getEnrollments(seminarID=arguments.seminarID)>		
		<cfelse>
			<cfset local.qryEnrollments = getFormBuilderEnrollments(seminarID=arguments.seminarID)>
		</cfif>
		<cfset local.qryLogEnrollments = getSeminarEnrollmentLogs(seminarID=arguments.seminarID, typeName=arguments.outgoingType)>
		<cfset local.logEnrollmentList = valueList(local.qryLogEnrollments.enrollmentID)>
		<cfloop query="local.qryEnrollments">
			<!--- send email if the seminar is not closed (has not happened yet) or only send email to people who attended the seminar --->
			<cfif ((local.qrySeminar.isOpen is 1) OR (local.qrySeminar.isOpen is 0 AND local.qryEnrollments.attended is 1))
					and not listFind(local.logEnrollmentList, local.qryEnrollments.enrollmentID)>
				<cfset local.objSWLEmail.sendConfirmation(seminarID=arguments.seminarID, enrollmentID=local.qryEnrollments.enrollmentID, 
					performedBy=0, outgoingType=arguments.outgoingType, withMaterials=false, orgcode=local.qryEnrollments.orgcode,
					emailOverride="", customtext="", forceConnectionInstructions=1)>
			</cfif>
		</cfloop>
	</cffunction>

	<cffunction name="sendMaterialsToAllRegistrants" access="public" returntype="void" output="no">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="outgoingType" type="string" required="yes">
		
		<cfset var local = structnew()>
		<cfset local.objSWLEmail = CreateObject("component","model.seminarweb.SWLiveEmails")>
		<cfset local.qrySeminar = CreateObject("component","model.seminarweb.SWLiveSeminars").getSeminarBySeminarID(seminarID=arguments.seminarID)>

		<!--- hotfix-8413596 If the seminar has not happened we need to grab all enrollments to send the email to --->
		<cfif local.qrySeminar.isOpen is 1>
			<cfset local.qryEnrollments = getEnrollments(seminarID=arguments.seminarID)>		
		<cfelse>
			<cfset local.qryEnrollments = getFormBuilderEnrollments(seminarID=arguments.seminarID)>
		</cfif>
		<cfset local.qryLogEnrollments = getSeminarEnrollmentLogs(seminarID=arguments.seminarID, typeName=arguments.outgoingType)>
		<cfset local.logEnrollmentList = valueList(local.qryLogEnrollments.enrollmentID)>
		<cfloop query="local.qryEnrollments">
			<!--- send email if the seminar is not closed (has not happened yet) or only send email to people who attended the seminar --->
			<cfif ((local.qrySeminar.isOpen is 1) OR (local.qrySeminar.isOpen is 0 AND local.qryEnrollments.attended is 1))
					and not listFind(local.logEnrollmentList, local.qryEnrollments.enrollmentID)>
				<cfset local.objSWLEmail.sendMaterials(seminarID=arguments.seminarID, enrollmentID=local.qryEnrollments.enrollmentID, 
					performedBy=0, outgoingType=arguments.outgoingType, emailToUse="", orgcode=local.qryEnrollments.orgcode,
					customtext="")>
			</cfif>
		</cfloop>
	</cffunction>

	<cffunction name="sendSpeakerInstructionsToAllAuthors" access="public" returntype="void" output="no">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="performedBy" type="numeric" required="yes">
		<cfargument name="outgoingType" type="string" required="yes">
		<cfargument name="emailOverride" type="string" required="no" default="">
		<cfargument name="allowResend" type="boolean" required="no" default="true">
		<cfargument name="customtext" type="string" required="no" default="">
		<cfargument name="isImportantCustomText" type="boolean" required="no" default="0">
		
		<cfset var local = structnew()>
		<cfset local.objSWAuthors = CreateObject("component","seminarWebAuthors")>
		<cfset local.qryLinkedAuthors = CreateObject("component","model.seminarWeb.SWAuthors").getAuthorsBySeminarID(seminarID=arguments.seminarID, authorType='Speaker')>
		<cfset local.qrySeminar = CreateObject("component","model.seminarweb.SWLiveSeminars").getSeminarBySeminarID(seminarID=arguments.seminarID)>

		<cfquery name="local.qrySWLProvider" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			select isOpen, providerID
			from dbo.tblSeminarsSWLive 
			where seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
		</cfquery>

		<cfif not arguments.allowResend>
			<cfset local.qryLogAuthors = getSeminarAuthorsLogs(seminarID=arguments.seminarID, typeName=arguments.outgoingType)>
			<cfset local.loggedAuthorsList = valueList(local.qryLogAuthors.authorID)>
		<cfelse>
			<cfset local.loggedAuthorsList = "">
		</cfif>

		<cfif local.qrySWLProvider.isOpen>
			<cfloop query="local.qryLinkedAuthors">
				<cfif len(local.qryLinkedAuthors.SWLCode) and local.qrySWLProvider.providerID is 3 and len(local.qrySeminar.ZoomWebinarID) and len(local.qryLinkedAuthors.ZoomWebinarPanelistID) AND not listFind(local.loggedAuthorsList, local.qryLinkedAuthors.authorID)>

					<cfset local.objSWAuthors.sendSpeakerInstructions(seminarID=arguments.seminarID, authorID=local.qryLinkedAuthors.authorID, 
						performedBy=arguments.performedBy, outgoingType=arguments.outgoingType, orgcode=local.qryLinkedAuthors.featureImageOrgCode,
						emailOverride=arguments.emailOverride, customtext=arguments.customText, isImportantCustomText=arguments.isImportantCustomText)>

				</cfif>
			</cfloop>
		</cfif>
	</cffunction>

	<cffunction name="getScheduledTaskDetails" access="public" output="false" returntype="query">
		<cfargument name="participantID" type="numeric" required="yes">

		<cfset var getScheduledTask = "">

		<cfquery name="getScheduledTask" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SELECT isRegistrantInstructionsEnabled, registrantSelectedTimeframes, isSpeakerInstructionsEnabled, speakerSelectedTimeframes, 
				isWebinarMaterialEnabled, webinarMaterialSelectedTimeframes
			FROM dbo.tblSeminarsSWLTasks
			WHERE participantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">
		</cfquery>
		
		<cfreturn getScheduledTask>
	</cffunction>

	<cffunction name="refreshSWLZoomWebinarLicenses" access="public" output="false" returntype="struct">
		<cfset var returnStruct = {
			"arrzoomwebinarlicenses": createObject("component","model.seminarWeb.SWZoomWebinar").refreshWebinarLicenses(),
			"success": true
		}>
		<cfreturn returnStruct>
	</cffunction>

	<cffunction name="disconnectSWLSeminarFromZoomWebinar" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="webinarID" type="string" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.data = { "success":false }>

		<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qrySeminar">
			select s.seminarID
			from dbo.tblSeminars as s
			inner join dbo.tblParticipants as p on p.participantID = s.participantID
			inner join dbo.tblSeminarsSWLive as swl on swl.seminarID = s.seminarID
			where s.seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
			and swl.ZoomWebinarID = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.webinarID#">
			and s.isDeleted = 0
		</cfquery>

		<cfif application.MCEnvironment eq "Production" and local.qrySeminar.recordCount>
			<cftry>
				<cfif not hasUpdateSeminarRights(siteID=arguments.mcproxy_siteID, seminarID=arguments.seminarID, action="disconnectSWLSeminarFromZoomWebinar")>
					<cfthrow message="invalid request">
				</cfif>

				<cfset local.strDeleteZoomWebinar = createObject("component","model.seminarWeb.SWZoomWebinar").deleteWebinar(webinarID=arguments.webinarID)>

				<cfif local.strDeleteZoomWebinar.success>
					<cfquery name="local.updateSWLProgram" datasource="#application.dsn.tlasites_seminarweb.dsn#">
						SET XACT_ABORT, NOCOUNT ON;
						BEGIN TRY

							DECLARE @seminarID int;
							SET @seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">;

							BEGIN TRAN;
								UPDATE dbo.tblSeminarsSWLive 
								SET providerID = NULL,
									ZoomWebinarID = NULL,
									ZoomWebinarHostID = NULL,
									ZoomWebinarPwd = NULL,
									ZoomWebinarPhoneNum = NULL
								WHERE seminarID = @seminarID;

								UPDATE eswl
								SET eswl.ZoomWebinarRegistrantID = NULL,
									eswl.ZoomWebinarJoinURL = NULL
								FROM dbo.tblEnrollmentsSWLive as eswl
								INNER JOIN dbo.tblEnrollments as e on e.enrollmentID = eswl.enrollmentID
								WHERE e.seminarID = @seminarID
								AND eswl.ZoomWebinarRegistrantID IS NOT NULL;

								UPDATE saa
								SET saa.ZoomWebinarPanelistID = NULL,
									saa.ZoomWebinarJoinURL = NULL
								FROM dbo.tblSeminarsAndAuthors as saa
								WHERE saa.seminarID = @seminarID
								AND saa.ZoomWebinarPanelistID IS NOT NULL;
							COMMIT TRAN;

						END TRY
						BEGIN CATCH
							IF @@trancount > 0 ROLLBACK TRANSACTION;
							EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
						END CATCH
					</cfquery>

					<cfset local.data.success = true>
				</cfif>
			<cfcatch type="any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				<cfset local.data.success = false>
			</cfcatch>
			</cftry>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getAssociatedSWODSeminar" access="public" output="false" returntype="query">
		<cfargument name="seminarID" type="numeric" required="yes">

		<cfset var qryAssociatedSWODSeminar = "">

		<cfquery name="qryAssociatedSWODSeminar" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			select s.seminarID, s.seminarName, s.seminarSubTitle
			from dbo.tblSeminarsSWOD as swod
			inner join dbo.tblSeminars as s on s.seminarID = swod.seminarID
			where swod.convertedFromSeminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
		</cfquery>

		<cfreturn qryAssociatedSWODSeminar>
	</cffunction>

	<cffunction name="getSWLEnrollmentsForSWODRegistration" access="public" output="false" returntype="query">
		<cfargument name="SWODSeminarID" type="numeric" required="yes">
		<cfargument name="enrollmentIDList" type="string" required="yes">
		<cfargument name="siteCode" type="string" required="yes">

		<cfset var qryEnrollments = "">
	
		<cfstoredproc procedure="swl_getEnrollmentsForSWODRegistration" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.SWODSeminarID#">
			<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#arguments.siteCode#">
			<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#arguments.enrollmentIDList#">
			<cfprocresult name="qryEnrollments">
		</cfstoredproc>

		<cfreturn qryEnrollments>
	</cffunction>

	<cffunction name="getAllSeminarsForInvitation" access="public" returntype="query" output="no">
		<cfset var qryBundles = "">
		
		<cfquery name="qryBundles" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET NOCOUNT ON;
			
			DECLARE @nowDate datetime = getDate(), @startdate datetime = '1-1-2005', @enddate datetime;
			SET @enddate = DATEADD(yy,2,@nowDate);

			SELECT s.seminarID, s.seminarName, swl.dateStart
			FROM dbo.tblSeminars s
			INNER JOIN dbo.tblSeminarsSWLive swl on swl.seminarID = s.seminarID
			WHERE swl.dateStart between @startdate and @enddate
			AND @nowDate between s.dateCatalogStart and s.dateCatalogEnd
			AND s.isPublished = 1
			AND s.isDeleted = 0
			ORDER BY swl.dateStart, s.seminarID;
		</cfquery>
		
		<cfreturn qryBundles>
	</cffunction>

	<cffunction name="updateSWLBillingInfo" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="actualSWLStartDate" type="string" required="yes">
		<cfargument name="actualSWLEndDate" type="string" required="yes">
		<cfargument name="numOtherSWLRegistrants" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.actualSWLStartDate = ParseDateTime(replace(arguments.actualSWLStartDate,' - ',' '))>
		<cfset local.actualSWLEndDate = ParseDateTime(replace(arguments.actualSWLEndDate,' - ',' '))>
		<cfset local.qrySeminar = CreateObject("component","model.seminarweb.SWLiveSeminars").getSeminarBySeminarID(seminarID=arguments.seminarID)>
		
		<cftry>
			<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) and local.qrySeminar.publisherOrgCode EQ arguments.mcproxy_siteCode>
				<cfstoredproc procedure="swl_closeBilling" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
					<cfprocparam type="in" cfsqltype="CF_SQL_TIMESTAMP" value="#local.actualSWLStartDate#">
					<cfprocparam type="in" cfsqltype="CF_SQL_TIMESTAMP" value="#local.actualSWLEndDate#">
					<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.numOtherSWLRegistrants#">
					<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">
					<cfprocparam type="in" cfsqltype="CF_SQL_DATE" value="#DateFormat(now(),'m/d/yyyy')#">
				</cfstoredproc>

				<cfset local.data.success = true>
			<cfelse>
				<cfset local.data.success = false>
			</cfif>
		<cfcatch type="Any">
			<cfset local.data.success = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="cancelSeminar" access="public" returntype="struct" output="no">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="cancellationFee" type="numeric" required="yes">
		<cfargument name="emailRegistrants" type="boolean" required="yes">
		<cfargument name="customText" type="string" required="yes">

		<cfset var local = structnew()>
		<cfset local.data = { "success":false, "errmsg":"" }>
		
		<cfsetting requesttimeout="600">

		<cftry>
			<cfif not hasUpdateSeminarRights(siteID=arguments.mcproxy_siteID, seminarID=arguments.seminarID, action="cancelSeminar")>
				<cfset local.data.errmsg = "You do not have rights to perform this operation.">
				<cfreturn local.data>
			</cfif>

			<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qryEnrollments">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT e.enrollmentID, u.depomemberdataID, ISNULL(m2.firstname,d.firstName) AS firstName, 
					ISNULL(m2.lastname,d.lastname) AS LastName, ISNULL(nullif(me.email,''),d.email) AS Email,
					s.seminarID, p.orgcode, eswl.ZoomWebinarRegistrantID,
					e.handlesOwnPayment, p.orgcode as signUpOrgCode, ISNULL(eo.email,'') AS overrideEmail
				FROM dbo.tblEnrollments AS e 
				INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID
				INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID
				INNER JOIN dbo.tblSeminars AS s ON e.seminarID = s.seminarID and s.isDeleted = 0
				INNER JOIN dbo.tblSeminarsSWLive AS sswl ON s.seminarID = sswl.seminarID 
				INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID
				INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
				LEFT OUTER JOIN membercentral.dbo.ams_members AS m
					INNER JOIN membercentral.dbo.ams_members as m2 on m2.orgID = m.orgID and m2.memberID = m.activeMemberID
					INNER JOIN memberCentral.dbo.ams_memberEmails AS me ON me.orgID = m2.orgID and me.memberID = m2.memberID
					INNER JOIN memberCentral.dbo.ams_memberEmailTags AS metag ON metag.orgID = m2.orgID and metag.memberID = me.memberID AND metag.emailTypeID = me.emailTypeID
					INNER JOIN memberCentral.dbo.ams_memberEmailTagTypes AS metagt ON metagt.orgID = m2.orgID and metagt.emailTagTypeID = metag.emailTagTypeID AND metagt.emailTagType = 'Primary'
				ON m.memberID = e.MCMemberID
				LEFT OUTER JOIN memberCentral.dbo.ams_emailAppOverrides AS eo ON eo.itemID = e.enrollmentID AND eo.itemType = 'semwebreg'
				WHERE s.seminarID = <cfqueryparam value="#arguments.seminarID#" cfsqltype="CF_SQL_INTEGER">
				AND e.isActive = 1;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfset local.arrEnrollmentsWithRefund = []>
			<cfloop query="local.qryEnrollments">
				<cfset local.strRemove = removeEnrollment(mcproxy_siteID=arguments.mcproxy_siteID, enrollmentID=local.qryEnrollments.enrollmentID, AROption='A', 
					emailRegistrant=arguments.emailRegistrants, customText=arguments.customText, emailRefundRequest=false)>

				<cfif local.strRemove.success and local.strRemove.hasSWRegRefund>
					<cfset arrayAppend(local.arrEnrollmentsWithRefund, {
						firstName=local.qryEnrollments.firstName,
						lastName=local.qryEnrollments.lastName,
						depoMemberDataID=local.qryEnrollments.depoMemberDataID,
						refundAmount=local.strRemove.SWRegRefundAmount
					})>
				</cfif>
			</cfloop>

			<cfstoredproc procedure="swl_cancelSeminar" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
				<cfprocparam type="in" cfsqltype="CF_SQL_DECIMAL" value="#arguments.cancellationFee#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>

			<!--- email refund request --->
			<cfif arrayLen(local.arrEnrollmentsWithRefund)>
				<cfset local.qrySeminar = CreateObject("component","model.seminarweb.SWLiveSeminars").getSeminarBySeminarID(seminarID=arguments.seminarID)>
				
				<cfsavecontent variable="local.refundEmailContent">
					<cfoutput>
					<b>SWL-#local.qrySeminar.seminarID#: #encodeForHTML(local.qrySeminar.seminarName)#</b> has been cancelled.<br/><br/>
					The following registrations may require a refund of registration fees:
					<table cellpadding="2" cellspacing="0">
					<cfloop array="#local.arrEnrollmentsWithRefund#" index="local.thisEnrollment">
						<tr valign="top">
							<td>#local.thisEnrollment.lastName#, #local.thisEnrollment.firstName#</td>
							<td><a href="https://admin.trialsmith.com/TransactionView.cfm?depoMemberDataID=#local.thisEnrollment.depoMemberDataID#">#local.thisEnrollment.depoMemberDataID#</a></td>
							<td align="right">#local.thisEnrollment.refundAmount#</td>
						</tr>
					</cfloop>
					</table>
					</cfoutput>
				</cfsavecontent>

				<cfset local.SWSiteInfo = application.objSiteInfo.getSiteInfo('SW')>
				<cfset local.emailtitle = "Refund Request for Registrations of Cancelled Seminar SWL-#arguments.seminarID#"/>

				<cfset application.objEmailWrapper.sendMailESQ(
					emailfrom={ name='SeminarWeb', email='<EMAIL>' },
					emailto=[{ name="", email="<EMAIL>" }],
					emailreplyto="",
					emailsubject=local.emailtitle,
					emailtitle=local.emailtitle,
					emailhtmlcontent=local.refundEmailContent,
					siteID=local.SWSiteInfo.siteID,
					memberID=local.SWSiteInfo.sysMemberID,
					messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SEMWEBREFUNDREQ"),
					sendingSiteResourceID=local.SWSiteInfo.siteSiteResourceID
				)>
			</cfif>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="copySWLProgram" access="public" output="false" returntype="numeric">
		<cfargument name="programID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="incBasics" type="boolean" required="true">
		<cfargument name="incSpeakers" type="boolean" required="true">
		<cfargument name="incSettings" type="boolean" required="true">
		<cfargument name="incMaterials" type="boolean" required="true">
		<cfargument name="incCatalog" type="boolean" required="true">
		<cfargument name="incSyndication" type="boolean" required="true">
		<cfargument name="incCredit" type="boolean" required="true">

		<cfset var newSeminarID = 0>

		<cfstoredproc procedure="swl_copySeminar" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.incBasics#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.incSpeakers#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.incSettings#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.incCatalog#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.incSyndication#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.incCredit#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="newSeminarID">
		</cfstoredproc>
		
		<cfif arguments.incMaterials and val(newSeminarID) gt 0>
			<!--- copying documents --->
			<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryDocuments">
				SELECT sd.documentID, dv.fileName
				FROM seminarWeb.dbo.tblSeminarsAndDocuments as sd
				INNER JOIN dbo.cms_documents as d ON sd.documentID = d.documentID
				INNER JOIN dbo.cms_documentLanguages as dl ON d.documentID = dl.documentID
				INNER JOIN dbo.cms_documentVersions as dv ON dl.documentLanguageID = dv.documentLanguageID AND dv.isActive = 1
				INNER JOIN dbo.cms_siteResources as sr on sr.siteResourceID = d.siteResourceID and sr.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID AND srs.siteResourceStatusDesc = 'Active'
				WHERE sd.seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
				ORDER BY sd.seminarDocumentID;
			</cfquery>
			
			<cfset local.objDocument = CreateObject("component","model.system.platform.document")>
			
			<cfloop query="local.qryDocuments">
				<cfset local.copyDocumentResult = local.objDocument.copyDocument(sourceDocumentID=local.qryDocuments.documentID, destinationSiteID=arguments.siteID,
					destinationSectionID=0, contributorMemberID=session.cfcuser.memberData.memberID, recordedByMemberID=session.cfcuser.memberData.memberID)>

				<cfif local.copyDocumentResult.documentID>
					<cfquery name="local.qrySaveSWLMaterialDoc" datasource="#application.dsn.membercentral.dsn#">
						INSERT INTO seminarWeb.dbo.tblSeminarsAndDocuments (seminarID, documentID)
						VALUES (
							<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#newSeminarID#">,
							<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.copyDocumentResult.documentID#">
						);
					</cfquery>
				</cfif>
			</cfloop>
		</cfif>

		<cfreturn newSeminarID>
	</cffunction>

	<cffunction name="hasUpdateSeminarRights" access="public" returntype="boolean" output="no">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="seminarID" type="numeric" required="true">
		<cfargument name="action" type="string" required="true">
		<cfargument name="checkLockSettings" type="boolean" required="false" default="0">

		<cfset var local = structNew()>
		<cfset local.hasRights = false>

		<cfset local.SeminarWebAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin',siteID=arguments.siteID)>

		<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qrySWInfo">
			SET NOCOUNT ON;

			DECLARE @siteID int, @siteResourceID int, @siteCode varchar(10);

			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
			SET @siteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.SeminarWebAdminSRID#">;
			SELECT @siteCode = memberCentral.dbo.fn_getSiteCodeFromSiteID(@siteID);

			SELECT @siteResourceID as semWebAdminSRID, isPublisher = CASE WHEN p.orgcode = @siteCode THEN 1 ELSE 0 END, s.lockSettings,
				swl.allowSyndication, p.handlesOwnPayment
			FROM dbo.tblSeminars AS s
			INNER JOIN dbo.tblParticipants AS p ON p.participantID = s.participantID
			INNER JOIN dbo.tblSeminarsSWLive as swl on swl.seminarID = s.seminarID
			WHERE s.seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
			AND s.isDeleted = 0;
		</cfquery>

		<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.qrySWInfo.semWebAdminSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.siteID)>

		<cfswitch expression="#arguments.action#">
			<cfcase value="Edit">
				<cfset local.hasRights = (local.tmpRights['editSWLProgramAll'] is 1 OR local.tmpRights['editSWLProgramPublish'] is 1) AND local.qrySWInfo.isPublisher EQ 1>
			</cfcase>
			<cfcase value="catalog">
				<cfset local.hasRights = (local.tmpRights['editSWLProgramAll'] is 1 OR local.tmpRights['editSWLProgramPublish'] is 1)>
			</cfcase>
			<cfcase value="manageSWOptIns">
				<cfset local.hasRights = local.tmpRights['manageSWOptIns'] is 1 AND local.qrySWInfo.isPublisher EQ 1 AND val(local.qrySWInfo.handlesOwnPayment) EQ 0 AND val(local.qrySWInfo.allowSyndication) EQ 1>
			</cfcase>
			<cfcase value="convertProgramToSWOD">
				<cfset local.hasRights = (local.tmpRights['editSWLProgramAll'] is 1 OR local.tmpRights['editSWLProgramPublish'] is 1) AND local.qrySWInfo.isPublisher EQ 1 AND local.tmpRights['convertProgramToSWOD'] is 1>
			</cfcase>
			<cfcase value="disconnectSWLSeminarFromZoomWebinar">
				<cfset local.hasRights = (local.tmpRights['editSWLProgramAll'] is 1 OR local.tmpRights['editSWLProgramPublish'] is 1 OR local.tmpRights['deleteProgram'] is 1) AND local.qrySWInfo.isPublisher EQ 1>
			</cfcase>
			<cfcase value="cancelSeminar">
				<cfset local.hasRights = application.objUser.isSuperUser(cfcuser=session.cfcuser) AND local.qrySWInfo.isPublisher EQ 1 AND local.tmpRights['deleteSWLRegistrantAll'] is 1>
			</cfcase>
			<cfcase value="billing">
				<cfset local.hasRights = (local.tmpRights['editSWLProgramAll'] is 1 OR local.tmpRights['editSWLProgramPublish'] is 1)>
			</cfcase>
		</cfswitch>

		<cfreturn local.hasRights AND (arguments.checkLockSettings ? NOT local.qrySWInfo.lockSettings : true)>
	</cffunction>
	
	<cffunction name="saveSWLProgramSettings" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="includeConnectionInstruction" type="numeric" required="yes">
		<cfargument name="includeConnectionInstructionCustomEmail" type="numeric" required="yes">
		<cfargument name="includeConnectionInstructionCustomText" type="numeric" required="yes">
		<cfargument name="customEmail" type="string" required="yes">
		<cfargument name="customText" type="string" required="yes">
		<cfargument name="enableCustomField" type="numeric" required="yes">
		<cfargument name="offerCertificate" type="numeric" required="yes">
		<cfargument name="preTestRequired" type="numeric" required="no">
		<cfargument name="examRequired" type="numeric" required="no">
		<cfargument name="evaluationRequired" type="numeric" required="no">
		<cfargument name="isNATLE" type="numeric" required="no">
		
		<cfset var local = structNew()>
		
		<cftry>
			<cfif not hasUpdateSeminarRights(siteID=arguments.mcproxy_siteID, seminarID=arguments.seminarID, action="Edit", checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<cfset local.SeminarWebAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin', siteID=arguments.mcproxy_siteID)>
			<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.SeminarWebAdminSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>
			<cfset local.hasEditSWLProgramAllRights = local.tmpRights.editSWLProgramAll is 1>
			<cfset local.hasManageSWFormRights = createObject("component","seminarWebForm").hasManageSWFormRights(siteID=arguments.mcproxy_siteID, siteCode=arguments.mcproxy_siteCode, seminarID=arguments.seminarID)>

			<cfif NOT isValid("regex",arguments.customEmail, application.regEx.email)>
				<cfset arguments.customEmail = "">
			</cfif>
			
			<cfstoredproc procedure="swl_updateSeminarSettings" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.mcproxy_siteCode#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.includeConnectionInstruction#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.includeConnectionInstructionCustomEmail#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.includeConnectionInstructionCustomText#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.customEmail#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.customText#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.enableCustomField#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.offerCertificate#">
				<cfif local.hasManageSWFormRights>
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.preTestRequired#">
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.examRequired#">
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.evaluationRequired#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" null="true">
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" null="true">
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" null="true">
				</cfif>
				<cfif local.hasEditSWLProgramAllRights>
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.isNATLE#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" null="true">
				</cfif>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>

			<cfset local.data["success"] = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data["success"] = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="saveSWLProgramCatalog" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="isPriceBasedOnActual" type="boolean" required="yes">
		<cfargument name="revenueGLAccountID" type="numeric" required="no">
		<cfargument name="allowCatalog" type="boolean" required="no">
		<cfargument name="dateCatalogStart" type="string" required="no">
		<cfargument name="dateCatalogEnd" type="string" required="no">
		<cfargument name="freeRateDisplay" type="string" required="yes">
		<cfargument name="isFeatured" type="boolean" required="no">
		<cfargument name="enableFeaturedImage" type="boolean" required="no">
		<cfargument name="enableSponsor" type="boolean" required="no">
		
		<cfset var local = structNew()>
		
		<cftry>
			<cfif not hasUpdateSeminarRights(siteID=arguments.mcproxy_siteID, seminarID=arguments.seminarID, action="catalog", checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>
			
			<cfstoredproc procedure="swl_updateSeminarCatalog" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.mcproxy_siteCode#">
				<cfif NOT isNull(arguments.allowCatalog)>
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.allowCatalog#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" null="true">
				</cfif>
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.isPriceBasedOnActual#">
				<cfif NOT isNull(arguments.dateCatalogStart) and arguments.allowCatalog is 1 and len(arguments.dateCatalogStart) and isDate(arguments.dateCatalogStart)>
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.dateCatalogStart#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="true">
				</cfif>
				<cfif NOT isNull(arguments.dateCatalogEnd) and arguments.allowCatalog is 1 and len(arguments.dateCatalogEnd) and isDate(arguments.dateCatalogEnd)>
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.dateCatalogEnd#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="true">
				</cfif>
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.freeRateDisplay#">
				<cfif NOT isNull(arguments.revenueGLAccountID) and val(arguments.revenueGLAccountID) GT 0>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.revenueGLAccountID#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
				</cfif>
				<cfif NOT isNull(arguments.isFeatured)>
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.isFeatured#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" null="true">
				</cfif>
				<cfif NOT isNull(arguments.enableFeaturedImage)>
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.enableFeaturedImage#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" null="true">
				</cfif>
				<cfif NOT isNull(arguments.enableSponsor)>
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.enableSponsor#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" null="true">
				</cfif>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>

			<cfset local.data["success"] = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data["success"] = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="saveSWLProgramBilling" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="preventSeminarFees" type="boolean" required="yes">
			
		<cfset var local = structNew()>
		
		<cftry>
			<cfif not hasUpdateSeminarRights(siteID=arguments.mcproxy_siteID, seminarID=arguments.seminarID, action="billing", checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>
			<cfquery name="local.updateSeminarSWL" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				SET NOCOUNT ON;

				DECLARE @seminarID INT, @preventSeminarFees bit;
				SET @seminarID = <cfqueryparam value="#arguments.seminarID#" cfsqltype="CF_SQL_INTEGER">;
				SET @preventSeminarFees = <cfqueryparam value="#arguments.preventSeminarFees#" cfsqltype="CF_SQL_BIT">;
				
				IF @preventSeminarFees = 1 AND EXISTS (
					SELECT 1 AS priceCount
					FROM dbo.tblSeminarsAndRates AS r 
					INNER JOIN memberCentral.dbo.cms_siteResources AS sr ON sr.siteResourceID = r.siteResourceID
						AND sr.siteResourceStatusID = 1
					WHERE r.seminarID = @seminarID
				)
					SET @preventSeminarFees = 0;

				UPDATE dbo.tblSeminars
				SET preventSeminarFees = @preventSeminarFees
				WHERE seminarID = @seminarID;
			</cfquery>

			<cfset local.data["success"] = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data["success"] = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="uploadSWLMaterialDocument" access="public" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="seminarID" type="numeric" required="true">
		<cfargument name="parentSiteResourceID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.objDocument = CreateObject("component","model.system.platform.document")>

		<cftry>
			<cfset local.newFile = local.objDocument.uploadFile("form.file")>
			<cfif local.newFile.uploadComplete>
				<cfset local.objDocument.forceFileExtentionIfBlank(local.newFile)>
				<cfset local.insertResults = local.objDocument.insertDocument(siteID=arguments.siteID, 
					resourceType='ApplicationCreatedDocument', parentSiteResourceID=arguments.parentSiteResourceID, sectionID=0, 
					docTitle=local.newFile.clientFile, docDesc='', author='', fileData=local.newFile, isActive=1, isVisible=true, 
					contributorMemberID=session.cfcuser.memberdata.memberid, recordedByMemberID=session.cfcuser.memberdata.memberid,
					oldFileExt=local.newFile.serverFileExt)>
				
				<cfif local.insertResults.documentID>
					<cfquery name="local.qrySaveSWLMaterialDoc" datasource="#application.dsn.membercentral.dsn#">
						INSERT INTO seminarWeb.dbo.tblSeminarsAndDocuments (seminarID, documentID)
						VALUES (
							<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">,
							<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.insertResults.documentID#">
						);
					</cfquery>
				</cfif>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfif fileExists("#local.newFile.ServerDirectory#/#local.newFile.ServerFile#")>
				<cffile action="delete" file="#local.newFile.ServerDirectory#/#local.newFile.ServerFile#">
			</cfif>
		</cfcatch>
		</cftry>
	</cffunction>
	
	<cffunction name="deleteSWLMaterialDocument" access="public" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="seminarID" type="numeric" required="true">
		<cfargument name="documentID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasUpdateSeminarRights(siteID=arguments.mcproxy_siteID, seminarID=arguments.seminarID, action="Edit", checkLockSettings=true)>
				<cfthrow message="Invalid request">
			</cfif>

			<cfset CreateObject("component","model.system.platform.document").deleteDocument(siteID=arguments.mcproxy_siteID, documentID=arguments.documentID)>

			<cfquery name="local.qryDeleteSWLMaterialDoc" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				DELETE FROM dbo.tblSeminarsAndDocuments
				WHERE seminarID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.seminarID#">
				AND documentID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.documentID#">
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="saveSWLMaterialDocTitle" access="public" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="seminarID" type="numeric" required="true">
		<cfargument name="documentID" type="numeric" required="true">
		<cfargument name="docTitle" type="string" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasUpdateSeminarRights(siteID=arguments.mcproxy_siteID, seminarID=arguments.seminarID, action="Edit", checkLockSettings=true)>
				<cfthrow message="Invalid request">
			</cfif>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdateSWLMaterialDoc">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @seminarID int, @documentID int, @docTitle varchar(255);
						
					SET @seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">;
					SET @documentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.documentID#">;
					SET @docTitle = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.docTitle#">;

					UPDATE cdl
					SET cdl.docTitle = @docTitle,
						cdl.dateModified = GETDATE()
					FROM dbo.cms_documentLanguages AS cdl
					INNER JOIN seminarWeb.dbo.tblSeminarsAndDocuments AS sd ON sd.documentID = cdl.documentID
					WHERE sd.seminarID = @seminarID AND sd.documentID = @documentID;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>
	<cffunction name="removeAllSWLDocuments" access="public" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = structNew()>
		<cfset local.data.success = true>
		<cfset local.pid = arguments.event.getValue('pid',0)>

		<cftry>
			<cfquery name="local.qryMassDeleteDocuments" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				DECLARE @siteID INT;
				IF OBJECT_ID('tempdb..##tmpDocumentsToDelete') IS NOT NULL
					DROP TABLE ##tmpDocumentsToDelete;
				CREATE TABLE ##tmpDocumentsToDelete (documentID int, siteID int);

				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">;

				INSERT INTO ##tmpDocumentsToDelete (documentID, siteID)
				SELECT sd.documentID, @siteID
				FROM seminarWeb.dbo.tblSeminarsAndDocuments as sd
				INNER JOIN dbo.cms_documents as d ON sd.documentID = d.documentID
				INNER JOIN dbo.cms_documentLanguages as dl ON d.documentID = dl.documentID
				INNER JOIN dbo.cms_documentVersions as dv ON dl.documentLanguageID = dv.documentLanguageID AND dv.isActive = 1
				INNER JOIN dbo.cms_siteResources as sr on sr.siteID = @siteID AND sr.siteResourceID = d.siteResourceID
				INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID AND srs.siteResourceStatusDesc = 'Active'
				WHERE sd.seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.pid#">
				ORDER BY sd.seminarDocumentID;

				EXEC dbo.cms_deleteDocuments;

				DELETE sd FROM seminarweb.dbo.tblSeminarsAndDocuments as sd
				INNER JOIN ##tmpDocumentsToDelete as td ON td.documentID = sd.documentID
				WHERE seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.pid#">
				
				IF OBJECT_ID('tempdb..##tmpDocumentsToDelete') IS NOT NULL
					DROP TABLE ##tmpDocumentsToDelete;
			</cfquery>
			
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="updateScheduledTaskDetails" access="public" output="false" returntype="struct">
		<cfargument name="participantID" type="numeric" required="true">
		<cfargument name="isRegistrantInstructionsEnabled" type="boolean" required="true">
		<cfargument name="registrantSelectedTimeframes" type="string" required="false" default="">
		<cfargument name="isSpeakerInstructionsEnabled" type="boolean" required="true">
		<cfargument name="speakerSelectedTimeframes" type="string" required="false" default="">
		<cfargument name="isWebinarMaterialEnabled" type="boolean" required="true" default="0">
		<cfargument name="webinarMaterialSelectedTimeframes" type="string" required="false" default="">
	
		<cfset var local = structNew()>
		<cfset local.returnStruct = { success=false }>

		<cftry>
			<cfquery name="local.qryUpdate" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				UPDATE dbo.tblSeminarsSWLTasks
					SET isRegistrantInstructionsEnabled = <cfqueryparam value="#arguments.isRegistrantInstructionsEnabled#" cfsqltype="cf_sql_bit">,
					registrantSelectedTimeframes = <cfqueryparam value="#arguments.registrantSelectedTimeframes#" cfsqltype="cf_sql_varchar">,
					isSpeakerInstructionsEnabled = <cfqueryparam value="#arguments.isSpeakerInstructionsEnabled#" cfsqltype="cf_sql_bit">,
					speakerSelectedTimeframes = <cfqueryparam value="#arguments.speakerSelectedTimeframes#" cfsqltype="cf_sql_varchar">,
					isWebinarMaterialEnabled = <cfqueryparam value="#arguments.isWebinarMaterialEnabled#" cfsqltype="cf_sql_bit">,
					webinarMaterialSelectedTimeframes = <cfqueryparam value="#arguments.webinarMaterialSelectedTimeframes#" cfsqltype="cf_sql_varchar">
				WHERE participantID = <cfqueryparam value="#arguments.participantID#" cfsqltype="cf_sql_integer">
			</cfquery>

			<cfset local.returnStruct.success = true>
		
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>
	
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getSWLReplayVideoUploaded" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_sitecode" type="string" required="yes">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="hasReplayVideoUploaded" type="string" required="no" default="0">
		<cfargument name="replayExpirationDate" type="string" required="yes">
	
		<cfset var local = structNew()>
		<cfset local.returnStruct = { success=true, isuploaded=0 }>
	
		<cfset local.qrySeminar = CreateObject("component","model.seminarweb.SWLiveSeminars").getSeminarBySeminarID(seminarID=arguments.seminarID)>
		
		<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) 
			AND local.qrySeminar.publisherOrgCode EQ arguments.mcproxy_sitecode
			AND local.qrySeminar.isUploadedReplay eq 1 		
			AND local.qrySeminar.isOpen eq 0 
			AND arguments.hasReplayVideoUploaded eq 1 
			AND DateCompare(arguments.replayExpirationDate,now()) NEQ -1			
		>
			<cfset local.returnStruct.isuploaded = 1>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>
</cfcomponent>