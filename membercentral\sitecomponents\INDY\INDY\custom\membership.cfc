<cfcomponent extends="model.customPage.customPage" output="false">

	<cffunction name="init" access="private" returntype="void" output="false">
		<cfargument name="Event" type="any">

		<cfscript>
		/* ************************* */
		/* Custom Page Custom Fields */
		/* ************************* */
		local.arrCustomFields = [];
		local.tmpField = { name="AccountLocatorTitle", type="STRING", desc="Account Locator Title", value="IndyBar Membership Application" }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorButton", type="STRING", desc="Account Locator Button", value="Account Lookup" }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="AccountLocatorInstructions", type="CONTENTOBJ", desc="Account Locator Instructions", value="Click the Account Lookup button to the left." }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="MembershipTypeContent", type="CONTENTOBJ", desc="Membership Type Content", value="Join the IndyBar for half price! " }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
		
		local.tmpField = { name="PracticeBuilderTitle", type="STRING", desc="Practice Builder Title", value="Indy Practice Builder" }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
		
		local.tmpField = { name="PracticeBuilderText", type="CONTENTOBJ", desc="Practice Builder Text", value="Practice Builder Subscription includes comprehensive tools and checklists, 24/7 online and on-demand access and connections to leading lawyers and business partners." }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
		
		local.tmpField = { name="PracticeBuilderCheckboxFull", type="CONTENTOBJ", desc="Practice Builder Checkbox Text full", value="I would like to purchase the Practice Builder Subscription for ${price} per year." }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
		
		local.tmpField = { name="PracticeBuilderCheckboxMonthly", type="CONTENTOBJ", desc="Practice Builder Checkbox Text Monthly", value="I would like to purchase the Practice Builder Subscription for ${price} per month." }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
		
		local.tmpField = { name="BusinessBuilderTitle", type="STRING", desc="Business Builder Title", value="Indy Business Builder" }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
		
		local.tmpField = { name="BusinessBuilderText", type="CONTENTOBJ", desc="Business Builder Text", value="Business Builder Subscription grants you access to powerful online and telephone-based business development tools including www.indylawyerfinder.com, Lawyer Referral Service and modest means (criminal, family and bankruptcy) programs." }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
		
		local.tmpField = { name="BusinessBuilderCheckboxFull", type="CONTENTOBJ", desc="Business Builder Checkbox Text Full", value="I would like to purchase the Business Builder Subscription for ${price} per year." }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
		
		local.tmpField = { name="BusinessBuilderCheckboxMonthly", type="CONTENTOBJ", desc="Business Builder Checkbox Text Monthly", value="I would like to purchase the Business Builder Subscription for ${price} per month." }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
		
		local.tmpField = { name="AttorneysNetworkTitle", type="STRING", desc="Attorneys Network Title", value="Indy Attorneys Network" }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
		
		local.tmpField = { name="AttorneysNetworkText", type="CONTENTOBJ", desc="Attorneys Network Text", value='Take the "work" out of "networking" with the Indy Attorneys Network! Choose this add-on service and get matched with a fellow IndyBar member each month for informal networking. Plus, get access to special events.' }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
		
		local.tmpField = { name="AttorneysNetworkCheckbox", type="CONTENTOBJ", desc="Attorneys Network Checkbox Text", value='I would like to join the Indy Attorneys Network for $12.50 per year.' }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
		
		local.tmpField = { name="FoundationTitle", type="STRING", desc="Foundation Title", value="Indianapolis Bar Foundation" }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
		
		local.tmpField = { name="FoundationText", type="CONTENTOBJ", desc="Foundation Text", value="Your contribution to the Indianapolis Bar Foundation is greatly appreciated and helps ensure quality legal services for people struggling with poverty, abuse, and discrimination." }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
		
		local.tmpField = { name="FoundationCheckbox1", type="CONTENTOBJ", desc="Foundation Checkbox Text 1", value="I would like to make the following one-time donation." }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
		
		local.tmpField = { name="FoundationCheckbox2", type="CONTENTOBJ", desc="Foundation Checkbox Text 2", value="I would like to receive more information about the Indianapolis Bar Foundation." }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
		
		local.tmpField = { name="StaffConfirmationTo", type="CONTENTOBJ", desc="who do we send staff confirmations to", value="<EMAIL>" }; 
		arrayAppend(local.arrCustomFields, local.tmpField);

		local.tmpField = { name="SectionDivisionTitle", type="STRING", desc="Sections/Divisions Title", value="Sections/Divisions" }; 
		arrayAppend(local.arrCustomFields, local.tmpField);

		local.tmpField = { name="SectionDivisionText", type="CONTENTOBJ", desc="Sections/Divisions Text", value="<p>Join and network with other members who share the same interest or practice area. As a member of a section or division, you will receive advance notice of the events sponsored by the group and gain access to list-serves, resources, and special educational and social events designated only for members of specific sections and divisions.</p><p><strong>Basic Membership</strong> -</p><p>Choose the Basic membership option to have access to basic section/division benefits like list-serves, open meetings, social events and section/division communications. Register for section/division programming at standard IndyBar CLE rates.</p><p><strong>Plus CLE Membership</strong> -</p><p>Choose the Plus CLE membership option to join a section or division AND attend designated Plus CLE programs at no additional cost throughout the calendar year! Each section/division will offer four Plus CLE programs each year.</p>" }; 
		arrayAppend(local.arrCustomFields, local.tmpField);

		local.tmpField = { name="IncludeVirtualMembership", type="STRING", desc="Include Virtual Membership", value="Yes" }; 
		arrayAppend(local.arrCustomFields, local.tmpField);

		variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'),siteResourceID=this.siteResourceID,arrCustomFields=local.arrCustomFields);

		</cfscript>

		<cfset StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event,
			formName='frmJoin',
			formNameDisplay='Membership Form',
			orgEmailTo= variables.strPageFields.StaffConfirmationTo,
			orgEmailFrom='<EMAIL>',
			memberEmailFrom='<EMAIL>'
		))>
		
		<cfset variables.getCategoryStarted =application.objCustomPageUtils.mh_getCategory(variables.siteID,'memAppHistory','Started')>
		<cfset variables.getCategoryCompleted =application.objCustomPageUtils.mh_getCategory(variables.siteID,'memAppHistory','Completed')>

		<cfset variables.profile_1._profileCode = "INDYCC">
		<cfset variables.profile_1._profileID = application.objCustomPageUtils.acct_getProfileID(siteid=arguments.event.getValue('mc_siteinfo.siteid'),profileCode=variables.profile_1._profileCode)>
		<cfset variables.qryStates = application.objCommon.getStates()>
		<cfset variables.qryCountry = application.objCommon.getCountries()>
		
		<cfset variables.aopStruct	= application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgID, columnName="Practice Areas") >
		<cfset variables.qryLawSchools	= application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgID, columnName="Law School") >
		<cfset variables.profOrg	= application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgID, columnName="Professional Organizations") >
		<cfset variables.gender	= application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgID, columnName="Gender") >
		<cfset variables.yearOfBirth	= application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgID, columnName="Year of Birth") >
		<cfset variables.languages	= application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgID, columnName="Other Languages") >
		<cfset variables.source	= application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgID, columnName="Source") >

		<cfset variables.ccRevenueGLAccountCode = "0100-4020-6000">

		<!--- for form action --->
		<cfset variables.applicationReservedURLParams = "fa,sid">
		<cfset variables.baselink = "/?#getBaseQueryString(false)#">

		<cfset variables.indyBarDuesUID = "9E887C5C-2209-452B-BB7C-77B519992267">
		<cfset variables.donationWithDuesUID = "BC3FC8EC-4196-4769-8A06-604A01BE1B1D">
		<cfset variables.donationRateUID = "96211B8A-9416-4A83-B43D-A742EC9073F4">
		<cfset variables.indyAttorneysNetworkUID = "0102897C-86E8-43A8-B5DB-A26CCB8EB5E0">
		<cfset variables.indyLawyerFinderUID = "2EDF136B-66A4-40E8-AE1A-32C74D7B3D40">
		<cfset variables.indyLawyerFinderRateUID = "85BD3045-35DE-4A4A-8A61-D8656F7D1152">
		<cfset variables.LRSMembershipUID = "FFD71F86-AAB9-40ED-9C5A-7C42EA648FD3">
		<cfset variables.alaCarteUID = "3783afb2-adf0-4534-9524-a6809e085e0b">
		<cfset variables.IndyPracticeBuilderUID = "58bf710b-e33b-4a5a-bc5a-b97b51e03126">
		<cfset variables.IndyBusinessBuilderUID = "cb1a393a-aca4-4a2e-88dc-a592a570be11">

		<cfset variables.IndyPracticeBuilderRateScheduleUID = "bb46a622-a12a-47e5-8b7c-b817dd614169">
		<cfset variables.IndyPracticeBuilderRateUID = "05ce9dc5-1582-44cd-a8bf-b5ac6c40739e">
		<cfset variables.IndyBusinessBuilderRateScheduleUID = "17ceab13-1b48-41f8-a499-9f0bbfc97ed6">
		<cfset variables.IndyBusinessBuilderRateUID = "cf8dec93-9a80-4575-88ff-7b069538c826">

		<cfset variables.rates = application.objCustomPageUtils.sub_getRateSchedule(siteID=variables.siteID, scheduleUID='32C4374B-04E4-45B4-A5F4-CCBAF6B43231', activeRatesOnly=true, ignoreRenewalRates=true)>
		<cfset variables.scheduleUID = "32C4374B-04E4-45B4-A5F4-CCBAF6B43231">
		<cfset variables.rateUIDList = 'EED072D0-78D3-4C96-BA7B-8FAE34583D62,FD70B79D-8078-4E13-96D5-798D03928016,7B491FF7-E426-4438-9BB1-E2C9252BAEA2,D3CD31B8-10F9-4427-A4F6-5FD4F5E9D2A2
		,61FE806E-89A5-4D6C-9FF7-823BB70404F0,71A12674-0473-49BC-8E2C-D3DA082A3473,E11BA4CE-B11C-4252-B48E-5CB8B0B02A15,5984F9D9-224D-4E71-9870-1D48FF5AC418
		,3BC7C9B2-C2B0-4566-BC0C-DC7316BC5697,5721435E-D211-4616-BF83-5023B0B960B5,49ECA9E1-D1CC-40CE-B5E4-C9305B5117E7,7A611EBD-55F2-4104-B4F3-3B289892B8DA,58DEDB19-E97B-43F6-91E8-4E87645C977A,192E2A65-E0DC-4D09-AF74-C3C0E69F5C36'>

		<cfset variables.qryRates =  application.objCustomPageUtils.sub_getRateSchedule(siteID=variables.siteID, scheduleUID='32C4374B-04E4-45B4-A5F4-CCBAF6B43231', activeRatesOnly=true, ignoreRenewalRates=true, isFrequencyFull = true)>
	</cffunction>
	
	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset init(event=arguments.Event)>
		<cfset local.returnHTML = "">

		<cfset local.strMsgCodes = structNew()>
		<cfset local.strMsgCodes['507D7690-F01F-AF51-C9CCB83F029DD786'] = { err=1, msg="This submission has been flagged as spam and was not submitted." }>
		<cfset local.strMsgCodes['50BA4017-F01F-AF51-C9CCE480104528F0'] = { err=1, msg="This submission is missing information. Ensure you have entered all required fields and the e-mail address is valid." }>
		
		<cfswitch expression="#arguments.event.getValue('fa','showForm')#">
			<cfcase value="processStep1">			
				<cfloop collection="#arguments.event.getCollection()#" item="local.key">
					<cfif listFindNoCase(event.getValue('fieldnames',''),local.key)
						and NOT listFindNoCase("fa,btnSubmit",local.key) 
						and left(local.key,9) neq "formfield"
						and left(local.key,4) neq "fld_">
						<cfset fieldArr[local.key] = arguments.event.getValue(local.key)>
					</cfif>
				</cfloop>
				
				<cfset session.fieldArr = fieldArr>
				<cfset local.returnHTML = processStep1(event=arguments.event)>
			</cfcase>

			<cfcase value="processStep2">			
				<cfloop collection="#arguments.event.getCollection()#" item="local.key">
					<cfif listFindNoCase(event.getValue('fieldnames',''),local.key)
						and NOT listFindNoCase("fa,btnSubmit",local.key) 
						and left(local.key,9) neq "formfield"
						and left(local.key,4) neq "fld_">
						<cfset fieldArr[local.key] = arguments.event.getValue(local.key)>
					</cfif>
				</cfloop>
				<cfset session.fieldArr = fieldArr>
				<cfset processStep2(event=arguments.event)>
			</cfcase>

			<cfcase value="complete">				
				<cfif NOT isDefined("session.invoice")>
					<cflocation url="#variables.baselink#" addtoken="false">
				</cfif>
				
				<cfsavecontent variable="local.pageCSS">
					<cfoutput>
					<style type="text/css">
						body { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
						.customPage { font-size:12pt !important; font-family: Calibri,Arial,Helvetica; }
						p { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
						.msgHeader{ background:##e2e4d5; font-weight:bold; padding:5px; }
						.frmText{ font-size:12pt; color:##505050; }
						.b{ font-weight:bold; }
						.required { color:red; }
						p.hint{ clear: left; color: ##777;  font: 0.85em arial,helvetica,sans-serif; margin: 0 0 0.5em 34% !important; overflow: hidden;}
						body { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
						h5 { font-size:13pt; font-weight:bold; margin:0 0 0 0;}
						div.CPSection { border:1px solid ##6C793E; margin-bottom:15px; }
						div.CPSection div.CPSectionTitle { font-size:14pt;  font-weight:bold; color:##4d7871fff; padding:10px; background:##e2e4d5; font-family: Calibri,Arial,Helvetica;}
						div.CPSection div.BB { border-bottom:1px solid ##6C793E;border-top:1px solid ##6C793E;  }
						div.CPSection span.frmText, div.CPSection td.frmText { font-size:12pt; color:##505050; font-family: Calibri,Arial,Helvetica; }
						div.CPSection span.frmText span.block { display:block; }
						div.CPSection span.frmText span.b { font-weight:bold; }
						div.CPSection td.r { text-align:right;width:30%; }
						div.frmButtons{ padding:12px; border-top:1px solid ##6C793E; border-bottom:1px solid ##6C793E; font-family: Calibri,Arial,Helvetica; }
						.alert { background:##4d78716bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; margin-top:4px; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
						.success { background:##4d78716bf url(/assets/common/images/tick.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##0f0; border-bottom:2px solid ##0f0; }
						.required { color:red; }
						.info{ font-style:italic; font-size:8pt; color:##777777; }
						label{cursor: pointer; cursor: hand;}
						label a {font-size:10pt;}
						##zoneR p { margin:0!important; line-height:0px!important;padding: 5px 0 !important; color:##4d7871fff !important; font-weight:bold; }
						##zoneS p { margin:0!important; padding:5px 0!important; }
						##zoneU p { margin:0!important; padding:5px 0!important; }
						.frmRow1{ background:##F0F1E9; }
						.frmRow2{ background:##e2e4d5; }		
						.bb { border-bottom:1px solid ##0d3566; }	
						##divTotals {font-weight:bold;}		
						.l a{font-size:10pt;}
						.amount_ul label{display: inline !important;padding-right: 2px;}	
						.borderN{border-top: none !important;}
						.r_option{font-size: 12pt;font-weight: normal;height: 30px;padding-top: 8px;}
						.r_option label{display: inline;margin-left: 25px;}
						.r_option input{vertical-align: top;}
						.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default{background: white !important;}
						.ui-multiselect span {font-size: 8pt !important;}
						.ui-multiselect-checkboxes label input {font-size: 8pt !important;}
						##barDate,##licenseDate,##licenseDate2,##gradDate { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; background-color: ##fff; cursor:text;}
						.hide{display: none;}
						.show{display: block;}
						.heading{font-weight: bold;
						    padding-bottom: 11px;
						    padding-top: 11px;}
						    .pad_left{padding-left: 10px;}							
						select ,.ui-multiselect{border-radius:0px;}
						@media screen and (max-width:979px) {
							.form-horizontal input[type=text], .form-horizontal select {
								width: 100% !important;
							}
							.form-horizontal input[type=text], .form-horizontal select,.form-horizontal button.ui-multiselect {
								width: 100% !important;
							}
						}
						@media screen and (min-width:980px) {
							.form-horizontal input[type=text], .form-horizontal select,.form-horizontal button.ui-multiselect {
							    width: 410px !important;
							}
						}					
						.padding10{
							padding:10px!important;
						}
					</style>
				<script>
					$(document).ready(function(){
					mca_setupDatePickerField('gradDate');
					$("##practiceAreas").multiselect({
						header: false,
						noneSelectedText: ' - Please Select - ',
						selectedList: 2,
						minWidth: 300
					});
					$("##profOrg").multiselect({
						header: false,
						noneSelectedText: ' - Please Select - ',
						selectedList: 2,
						minWidth: 300
					});
					$("##languages").multiselect({
						header: false,
						noneSelectedText: ' - Please Select - ',
						selectedList: 2,
						minWidth: 300
					});

				});
				</script>

					</cfoutput>
				</cfsavecontent>						

				<cfsavecontent variable="local.returnHTML">
					<cfoutput>
					
					<cfhtmlhead text="#local.pageCSS#">
					<div class="TitleText" style="padding-bottom:15px;">Step 4 - Additional Profile Information</div>
					<div>
						<p>
						<b>Thank you for your payment. Your membership application is now complete</b>
						</p>
						<div>To better serve you, we'd like to collect a bit more information. You can complete this information now or update it at any time by visiting IndyBar.org/myprofile.</div>
						<br />

						<cfset local.formName = "frmPACPay">
						<cfform class="form-horizontal" name="#local.formName#" id="#local.formName#" method="post" action="#variables.baselink#" onsubmit="return _FB_validateForm();">
							<cfinput type="hidden" name="fa" id="fa" value="step4">
							<div class="CPSection">
								<div class="CPSectionTitle NVTitle">Additional Information</div>
								
								<div class=" row-fluid padding10">
									<input name="pro_bonus"  type="checkbox" value="1" id="pro_bonus" class="address_info pull-left"><label for="pro_bonus">I would like to receive more information about the IndyBar pro bono programs.&nbsp;</label> 
									
								</div>
								
								
								<cfif session.fieldarr.membership_type neq 7>	
									<div class=" row-fluid padding10">
										<div class="control-group">
											<label class="control-label" for="lawschool">Law School:</label>
											<div class="controls">
												<select class="tsAppBodyText largeBox" name="lawschool" id="lawschool" >
														<option value="">&nbsp;- Please Select - </option>
														<cfloop array="#variables.qryLawSchools.columnValueArr#" index="variables.qryLawSchools">
															<option value="#variables.qryLawSchools.columnValueString#">&nbsp;#variables.qryLawSchools.columnValueString#</option>
														</cfloop>
													</select>
											</div>
										</div>
									</div>
								</cfif>
								
								<cfif session.fieldarr.membership_type neq 7>
									<div class=" row-fluid padding10">
										<div class="control-group">
											<label class="control-label" for="gradDate">Law School Graduation Date:</label>
											<div class="controls">
												<cfinput size="13" maxlength="13" name="gradDate" id="gradDate" type="text" value="" />
											</div>
										</div>
									</div>
								</cfif>
								
								<cfif session.fieldarr.membership_type neq 7>
									<div class=" row-fluid padding10">
										<div class="control-group">
											<label class="control-label" for="practiceAreas">Practice Areas:</label>
											<div class="controls">
												<select class="tsAppBodyText largeBox" name="practiceAreas" id="practiceAreas" multiple="multiple" >
													<cfloop array="#variables.aopStruct.columnValueArr#" index="variables.thisAOP">
														<option value="#variables.thisAOP.valueID#">&nbsp;#variables.thisAOP.columnValueString#</option>
													</cfloop>
												</select>
											</div>
										</div>
									</div>
								</cfif>
								
								<div class=" row-fluid padding10">
									<div class="control-group">
										<label class="control-label" for="profOrg">Professional Organizations:</label>
										<div class="controls">
											<select class="tsAppBodyText largeBox" name="profOrg" id="profOrg" multiple="multiple">
												<cfloop array="#variables.profOrg.columnValueArr#" index="variables.profOrg">
													<option class="tsAppBodyText" value="#variables.profOrg.valueID#">&nbsp;#variables.profOrg.columnValueString#</option>
												</cfloop>
											</select>
										</div>
									</div>
								</div>
								<div class=" row-fluid padding10">
									<div class="control-group">
										<label class="control-label" for="gender">Gender:</label>
										<div class="controls">
											<select class="tsAppBodyText largeBox" name="gender" id="gender" >
												<option value="">&nbsp;- Please Select - </option>
												<cfloop array="#variables.gender.columnValueArr#" index="variables.gender">
													<option value="#variables.gender.columnValueString#">&nbsp;#variables.gender.columnValueString#</option>
												</cfloop>
											</select>
										</div>
									</div>
								</div>
								
								<div class=" row-fluid padding10">
									<div class="control-group">
										<label class="control-label" for="languages">Other Languages Spoken:</label>
										<div class="controls">
											<select class="tsAppBodyText largeBox" name="languages" id="languages"  multiple="multiple">
												<cfloop array="#variables.languages.columnValueArr#" index="variables.languages">
													<option value="#variables.languages.valueID#">&nbsp;#variables.languages.columnValueString#</option>
												</cfloop>
											</select>
										</div>
									</div>
								</div>
								
								<div class=" row-fluid padding10">
									<div class="control-group">
										<label class="control-label" for="significant_other">Significant Other:</label>
										<div class="controls">
											<cfinput size="13" maxlength="60" name="significant_other" id="significant_other" type="text" value="" />
										</div>
									</div>
								</div>
								
								<div class=" row-fluid padding10">
									<div class="control-group">
										<label class="control-label" for="source">How did you find out about the IndyBar?:</label>
										<div class="controls">
											<select class="tsAppBodyText largeBox" name="source" id="source" >
												<option value="">&nbsp;- Please Select - </option>
												<cfloop array="#variables.source.columnValueArr#" index="variables.source">
													<option value="#variables.source.columnValueString#">&nbsp;#variables.source.columnValueString#</option>
												</cfloop>
											</select>
										</div>
									</div>
								</div>
								<div class="row-fluid padding10">
									<button type="submit" class="btn btn-default btnCustom" name="btnSubmit">SUBMIT</button>
								</div>
							</div>			
						</cfform>
					</div>	
					</cfoutput>
				</cfsavecontent>
			</cfcase>

			<cfcase value="step4">				
				<cfif NOT isDefined("session.fieldarr.memberID")>
					<cflocation url="#variables.baselink#" addtoken="false">
				</cfif>
				 
				<cftry>
					<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=session.fieldarr.memberID)>
					<cfif len(event.getValue('lawschool',''))>
						<cfset local.objSaveMember.setCustomField(field='Law School', value=event.getValue('lawschool',''))>
					</cfif>
					<cfif len(event.getValue('gradDate',''))>
                        <cfset local.objSaveMember.setCustomField(field='Law School Grad Date', value=event.getValue('gradDate'))>
                    </cfif>
					 <cfif len(event.getValue('practiceAreas',''))>
                        <cfset local.objSaveMember.setCustomField(field='Practice Areas', valueID=arguments.event.getValue('practiceAreas',''))>
                    </cfif>
					<cfif len(event.getValue('profOrg', ''))>
                        <cfset local.objSaveMember.setCustomField(field='Professional Organizations', valueID=arguments.event.getValue('profOrg',''))>                        
                    </cfif>
					<cfif len(event.getValue('gender', ''))>
                        <cfset local.objSaveMember.setCustomField(field='Gender', value=event.getValue('gender'))>    
                    </cfif>
					<cfif len(event.getValue('languages',''))>
                        <cfset local.objSaveMember.setCustomField(field='Other Languages', valueID=arguments.event.getValue('languages',''))>
                    </cfif>
					<cfif len(event.getValue('significant_other',''))>
                        <cfset local.objSaveMember.setCustomField(field='Significant Other', value=event.getValue('significant_other'))>
                    </cfif>
					<cfif len(event.getValue('source',''))>
                        <cfset local.objSaveMember.setCustomField(field='Source', value=event.getValue('source'))>
                    </cfif>
					<cfset local.strResult = local.objSaveMember.saveData()>
                    <cfif NOT local.strResult.success>
                    	<cfthrow message="Unable to save member record">
                    </cfif>
                <cfcatch type="Any">
                    <cfset application.objError.sendError(cfcatch=cfcatch)>
                </cfcatch>
                </cftry>
			
				<cfsavecontent variable="local.pageCSS">
					<cfoutput>
					<style type="text/css">
						body { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
						.customPage { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
						p { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
						.msgHeader{ background:##7a864e; font-weight:bold; padding:5px; }
						.frmText{ font-size:12pt; color:##505050; } 
						.b{ font-weight:bold; }
					</style>
					</cfoutput>
				</cfsavecontent>

				<cfsavecontent variable="local.invoice">
					<cfoutput>
						<table cellpadding="2" cellspacing="0" width="100%" border="1" class="customPage ">            
							<tr class="msgHeader"><td colspan="2" class="b">MEMBER INFORMATION</td></tr>

							<tr><td class="frmText" width="50%">MemberNumber</td><td class="frmText">#session.fieldarr.memberNumber#&nbsp;</td></tr>
							<tr><td class="frmText">First Name</td><td class="frmText">#session.fieldarr.firstName#&nbsp;</td></tr>    
							<tr><td class="frmText">Middle Name</td><td class="frmText">#session.fieldarr.middlename#&nbsp;</td></tr>    
							<tr><td class="frmText highlightRow">Last Name</td><td class="frmText highlightRow">#session.fieldarr.lastName#&nbsp;</td></tr>    

							<tr class="msgHeader"><td colspan="2" class="b">ADDITIONAL INFORMATION</td></tr>
							<tr >
							<td class="frmText" width="50%">I would like to receive more information about the IndyBar pro bono programs</td>
							<td class="frmText highlightRow">
								<cfif event.getValue('pro_bonus','0')>
									Yes&nbsp;
								<cfelse>
									No&nbsp;
								</cfif>
							</td>
							</tr>
							<cfif session.fieldarr.membership_type neq 7>                            
							<tr>
								<td class="frmText" width="50%">Law School</td>
								<td class="frmText highlightRow">
									<cfif len(event.getValue('lawschool',''))>
										#event.getValue('lawschool')#
									</cfif>
									&nbsp;
								</td>
							</tr>
							<tr>
								<td class="frmText">Law School Graduation Date</td>
								<td class="frmText highlightRow">
									<cfif len(event.getValue('gradDate',''))>
										#event.getValue('gradDate')#
									</cfif>
									&nbsp;
								</td>
							</tr>

							<tr>
								<td class="frmText">Practice Areas</td>
								<td class="frmText highlightRow">
									<cfif len(event.getValue('practiceAreas',''))>    
										<cfset local.areasAsString = replace(application.objCustomPageUtils.mem_getCustomFieldValueFromValueID(orgID=variables.orgID, columnName='Practice Areas', valueIDList=arguments.event.getValue('practiceAreas','')),'|',', ','ALL')>    
										#local.areasAsString#
									</cfif>
										&nbsp;
								</td>
							</tr>
							</cfif>
							<tr>
								<td class="frmText">Professional Organizations</td>
								<td class="frmText highlightRow">
									<cfif len(event.getValue('profOrg',''))>
										<cfset local.profOrgAsString = replace(application.objCustomPageUtils.mem_getCustomFieldValueFromValueID(orgID=variables.orgID, columnName='Professional Organizations', valueIDList=arguments.event.getValue('profOrg','')),'|',', ','ALL')>
										#local.profOrgAsString#
									</cfif>
									&nbsp;
								</td>
							</tr>
							<tr>
								<td class="frmText">Gender</td>
								<td class="frmText highlightRow">
									<cfif len(event.getValue('gender',''))>
										#event.getValue('gender')#
									</cfif>
									&nbsp;
								</td>
							</tr>
							<tr>
								<td class="frmText">Other Languages Spoken</td>
								<td class="frmText highlightRow">
									<cfif len(event.getValue('languages',''))>
										<cfset local.languagesAsString = replace(application.objCustomPageUtils.mem_getCustomFieldValueFromValueID(orgID=variables.orgID, columnName='Other Languages', valueIDList=arguments.event.getValue('languages','')),'|',', ','ALL')>
										#local.languagesAsString#
									</cfif>
									&nbsp;
								</td>
							</tr>
							<tr>
								<td class="frmText">Significant Other</td>
								<td class="frmText highlightRow">
									<cfif len(event.getValue('significant_other',''))>                            
										#event.getValue('significant_other')#
									</cfif>
									&nbsp;
								</td>
							</tr>
							<tr>
								<td class="frmText">How did you find out about the IndyBar?</td>
								<td class="frmText highlightRow">
									<cfif len(event.getValue('source',''))>
										#event.getValue('source')#
									</cfif>
									&nbsp;
								</td>
							</tr>
						</table>
					</cfoutput>
				</cfsavecontent>

				<cfset variables.emailAdditionalSubject = "Thank you for providing additional information!">
				<cfset variables.memberEmail.subject = variables.emailAdditionalSubject>
				<cfset variables.ORGEmail.subject = variables.emailAdditionalSubject>
				<cfset variables.memberEmail.to = session.fieldarr.email>
				
				<cfset session.invoice2 = local.invoice>

				<cfsavecontent variable="local.mailContent">
					<cfoutput>
						#local.pageCSS#
						<p>Thank you! Please print this page - it is your confirmation.</p><hr/>
						#local.invoice#
					</cfoutput>
				</cfsavecontent>

				<cfscript>
					local.responseStruct = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name="", email=variables.memberEmail.from },
						emailto=[{ name="", email=variables.memberEmail.to }],
						emailreplyto= variables.ORGEmail.to,
						emailsubject= variables.memberEmail.SUBJECT,
						emailtitle=arguments.event.getTrimValue('mc_siteinfo.sitename') & " - " & variables.formNameDisplay,
						emailhtmlcontent=local.mailContent,
						siteID=arguments.event.getTrimValue('mc_siteinfo.siteID'),
						memberID=session.fieldarr.memberID,
						messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
						sendingSiteResourceID=this.siteResourceID
					);
					local.emailSentToUser = local.responseStruct.success;
				</cfscript>

				<!--- email staff (no error shown to user) --->
				<cfsavecontent variable="local.mailContent">
					<cfoutput>
						#local.pageCSS#
						#local.invoice#
					</cfoutput>
				</cfsavecontent>

				<cfscript>
					local.arrEmailTo = [];
					variables.ORGEmail.to = replace(variables.ORGEmail.to,",",";","all");
					local.toEmailArr = listToArray(variables.ORGEmail.to,';');
					for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
						local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
					}
				</cfscript>

				<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
					emailfrom={ name="", email=variables.ORGEmail.from},
					emailto=local.arrEmailTo,
					emailreplyto=variables.ORGEmail.from,
					emailsubject=variables.ORGEmail.SUBJECT,
					emailtitle=arguments.event.getTrimValue('mc_siteinfo.sitename') & " - " & variables.formNameDisplay,
					emailhtmlcontent=local.mailContent,
					siteID=arguments.event.getValue('mc_siteinfo.siteID'),
					memberID=arguments.event.getValue('mc_siteinfo.sysMemberID'),
					messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
					sendingSiteResourceID=this.siteResourceID
				)>

				<cflocation url="#variables.baselink#&fa=confirm" addtoken="false">
			</cfcase>

			<cfcase value="confirm">				
				<cfsavecontent variable="local.pageCSS">
					<cfoutput>
						<style type="text/css">
							body { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
							.customPage { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
							p { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
							.msgHeader{ background:##e2e4d5; font-weight:bold; padding:5px; color: ##000}
							.frmText{ font-size:12pt; color:##505050; } 
							.b{ font-weight:bold; }

						</style>
					</cfoutput>
				</cfsavecontent>
				<cfsavecontent variable="local.returnHTML">
					<cfoutput>
					<cfhtmlhead text="#local.pageCSS#">
					<div>
						<b>Thank you for providing this information. You can update your member profile at any time by visiting <a href="/?pg=updatemember">IndyBar.org/myprofile</a>. We look forward to serving you as an IndyBar member!</b>
						<br/>
					</div>
					</cfoutput>
				</cfsavecontent>
			</cfcase>

			<cfcase value="step3">				
				<cfloop collection="#arguments.event.getCollection()#" item="local.key">
					<cfif listFindNoCase(event.getValue('fieldnames',''),local.key)
						and NOT listFindNoCase("fa,btnSubmit",local.key) 
						and left(local.key,9) neq "formfield"
						and left(local.key,4) neq "fld_">
						<cfset session.fieldArr[local.key] = arguments.event.getValue(local.key)>
					</cfif>
				</cfloop>
				
				<cfset local.memberTypeDues = StructNew()>	
				<cfset local.memberTypeDues["Attorney 10+ Years"] = "D3CD31B8-10F9-4427-A4F6-5FD4F5E9D2A2" />
				<cfset local.memberTypeDues["Attorney 2-4 Years"] = "EED072D0-78D3-4C96-BA7B-8FAE34583D62" />
				<cfset local.memberTypeDues["Attorney 5-7 Years"] = "FD70B79D-8078-4E13-96D5-798D03928016"/>
				<cfset local.memberTypeDues["Attorney 8-9 Years"] = "7B491FF7-E426-4438-9BB1-E2C9252BAEA2" />
				<cfset local.memberTypeDues["New Attorney"] = "61FE806E-89A5-4D6C-9FF7-823BB70404F0" />

				<cfset local.memberTypeDues["Life Member"] = "71A12674-0473-49BC-8E2C-D3DA082A3473" />
				<cfset local.memberTypeDues["Judiciary Member"] = "E11BA4CE-B11C-4252-B48E-5CB8B0B02A15" />
				<cfset local.memberTypeDues["Law Faculty Member"] ="5984F9D9-224D-4E71-9870-1D48FF5AC418" />
				<cfset local.memberTypeDues["Government Member"] = "3BC7C9B2-C2B0-4566-BC0C-DC7316BC5697" />
				<cfset local.memberTypeDues["Indigent Service Staff Attorney"] = "5721435E-D211-4616-BF83-5023B0B960B5" />
				<cfset local.memberTypeDues["Paralegal Member"] = "49ECA9E1-D1CC-40CE-B5E4-C9305B5117E7" />
				<cfset local.memberTypeDues["Inactive Practice Member"] = "7A611EBD-55F2-4104-B4F3-3B289892B8DA" />
				<cfset local.memberTypeDues["Graduate - JD Only"] = "58DEDB19-E97B-43F6-91E8-4E87645C977A" />
				<cfset local.memberTypeDues["Virtual Membership"] = "192E2A65-E0DC-4D09-AF74-C3C0E69F5C36" />


				<cfset local.memberAmtDues = StructNew()>	
				<cfset local.scheduleUID = "32C4374B-04E4-45B4-A5F4-CCBAF6B43231">
				
				<cfloop query="variables.qryRates">
					<cfswitch expression="#variables.qryRates.rateUID#" >
					<!--- Regular Members / Private Sector Rates --->
					<cfcase value="EED072D0-78D3-4C96-BA7B-8FAE34583D62,2DB358A4-1B2D-43CD-B413-2B34193D2B0D,F520B1EF-5A0B-408F-8DC5-43883B94E5F8">
						<cfset local.memberAmtDues["Attorney 2-4 Years"] = variables.qryRates.rateAmt />
					</cfcase>
					<cfcase value="FD70B79D-8078-4E13-96D5-798D03928016,AAEE3413-1B7C-4812-8CF9-0664A05A1194,1295EE70-6D5D-4AAE-953A-FCC9EA118576">
						<cfset local.memberAmtDues["Attorney 5-7 Years"] = variables.qryRates.rateAmt />
					</cfcase>
					<cfcase value="7B491FF7-E426-4438-9BB1-E2C9252BAEA2,5A434473-39A3-49F9-AEA9-2AFB09125960,61B54A60-47BE-420F-B2EB-937ACDC4A524">
						<cfset local.memberAmtDues["Attorney 8-9 Years"] = variables.qryRates.rateAmt />
					</cfcase>
					<cfcase value="D3CD31B8-10F9-4427-A4F6-5FD4F5E9D2A2,76283B52-C5DD-4B87-8AD6-4984FB4ADEC4,5D94EAEA-06EB-41B6-A1AE-54AA3351691D">
						<cfset local.memberAmtDues["Attorney 10+ Years"] = variables.qryRates.rateAmt />
					</cfcase>
					<cfcase value="61FE806E-89A5-4D6C-9FF7-823BB70404F0,D1463119-E4B1-4F3F-8CF0-89E29900CB0E,49B0F21D-5342-4107-8F0D-FD159971E66D">
						<cfset local.memberAmtDues["New Attorney"] = variables.qryRates.rateAmt />
					</cfcase>		
					<cfcase value="71A12674-0473-49BC-8E2C-D3DA082A3473,7B1F8F2E-2C2E-45C1-BB9D-7958AF2B7D36,3E937C3B-8F6B-4186-8287-AD51B6CB2A05">
						<cfset local.memberAmtDues["Life Member"] = variables.qryRates.rateAmt />
					</cfcase>
					<cfcase value="E11BA4CE-B11C-4252-B48E-5CB8B0B02A15,322C40DB-1F68-4799-B48E-67A32CECDBCE,E29CB080-62DA-4A73-A2E5-288246ACC5BB">
						<cfset local.memberAmtDues["Judiciary Member"] = variables.qryRates.rateAmt />
					</cfcase>
					<cfcase value="5984F9D9-224D-4E71-9870-1D48FF5AC418,24603410-7E07-402A-A872-6AF0F26A4A19,66EF2FAF-971F-4886-93B2-77CBAA07DAE5">
						<cfset local.memberAmtDues["Law Faculty Member"] = variables.qryRates.rateAmt />
					</cfcase>
					<cfcase value="3BC7C9B2-C2B0-4566-BC0C-DC7316BC5697,B1F403E3-1B63-4CBA-8C48-C3DDA729BA42,3E9166C2-F5AD-4CE6-AB1B-0DB3F9789C64">
						<cfset local.memberAmtDues["Government Member"] = variables.qryRates.rateAmt />
					</cfcase>
					<cfcase value="5721435E-D211-4616-BF83-5023B0B960B5,BF9A4A7E-74EE-4E16-A917-2C206026C084,E60FE74F-5A57-45A8-90CF-DDCE9F98B9F7">
						<cfset local.memberAmtDues["Indigent Service Staff Attorney"] = variables.qryRates.rateAmt />
					</cfcase>
					<cfcase value="49ECA9E1-D1CC-40CE-B5E4-C9305B5117E7,48D772F9-C0A1-4A78-AE4E-5F4708601FEC,14B8DF61-BD3D-4B7A-9E21-25F0D5CE3B5E">
						<cfset local.memberAmtDues["Paralegal Member"] = variables.qryRates.rateAmt />
					</cfcase>
					<cfcase value="7A611EBD-55F2-4104-B4F3-3B289892B8DA,8241BE65-FE6C-46B6-A641-CB3DDC380E6F,EBFE607F-81AA-409F-9BAC-19D9B1B9B93B">
						<cfset local.memberAmtDues["Inactive Practice Member"] = variables.qryRates.rateAmt />
					</cfcase>
					<cfcase value="58DEDB19-E97B-43F6-91E8-4E87645C977A,E895A1EB-9D53-4BED-80C1-1B82B2431577,B7BCC7BB-670D-4DF8-B6A5-985D7A50F0E8">
						<cfset local.memberAmtDues["Graduate - JD Only"] = variables.qryRates.rateAmt />
					</cfcase>
					<cfcase value="192E2A65-E0DC-4D09-AF74-C3C0E69F5C36,7039AAA6-82DF-4446-9B54-2EF839C7F22A,50BE547D-5459-4DB0-8D4A-0EBC485575FA">
						<cfset local.memberAmtDues["Virtual Membership"] = variables.qryRates.rateAmt />
					</cfcase>
				</cfswitch>
				</cfloop>

				<cfset local.qryLawyerFinder = application.objCustomPageUtils.sub_getRateSchedule(siteID=variables.siteID, scheduleUID="B1EA0CC6-8E39-4950-9785-E71CB8ED07A7", rateUID="85BD3045-35DE-4A4A-8A61-D8656F7D1152")>
				<cfloop query="local.qryLawyerFinder">
					<cfswitch expression="#local.qryLawyerFinder.rateUID#" >
						<!--- Regular Members / Private Sector Rates --->
						<cfcase value="85BD3045-35DE-4A4A-8A61-D8656F7D1152">
							<cfset local.lawyerFinderRates = local.qryLawyerFinder.rateAmt />
						</cfcase>
					</cfswitch>
				</cfloop>
				<cfset local.qryLRS = application.objCustomPageUtils.sub_getRateSchedule(siteID=variables.siteID, scheduleUID='0527AE51-A6F0-4C35-ACD5-0BD6192C6E62', activeRatesOnly=true, ignoreRenewalRates=true)> 
				<cfset local.LRSRates = local.qryLRS.rateAmt />
				<cfset local.foundationDonation = application.objCustomPageUtils.sub_getRateSchedule(siteID=variables.siteID, scheduleUID="1C7D9C22-1569-4D36-854B-B0B9FEDAE2C8", rateUID="96211B8A-9416-4A83-B43D-A742EC9073F4")>
				<cfloop query="local.foundationDonation">
					<cfswitch expression="#local.foundationDonation.rateUID#" >
						<!--- Regular Members / Private Sector Rates --->
						<cfcase value="96211B8A-9416-4A83-B43D-A742EC9073F4">
							<cfset local.foundationRate = local.foundationDonation.rateAmt />
						</cfcase>
					</cfswitch>
				</cfloop>

				<cfset local.getContactTypes = application.objCustomPageUtils.getCustomFieldData(orgID=variables.orgID,columnName='Contact Type')>

				<cfset contactTypes = StructNew()>
				<cfloop query="local.getContactTypes">
					<cfset contactTypes[local.getContactTypes.columnValueString]  = local.getContactTypes.valueID >
				</cfloop>
				<cfset local.amount= 0>
				<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"membership_type") >
					<cfset membership = session.fieldArr.membership_type>
				<cfelse>
					<cfset membership = event.getValue('membership_type','')>
				</cfif>
				<cfif membership eq 1>
					<cfset contactType = contactTypes['Attorney']>
				<cfelseif membership eq 2>
					<cfset contactType = contactTypes['Life Member']>
				<cfelseif membership eq 3>
					<cfset contactType = contactTypes['Judge']>
				<cfelseif membership eq 4>
					<cfset contactType = contactTypes['Law Faculty']>
				<cfelseif membership eq 5>
					<cfset contactType = contactTypes['Government Attorney']>
				<cfelseif membership eq 6>
					<cfset contactType = contactTypes['Indigent Service Provider']>
				<cfelseif membership eq 7>
					<cfset contactType = contactTypes['Paralegal']>
				<cfelseif membership eq 8>
					<cfset contactType = contactTypes['Inactive Practice Attorney']>
				<cfelseif membership eq 9>
					<cfset contactType = contactTypes['JD Only']>
				<cfelseif membership eq 10>
					<cfset contactType = contactTypes['Attorney']>
				</cfif>

				<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"memberID") >
					<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=session.fieldArr.memberID)>
					<cfset local.objSaveMember.setCustomField(field='Contact Type', valueID=contactType)>
				<cfelse>
					<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=event.getValue('memberID'))>
					<cfset local.objSaveMember.setCustomField(field='Contact Type', valueID=contactType)>
				</cfif>

				<!--- Professional License --->
				<cfset local.qryOrgProLicenseStatuses = application.objOrgInfo.getOrgProfessionalLicenseStatuses(orgID=variables.orgID)>
				<cfset local.qryProfessionalLicenseTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=variables.orgID)>
				<cfset local.licenseStatus = {}>
				<cfset local.licenseDates  = ArrayNew(1)>
				<cfloop query="local.qryOrgProLicenseStatuses">
					<cfset local.licenseStatus[LCase(local.qryOrgProLicenseStatuses.statusName)] = local.qryOrgProLicenseStatuses.PLStatusID>	
				</cfloop>
				<cfset local.licensesNames = ArrayNew(1)>
				<cfloop query="local.qryProfessionalLicenseTypes">
					<cfset local.licensesNames[local.qryProfessionalLicenseTypes.pltypeid] = local.qryProfessionalLicenseTypes.PLName>	
				</cfloop>
				<cfif event.getValue('professionalLicenseDropdown','') neq "">
					<cfset local.licenses = event.getValue('professionalLicenseDropdown')>
					<cfset local.licenseArr = ListToArray(local.licenses)>

					<cfset local.userHasActiveLicense = false>
					<cfloop array="#local.licenseArr#" index="local.key">
						<cfset  local.license_no = event.getValue('state_#local.key#_licensenumber')>
						<cfset  local.license_date = event.getValue('state_#local.key#_licenseDate')>
						<cfset  local.licenseDates[local.key] = local.license_date>
						<cfset  local.license_status = event.getValue('state_#local.key#_status')>

						<cfif local.license_status eq "Active">
							<cfset local.userHasActiveLicense = true>
						</cfif>
						
						<cfset  local.objSaveMember.setProLicense(name=local.licensesNames[local.key],status=local.license_status,license=local.license_no, date=local.license_date)>
					</cfloop>

					<cfif local.userHasActiveLicense>
						<cfset local.objSaveMember.setCustomField(field='Active Attorney License', value='1')>
					</cfif>
				</cfif>

				<cfset local.strSaveMember = local.objSaveMember.saveData(runImmediately=1)>
				<cfif not local.strSaveMember.success>
					<cfthrow message="Unable to save member info.">
				</cfif>

				<cfset local.qryAttorneyRates = application.objCustomPageUtils.sub_getRateSchedule(siteID=variables.siteID, scheduleUID="1DB561E2-F5D1-4859-9875-F7F8CAA45B97", rateUID="90F4EF7E-FBC0-42BD-B797-F7E26A756BB0,A11237F0-E22F-4901-9CDE-C9788A81FC42,34AEF4AB-930D-40F3-AF8B-2F7098AED46C")>
				<cfset local.qryGetAttornayMostExclusiveRateInfo = application.objCustomPageUtils.sub_getMostExclusiveRateInfo(memberID=event.getValue('memberID'), subscriptionUID=variables.indyAttorneysNetworkUID, isRenewalRate=0) />				
				
				<cfset local.attorneyRates = 0 />
				<cfloop query="local.qryAttorneyRates">
					<cfif trim(local.qryAttorneyRates.rateUID) eq trim(local.qryGetAttornayMostExclusiveRateInfo.rateUID)>
						<cfset local.attorneyRates = local.qryAttorneyRates.rateAmt />
						<cfbreak />
					</cfif>
				</cfloop>				
				<cfsavecontent variable="local.headCode">
					<cfoutput>
						<style type="text/css">
							p.hint{ clear: left; color: ##777;  font: 0.85em arial,helvetica,sans-serif; margin: 0 0 0.5em 34% !important; overflow: hidden;}
							body { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
							h5 { font-size:13pt; font-weight:bold; margin:0 0 0 0;}
							div.CPSection { border:1px solid ##6C793E; margin-bottom:15px; }
							div.CPSection div.CPSectionTitle { font-size:14pt;  font-weight:bold; color:##4d7871fff; padding:10px; background:##e2e4d5; font-family: Calibri,Arial,Helvetica;}
							div.CPSection div.BB { border-bottom:1px solid ##6C793E;border-top:1px solid ##6C793E; background:##899461 !important;color:##fff !important;  }
							div.CPSection span.frmText, div.CPSection td.frmText { font-size:12pt; color:##505050; font-family: Calibri,Arial,Helvetica; }
							div.CPSection span.frmText span.block { display:block; }
							div.CPSection span.frmText span.b { font-weight:bold; }
							div.CPSection td.r { text-align:right;width:35%; }
							div.CPSection td.l { text-align:left;width:35%; }
							div.frmButtons{ padding:12px; border-top:1px solid ##6C793E; border-bottom:1px solid ##6C793E; font-family: Calibri,Arial,Helvetica; }
							.alert { background:##4d78716bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; margin-top:4px; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
							.success { background:##4d78716bf url(/assets/common/images/tick.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##0f0; border-bottom:2px solid ##0f0; }
							.required { color:red; }
							.info{ font-style:italic; font-size:8pt; color:##777777; }
							label{cursor: pointer; cursor: hand;}
							label a {font-size:10pt;}
							##zoneR p { margin:0!important; line-height:0px!important;padding: 5px 0 !important; color:##4d7871fff !important; font-weight:bold; }
							##zoneS p { margin:0!important; padding:5px 0!important; }
							##zoneU p { margin:0!important; padding:5px 0!important; }
							.frmRow1{ background:##fff; }
							.frmRow2{ background:##fff; }		
							.bb { border-bottom:1px solid ##0d3566; }	
							##divTotals {font-weight:bold;}		
							.l a{font-size:10pt;}
							.amount_ul label{display: inline !important;padding-right: 2px;}	
							.borderN{border-top: none !important;}
							.r_option{float: right;font-size: 12pt;font-weight: normal;}
							.r_option label{display: inline;}
							.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default{background: white !important;}
							.ui-multiselect span{font-size: 12pt !important;}
							##barDate,##licenseDate,##licenseDate2 { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; }
							.rghtsid_cntnr p {
							    line-height: 1.4em;
							    margin-bottom: 0;
							    margin-top: 3px;
							}
							.checkbox {
							    vertical-align: bottom;
							}
							##gotoStep2{margin-right: 15px;}
							.CPSectionTitleTemp{font-size:13pt;  font-weight:bold; color:##4d7871fff; padding:10px; background:##fff; font-family: Calibri,Arial,Helvetica;}
							form {
								margin: 0px !important;
							}
							.CPButtonTitle{padding:5px; background:##f0f1e9; }
							.cleMemWrap .controls{
								text-align: right;
							}
							.cleMemWrap label{
								font-size: 12pt;
								font-family: Calibri,Arial,Helvetica;
							}
							.cleMemWrap .controls input[type="checkbox"],
							.cleMemWrap .controls input[type="radio"],
							.checkboxWrap input[type="checkbox"]{
								vertical-align: bottom !important;
							}
							.checkboxWrap p,.checkboxWrap{
								font-weight: 300;
								line-height: 26px;
								font-size: 18px;
								color: ##434343;
							}
							.cleMemWrap .control-group{
								margin-bottom: 0;
							}
							.cleMemWrap .cleMemListWrap .row-fluid{
								padding-top: 0px!important;
								padding-bottom: 0px!important;
							}
							@media screen and (min-width: 1024px) {
								.cleMemWrap .control-label{
									width: 300px;
									text-align: left;
								}
							}
							.padding10{
								padding:10px !important;
							}
							.continueBtnWrap{
								border-bottom: 0px solid ##6C793E;
								border-top: 1px solid ##6C793E;
								background: ##ffffff !important;
							}
							.hideClass{
								display:none;
							}
						</style>
					</cfoutput>
				</cfsavecontent>
				
				<cfhtmlhead text="#local.headCode#">
				
				<cfsavecontent variable="local.returnHTML">
					<cfoutput>
						<style type="text/css">
							.overlay{								
								position: absolute;
								width: 100%;
								height: 100%;
								background-color: gray;
								top: 0px;
								left: 0px;
								opacity: 0.3;
							}
						</style>
						<script type="text/javascript">
							function _FB_validateForm(stepNum) {
								hideAlert();
								
								var arrReq = new Array();
								var todayDate = "#dateFormat(now(),'mm/dd/yyyy')#";							
								
								var amountRegex =  /(?:^\d{1,3}(?:\.?\d{3})*(?:,\d{2})?$)|(?:^\d{1,3}(?:,?\d{3})*(?:\.\d{2})?$)/;
								if($("##donation").is(':checked')){
									var don_amount = $("##donation_amount").val();
									if( ($.trim($('##donation_amount').val()).length == 0) ||( $.trim($('##donation_amount').val()).length > 0 && !amountRegex.test($.trim($('##donation_amount').val())) )  )   {
										arrReq[arrReq.length] = 'Donation - Enter a valid amount. Only positive amounts are allowed.';
									}	
								}
								if($("##lawyer_referral_service").is(':checked')){
									if ($('##lawyer_referral_service_address').val().length == 0) arrReq[arrReq.length] = 'Enter your Address For Lawyer Referral Service.';
								}
								if($(".indys_practiceBuilder").is(':checked') && $(".indys_businessBuilder").is(':checked')){
									if($(".indys_businessBuilder:checked").attr('frequency') != $(".indys_practiceBuilder:checked").attr('frequency')){
										arrReq[arrReq.length] = 'Rate frequencies for subscriptions Indy Business Builder & Indy Practice Builder must be same.';
									}							
								}								
								
								if (arrReq.length > 0) {
									var msg = 'Please address the following issues with your application:<br/>';
									for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
									showAlert(msg);
									$('html,body').animate({scrollTop: $('##issuemsg').offset().top},500);
									return false;
								}

								return true;
							}
								
							function loadMember(memNumber){
								var objParams = { memberNumber:memNumber };
								assignMemberData(objParams);
							}						

							function hideAlert() { $('##issuemsg').html('').hide(); };
							function showAlert(msg) { $('##issuemsg').html(msg).attr('class','alert-danger').show(); };
							function showAlertDone(msg) { $('##issuemsg').html(msg).attr('class','success').show(); };						
							function calcDues(){
								var membership_type	= document.getElementsByName('membership_type');
								var selectedMembership  = getSelectedRadio(membership_type);
								
								if(selectedMembership == 6){
									$(".license_info").hide();
								}else{
									$(".license_info").show();
								}

							}
							function getSelectedRadio(buttonGroup) {
								if (buttonGroup[0]) {
									for (var i=0; i<buttonGroup.length; i++) { if (buttonGroup[i].checked) return i }
								} else { if (buttonGroup.checked) return 0; }
								return -1;
							}
							function checkPrice(){
								var total = 0;
								$(".check_price").each(function(){
									if($(this).is(':checked')){
										total = total+parseFloat($(this).attr('price'));
									}
								});
								$('##cle_membership').val(total);
								checkTotal();
							}

							function addLRS(){
								if($("##lawyer_referral_service").is(':checked')){
									$('##lrs').val(parseFloat($("##lawyer_referral_service").attr('price')));
									$('.showdd').show();
								}else{
									$('##lrs').val(0);
									$('.showdd').hide();
								}
								
								checkTotal();
							}
							function addFoundation(){
								
								if($("##donation").is(':checked')){
									$("##donation_amount").show();
									var valD = $("##donation_amount").val();
									$("##foundation_donation").val(valD);
								}else{
									$("##donation_amount").hide();
									$("##foundation_donation").val("0");
								}
								checkTotal();
									
							}
							function addIndyLawyer(){
								
								if($("##indy_lawyer").is(':checked')){
									var val_law = parseFloat($("##indy_lawyer").attr('price'));
									$("##indy_lawyer_finder").val(val_law);
								}else{
									$("##indy_lawyer_finder").val(0);
								}
								
								checkTotal();
							}
							function addIndysAttorney(){
								if($('##indys_attorney').is(":checked")){
									var val_att = parseFloat($('##indys_attorney').attr('price'));
									$('##indys_attorney_value').val(val_att);
								}else{
										$("##indys_attorney_value").val(0);
								}
							}
							function addRateAmtPracticeBuilder(thisObj){
								if ($(thisObj).prop("checked")) {	
									$('.indys_practiceBuilder').attr('checked',false);
									$(thisObj).attr('checked','checked');
									var rfid = $(thisObj).attr('rfid');
									var val_att = parseFloat($(thisObj).attr('price'));
									var f_att = $(thisObj).attr('frequency');
									$('##indys_practiceBuilder_value').val(val_att);
									$('##indys_practiceBuilder').val(val_att);
									$('##indys_practiceBuilderRFID').val(rfid);
									$('##indys_practiceBuilder_Frequency').val(f_att);
								}else{
									$('.indys_practiceBuilder').attr('checked',false);
									$('##indys_practiceBuilder').val(0);
									$('##indys_practiceBuilder_value').val(0);
									$('##indys_practiceBuilderRFID').val(0);
									$('##indys_practiceBuilder_frequency').val('');
								}
							}
												
							function addRateAmtbusinessBuilder(thisObj){
								if($(thisObj).prop("checked")) {	
									$('.indys_businessBuilder').attr('checked',false);
									$(thisObj).attr('checked','checked');
									
									var val_att = parseFloat($(thisObj).attr('price'));
									var rfid = $(thisObj).attr('rfid');
									var f_att = $(thisObj).attr('frequency');
									$('##indys_businessBuilder_value').val(val_att);
									$('##indys_businessBuilder_Frequency').val(f_att);
									$('##indys_businessBuilder').val(val_att);
									$('##indys_businessBuilderRFID').val(rfid);
								}else{
									$('.indys_businessBuilder').attr('checked',false);
									$('##indys_businessBuilder_value').val(0);
									$('##indys_businessBuilder').val(0);
									$('##indys_businessBuilderRFID').val(0);
									$('##indys_businessBuilder_Frequency').val('');
								}
							}

							function checkTotal(){
								var total = parseFloat($("##amount").val());
								$(".check_price").each(function(){
									if($(this).is(':checked')){
										total = total+parseFloat($(this).attr('price'));
									}
								});

								if($("##lawyer_referral_service").is(':checked')){
									var val_lrs = parseFloat($('##lawyer_referral_service').attr('price'));								
									total = total+ val_lrs;
								}
								var donation = $("##donation_amount").val();
								if(donation != ""){
									if($("##donation").is(':checked')){
										total = total+ parseFloat(donation);										
									}
								}
								if($("##indy_lawyer").is(':checked')){
									var val_law = $("##indy_lawyer").attr('price');
									total = total+ parseFloat(val_law);
								}
								if($('##indys_attorney').is(":checked")){
									var val_att = parseFloat($('##indys_attorney').attr('price'));
									total = total+ val_att;
								}
								$('##divTotal').html("$ "+total);
								$('##total').val(total);
								
								
							}
							$(document).ready(function(){

								if($('##membership_type').val() == 1){
									$('.optional_div').show();
								}else{
									$('.optional_div').hide();
								}
								
								mca_setupDatePickerField('gradDate');
								mca_setupDatePickerField('dob');

								$('.indys_practiceBuilder').change(function(){
									var _this = $(this);
									var _freq = '';
									var _isChecked = _this.attr('checked');
									if(_isChecked){
										_freq = _this.attr('frequency');
										$('.indys_businessBuilder').each(function(){
											$(this).attr('disabled',false);
											if($(this).attr('frequency') != _freq){
												$(this).attr('checked',false);
												$(this).attr('disabled','disabled');
											}
										});
									} else {
										$('.indys_practiceBuilder').attr('disabled',false);
										$('.indys_businessBuilder').attr('disabled',false);
									}
								});

								$('.indys_businessBuilder').change(function(){
									var _this = $(this);
									var _freq = '';
									var _isChecked = _this.attr('checked');
									if(_isChecked){
										_freq = _this.attr('frequency');
										$('.indys_practiceBuilder').each(function(){
											$(this).attr('disabled',false);
											if($(this).attr('frequency') != _freq){
												$(this).attr('checked',false);
												$(this).attr('disabled','disabled');
											}
										});
									} else {
										$('.indys_practiceBuilder').attr('disabled',false);
										$('.indys_businessBuilder').attr('disabled',false);
									}
								});
							});
						</script>
						
						<cfset local.amount= 0>
						<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"membership_type") >
							<cfset local.membership = session.fieldArr.membership_type>
						<cfelse>
							<cfset local.membership = event.getValue('membership_type','')>
						</cfif>
						
						<cfswitch expression="#local.membership#">
							<cfcase value="1">
								<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"memberID") >
									<cfset memberID =session.fieldArr.memberID >
								<cfelse>
									<cfset memberID =event.getValue('memberID') >
								</cfif>
								<cfset local.10PlusQry = application.objCustomPageUtils.mem_getGroups(memberID,variables.orgID,'10PlusAttyCred')>	
								<cfset local.2to4Qry = application.objCustomPageUtils.mem_getGroups(memberID,variables.orgID,'2to4AttyCred')>	
								<cfset local.5to7Qry = application.objCustomPageUtils.mem_getGroups(memberID,variables.orgID,'5to7AttyCred')>	
								<cfset local.8to9Qry = application.objCustomPageUtils.mem_getGroups(memberID,variables.orgID,'8to9AttyCred')>
								<cfset local.lessthenyear = application.objCustomPageUtils.mem_getGroups(memberID,variables.orgID,'NewAttyCred')>	

								<!--- <cfset amount = StructFindValue(local.memberTypeDues,'Attorney 10+ Years',"one")> --->
								
								<cfif local.10PlusQry.recordCount neq 0>
									<cfif structKeyExists(local.memberTypeDues, "Attorney 10+ Years")>
										<cfset local.amount="#local.memberAmtDues['Attorney 10+ Years']#">
										<cfset session.attorney_type="Attorney 10+ Years">
									</cfif>
								</cfif>
								<cfif local.2to4Qry.recordCount neq 0>
									<cfif structKeyExists(local.memberTypeDues, "Attorney 2-4 Years")>
										<cfset local.amount="#local.memberAmtDues['Attorney 2-4 Years']#">
										
										<cfset session.attorney_type="Attorney 2-4 Years">
									</cfif>
								</cfif>
								<cfif local.5to7Qry.recordCount neq 0>
									<cfif structKeyExists(local.memberTypeDues, "Attorney 5-7 Years")>
										<cfset local.amount="#local.memberAmtDues['Attorney 5-7 Years']#">
										<cfset session.attorney_type="Attorney 5-7 Years">
									</cfif>
								</cfif>
								<cfif local.8to9Qry.recordCount neq 0>
									<cfif structKeyExists(local.memberTypeDues, "Attorney 8-9 Years")>
										<cfset local.amount="#local.memberAmtDues['Attorney 8-9 Years']#">
										<cfset session.attorney_type="Attorney 8-9 Years">
									</cfif>
								</cfif>
								<cfif local.lessthenyear.recordCount neq 0>
									<cfif structKeyExists(local.memberTypeDues, "New Attorney")>
										<cfset local.amount="#local.memberAmtDues['New Attorney']#">
										<cfset session.attorney_type="New Attorney">
									</cfif>
								</cfif>
							</cfcase>	
							<cfcase value="2">
								<cfset local.amount="#local.memberAmtDues['Life Member']#">
							</cfcase>	
							<cfcase value="3">
								<cfset local.amount="#local.memberAmtDues['Judiciary Member']#">
							</cfcase>	
							<cfcase value="4">
								<cfset local.amount="#local.memberAmtDues['Law Faculty Member']#">
							</cfcase>	
							<cfcase value="5">
								<cfset local.amount="#local.memberAmtDues['Government Member']#">
							</cfcase>	
							<cfcase value="6">
								<cfset local.amount="#local.memberAmtDues['Indigent Service Staff Attorney']#">
							</cfcase>	
							<cfcase value="7">
								<cfset local.amount="#local.memberAmtDues['Paralegal Member']#">
							</cfcase>
							<cfcase value="8">
								<cfset local.amount="#local.memberAmtDues['Inactive Practice Member']#">
							</cfcase>
							<cfcase value="9">
								<cfset local.amount="#local.memberAmtDues['Graduate - JD Only']#">
							</cfcase>
							<cfcase value="10">
								<cfset local.amount="#local.memberAmtDues['Virtual Membership']#">
							</cfcase>
						</cfswitch>

						<div id="customPage">
							<div id="issuemsg" style="display:none;margin:6px 0 10px 0;"></div>
							<div id="formTable">
								<div class=" row-fluid" style="padding-bottom:15px;">
									<div class="TitleText span12" >
										Step 2 - Additional Services and Add-Ons
										<a class="pull-right btn btn-default btnCustom" id="gotoStep2" class="btn backLink"  href="/?pg=membership&fa=backto1">Back To Step 1</a>
									</div>				
								
								</div>
								<div class="CPSection step1">
									<cfform class="form-horizontal" name="frmFB190" id="frmFB190" method="POST" action="/?#cgi.QUERY_STRING#" onsubmit="return _FB_validateForm(2);">
										<cfset collection = arguments.event.getCollection()>
										
										<cfloop collection="#arguments.event.getCollection()#" item="local.key">
											<cfif listFindNoCase(event.getValue('fieldnames',''),local.key)
												and NOT listFindNoCase("fa,amount,cle_membership,lrs,btnSubmit,referral_service_info,donation_amount,foundation_donation,donation,indys_attorney,indys_attorney_value,indy_lawyer,indy_lawyer_finder,bar_foundation_info,lawyer_referral_service,lawyer_referral_service_address,total,indys_practiceBuilder_value,indys_businessBuilder_value,indys_practiceBuilder,indys_practiceBuilderFull,indys_practiceBuilderMonthly,indys_businessBuilderFull,indys_businessBuilder,indys_businessBuilderMonthly,indys_businessBuilder_Frequency,indys_practiceBuilder_Frequency,indys_businessBuilderRFID,indys_practiceBuilderRFID",local.key) 
												and left(local.key,7) neq "section"
												and left(local.key,9) neq "formfield"
												and left(local.key,4) neq "fld_">
												<cfinput type="hidden" name="#local.key#"  id="#local.key#" value="#arguments.event.getValue(local.key)#">
											</cfif>
										</cfloop>
								
										<cfinput type="hidden" name="amount" id="amount" value="#local.amount#">
										<cfinput type="hidden" name="fa" id="fa" value="processStep1">
										<cfset cle_membership = 0 >
										<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"cle_membership") >
											<cfset cle_membership = session.fieldArr.cle_membership >
										</cfif>
										<cfinput type="hidden" name="cle_membership" id="cle_membership" value="#cle_membership#">
										<cfset lrs = 0 >
										<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"lrs") >
											<cfset lrs = session.fieldArr.lrs >
										</cfif>
										<cfinput type="hidden" name="lrs" id="lrs" value="#lrs#">
										<cfset local.foundation_donation = 0 >
										<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"foundation_donation") >
											<cfset local.foundation_donation = session.fieldArr.foundation_donation >
										</cfif>
										<cfinput type="hidden" name="foundation_donation" id="foundation_donation" value="#local.foundation_donation#">
										<cfset indys_attorney_value = 0 >
										<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"indys_attorney_value") >
											<cfset indys_attorney_value = session.fieldArr.indys_attorney_value >
										</cfif>
										<cfinput type="hidden" name="indys_attorney_value" id="indys_attorney_value" value="#indys_attorney_value#">
										
										<cfset indys_practiceBuilder_value = 0 >
										<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"indys_practiceBuilder_value") >
											<cfset indys_practiceBuilder_value = session.fieldArr.indys_practiceBuilder_value >
										</cfif>
										<cfinput type="hidden" name="indys_practiceBuilder_value" id="indys_practiceBuilder_value" value="#indys_practiceBuilder_value#">
										
										<cfset indys_businessBuilder_Frequency = 0 >
										<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"indys_businessBuilder_Frequency") >
											<cfset indys_businessBuilder_Frequency = session.fieldArr.indys_businessBuilder_Frequency >
										</cfif>
										<cfinput type="hidden" name="indys_businessBuilder_Frequency" id="indys_businessBuilder_Frequency" value="#indys_businessBuilder_Frequency#">
										
										<cfset indys_practiceBuilder_Frequency = 0 >
										<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"indys_practiceBuilder_Frequency") >
											<cfset indys_practiceBuilder_Frequency = session.fieldArr.indys_practiceBuilder_Frequency >
										</cfif>
										<cfinput type="hidden" name="indys_practiceBuilder_Frequency" id="indys_practiceBuilder_Frequency" value="#indys_practiceBuilder_Frequency#">					
										
										<cfset indys_businessBuilder_value = 0 >
										<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"indys_businessBuilder_value") >
											<cfset indys_businessBuilder_value = session.fieldArr.indys_businessBuilder_value >
										</cfif>
										<cfinput type="hidden" name="indys_businessBuilder_value" id="indys_businessBuilder_value" value="#indys_businessBuilder_value#">
										
										<cfset indy_lawyer_finder = 0 >
										<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"indy_lawyer_finder") >
											<cfset indy_lawyer_finder = 0 >
										</cfif>
										<cfinput type="hidden" name="indy_lawyer_finder" id="indy_lawyer_finder" value="0">
										<cfset total = 0 >
										<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"total") >
											<cfset total = session.fieldArr.total >
										</cfif>
										<cfset local.hideVal = ''>
										<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"indys_businessBuilder") && (session.fieldArr.indys_businessBuilder eq 1) >
												<cfset local.hideVal = "hideClass">
										</cfif>
										<cfinput type="hidden" name="total" id="total" value="#total#">

										<div class="NVTitle CPSectionTitle BB">#variables.strPageFields.SectionDivisionTitle#:</div>
										<cfset local.subscriptionDetails = application.objCustomPageUtils.sub_getSubscriptionsDetailsFromSubsetUID(setUID='7FA6870E-CCBC-4F71-9627-A53FA0AF036E',siteID=event.getValue('mc_siteInfo.siteID'),rateUID="3248A928-5DA7-4483-B6E7-8EA2C0E657A7,B5997CCB-B025-4793-B7FA-FE98F8E95334,5AD8E6CC-350E-4959-BBE6-379B07F44A0C,06B031DC-8931-453E-9379-52A4F7DD6EEA",isFrequencyFull=true)/>																	
										
										<cfset local.sectionSubUIDs = StructNew()>
										<cfset sections = createObject("java", "java.util.LinkedHashMap").init()/>
										<cfloop query="local.subscriptionDetails">
											<cfset  sections[local.subscriptionDetails.subscriptionname][local.subscriptionDetails.ratename] =local.subscriptionDetails.rateamt >	
											<cfset local.sectionSubUIDs[local.subscriptionDetails.subscriptionname] = local.subscriptionDetails.uid />
										</cfloop>

										<div class="frmRow1 cleMemWrap frmText" style="padding:10px;position:relative">
											<cfif arguments.event.getTrimValue('membership_type','0') EQ 10>
												<div class="overlay"></div>
											</cfif>
											<div class="row-fluid" style="padding:10px;">											
												#variables.strPageFields.SectionDivisionText#									
											</div>
											<div class="row-fluid" style="padding:10px;">
												<cfset i = 1>
												<cfloop list="#structKeyList(sections)#" index="key">
													<cfif NOT (local.membership eq 7 and (#key# eq "Women and the Law Division" OR #key# eq "Young Lawyers Division"))>
													<div class=" row-fluid cleMemListWrap padding10">
														<div class="control-group">
															<label class="control-label" for="">#key# :</label>
															<div class="controls">
															<cfset indexVal = 0 >
															<cfloop list="#ArrayToList(StructSort(sections[key]))#" index="key1">
																
																<cfif indexVal eq 1 >
																  &nbsp;&nbsp;
																</cfif>
																	<cfset chk_val = "#key#:#key1#:#dollarFormat(sections[key][key1])#">
																	<input 
																	<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr, "section_division_#i#") && (session.fieldArr["section_division_#i#"] eq chk_val) >
																		checked
																	</cfif>
																	type="radio" price="#sections[key][key1]#" class="checkbox check_price" value="#chk_val#" name="section_division_#i#" id="sectionDivision1" onClick="checkPrice();">#key1#: #dollarFormat(sections[key][key1])#</label>
																	<cfset indexVal = 1 >
																
															</cfloop>
															</div>
														</div>
													</div>
													<cfset i = i+1>
													</cfif>
												</cfloop>
											</div>
																				
										</div>
										<div class="NVTitle CPSectionTitle BB">#variables.strPageFields.PracticeBuilderTitle#:</div>
										<div class="frmRow1 frmText row-fluid" style="padding:10px;">
											#variables.strPageFields.PracticeBuilderText#									
										</div>

										
										<cfset local.qryRateInfo = getSubscriptionRates(variables.IndyPracticeBuilderUID,variables.IndyPracticeBuilderRateScheduleUID,variables.IndyPracticeBuilderRateUID,variables.useMID)>
										
										<cfset local.rateAmtpracticeBuilderFull = 0/>
										<cfset local.rateAmtpracticeBuilderMonthly = 0/>
										<cfloop query="local.qryRateInfo">
											<cfif local.qryRateInfo.frequencyname eq 'Full'>
												<cfset local.rateAmtpracticeBuilderFull = local.qryRateInfo.rateAmt/>
												<cfset local.rfidpracticeBuilderFull = local.qryRateInfo.rfid/>
											</cfif>
											<cfif local.qryRateInfo.frequencyname eq 'Monthly'>
												<cfset local.rateAmtpracticeBuilderMonthly = local.qryRateInfo.rateAmt/>
												<cfset local.rfidpracticeBuilderMonthly = local.qryRateInfo.rfid/>
											</cfif>
										</cfloop>
										<div class="row-fluid checkboxWrap" style="padding:10px;">
											<cfif  local.rateAmtpracticeBuilderFull gt 0>
												<input 
												<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"indys_practiceBuilderFull") && (session.fieldArr.indys_practiceBuilderFull eq 1) >
													checked
												</cfif>
												type="checkbox" frequency="F" rfid="#local.rfidpracticeBuilderFull#"  price="#local.rateAmtpracticeBuilderFull#" class="indys_practiceBuilder checkbox pull-left" value="1" id="indys_practiceBuilderFull" data-price="#NumberFormat(local.rateAmtpracticeBuilderFull,'00.00')#"   name="indys_practiceBuilderFull" onchange="addRateAmtPracticeBuilder(this);" >
												
												<p>#Replace(variables.strPageFields.practiceBuilderCheckboxFull, '{price}', NumberFormat(local.rateAmtpracticeBuilderFull,'00.00') )#</p>
											</cfif>
											<cfif  local.rateAmtpracticeBuilderMonthly gt 0>
												<input 
												<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"indys_practiceBuilderMonthly") && (session.fieldArr.indys_practiceBuilderMonthly eq 1) >
													checked
												</cfif>
												type="checkbox" frequency="M" rfid="#local.rfidpracticeBuilderMonthly#" price="#local.rateAmtpracticeBuilderMonthly#" class="indys_practiceBuilder checkbox pull-left" value="1" id="indys_practiceBuilderMonthly" data-price="#NumberFormat(local.rateAmtpracticeBuilderMonthly,'00.00')#" name="indys_practiceBuilderMonthly" onchange="addRateAmtPracticeBuilder(this);" >
												<p>#Replace(variables.strPageFields.practiceBuilderCheckboxMonthly, '{price}', NumberFormat(local.rateAmtpracticeBuilderMonthly,'00.00') )#</p>
											</cfif>
											<cfset local.indys_practiceBuilderHidVal = 0>
											<cfset local.indys_businessBuilderHidVal = 0>
											<cfset local.indys_businessBuilderHidRFID = 0>
											<cfset local.indys_practiceBuilderHidRFID = 0>
											
											<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"indys_practiceBuilder") >
												<cfset local.indys_practiceBuilderHidVal = session.fieldArr.indys_practiceBuilder>
											</cfif>
											<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"indys_businessBuilder") >
												<cfset local.indys_businessBuilderHidVal = session.fieldArr.indys_businessBuilder>
											</cfif>
											<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"indys_businessBuilderRFID") >
												<cfset local.indys_businessBuilderHidRFID = session.fieldArr.indys_businessBuilderRFID>
											</cfif>
											<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"indys_practiceBuilderRFID") >
												<cfset local.indys_practiceBuilderHidRFID = session.fieldArr.indys_practiceBuilderRFID>
											</cfif>
											
											<input type="hidden" name="indys_practiceBuilder" id="indys_practiceBuilder" value="#local.indys_practiceBuilderHidVal#"/>
											<input type="hidden" name="indys_businessBuilder" id="indys_businessBuilder" value="#local.indys_businessBuilderHidVal#"/>
											<input type="hidden" name="indys_businessBuilderRFID" id="indys_businessBuilderRFID" value="#local.indys_businessBuilderHidRFID#"/>
											<input type="hidden" name="indys_practiceBuilderRFID" id="indys_practiceBuilderRFID" value="#local.indys_practiceBuilderHidRFID#"/>
										</div>
										
										<div class="NVTitle CPSectionTitle BB">#variables.strPageFields.BusinessBuilderTitle#:</div>
										<div class="frmRow1 frmText row-fluid" style="padding:10px;">
											#variables.strPageFields.BusinessBuilderText#									
										</div>	

										<cfset local.qryRateInfo = getSubscriptionRates(variables.IndyBusinessBuilderUID,variables.IndyBusinessBuilderRateScheduleUID,variables.IndyBusinessBuilderRateUID,variables.useMID)>
										
										<cfset local.rateAmtbusinessBuilderFull = 0/>
										<cfset local.rateAmtbusinessBuilderMonthly = 0/>
										<cfloop query="local.qryRateInfo">
											<cfif local.qryRateInfo.frequencyname eq 'Full'>
												<cfset local.rateAmtbusinessBuilderFull = local.qryRateInfo.rateAmt/>
												<cfset local.rfidbusinessBuilderFull = local.qryRateInfo.rfid/>
											</cfif>
											<cfif local.qryRateInfo.frequencyname eq 'Monthly'>
												<cfset local.rateAmtbusinessBuilderMonthly = local.qryRateInfo.rateAmt/>
												<cfset local.rfidbusinessBuilderMonthly = local.qryRateInfo.rfid/>
											</cfif>
										</cfloop>
										
										<div class="row-fluid checkboxWrap" style="padding:10px;">
											<cfif  local.rateAmtbusinessBuilderFull gt 0>
												<input 
												<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"indys_businessBuilderFull") && (session.fieldArr.indys_businessBuilderFull eq 1) >
													checked
												</cfif>
												type="checkbox" frequency="F" rfid="#local.rfidbusinessBuilderFull#" price="#local.rateAmtbusinessBuilderFull#" class="indys_businessBuilder checkbox pull-left" value="1" id="indys_businessBuilderFull" name="indys_businessBuilderFull" onchange="addRateAmtbusinessBuilder(this);" >
												
												<p>#Replace(variables.strPageFields.BusinessBuilderCheckboxFull, '{price}', NumberFormat(local.rateAmtbusinessBuilderFull,'00.00') )#</p>
											</cfif>
											<cfif  local.rateAmtbusinessBuilderMonthly gt 0>
												<input 
												<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"indys_businessBuilderMonthly") && (session.fieldArr.indys_businessBuilderMonthly eq 1) >
													checked
												</cfif>
												type="checkbox" frequency="M" rfid="#local.rfidbusinessBuilderMonthly#" price="#local.rateAmtbusinessBuilderMonthly#" class="indys_businessBuilder checkbox pull-left" value="1" id="indys_businessBuilderMonthly" name="indys_businessBuilderMonthly" onchange="addRateAmtbusinessBuilder(this);" >
												<p>#Replace(variables.strPageFields.BusinessBuilderCheckboxMonthly, '{price}', NumberFormat(local.rateAmtbusinessBuilderMonthly,'00.00') )#</p>
											</cfif>
											
										</div>

										
										<div class="NVTitle CPSectionTitle BB sectionToHide #local.hideVal#" >#variables.strPageFields.AttorneysNetworkTitle#:</div>
										<div class="frmRow1 frmText sectionToHide #local.hideVal#" style="padding:10px;position:relative">
											<cfif arguments.event.getTrimValue('membership_type','0') EQ 10>
												<div class="overlay"></div>
											</cfif>
											<div class="row-fluid" style="padding:10px;">
												#variables.strPageFields.AttorneysNetworkText#
											</div>
											<div class="row-fluid checkboxWrap" style="padding:10px;">
												<cfset indys_attorney = "" >
														
												<input 
												<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"indys_attorney") && (session.fieldArr.indys_attorney eq 1) >
													checked
												</cfif>
												type="checkbox" price="#local.attorneyRates#" class="checkbox pull-left" value="1" id="indys_attorney" name="indys_attorney" onchange="addIndysAttorney();">
												
												<p>#variables.strPageFields.AttorneysNetworkCheckbox#</p>
											</div>
										</div>										
										<div class="NVTitle CPSectionTitle BB ">#variables.strPageFields.FoundationTitle#:</div>
										<div class="frmRow1 frmText " style="padding:10px;position:relative">
											<cfif arguments.event.getTrimValue('membership_type','0') EQ 10>
												<div class="overlay"></div>
											</cfif>
											<div class="row-fluid " >
												#variables.strPageFields.FoundationText#
											</div>
											<div class="row-fluid checkboxWrap" >
												<input
													<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"donation") && (session.fieldArr.donation eq "yes") >
															checked
													</cfif>
													 type="checkbox"  class="checkbox" value="yes" name="donation" id="donation" onchange="addFoundation()">
													#variables.strPageFields.FoundationCheckbox1#
											</div>
											<div class="row-fluid " >
												<cfset donation_amount = "#DecimalFormat(local.foundationRate)#">
												<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"donation") >
														<cfset donation_amount =session.fieldArr.donation_amount>
												</cfif>
												<input style=<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"donation") && (session.fieldArr.donation eq "yes") >
														"display:block"<cfelse>"display:none"
												</cfif> type="text"  value="#donation_amount#" name="donation_amount" id="donation_amount" onBlur="addFoundation()">
											</div>
											<div class="row-fluid checkboxWrap" >
												<input 
													<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"bar_foundation_info") && (session.fieldArr.bar_foundation_info eq "yes") >
															checked
													</cfif>
													type="checkbox"  class="checkbox" value="yes" name="bar_foundation_info" id="bar_foundation_info">
													#variables.strPageFields.FoundationCheckbox2#
											</div>
										</div>
										<div class="actionBox NVTitle row-fluid CPButtonTitle  continueBtnWrap" id="step3">
											<div class="span12 padding10" >
												<div class="span4" >
													<input class="btn btn-default formButton btnCustom" name="btnSubmit" type="Submit" value="Continue">
												</div>
												<div class="span8">
													On the next screen you will be shown a total and can provide payment information
												</div>
											</div>
										</div>
									</cfform>
									<a style="margin-left:10px;margin-top:10px;" id="gotoStep2" class="btn btn-default backLink pull-right btnCustom" href="/?pg=membership&fa=backto1">Back To Step 1</a>
								</div>
							</div>
							</div>
					</cfoutput>
				
				</cfsavecontent>
			</cfcase>

			<cfcase value="backto1">				

				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryProfessionalLicenseTypes">
					select mplt.[PLTypeID], mplt.PLName
					from membercentral.dbo.ams_memberProfessionalLicenseTypes mplt
					where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.orgID#">
					order by ordernum
				</cfquery>

				<cfset local.memberAmtDues = structNew()>
				<cfset local.memberAmtDues["Attorney 2-4 Years"] = 0 />
				<cfset local.memberAmtDues["Attorney 5-7 Years"] = 0 />
				<cfset local.memberAmtDues["Attorney 8-9 Years"] = 0 />
				<cfset local.memberAmtDues["Attorney 10+ Years"] = 0 />
				<cfset local.memberAmtDues["New Attorney"] = 0/>
				<cfset local.memberAmtDues["Life Member"] = 0 />
				<cfset local.memberAmtDues["Judiciary Member"] = 0 />
				<cfset local.memberAmtDues["Law Faculty Member"] = 0 />
				<cfset local.memberAmtDues["Government Member"] = 0 />
				<cfset local.memberAmtDues["Indigent Service Staff Attorney"] = 0 />
				<cfset local.memberAmtDues["Paralegal Member"] = 0 />
				<cfset local.memberAmtDues["Inactive Practice Member"] = 0 />
				<cfset local.memberAmtDues["Graduate - JD Only"] = 0 />
				<cfset local.memberAmtDues["Virtual Membership"] = 0 />

				<cfloop query="variables.qryRates">
					<cfswitch expression="#variables.qryRates.rateUID#" >
					<!--- Regular Members / Private Sector Rates --->
					<cfcase value="EED072D0-78D3-4C96-BA7B-8FAE34583D62,2DB358A4-1B2D-43CD-B413-2B34193D2B0D,F520B1EF-5A0B-408F-8DC5-43883B94E5F8">
						<cfset local.memberAmtDues["Attorney 2-4 Years"] = variables.qryRates.rateAmt />
					</cfcase>
					<cfcase value="FD70B79D-8078-4E13-96D5-798D03928016,AAEE3413-1B7C-4812-8CF9-0664A05A1194,1295EE70-6D5D-4AAE-953A-FCC9EA118576">
						<cfset local.memberAmtDues["Attorney 5-7 Years"] = variables.qryRates.rateAmt />
					</cfcase>
					<cfcase value="7B491FF7-E426-4438-9BB1-E2C9252BAEA2,5A434473-39A3-49F9-AEA9-2AFB09125960,61B54A60-47BE-420F-B2EB-937ACDC4A524">
						<cfset local.memberAmtDues["Attorney 8-9 Years"] = variables.qryRates.rateAmt />
					</cfcase>
					<cfcase value="D3CD31B8-10F9-4427-A4F6-5FD4F5E9D2A2,76283B52-C5DD-4B87-8AD6-4984FB4ADEC4,5D94EAEA-06EB-41B6-A1AE-54AA3351691D">
						<cfset local.memberAmtDues["Attorney 10+ Years"] = variables.qryRates.rateAmt />
					</cfcase>
					<cfcase value="61FE806E-89A5-4D6C-9FF7-823BB70404F0,D1463119-E4B1-4F3F-8CF0-89E29900CB0E,49B0F21D-5342-4107-8F0D-FD159971E66D">
						<cfset local.memberAmtDues["New Attorney"] = variables.qryRates.rateAmt />
					</cfcase>		
					<cfcase value="71A12674-0473-49BC-8E2C-D3DA082A3473,7B1F8F2E-2C2E-45C1-BB9D-7958AF2B7D36,3E937C3B-8F6B-4186-8287-AD51B6CB2A05">
						<cfset local.memberAmtDues["Life Member"] = variables.qryRates.rateAmt />
					</cfcase>
					<cfcase value="E11BA4CE-B11C-4252-B48E-5CB8B0B02A15,322C40DB-1F68-4799-B48E-67A32CECDBCE,E29CB080-62DA-4A73-A2E5-288246ACC5BB">
						<cfset local.memberAmtDues["Judiciary Member"] = variables.qryRates.rateAmt />
					</cfcase>
					<cfcase value="5984F9D9-224D-4E71-9870-1D48FF5AC418,24603410-7E07-402A-A872-6AF0F26A4A19,66EF2FAF-971F-4886-93B2-77CBAA07DAE5">
						<cfset local.memberAmtDues["Law Faculty Member"] = variables.qryRates.rateAmt />
					</cfcase>
					<cfcase value="3BC7C9B2-C2B0-4566-BC0C-DC7316BC5697,B1F403E3-1B63-4CBA-8C48-C3DDA729BA42,3E9166C2-F5AD-4CE6-AB1B-0DB3F9789C64">
						<cfset local.memberAmtDues["Government Member"] = variables.qryRates.rateAmt />
					</cfcase>
					<cfcase value="5721435E-D211-4616-BF83-5023B0B960B5,BF9A4A7E-74EE-4E16-A917-2C206026C084,E60FE74F-5A57-45A8-90CF-DDCE9F98B9F7">
						<cfset local.memberAmtDues["Indigent Service Staff Attorney"] = variables.qryRates.rateAmt />
					</cfcase>
					<cfcase value="49ECA9E1-D1CC-40CE-B5E4-C9305B5117E7,48D772F9-C0A1-4A78-AE4E-5F4708601FEC,14B8DF61-BD3D-4B7A-9E21-25F0D5CE3B5E">
						<cfset local.memberAmtDues["Paralegal Member"] = variables.qryRates.rateAmt />
					</cfcase>
					<cfcase value="7A611EBD-55F2-4104-B4F3-3B289892B8DA,8241BE65-FE6C-46B6-A641-CB3DDC380E6F,EBFE607F-81AA-409F-9BAC-19D9B1B9B93B">
						<cfset local.memberAmtDues["Inactive Practice Member"] = variables.qryRates.rateAmt />
					</cfcase>
					<cfcase value="58DEDB19-E97B-43F6-91E8-4E87645C977A,E895A1EB-9D53-4BED-80C1-1B82B2431577,B7BCC7BB-670D-4DF8-B6A5-985D7A50F0E8">
						<cfset local.memberAmtDues["Graduate - JD Only"] = variables.qryRates.rateAmt />
					</cfcase>
					<cfcase value="192E2A65-E0DC-4D09-AF74-C3C0E69F5C36,7039AAA6-82DF-4446-9B54-2EF839C7F22A,50BE547D-5459-4DB0-8D4A-0EBC485575FA">
						<cfset local.memberAmtDues["Virtual Membership"] = variables.qryRates.rateAmt />
					</cfcase>
				</cfswitch>
				</cfloop>				

				<cfsavecontent variable="local.returnHTML">
					<cfinclude template="membership_backto1.cfm">
				</cfsavecontent>
			</cfcase>

			<cfdefaultcase>			
				<cfset local.returnHTML = afterAccountSelection(event=arguments.event)>
			</cfdefaultcase>
		</cfswitch>

		<cfreturn returnAppStruct(local.returnHTML,"echo")>
	</cffunction>	

	<cffunction name="afterAccountSelection" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.strMsgCodes = structNew()>
		<cfset local.strMsgCodes['507D7690-F01F-AF51-C9CCB83F029DD786'] = { err=1, msg="This submission has been flagged as spam and was not submitted." }>
		<cfset local.strMsgCodes['50BA4017-F01F-AF51-C9CCE480104528F0'] = { err=1, msg="This submission is missing information. Ensure you have entered all required fields and the e-mail address is valid." }>

		<!--- setup memberdata struct and prefill with logged in member --->
		<cfset local.memberData = {}>
		<cfset session.fieldarr = {}>

		<cfset local.memberData.phone = variables.data.phone>
		<cfset local.memberData.officeAddress = application.objMember.getMemberAddressByAddressType(orgID=variables.orgID, memberID=session.cfcUser.memberData.memberID, addressType="Office")>
		<cfset local.memberData.residenceAddress = application.objMember.getMemberAddressByAddressType(orgID=variables.orgID, memberID=session.cfcUser.memberData.memberID, addressType="Residence")>
		
		<cfset local.qryProfessionalLicenseTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=variables.orgID)>
		
		<cfloop query="variables.qryRates">
			<cfswitch expression="#variables.qryRates.rateUID#" >
				<!--- Regular Members / Private Sector Rates --->
				<cfcase value="EED072D0-78D3-4C96-BA7B-8FAE34583D62,2DB358A4-1B2D-43CD-B413-2B34193D2B0D,F520B1EF-5A0B-408F-8DC5-43883B94E5F8">
					<cfset local.memberAmtDues["Attorney 2-4 Years"] = variables.qryRates.rateAmt />
				</cfcase>
				<cfcase value="FD70B79D-8078-4E13-96D5-798D03928016,AAEE3413-1B7C-4812-8CF9-0664A05A1194,1295EE70-6D5D-4AAE-953A-FCC9EA118576">
					<cfset local.memberAmtDues["Attorney 5-7 Years"] = variables.qryRates.rateAmt />
				</cfcase>
				<cfcase value="7B491FF7-E426-4438-9BB1-E2C9252BAEA2,5A434473-39A3-49F9-AEA9-2AFB09125960,61B54A60-47BE-420F-B2EB-937ACDC4A524">
					<cfset local.memberAmtDues["Attorney 8-9 Years"] = variables.qryRates.rateAmt />
				</cfcase>
				<cfcase value="D3CD31B8-10F9-4427-A4F6-5FD4F5E9D2A2,76283B52-C5DD-4B87-8AD6-4984FB4ADEC4,5D94EAEA-06EB-41B6-A1AE-54AA3351691D">
					<cfset local.memberAmtDues["Attorney 10+ Years"] = variables.qryRates.rateAmt />
				</cfcase>
				<cfcase value="61FE806E-89A5-4D6C-9FF7-823BB70404F0,D1463119-E4B1-4F3F-8CF0-89E29900CB0E,49B0F21D-5342-4107-8F0D-FD159971E66D">
					<cfset local.memberAmtDues["New Attorney"] = variables.qryRates.rateAmt />
				</cfcase>		
				<cfcase value="71A12674-0473-49BC-8E2C-D3DA082A3473,7B1F8F2E-2C2E-45C1-BB9D-7958AF2B7D36,3E937C3B-8F6B-4186-8287-AD51B6CB2A05">
					<cfset local.memberAmtDues["Life Member"] = variables.qryRates.rateAmt />
				</cfcase>
				<cfcase value="E11BA4CE-B11C-4252-B48E-5CB8B0B02A15,322C40DB-1F68-4799-B48E-67A32CECDBCE,E29CB080-62DA-4A73-A2E5-288246ACC5BB">
					<cfset local.memberAmtDues["Judiciary Member"] = variables.qryRates.rateAmt />
				</cfcase>
				<cfcase value="5984F9D9-224D-4E71-9870-1D48FF5AC418,24603410-7E07-402A-A872-6AF0F26A4A19,66EF2FAF-971F-4886-93B2-77CBAA07DAE5">
					<cfset local.memberAmtDues["Law Faculty Member"] = variables.qryRates.rateAmt />
				</cfcase>
				<cfcase value="3BC7C9B2-C2B0-4566-BC0C-DC7316BC5697,B1F403E3-1B63-4CBA-8C48-C3DDA729BA42,3E9166C2-F5AD-4CE6-AB1B-0DB3F9789C64">
					<cfset local.memberAmtDues["Government Member"] = variables.qryRates.rateAmt />
				</cfcase>
				<cfcase value="5721435E-D211-4616-BF83-5023B0B960B5,BF9A4A7E-74EE-4E16-A917-2C206026C084,E60FE74F-5A57-45A8-90CF-DDCE9F98B9F7">
					<cfset local.memberAmtDues["Indigent Service Staff Attorney"] = variables.qryRates.rateAmt />
				</cfcase>
				<cfcase value="49ECA9E1-D1CC-40CE-B5E4-C9305B5117E7,48D772F9-C0A1-4A78-AE4E-5F4708601FEC,14B8DF61-BD3D-4B7A-9E21-25F0D5CE3B5E">
					<cfset local.memberAmtDues["Paralegal Member"] = variables.qryRates.rateAmt />
				</cfcase>
				<cfcase value="7A611EBD-55F2-4104-B4F3-3B289892B8DA,8241BE65-FE6C-46B6-A641-CB3DDC380E6F,EBFE607F-81AA-409F-9BAC-19D9B1B9B93B">
					<cfset local.memberAmtDues["Inactive Practice Member"] = variables.qryRates.rateAmt />
				</cfcase>
				<cfcase value="58DEDB19-E97B-43F6-91E8-4E87645C977A,E895A1EB-9D53-4BED-80C1-1B82B2431577,B7BCC7BB-670D-4DF8-B6A5-985D7A50F0E8">
					<cfset local.memberAmtDues["Graduate - JD Only"] = variables.qryRates.rateAmt />
				</cfcase>
				<cfcase value="192E2A65-E0DC-4D09-AF74-C3C0E69F5C36,7039AAA6-82DF-4446-9B54-2EF839C7F22A,50BE547D-5459-4DB0-8D4A-0EBC485575FA">
					<cfset local.memberAmtDues["Virtual Membership"] = variables.qryRates.rateAmt />
				</cfcase>
				</cfswitch>
		</cfloop>
		
		<cfsavecontent variable="local.returnHTML">
			<cfinclude template="membership_default.cfm">
		</cfsavecontent>		

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processStep1" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<!--- form validation --->
		<cfset local.error = false >	
		<cfif NOT val(arguments.event.getTrimValue('memberID','0')) 
			OR NOT len(arguments.event.getTrimValue('firstName','')) 
			OR NOT len(arguments.event.getTrimValue('lastName','')) 	 
			OR NOT len(arguments.event.getTrimValue('email',''))
			OR NOT IsValid("email",arguments.event.getTrimValue('email',''))
			OR NOT len(arguments.event.getTrimValue('membership_type',''))>
			<cfset  local.error = true >
			
		</cfif>
		<cfif  arguments.event.getTrimValue('Billing','') eq 'office'>
			<cfif  NOT len(arguments.event.getTrimValue('office_address',''))
				OR NOT len(arguments.event.getTrimValue('office_city','')) 
				OR NOT len(arguments.event.getTrimValue('office_stateID',''))
				OR NOT len(arguments.event.getTrimValue('office_zip','')) 
				OR NOT len(arguments.event.getTrimValue('office_phone','')) 
			>
			<cfset  local.error = true >
			</cfif>
		</cfif>
		<cfif  arguments.event.getTrimValue('Billing','') eq 'company'>
			<cfif   NOT len(arguments.event.getTrimValue('company_address',''))
			OR NOT len(arguments.event.getTrimValue('company_city','')) 
			OR NOT len(arguments.event.getTrimValue('company_stateID',''))
			OR NOT len(arguments.event.getTrimValue('company_zip','')) 
			OR NOT len(arguments.event.getTrimValue('company_phone',''))
			>
			<cfset  local.error = true/>
			</cfif>
		</cfif>
		<cfif  arguments.event.getTrimValue('Billing','') eq 'residence'>
			<cfif   NOT len(arguments.event.getTrimValue('residence_address',''))
			OR NOT len(arguments.event.getTrimValue('residence_city','')) 
			OR NOT len(arguments.event.getTrimValue('residence_stateID',''))
			OR NOT len(arguments.event.getTrimValue('residence_zip','')) 
			OR NOT len(arguments.event.getTrimValue('residence_phone',''))
			>
			<cfset  local.error = true/>
			</cfif>
		</cfif>

		<cfif arguments.event.getTrimValue('membership_type','') eq 1>
			<cfif   NOT len(arguments.event.getTrimValue('professionalLicenseDropdown',''))>
				<cfset  local.error = true/>
			</cfif>
		</cfif>
		<cfif local.error eq true>
			<cflocation url="#variables.baselink#&fa=showForm&sid=50BA4017-F01F-AF51-C9CCE480104528F0" addtoken="no">
		</cfif>

		<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"memberID") >
			<cfset memberID =session.fieldArr.memberID >
		<cfelse>
			<cfset memberID =arguments.event.getTrimValue('memberID') >
		</cfif>
		<cfset session.historyId = application.objCustomPageUtils.mh_addHistory(memberID=memberID, categoryID=variables.getCategoryStarted.CATEGORYID,
						subCategoryID=variables.getCategoryStarted.SUBCATEGORYID, description='Member Started Membership Form.', linkMemberID=0,
						enteredByMemberID=memberID, newAccountsOnly=false)>

		<cfset variables.profile_1.strPaymentForm = application.objPayments.showGatewayInputForm(
																	siteid=variables.siteID,
																	profilecode=variables.profile_1._profileCode,
																	pmid = memberID,
																	showCOF = memberID EQ session.cfcUser.memberData.memberID,
																	usePopup=false,
																	usePopupDIVName='ccForm',
																	autoShowForm=1)>
		<cfif len(variables.profile_1.strPaymentForm.headCode)>
			<cfhtmlhead text="#application.objCommon.minText(variables.profile_1.strPaymentForm.headCode)#">
		</cfif>
		
		<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"membership_type") >
			<cfset membership =session.fieldArr.membership_type >
		<cfelse>
			<cfset membership =event.getValue('membership_type','') >
		</cfif>
		
		<cfset local.memType = "">
		<cfif membership eq 1>
			<cfif structKeyExists(session, "attorney_type")>
				<cfset local.memType = session.attorney_type>
			</cfif>	
		<cfelseif membership eq 2>
			<cfset local.memType = "Life Member">
		<cfelseif membership eq 3>
			<cfset local.memType = "Judiciary Member">
		<cfelseif membership eq 4>
			<cfset local.memType = "Law Faculty Member">
		<cfelseif membership eq 5>
			<cfset local.memType = "Government Member">
		<cfelseif membership eq 6>
			<cfset local.memType = "Indigent Service Provider">
		<cfelseif membership eq 7>
			<cfset local.memType = "Paralegal Member">
		<cfelseif membership eq 8>
			<cfset local.memType = "Inactive Practice Attorney">
		<cfelseif membership eq 9>
			<cfset local.memType = "JD Only (no law license in any US jurisdiction)">
		<cfelseif membership eq 10>
			<cfset local.memType = "Virtual Membership">
		</cfif>
		<cfset cle_mem_types_arr = arrayNew(1)>

		<cfloop collection="#form#" item="thisField">
		     <cfif left(thisField,16) eq 'SECTION_DIVISION' and listLen(thisField,'_') eq 3>
		     	<cfset temp = ArrayAppend(cle_mem_types_arr, form[thisField])>
		     </cfif>
		</cfloop>
		<cfset arraySort(cle_mem_types_arr, "text")>
		
		<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"amount") >
			<cfset local.memAmt = #session.fieldArr.amount# />
		<cfelseif len(event.getValue('amount'))>
			<cfset local.memAmt = #event.getValue('amount')# />
		<cfelse>
			<cfset local.memAmt = 0 />
		</cfif>

		<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"cle_membership") >
			<cfset local.cleAmt = #session.fieldArr.cle_membership# />
		<cfelseif len(event.getValue('cle_membership'))>
			<cfset local.cleAmt = #event.getValue('cle_membership')# />
		<cfelse>
			<cfset local.cleAmt = 0 />
		</cfif>

		<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"lrs") &&  event.getValue('indys_businessBuilder','0') eq 0>
			<cfset local.lrsAmt = #session.fieldArr.lrs# />
		<cfelseif len(event.getValue('lrs')) &&  event.getValue('indys_businessBuilder','0') eq 0>
			<cfset local.lrsAmt = #event.getValue('lrs')# />
		<cfelse>
			<cfset local.lrsAmt = 0 />
		</cfif>

		<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"donation") >
			<cfset local.donationAmt = #session.fieldArr.donation_amount# />
		<cfelseif event.getValue('donation','0')>
			<cfset local.donationAmt = #event.getValue('donation_amount')# />
		<cfelse>
			<cfset local.donationAmt = 0 />
		</cfif>

		<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"indys_attorney_value") &&  event.getValue('indys_businessBuilder','0') eq 0>
			<cfset local.attorneyAmt = #session.fieldArr.indys_attorney_value# />
		<cfelseif event.getValue('indys_attorney','0') &&  event.getValue('indys_businessBuilder','0') eq 0>
			<cfset local.attorneyAmt = #event.getValue('indys_attorney_value')# />
		<cfelse>
			<cfset local.attorneyAmt = 0 />
		</cfif>

		<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"indy_lawyer_finder") &&  event.getValue('indys_businessBuilder','0') eq 0>
			<cfset local.lawyerAmt = #session.fieldArr.indy_lawyer_finder# />
		<cfelseif event.getValue('indy_lawyer','0') &&  event.getValue('indys_businessBuilder','0') eq 0>
			<cfset local.lawyerAmt = #event.getValue('indy_lawyer_finder')# />
		<cfelse>
			<cfset local.lawyerAmt = 0 />
		</cfif>
		
		<cfif event.getValue('indys_practiceBuilder','0')>
			<cfset local.practiceBuilderAmt = #event.getValue('indys_practiceBuilder_value')# />
			<cfelse>
			<cfset local.practiceBuilderAmt = 0 />
		</cfif>
		<cfif event.getValue('indys_businessBuilder','0')>
			<cfset local.businessBuilderAmt = #event.getValue('indys_businessBuilder_value')# />
			<cfelse>
			<cfset local.businessBuilderAmt = 0 />
		</cfif>
		
		<cfset local.totalAmount = local.memAmt + local.cleAmt +  local.lrsAmt + local.donationAmt + local.attorneyAmt + local.lawyerAmt+ local.practiceBuilderAmt + local.businessBuilderAmt >				
		<cfset local.amountToChargeNow = local.totalAmount>

		<cfset local.formName = "frmPACPay">

		<cfsavecontent variable="local.headCode">
			<cfoutput>
			<style type="text/css">
				body { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
				h5 { font-size:13pt; font-weight:bold; margin:0 0 0 0;}
				div.CPSection { border:1px solid ##6C793E; margin-bottom:15px; }
				div.CPSection div.CPSectionTitle {border-bottom: 1px solid ##6C793E ;font-size:14pt;font-weight:bold; color:##fff; padding:10px; background:##899461; font-family: Calibri,Arial,Helvetica; } 
				div.CPSection div.BB { border-bottom:1px solid ##6C793E;border-top:1px solid ##6C793E; }
				div.CPSection span.frmText, div.CPSection td.frmText { font-size:12pt; color:##505050; font-family: Calibri,Arial,Helvetica; }
				div.CPSection span.frmText span.block { display:block; }
				div.CPSection span.frmText span.b { font-weight:bold; }
				div.CPSection td.r { text-align:right; }
				label{cursor: pointer; cursor: hand;}
				a {font-size:10pt;}
				.add_pad{padding: 10px;}
				.padding10{padding: 10px;}
				.paymentTypeWrap .optionsRadio{
					vertical-align:top;
				}
			</style>
			<script type="text/javascript"> 
				function getFormFields(){
					$('##fa').val('step3');
					$("###local.formName#").attr("onsubmit", '');
					$("###local.formName#").submit();
				}

				function getSelectedRadio(buttonGroup) {
					if (buttonGroup[0]) {
						for (var i=0; i<buttonGroup.length; i++) { if (buttonGroup[i].checked) return i }
					} else { if (buttonGroup.checked) return 0; }
					return -1;
				}						
				
				function checkPaymentMethod() {							
					if ($('##payMethCC').is(':checked')) {
						$('##CCInfo').show();
						$('##CheckInfo').hide();
						if(!$('##ccForm').is(':visible')) {
							listenToFrame();
						}
					}else{
						$('##CCInfo').hide();
						$('##CheckInfo').show();
					}
				}	
				
				function getMethodOfPayment() {
					var btnGrp = document.forms['#local.formName#'].payMeth;
					var i = getSelectedRadio(btnGrp);
					if (i == -1) return "";
					else {
						if (btnGrp[i]) return btnGrp[i].value;
						else return btnGrp.value;
					}
				}						
				
				function _FB_hasValue(obj, obj_type){
					if (obj_type == 'TEXT' || obj_type == 'TEXTAREA'){ tmp = obj.value; tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,''); if (tmp.length == 0) return false; else return true; }
					else if (obj_type == 'SELECT'){ for (var i=0; i < obj.length; i++) { if (obj.options[i].selected){ tmp = obj.options[i].value; tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,''); if (tmp.length > 0) return true; } } return false; } 
					else if (obj_type == 'SINGLE_VALUE_RADIO' || obj_type == 'SINGLE_VALUE_CHECKBOX'){ if (obj.checked) return true; else return false;	} 
					else if (obj_type == 'RADIO' || obj_type == 'CHECKBOX'){ if (obj.length == undefined && obj.checked) return true; else{for (var i=0; i < obj.length; i++){ if (obj[i].checked) return true; }} return false; }
					else{ return true; }
				}
				function _FB_validateForm() {
					var thisForm = document.forms["#local.formName#"];
					var arrReq = new Array();

					$('.submitFrmBtn').attr("disabled", true);	
					$(".submitTxt").show();

					// -----------------------------------------------------------------------------------------------------------------
					if (!_FB_hasValue(thisForm['payMeth'], 'RADIO')) arrReq[arrReq.length] 	= 'Method of Payment';
					var MethodOfPaymentValue = getMethodOfPayment();
					
					if( MethodOfPaymentValue == 'CC' )	{
						#variables.profile_1.strPaymentForm.jsvalidation#	
					}
					
					// -----------------------------------------------------------------------------------------------------------------
					if (arrReq.length > 0) {
						var msg = 'Please address the following issues with your application:\n\n';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
						alert(msg);
						return false;
					}
					return true;
				}					
				
				function hideAlert() { $('##issuemsg').html('').hide(); };
				function showAlert(msg) { $('##issuemsg').html(msg).attr('class','alert-danger').show(); };
				function showAlertDone(msg) { $('##issuemsg').html(msg).attr('class','success').show(); }
				function listenToFrame(){
					var windowWidth = $(window).width();
					var _scrollPx = 300;
					if(windowWidth < 767) {
						_scrollPx = 600;
					}
					var formListener = setInterval(function(){
						if(!$('div[id^="divManageFormWrapper"]').is(':visible') ){
							$('html, body').animate({scrollTop : _scrollPx},800);
							clearInterval(formListener);
						}
					},100);
				};
				$(document).ready(function(){
					 $(".submitTxt").hide();
					 $(document).on('click','.cof_edit',function(){
						listenToFrame();
					});
				});					
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.headCode#">

		<cfsavecontent variable="local.returnHTML">

			<cfoutput>
			<div>
				<div class=" row-fluid" style="padding-bottom:15px;">
					<div class="TitleText span12" >
						Step 3 - Payment Information
						 <a class="pull-right btn btn-default backLink btnCustom" id="gotoStep2" onClick='getFormFields();' href="##">Back To Step  2</a>
					</div>
				</div>
				
				<div class="CPSection">
					<div class="CPSectionTitle NVTitle">Payment Information</div>

					<div style="padding:10px;">
						
						<cfif local.memAmt neq 0>
							<div class="row-fluid" >
								<div class="span6">
									Annual Membership Dues: 
									<cfif local.memType EQ "Inactive Practice Attorney">
										Inactive Practice Member
									<cfelse>
										#local.memType#								
									</cfif>
								</div>
								<div class="span6">
									#dollarFormat(local.memAmt)#
								</div>
							</div>
						</cfif>
						<cfif NOT ArrayIsEmpty(cle_mem_types_arr)>
							<cfloop array="#cle_mem_types_arr#" index="key">
								<cfset types = key.split(':')>
								<div class="row-fluid" >
									<div class="span6">
										#types[1]# (#types[2]#)
									</div>
									<div class="span6">
										#types[3]#
									</div>
								</div>
							
							</cfloop>
						</cfif>
						<cfif local.practiceBuilderAmt neq 0>
							<div class="row-fluid" >
								<div class="span6">
									#variables.strPageFields.PracticeBuilderTitle#
								</div>
								<div class="span6">
									#dollarFormat(local.practiceBuilderAmt)#
								</div>
							</div>
						</cfif>
						<cfif local.businessBuilderAmt neq 0>
							<div class="row-fluid" >
								<div class="span6">
									#variables.strPageFields.BusinessBuilderTitle#
								</div>
								<div class="span6">
									#dollarFormat(local.businessBuilderAmt)#
								</div>
							</div>
						</cfif>
						<cfif local.attorneyAmt neq 0>
							<div class="row-fluid" >
								<div class="span6">
									#variables.strPageFields.AttorneysNetworkTitle#
								</div>
								<div class="span6">
									#dollarFormat(local.attorneyAmt)#
								</div>
							</div>
						</cfif>
						<cfif local.donationAmt neq 0>
							<div class="row-fluid" >
								<div class="span6">
									Foundation Donation
								</div>
								<div class="span6">
									#dollarFormat(local.donationAmt)#
								</div>
							</div>
						</cfif>
						
						<div class="row-fluid" >
							<div class="span6">
								Total Charge
							</div>
							<div class="span6">
								#dollarFormat(local.totalAmount)#
							</div>
						</div>
					</div>
				</div>
				
				<div id="paymentTable">
					<div id="payerrDIV" style="display:none;margin:6px 0;"></div>
					<div class="form">
						<cfform class="form-horizontal" name="#local.formName#" id="#local.formName#" method="post" action="#variables.baselink#" 
						onsubmit="return _FB_validateForm();">
							<cfinput type="hidden" name="fa" id="fa" value="processStep2">
							
							<cfloop collection="#arguments.event.getCollection()#" item="local.key">
								<cfif listFindNoCase(event.getValue('fieldnames',''),local.key)
									and NOT listFindNoCase("fa,btnSubmit",local.key) 
									and left(local.key,9) neq "formfield"
									and left(local.key,4) neq "fld_">
									<cfset fieldArr[local.key] = 	arguments.event.getValue(local.key)>
									<cfinput type="hidden" name="#local.key#"  id="#local.key#" value="#arguments.event.getValue(local.key)#">
								</cfif>
							</cfloop>
							<cfset session.fieldArr = fieldArr>

							<div class="CPSection ">
								<div class="CPSectionTitle NVTitle">*Method of Payment for #variables.formNameDisplay#</div>
								<div class="P padding10 paymentTypeWrap">
									<div class="row-fluid" >
										<div class="span12">
											Please select your preferred method of payment from the options.
										</div>
									</div>
									<div class="row-fluid" >
										<div class="span12">
											<input  value="CC" class="tsAppBodyText optionsRadio" name="payMeth" id="payMethCC" type="radio" onClick="checkPaymentMethod();"> Credit Card
										</div>
									</div>
									<div class="row-fluid" >
										<div class="span12">
											<input value="Check" class="tsAppBodyText optionsRadio" name="payMeth" type="radio" onClick="javascript:{checkPaymentMethod();}"> Check
										</div>
									</div>
								</div>
							</div>								
								
							<div id="CCInfo" class="CPSection" style="display:none;">
								<div class="CPSectionTitle NVTitle">Credit Card Information</div>
								<div id="ccForm" style="padding:10px;">
									<div>#variables.profile_1.strPaymentForm.inputForm#</div>
									<div><button type="submit" class="btn submitFrmBtn btnCustom" name="btnSubmit">SUBMIT</button> <span class="submitTxt">Submission being processed...</span></div>
								</div>
							</div>
							<div id="CheckInfo" style="display:none;" class="CPSection">
							<div class="CPSectionTitle">Check Information</div>
							<div class="P add_pad">
								<p style="font-size:18px;font-weight:bold;">If you choose "Check" as your payment method, you will not gain access to IndyBar membership and member benefits until payment is received. For immediate access, please pay by credit card.</p>
								<p>Please make your check payable to the Indianapolis Bar Association and mail to:<br/>
								<div style="padding-left:25px;">135 N. Pennsylvania St.<br/>Suite 1500<br/>Indianapolis, IN 46204</div></p>
								<p>Please include a copy of the receipt with your payment.</p>
							</div>
							<div class="add_pad"><button type="submit" class="btn submitFrmBtn btnCustom" name="btnSubmit">SUBMIT</button> <span class="submitTxt">Submission being processed...</span></div>
						</div>
						<div >
							<a  id="gotoStep2" class="btn backLink pull-right btnCustom" onClick='getFormFields();' href="##">Back To Step  2</a>
						</div>
						</cfform>
					</div>
				</div>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processStep2" access="private" output="false" returntype="Query">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<!--- ensure card is selected --->
		
		<!--- <cfif NOT val(arguments.event.getTrimValue('p_#variables.profile_1._profileID#_mppid','0'))>
			<cflocation url="#variables.baselink#&fa=showForm&sid=50BA4017-F01F-AF51-C9CCE480104528F0" addtoken="no">
		</cfif> --->
		

		<!--- UPDATE MEMBER RECORD  --->
		<cftry>
		
			<cfset local.recordUpdated = false>

			<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"memberID") >
				<cfset local.memberID = session.fieldArr.memberID >
			<cfelse>
				<cfset local.memberID = event.getValue('memberID','0') >
			</cfif>
			<cfset local.newMemIdArr = application.mcCacheManager.sessionGetValue(keyname='newMemIdArr', defaultValue=arrayNew(1))>
			<cfif IsArray(local.newMemIdArr) AND listFind(ArrayToList(local.newMemIdArr),local.memberID)>		
				<cfscript>
					local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=local.memberID);
					local.objSaveMember.setDemo(prefix=arguments.event.getValue('prefix',''),
												firstName=arguments.event.getValue('firstName',''),
												middleName=arguments.event.getValue('middleName',''),
												lastName=arguments.event.getValue('lastName',''),
												company=arguments.event.getValue('company_name',''));
					if (len(arguments.event.getValue('email')))
						local.objSaveMember.setEmail(type='Email', value=arguments.event.getValue('email'));
					local.objSaveMember.setAddress(type='Office', 
													address1=arguments.event.getValue('office_address',''),
													address2=arguments.event.getValue('office_address_2',''),
													city=arguments.event.getValue('office_city',''),
													stateID=arguments.event.getValue('office_stateID',0),
													postalCode=arguments.event.getValue('office_zip',''));
					local.objSaveMember.setAddress(type='Residence',
													address1=arguments.event.getValue('residence_address',''),
													address2=arguments.event.getValue('residence_address_2',''), 
													city=arguments.event.getValue('residence_city',''), 
													stateID=arguments.event.getValue('residence_stateID',0), 
													postalCode=arguments.event.getValue('residence_zip',''));
					local.objSaveMember.setWebsite(type='Website', value=arguments.event.getValue('website'));
					local.objSaveMember.setPhone(addressType='Office', type='Phone', value=arguments.event.getValue('office_phone'));
					local.objSaveMember.setPhone(addressType='Residence', type='Phone', value=arguments.event.getValue('residence_phone'));
					local.objSaveMember.setRecordType(recordType='Individual');
					local.objSaveMember.setMemberType(memberType='User');
					if (len(event.getValue('maiden_name','')))
						local.objSaveMember.setCustomField(field='Previous Name', value=event.getValue('maiden_name'));
					if(len(event.getValue('call_name','')))
						local.objSaveMember.setCustomField(field='Call Name', value=event.getValue('call_name'));
					if(len(event.getValue('yearOFBirth','')))
						local.objSaveMember.setCustomField(field='Year Of Birth', value=event.getValue('yearOFBirth'));
					if(len(event.getValue('member_directory_address','')) and event.getValue('member_directory_address','') eq "directory")
						local.objSaveMember.setCustomField(field='Directory Opt Out', value='1');
					if (listFindNoCase("Office,Residence",arguments.event.getTrimValue('billing_adderss')))
 						local.objSaveMember.setAddressTag(tag="Billing", type=arguments.event.getTrimValue('billing_adderss'));
 					if (listFindNoCase("Office,Residence",arguments.event.getTrimValue('mailing_adderss')))
 						local.objSaveMember.setAddressTag(tag="Mailing", type=arguments.event.getTrimValue('mailing_adderss'));
 					if (listFindNoCase("Office,Residence",arguments.event.getTrimValue('member_directory_address')))
 						local.objSaveMember.setAddressTag(tag="Professional", type=arguments.event.getTrimValue('member_directory_address'));	
					local.strResult = local.objSaveMember.saveData(runImmediately=1);
					if (local.strResult.success)
                		local.recordUpdated = true;
				</cfscript>				
			</cfif>

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.recordUpdated = false>
		</cfcatch>
		</cftry>
		
		<cfset local.memAmt = replaceNoCase(arguments.event.getTrimValue('amount'), ",", "", "ALL") />
		<cfset local.membership = event.getValue('membership_type','')>
		<cfset local.memType = "">
		<cfif local.membership eq 1>
			<cfset local.memType = "Attorney Member">
			<cfset local.10PlusQry = application.objCustomPageUtils.mem_getGroups(memberID,variables.orgID,'10PlusAttyCred')>	
			<cfset local.2to4Qry = application.objCustomPageUtils.mem_getGroups(memberID,variables.orgID,'2to4AttyCred')>	
			<cfset local.5to7Qry = application.objCustomPageUtils.mem_getGroups(memberID,variables.orgID,'5to7AttyCred')>	
			<cfset local.8to9Qry = application.objCustomPageUtils.mem_getGroups(memberID,variables.orgID,'8to9AttyCred')>
			<cfset local.lessthenyear = application.objCustomPageUtils.mem_getGroups(memberID,variables.orgID,'NewAttyCred')>	

			<cfif local.10PlusQry.recordCount neq 0>
				<cfset local.memType = "Attorney 10+ Years">
			</cfif>
			<cfif local.2to4Qry.recordCount neq 0>
				<cfset local.memType = "Attorney 2-4 Years">
			</cfif>
			<cfif local.5to7Qry.recordCount neq 0>
				<cfset local.memType = "Attorney 5-7 Years">
			</cfif>
			<cfif local.8to9Qry.recordCount neq 0>
				<cfset local.memType = "Attorney 8-9 Years">
			</cfif>
			<cfif local.lessthenyear.recordCount neq 0>
				<cfset local.memType =  "New Attorney">
			</cfif>
		<cfelseif local.membership eq 2>
			<cfset local.memType = "Life Member">
		<cfelseif local.membership eq 3>
			<cfset local.memType = "Judiciary Member">
		<cfelseif local.membership eq 4>
			<cfset local.memType = "Law Faculty Member">
		<cfelseif local.membership eq 5>
			<cfset local.memType = "Government Member">
		<cfelseif local.membership eq 6>
			<cfset local.memType = "Indigent Service Provider">
		<cfelseif local.membership eq 7>
			<cfset local.memType = "Paralegal Member">
		<cfelseif local.membership eq 8>
			<cfset local.memType = "Inactive Practice Attorney">
		<cfelseif local.membership eq 9>
			<cfset local.memType = "JD Only (no law license in any US jurisdiction)">
		<cfelseif local.membership eq 10>
			<cfset local.memType = "Virtual Membership">
		</cfif>
		
		<cfset cle_mem_types_arr = arrayNew(1)>
				
		<cfloop collection="#form#" item="thisField">
		     <cfif left(thisField,16) eq 'SECTION_DIVISION' and listLen(thisField,'_') eq 3>
		     	<cfset temp = ArrayAppend(cle_mem_types_arr, form[thisField])>
		     </cfif>
		</cfloop>
		<cfif len(event.getValue('amount'))>
			<cfset local.memAmt = #event.getValue('amount')# />
		<cfelse>
			<cfset local.memAmt = 0 />
		</cfif>
		<cfif len(event.getValue('cle_membership'))>
			<cfset local.cleAmt = #event.getValue('cle_membership')# />
			<cfelse>
			<cfset local.cleAmt = 0 />
		</cfif>
		<cfif len(event.getValue('lrs')) && event.getValue('indys_businessBuilder','0') eq 0>
			<cfset local.lrsAmt = #event.getValue('lrs')# />
			<cfelse>
			<cfset local.lrsAmt = 0 />
		</cfif>
		<cfif event.getValue('donation','0')>
			<cfset local.donationAmt = #event.getValue('donation_amount')# />
			<cfelse>
			<cfset local.donationAmt = 0 />
		</cfif>
		<cfif event.getValue('indys_attorney','0') && event.getValue('indys_businessBuilder','0') eq 0>
			<cfset local.attorneyAmt = #event.getValue('indys_attorney_value')# />
			<cfelse>
			<cfset local.attorneyAmt = 0 />
		</cfif>
		<cfif event.getValue('indy_lawyer','0') && event.getValue('indys_businessBuilder','0') eq 0>
			<cfset local.lawyerAmt = #event.getValue('indy_lawyer_finder')# />
			<cfelse>
			<cfset local.lawyerAmt = 0 />
		</cfif>
		<cfif event.getValue('indys_practiceBuilder','0')>
			<cfset local.practiceBuilderAmt = #event.getValue('indys_practiceBuilder_value')# />
			<cfelse>
			<cfset local.practiceBuilderAmt = 0 />
		</cfif>
		<cfif event.getValue('indys_businessBuilder','0')>
			<cfset local.businessBuilderAmt = #event.getValue('indys_businessBuilder_value')# />
			<cfelse>
			<cfset local.businessBuilderAmt = 0 />
		</cfif>
		
		
		<cfset local.totalAmount = local.memAmt + local.cleAmt +  local.lrsAmt + local.donationAmt + local.attorneyAmt + local.lawyerAmt+ local.practiceBuilderAmt + local.businessBuilderAmt>	
		
		<cfset local.amountToChargeNow = local.totalAmount>
		
		<!--- get statecode from stateid --->
		<cfif len(event.getValue('office_stateid',''))>
			<cfquery name="local.qryGetOfficeState" dbtype="query">
				select stateName
				from variables.qryStates
				where stateID = <cfqueryparam cfsqltype="cf_sql_integer" value="#event.getValue('office_stateid',0)#">
			</cfquery>
		</cfif>
		<cfif len(event.getValue('residence_stateid',0))>
		<cfquery name="local.qryGetResidenceState" dbtype="query">
			select stateName
			from variables.qryStates
			where stateID = <cfqueryparam cfsqltype="cf_sql_integer" value="#event.getValue('residence_stateid',0)#">
		</cfquery>
		</cfif>
		
		<!--- construct email / confirmation --->
		<cfsavecontent variable="local.pageCSS">
			<cfoutput>
			<style type="text/css">
				body { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
				.customPage { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
				p { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
				.msgHeader{ background:##7a864e; font-weight:bold; padding:5px; }
				.frmText{ font-size:12pt; color:##505050; } 
				.b{ font-weight:bold; }
			</style>
			</cfoutput>
		</cfsavecontent>	

		<cfset local.memberData.firmAddress = application.objMember.getMemberDataByMemberNumber(mcproxy_orgID=variables.orgID, memberNumber=event.getValue('memberNumber',''))>
		
		<cfset local.historyId  = application.objCustomPageUtils.mh_updateHistory(arguments.event.getTrimValue('memberID'),session.historyId,variables.getCategoryCompleted.SUBCATEGORYID,'Member Completed Join Form.',true)>
		
		<cfsavecontent variable="local.invoice">
			<cfoutput>
			<style>
				.highlightRow{background-color: yellow;}
				
			</style>
			
			<!-- @accResponseMessage@ -->
			<p>#variables.formNameDisplay# submitted on #dateformat(now(),"dddd, m/d/yyyy")# #timeformat(now(),"h:mm tt")#.</p>
			<table cellpadding="2" cellspacing="0" width="100%" border="1" class="customPage ">			
			<tr class="msgHeader"><td colspan="2" class="b">MEMBER INFORMATION</td></tr>
			<cfif event.getValue('memberNumber_old','') NEQ 0 >
				<tr ><td class="frmText" width="50%">MemberNumber Selected:</td><td class="frmText">#event.getValue('memberNumber_old','')#&nbsp;</td></tr>
				<tr ><td class="frmText" width="50%">MemberNumber Created:</td><td class="frmText">#event.getValue('memberNumber','')#&nbsp;</td></tr>
			<cfelse>
				<tr ><td class="frmText" width="50%">MemberNumber:</td><td class="frmText">#event.getValue('memberNumber','')#&nbsp;</td></tr>
			</cfif>
			
			<cfif local.memberData.firmAddress.prefix neq event.getValue('prefix','')>
				<tr ><td class="frmText highlightRow">Prefix</td><td class="frmText highlightRow">#event.getValue('prefix','')#<span class="changed">*</span>&nbsp;</td></tr>	
			<cfelse>
				<tr ><td class="frmText">Prefix:</td><td class="frmText">#event.getValue('prefix','')#&nbsp;</td></tr>	
			</cfif>
			<cfif local.memberData.firmAddress.firstname neq event.getValue('firstName','')>
				<tr ><td class="frmText highlightRow">First Name:</td><td class="frmText highlightRow">#event.getValue('firstName','')#<span class="changed">*</span>&nbsp;</td></tr>	
			<cfelse>
				<tr ><td class="frmText">First Name:</td><td class="frmText">#event.getValue('firstName','')#&nbsp;</td></tr>	
			</cfif>
			<cfif local.memberData.firmAddress.middlename neq event.getValue('middlename','')>
				<tr ><td class="frmText highlightRow">Middle Name:</td><td class="frmText highlightRow">#event.getValue('middlename','')#<span class="changed">*</span>&nbsp;</td></tr>	
			<cfelse>
				<tr ><td class="frmText">Middle Name:</td><td class="frmText">#event.getValue('middlename','')#&nbsp;</td></tr>	
			</cfif>	

			<tr ><td class="frmText highlightRow">Maiden Name:</td><td class="frmText highlightRow">#event.getValue('maiden_name','')#<span class="changed">*</span>&nbsp;</td></tr>	
			<tr ><td class="frmText highlightRow">Call Name:</td><td class="frmText highlightRow">#event.getValue('call_name','')#<span class="changed">*</span>&nbsp;</td></tr>	
			<tr ><td class="frmText highlightRow">Year Of Birth:</td><td class="frmText highlightRow">#event.getValue('yearOFBirth','')#<span class="changed">*</span>&nbsp;</td></tr>	
					
			<cfif local.memberData.firmAddress.lastname neq event.getValue('lastName','')>
				<tr ><td class="frmText highlightRow">Last Name:</td><td class="frmText highlightRow">#event.getValue('lastName','')#<span class="changed">*</span>&nbsp;</td></tr>	
			<cfelse>
				<tr ><td class="frmText">Last Name:</td><td class="frmText">#event.getValue('lastName','')#&nbsp;</td></tr>	
			</cfif>
		
			<cfif local.memberData.firmAddress.company neq event.getValue('company_name','')>
				<tr ><td class="frmText highlightRow">Company:</td><td class="frmText highlightRow">#event.getValue('company_name','')#<span class="changed">*</span>&nbsp;</td></tr>	
			<cfelse>
				<tr ><td class="frmText">Company:</td><td class="frmText">#event.getValue('company_name','')#&nbsp;</td></tr>	
			</cfif>			
			<tr ><td class="frmText">Title:</td><td class="frmText">#event.getValue('your_title','')#&nbsp;</td></tr>
			<cfif local.memberData.firmAddress.email neq event.getValue('email','')>
				<tr ><td class="frmText highlightRow">Email:</td><td class="frmText highlightRow">#event.getValue('email','')#<span class="changed">*</span>&nbsp;</td></tr>	
			<cfelse>
				<tr ><td class="frmText">Email:</td><td class="frmText">#event.getValue('email','')#&nbsp;</td></tr>	
			</cfif>	
			<tr ><td class="frmText">Alternate Email:</td><td class="frmText">#event.getValue('alt_email','')#&nbsp;</td></tr>
			<tr ><td class="frmText">Assistant's Email:</td><td class="frmText">#event.getValue('assit_email','')#&nbsp;</td></tr>
			<tr ><td class="frmText">Website:</td><td class="frmText">#event.getValue('website','')#&nbsp;</td></tr>
			<tr><td colspan="2">&nbsp;</td></tr>

			<cfif event.getValue('office','') NEQ ''>

				<tr class="msgHeader"><td colspan="2" class="b">Office Address</td></tr>
				
				<cfif local.memberData.firmAddress.address1 neq event.getValue('office_address','')>
					<tr ><td class="frmText highlightRow">Address:</td><td class="frmText highlightRow">#event.getValue('office_address','')#<span class="changed">*</span>&nbsp;</td></tr>	
				<cfelse>
					<tr ><td class="frmText">Address:</td><td class="frmText">#event.getValue('office_address','')#&nbsp;</td></tr>	
				</cfif>	

				<cfif local.memberData.firmAddress.address2 neq event.getValue('office_address_2','')>
					<tr ><td class="frmText highlightRow"><span class="changed">*</span>&nbsp;</td><td class="frmText highlightRow">#event.getValue('office_address_2','')#<span class="changed">*</span>&nbsp;</td></tr>	
				<cfelse>
					<tr ><td class="frmText">&nbsp;</td><td class="frmText">#event.getValue('office_address_2','')#&nbsp;</td></tr>	
				</cfif>	
				
				<cfif local.memberData.firmAddress.city neq event.getValue('office_city','')>
					<tr ><td class="frmText highlightRow">City:</td><td class="frmText highlightRow">#event.getValue('office_city','')#<span class="changed">*</span>&nbsp;</td></tr>	
				<cfelse>
					<tr ><td class="frmText">City:</td><td class="frmText">#event.getValue('office_city','')#&nbsp;</td></tr>	
				</cfif>
				<cfif local.memberData.firmAddress.stateid neq event.getValue('office_stateid','')>
					<tr><td class="frmText highlightRow">State/Province:</td><td class="frmText highlightRow">
						#local.qryGetOfficeState.stateName#<span class="changed">*</span>&nbsp;
					</td></tr>
				<cfelse>
					<tr><td class="frmText">State/Province:</td><td class="frmText">
						#local.qryGetOfficeState.stateName#&nbsp;
					</td></tr>
				</cfif>
				<cfif local.memberData.firmAddress.postalcode neq event.getValue('office_zip','')>
					<tr ><td class="frmText highlightRow">Zip/Postal Code:</td><td class="frmText highlightRow">#event.getValue('office_zip','')#<span class="changed">*</span>&nbsp;</td></tr>	
				<cfelse>
					<tr ><td class="frmText">Zip/Postal Code:</td><td class="frmText">#event.getValue('office_zip','')#&nbsp;</td></tr>	
				</cfif>
				<tr><td class="frmText">Country:</td><td class="frmText">#event.getValue('office_country','')#&nbsp;</td></tr>
				
				<cfif local.memberData.firmAddress.phone neq event.getValue('office_phone','')>
					<tr ><td class="frmText highlightRow">Phone:</td><td class="frmText highlightRow">#event.getValue('office_phone','')#<span class="changed">*</span>&nbsp;</td></tr>	
				<cfelse>
					<tr ><td class="frmText">Phone:</td><td class="frmText">#event.getValue('office_phone','')#&nbsp;</td></tr>	
				</cfif>
				<tr ><td class="frmText">Fax:</td><td class="frmText">#event.getValue('office_fax','')#&nbsp;</td></tr>
			</cfif>	
			<cfif event.getValue('residence','') NEQ ''>
				<tr><td colspan="2">&nbsp;</td></tr>
				<tr class="msgHeader"><td colspan="2" class="b">Residence Address</td></tr>

				<cfif local.memberData.firmAddress.address1 neq event.getValue('residence_address','')>
					<tr ><td class="frmText highlightRow">Address:</td><td class="frmText highlightRow">#event.getValue('residence_address','')#<span class="changed">*</span>&nbsp;</td></tr>	
				<cfelse>
					<tr ><td class="frmText">Address:</td><td class="frmText">#event.getValue('residence_address','')#&nbsp;</td></tr>	
				</cfif>	

				<cfif local.memberData.firmAddress.address2 neq event.getValue('residence_address_2','')>
					<tr ><td class="frmText highlightRow"><span class="changed">*</span>&nbsp;</td><td class="frmText highlightRow">#event.getValue('residence_address_2','')#<span class="changed">*</span>&nbsp;</td></tr>	
				<cfelse>
					<tr ><td class="frmText">&nbsp;</td><td class="frmText">#event.getValue('residence_address_2','')#&nbsp;</td></tr>	
				</cfif>	
				
				<cfif local.memberData.firmAddress.city neq event.getValue('residence_city','')>
					<tr ><td class="frmText highlightRow">City:</td><td class="frmText highlightRow">#event.getValue('residence_city','')#<span class="changed">*</span>&nbsp;</td></tr>	
				<cfelse>
					<tr ><td class="frmText">City:</td><td class="frmText">#event.getValue('residence_city','')#&nbsp;</td></tr>	
				</cfif>
				<cfif len(event.getValue('residence_stateid','')) and local.memberData.firmAddress.stateid neq event.getValue('residence_stateid','')>
					<tr><td class="frmText highlightRow">State/Province:</td><td class="frmText highlightRow">
						#local.qryGetResidenceState.stateName#<span class="changed">*</span>&nbsp;
					</td></tr>
				<cfelseif len(event.getValue('residence_stateid',''))>
					<tr><td class="frmText">State/Province:</td><td class="frmText">
						#local.qryGetResidenceState.stateName#&nbsp;
					</td></tr>
				</cfif>
				<cfif local.memberData.firmAddress.postalcode neq event.getValue('residence_zip','')>
					<tr ><td class="frmText highlightRow">Zip/Postal Code:</td><td class="frmText highlightRow">#event.getValue('residence_zip','')#<span class="changed">*</span>&nbsp;</td></tr>	
				<cfelse>
					<tr ><td class="frmText">Zip/Postal Code:</td><td class="frmText">#event.getValue('residence_zip','')#&nbsp;</td></tr>	
				</cfif>
				<tr><td class="frmText">Country:</td><td class="frmText">#event.getValue('residence_country','')#&nbsp;</td></tr>
				
				<cfif local.memberData.firmAddress.phone neq event.getValue('residence_phone','')>
					<tr ><td class="frmText highlightRow">Phone:</td><td class="frmText highlightRow">#event.getValue('residence_phone','')#<span class="changed">*</span>&nbsp;</td></tr>	
				<cfelse>
					<tr ><td class="frmText">Phone:</td><td class="frmText">#event.getValue('residence_phone','')#&nbsp;</td></tr>	
				</cfif>
				<tr ><td class="frmText">Fax:</td><td class="frmText">#event.getValue('residence_fax','')#&nbsp;</td></tr>
	
			</cfif>
			
			<tr><td colspan="2">&nbsp;</td></tr>
			<tr class="msgHeader"><td colspan="2" class="b">Address Preferences</td></tr>
			<tr ><td class="frmText">Billing Address:</td><td class="frmText">#event.getValue('billing_adderss','')#&nbsp;</td></tr>
			<tr ><td class="frmText">Mailing Address:</td><td class="frmText">#event.getValue('mailing_adderss','')#&nbsp;</td></tr>
			<cfset local.directoryOption = "#event.getValue('member_directory_address','')#">
			<cfif local.directoryOption EQ "directory">
				<cfset local.directoryOption = "Opt Out of Directory">
			</cfif>
			<tr ><td class="frmText">Directory Address:</td><td class="frmText">#local.directoryOption#&nbsp;</td></tr>
			<cfif event.getValue('professionalLicenseDropdown','') neq "">
			<tr class="msgHeader"><td colspan="2" class="b">License Information</td></tr>
			</cfif>
				<cfset local.qryOrgProLicenseStatuses = application.objOrgInfo.getOrgProfessionalLicenseStatuses(orgID=variables.orgID)>
				<cfset local.qryProfessionalLicenseTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=variables.orgID)>
				<cfset local.licenseStatus = 	{}>
				<cfset local.licenseDates  = ArrayNew(1)>
				<cfloop query="local.qryOrgProLicenseStatuses">
					<cfset local.licenseStatus[LCase(local.qryOrgProLicenseStatuses.statusName)] = local.qryOrgProLicenseStatuses.PLStatusID>	
				</cfloop>
				<cfset local.licensesNames  = ArrayNew(1)>
				<cfloop query="local.qryProfessionalLicenseTypes">
					<cfset local.licensesNames[local.qryProfessionalLicenseTypes.pltypeid] = local.qryProfessionalLicenseTypes.PLName>	
				</cfloop>
				<cfif event.getValue('professionalLicenseDropdown','') neq "">
					<cfset local.licenses = event.getValue('professionalLicenseDropdown')>
					<cfset  local.licenseArr = ListToArray(local.licenses)>

					<cfloop array="#local.licenseArr#" index="local.key">
						<cfset  local.license_no  = event.getValue('state_#local.key#_licensenumber')>
						<cfset  local.license_date  = event.getValue('state_#local.key#_licenseDate')>
						<cfset  local.licenseDates[local.key] = local.license_date >
						<cfset  local.license_status  = event.getValue('state_#local.key#_status')>
						<cfoutput>
							<tr>
								<td class="frmText">License State: </td>
								<td class="frmText">#local.licensesNames[local.key]# </td>
							</tr>
							<tr>
								<td class="frmText">License Date: </td>
								<td class="frmText">#local.license_date# </td>
							</tr>
							<tr>
								<td class="frmText">License No: </td>
								<td class="frmText">#local.license_no# </td>
							</tr>
							<tr>
								<td class="frmText">License Status: </td>
								<td class="frmText">#application.objCustomPageUtils.upperFirst(local.license_status)# </td>
							</tr>
						</cfoutput>
					</cfloop>
			</cfif>
			<tr><td colspan="2">&nbsp;</td></tr>
			<tr class="msgHeader"><td colspan="2" class="b">MEMBERSHIP SUMMARY</td></tr>
			<tr><td class="frmText">Membership Type </td><td class="frmText">
				<cfif local.memType EQ "Inactive Practice Attorney">
					Inactive Practice Member
				<cfelse>
					#local.memType#								
				</cfif>
				&nbsp;
			</td></tr>
			<tr><td class="frmText">Membership Amount</td><td class="frmText">
				#dollarFormat(local.memAmt)#&nbsp;
			</td></tr>
			<cfif NOT ArrayIsEmpty(cle_mem_types_arr)>
					<cfloop array="#cle_mem_types_arr#" index="key">
						<cfset types = key.split(':')>
						<tr><td class="frmText">#types[1]# (#types[2]#)</td><td class="frmText">#types[3]#&nbsp;</td></tr>
					</cfloop>
			</cfif>
			<cfif local.lrsAmt neq 0>
				<tr><td class="frmText">Lawyer Referral Service Amount</td><td class="frmText">#dollarFormat(local.lrsAmt)#&nbsp;</td></tr>
			</cfif>
			<cfif local.donationAmt neq 0>
				<tr><td class="frmText">IBF Donation Amount</td><td class="frmText">#dollarFormat(local.donationAmt)#&nbsp;</td></tr>
			</cfif>
			<cfif local.practiceBuilderAmt neq 0>
				<tr><td class="frmText">#variables.strPageFields.PracticeBuilderTitle#</td><td class="frmText">#dollarFormat(local.practiceBuilderAmt)#&nbsp;</td></tr>
			</cfif>
			<cfif local.businessBuilderAmt neq 0>
				<tr><td class="frmText">#variables.strPageFields.BusinessBuilderTitle#</td><td class="frmText">#dollarFormat(local.businessBuilderAmt)#&nbsp;</td></tr>
			</cfif>
			<cfif local.attorneyAmt neq 0>
				<tr><td class="frmText">#variables.strPageFields.AttorneysNetworkTitle#</td><td class="frmText">#dollarFormat(local.attorneyAmt)#&nbsp;</td></tr>
			</cfif>
			<tr><td class="frmText b"><b>Total Amount:</b> &nbsp;</td><td class="r"><b>#dollarFormat(local.totalAmount)#</b>&nbsp;</td></tr>
			<tr><td colspan="2">&nbsp;</td></tr>
			<cfif event.getValue('indys_practiceBuilder','0')>
				<tr><td class="frmText">#Replace(variables.strPageFields.practiceBuilderCheckboxMonthly, '{price}', NumberFormat(event.getValue('indys_practiceBuilder','0'),'00.00') )#</td>
				<td class="frmText">Yes&nbsp;</td></tr>
			<cfelse>
				<tr><td class="frmText">#Replace(variables.strPageFields.practiceBuilderCheckboxMonthly, '{price}', NumberFormat(event.getValue('indys_practiceBuilder','0'),'00.00') )#</td>
				<td class="frmText">No&nbsp;</td></tr>
			</cfif>
			<cfif event.getValue('bar_foundation_info','0')>
				<tr><td class="frmText">#variables.strPageFields.FoundationCheckbox2#  &nbsp;</td>
				<td class="frmText">Yes&nbsp;</td></tr>
			<cfelse>
				<tr><td class="frmText">#variables.strPageFields.FoundationCheckbox2#  &nbsp;</td>
				<td class="frmText">No&nbsp;</td></tr>
			</cfif>			
			<cfif event.getValue('lawyer_referral_service','0')>
				<tr><td class="frmText">Use this address for Lawyer Referral Service: &nbsp;</td>
				<td class="frmText">#event.getValue('lawyer_referral_service_address')#&nbsp;</td></tr>
			</cfif>
			<tr><td colspan="2">&nbsp;</td></tr>
			<tr>
				<td class="frmText">Membership Payment Method:</td><td class="frmText">
					<cfif event.getValue('payMeth','CC') EQ 'CC'>
						Credit Card
						<cfset arguments.event.setValue('p_#variables.profile_1._profileID#_mppid',int(val(arguments.event.getValue('p_#variables.profile_1._profileID#_mppid',0)))) />
						
						<cfif arguments.event.getValue('p_#variables.profile_1._profileID#_mppid') gt 0>
							<cfset local.qrySavedInfoOnFile = application.objPayments.getSavedInfoOnFile(
									mppid     = arguments.event.getValue('p_#variables.profile_1._profileID#_mppid'),
									memberID  = val(event.getValue('memberID',0)),
									profileID = int(variables.profile_1._profileID)) />
							- #local.qrySavedInfoOnFile.detail#
						</cfif>	
					<cfelse>				
						Check
					</cfif>	
				</td>
			</tr>
			</table>

			</cfoutput>
		</cfsavecontent>


		<cfset local.memberTypeDues = StructNew()>	
		<cfset local.memberTypeDues["Attorney 10+ Years"] = "D3CD31B8-10F9-4427-A4F6-5FD4F5E9D2A2" />
		<cfset local.memberTypeDues["Attorney 2-4 Years"] = "EED072D0-78D3-4C96-BA7B-8FAE34583D62" />
		<cfset local.memberTypeDues["Attorney 5-7 Years"] = "FD70B79D-8078-4E13-96D5-798D03928016"/>
		<cfset local.memberTypeDues["Attorney 8-9 Years"] = "7B491FF7-E426-4438-9BB1-E2C9252BAEA2" />
		<cfset local.memberTypeDues["New Attorney"] = "61FE806E-89A5-4D6C-9FF7-823BB70404F0" />

		<cfset local.memberTypeDues["Life Member"] = "71A12674-0473-49BC-8E2C-D3DA082A3473" />
		<cfset local.memberTypeDues["Judiciary Member"] = "E11BA4CE-B11C-4252-B48E-5CB8B0B02A15" />
		<cfset local.memberTypeDues["Law Faculty Member"] ="5984F9D9-224D-4E71-9870-1D48FF5AC418" />
		<cfset local.memberTypeDues["Government Member"] = "3BC7C9B2-C2B0-4566-BC0C-DC7316BC5697" />
		<cfset local.memberTypeDues["Indigent Service Staff Attorney"] = "5721435E-D211-4616-BF83-5023B0B960B5" />
		<cfset local.memberTypeDues["Paralegal Member"] = "49ECA9E1-D1CC-40CE-B5E4-C9305B5117E7" />
		<cfset local.memberTypeDues["Inactive Practice Attorney"] = "7A611EBD-55F2-4104-B4F3-3B289892B8DA" />
		<cfset local.memberTypeDues["Graduate - JD Only"] = "58DEDB19-E97B-43F6-91E8-4E87645C977A" />
		<cfset local.memberTypeDues["Virtual Membership"] = "192E2A65-E0DC-4D09-AF74-C3C0E69F5C36" />
		
		<cfset local.subscriptionDetails = application.objCustomPageUtils.sub_getSubscriptionsDetailsFromSubsetUID(setUID='7FA6870E-CCBC-4F71-9627-A53FA0AF036E',siteID=event.getValue('mc_siteInfo.siteID'),rateUID="3248A928-5DA7-4483-B6E7-8EA2C0E657A7,B5997CCB-B025-4793-B7FA-FE98F8E95334,5AD8E6CC-350E-4959-BBE6-379B07F44A0C,06B031DC-8931-453E-9379-52A4F7DD6EEA")/>

		<cfset local.sectionSubUIDs = StructNew()>
		<cfloop query="local.subscriptionDetails">
			<cfset local.sectionSubUIDs[local.subscriptionDetails.subscriptionname] = local.subscriptionDetails.uid />
		</cfloop>

		<cfset local.memberAmtDues = StructNew()>	
		<cfset local.scheduleUID = "32C4374B-04E4-45B4-A5F4-CCBAF6B43231">
		
		<cfloop query="variables.qryRates">
			<cfswitch expression="#variables.qryRates.rateUID#" >
			<!--- Regular Members / Private Sector Rates --->
			<cfcase value="EED072D0-78D3-4C96-BA7B-8FAE34583D62,2DB358A4-1B2D-43CD-B413-2B34193D2B0D,F520B1EF-5A0B-408F-8DC5-43883B94E5F8">
				<cfset local.memberAmtDues["Attorney 2-4 Years"] = variables.qryRates.rateAmt />
			</cfcase>
			<cfcase value="FD70B79D-8078-4E13-96D5-798D03928016,AAEE3413-1B7C-4812-8CF9-0664A05A1194,1295EE70-6D5D-4AAE-953A-FCC9EA118576">
				<cfset local.memberAmtDues["Attorney 5-7 Years"] = variables.qryRates.rateAmt />
			</cfcase>
			<cfcase value="7B491FF7-E426-4438-9BB1-E2C9252BAEA2,5A434473-39A3-49F9-AEA9-2AFB09125960,61B54A60-47BE-420F-B2EB-937ACDC4A524">
				<cfset local.memberAmtDues["Attorney 8-9 Years"] = variables.qryRates.rateAmt />
			</cfcase>
			<cfcase value="D3CD31B8-10F9-4427-A4F6-5FD4F5E9D2A2,76283B52-C5DD-4B87-8AD6-4984FB4ADEC4,5D94EAEA-06EB-41B6-A1AE-54AA3351691D">
				<cfset local.memberAmtDues["Attorney 10+ Years"] = variables.qryRates.rateAmt />
			</cfcase>
			<cfcase value="61FE806E-89A5-4D6C-9FF7-823BB70404F0,D1463119-E4B1-4F3F-8CF0-89E29900CB0E,49B0F21D-5342-4107-8F0D-FD159971E66D">
				<cfset local.memberAmtDues["New Attorney"] = variables.qryRates.rateAmt />
			</cfcase>		
			<cfcase value="71A12674-0473-49BC-8E2C-D3DA082A3473,7B1F8F2E-2C2E-45C1-BB9D-7958AF2B7D36,3E937C3B-8F6B-4186-8287-AD51B6CB2A05">
				<cfset local.memberAmtDues["Life Member"] = variables.qryRates.rateAmt />
			</cfcase>
			<cfcase value="E11BA4CE-B11C-4252-B48E-5CB8B0B02A15,322C40DB-1F68-4799-B48E-67A32CECDBCE,E29CB080-62DA-4A73-A2E5-288246ACC5BB">
				<cfset local.memberAmtDues["Judiciary Member"] = variables.qryRates.rateAmt />
			</cfcase>
			<cfcase value="5984F9D9-224D-4E71-9870-1D48FF5AC418,24603410-7E07-402A-A872-6AF0F26A4A19,66EF2FAF-971F-4886-93B2-77CBAA07DAE5">
				<cfset local.memberAmtDues["Law Faculty Member"] = variables.qryRates.rateAmt />
			</cfcase>
			<cfcase value="3BC7C9B2-C2B0-4566-BC0C-DC7316BC5697,B1F403E3-1B63-4CBA-8C48-C3DDA729BA42,3E9166C2-F5AD-4CE6-AB1B-0DB3F9789C64">
				<cfset local.memberAmtDues["Government Member"] = variables.qryRates.rateAmt />
			</cfcase>
			<cfcase value="5721435E-D211-4616-BF83-5023B0B960B5,BF9A4A7E-74EE-4E16-A917-2C206026C084,E60FE74F-5A57-45A8-90CF-DDCE9F98B9F7">
				<cfset local.memberAmtDues["Indigent Service Staff Attorney"] = variables.qryRates.rateAmt />
			</cfcase>
			<cfcase value="49ECA9E1-D1CC-40CE-B5E4-C9305B5117E7,48D772F9-C0A1-4A78-AE4E-5F4708601FEC,14B8DF61-BD3D-4B7A-9E21-25F0D5CE3B5E">
				<cfset local.memberAmtDues["Paralegal Member"] = variables.qryRates.rateAmt />
			</cfcase>
			<cfcase value="7A611EBD-55F2-4104-B4F3-3B289892B8DA,8241BE65-FE6C-46B6-A641-CB3DDC380E6F,EBFE607F-81AA-409F-9BAC-19D9B1B9B93B">
				<cfset local.memberAmtDues["Inactive Practice Attorney"] = variables.qryRates.rateAmt />
			</cfcase>
			<cfcase value="58DEDB19-E97B-43F6-91E8-4E87645C977A,E895A1EB-9D53-4BED-80C1-1B82B2431577,B7BCC7BB-670D-4DF8-B6A5-985D7A50F0E8">
				<cfset local.memberAmtDues["Graduate - JD Only"] = variables.qryRates.rateAmt />
			</cfcase>
			<cfcase value="192E2A65-E0DC-4D09-AF74-C3C0E69F5C36,7039AAA6-82DF-4446-9B54-2EF839C7F22A,50BE547D-5459-4DB0-8D4A-0EBC485575FA">
				<cfset local.memberAmtDues["Virtual Membership"] = variables.qryRates.rateAmt />
			</cfcase>
		</cfswitch>
		</cfloop>
		<cfset local.qryAttorneyRates = application.objCustomPageUtils.sub_getRateSchedule(siteID=variables.siteID, scheduleUID="1DB561E2-F5D1-4859-9875-F7F8CAA45B97", rateUID="A11237F0-E22F-4901-9CDE-C9788A81FC42")>

		<cfset local.rateUIDtoUse = "">
		
		<cfset local.qualifySubRateRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SubscriptionRate", functionName="qualify")>
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.membershipRates">
			SET NOCOUNT ON;

		 	declare @FID int, @scheduleID int, @currentDate datetime, @subscriptionID int;
		    set @currentDate = getDate();
		    set @FID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qualifySubRateRFID#">;

			select @scheduleID = scheduleID, @subscriptionID = subscriptionID
			from sub_subscriptions
			where uid = '#variables.indyBarDuesUID#';

			select newRate.rateID, newRate.uid,  newRate.rateName, newRate.termEndDate,
				rs.scheduleName,rf.rateAmt, f.frequencyShortName,rf.rfid
			from sub_subscriptions subs
			inner join dbo.sub_rateSchedules as rs
				on rs.scheduleID = subs.scheduleID
				and rs.status = 'A'
				and subs.subscriptionID = @subscriptionID
			inner join dbo.sub_rates as newRate 
				on newRate.scheduleID = rs.scheduleID 
				and newRate.status = 'A' 
				and newRate.isRenewalRate = 0
				and @currentDate between newRate.rateAFStartDate and dateadd(day, datediff(day, 0, newRate.rateAFEndDate)+1, 0)
			inner join dbo.sub_rateFrequencies rf on rf.rateID = newRate.rateID 
				and rf.status = 'A'
			inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID
				and f.status = 'A'
			INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints srfrp 
				ON srfrp.siteResourceID = newRate.siteResourceID
				AND srfrp.functionID = @FID
			INNER JOIN dbo.cache_perms_groupPrintsRightPrints gprp on srfrp.rightPrintID = gprp.rightPrintID and srfrp.siteID = gprp.siteID
			inner join ams_members m
				on m.groupPrintID = gprp.groupPrintID
				and m.memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getTrimValue('memberID')#"> 
			order by newRate.rateORder;
		</cfquery>	

		<cfif local.membershipRates.recordCount>
			<cfset local.rateUIDtoUse = local.membershipRates.uid>	
		</cfif>	

		<!--- CREATE SUBSCRIPTION ----------------------------------------------------------------------------- --->
		<cfset local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg")>
		<cfset local.subStruct = structNew()>
		<cfset local.subStruct.children = arrayNew(1)>

		<cfset local.subStruct.uid = variables.indyBarDuesUID>
		<cfset local.subStruct.rateUID = local.rateUIDtoUse>

		<cfif event.getValue('indys_attorney','0') NEQ 0>
			<cfset local.childStruct = structNew()>
			<cfset local.childStruct.uid = variables.indyAttorneysNetworkUID />
			<cfset ArrayAppend(local.subStruct.children, local.childStruct)>
		</cfif>	

		<cfif event.getValue('lawyer_referral_service','0') NEQ 0>
			<cfset local.childStruct = structNew()>
			<cfset local.childStruct.uid = variables.LRSMembershipUID />
			<cfset ArrayAppend(local.subStruct.children, local.childStruct)>
		</cfif>				

		<cfif event.getValue('foundation_donation','0') NEQ 0>
			<cfset local.childStruct = structNew()>
			<cfset local.childStruct.uid = variables.donationWithDuesUID />
			<cfset local.childStruct.rateUID = variables.donationRateUID />
			<cfset local.childStruct.rateOverride = event.getValue('donation_amount',0) />
			<cfset ArrayAppend(local.subStruct.children, local.childStruct)>
		</cfif>

		<cfif NOT ArrayIsEmpty(cle_mem_types_arr)>
			<cfloop array="#cle_mem_types_arr#" index="key">
				<cfset types = key.split(':')>
				<cfset local.childStruct = structNew()>
				<cfset local.childStruct.uid = local.sectionSubUIDs[types[1]]>
				<cfset local.qryRate = application.objCustomPageUtils.sub_getEligibleRates(siteID=event.getValue('mc_siteInfo.siteID'), memberID=arguments.event.getTrimValue('memberID'), subscriptionUID=local.childStruct.uid, isRenewal=0) />	
				<cfloop query="local.qryRate">
					<cfif findNoCase(types[2],local.qryRate.rateName)>
						<cfset local.childStruct.rateUID = local.qryRate.rateUID/>
						<cfbreak>
					</cfif>					
				</cfloop>
				<cfset ArrayAppend(local.subStruct.children, local.childStruct)>
			</cfloop>
		</cfif>

		<cfset local.subscriberIDlist = "">
		<cfset local.subReturn = local.objSubReg.autoSubscribe(event=event, memberID=arguments.event.getTrimValue('memberID'), subStruct=local.subStruct, newAsBilled=false)>
		
		<cfif local.subReturn.success>
			<cfset local.subscriberIDlist = local.subReturn.rootSubscriberID>
		</cfif>	

		<!--- If Indy Lawyer finder is selected --->
		<cfif event.getValue('indy_lawyer_finder','0') NEQ 0>
			<cfset local.subStruct2 = structNew()>
			<cfset local.subStruct2.children = arrayNew(1)>

			<cfset local.subStruct2.uid = variables.indyLawyerFinderUID>
			<cfset local.subStruct2.rateUID = variables.indyLawyerFinderRateUID>

			<cfset local.subReturn2 = local.objSubReg.autoSubscribe(event=event, memberID=arguments.event.getTrimValue('memberID'), subStruct=local.subStruct2, newAsBilled=false)>
			<cfif local.subReturn2.success>
				<cfset local.subscriberIDlist = listAppend(local.subscriberIDlist,local.subReturn2.rootSubscriberID)>
			</cfif>				
		</cfif>

		<cfif event.getValue('indys_practiceBuilder','0') NEQ 0 OR event.getValue('indys_businessBuilder','0') NEQ 0>		

			<cfset local.subStruct3 = structNew()>
			<cfset local.subStruct3.children = arrayNew(1)>
			<cfset local.indys_BuilderFrequency = 'F'>
			<cfif event.getValue('indys_practiceBuilder_Frequency','F') eq 'M' OR event.getValue('indys_businessBuilder_Frequency','F') eq 'M'>
				<cfset local.indys_BuilderFrequency = 'M'>
			</cfif>
			<cfset local.qryRateInfo = application.objCustomPageUtils.sub_GetEligibleRates(siteid=variables.siteID, memberID=variables.useMID, subscriptionUID=variables.alaCarteUID,frequencyShortName=local.indys_BuilderFrequency,isRenewal=false)>
			
			<cfset local.subStruct3.uid = variables.alaCarteUID>
			<cfset local.subStruct3.rateUID = local.qryRateInfo.rateUID/>
			<cfset local.subStruct3.freqUID = Ucase(local.qryRateInfo.frequencyUID)>
			<cfif event.getValue('indys_practiceBuilder','0') NEQ 0>
				<cfset local.childStruct = structNew()>
				<cfset local.qryBusinessMembershipRates = getMembershipRates(subscriptionUID=variables.IndyPracticeBuilderUID,rateUID=event.getValue('indys_practiceBuilderRFID','0'),memberID=variables.useMID)>
				<cfset local.rateOverrideAmt = application.objCustomPageUtils.sub_getSRateInfo(rateUID=variables.IndyPracticeBuilderRateUID, frequencyShortName="F", siteId=variables.siteId).rateAmt>
				<cfset local.childStruct.uid = variables.IndyPracticeBuilderUID />				
				<cfset local.childStruct.rateUID = Ucase(local.qryBusinessMembershipRates.uid)>
				<cfset local.childStruct.freqUID = Ucase(local.qryBusinessMembershipRates.freqUID)>
				<cfif local.indys_BuilderFrequency eq "M">
					<cfset local.childStruct.rateOverride = 239.88>
				<cfelse>
					<cfset local.childStruct.rateOverride = local.rateOverrideAmt>
				</cfif>
				<cfset ArrayAppend(local.subStruct3.children, local.childStruct)>		
			</cfif>
		
			<cfif event.getValue('indys_businessBuilder','0') NEQ 0>
				<cfset local.childStruct = structNew()>
				<cfset local.childStruct.children = arrayNew(1)>
				<cfset local.qryBusinessMembershipRates = getMembershipRates(subscriptionUID=variables.IndyBusinessBuilderUID,rateUID=event.getValue('indys_businessBuilderRFID','0'),memberID=variables.useMID)>
				<cfset local.rateOverrideAmt = application.objCustomPageUtils.sub_getSRateInfo(rateUID=variables.IndyBusinessBuilderRateUID, frequencyShortName="F", siteId=variables.siteId).rateAmt>		
				<cfset local.childStruct.uid = variables.IndyBusinessBuilderUID />				
				<cfset local.childStruct.rateUID = Ucase(local.qryBusinessMembershipRates.uid)>
				<cfset local.childStruct.freqUID = Ucase(local.qryBusinessMembershipRates.freqUID)>
				<cfset local.childStruct.rateOverride = local.rateOverrideAmt>

				<!---Addon--->
				<cfset variables.lawyerFinderUID = 'F64AFF6F-D9B1-4C01-8C1D-17A016F0DA67'>
				<cfset variables.LRSMembershipUID = 'C0760504-EE68-4373-A4A2-701060A23DE0'>

				<cfset local.qryRateDetailsForlawyerFinder = application.objCustomPageUtils.sub_GetEligibleRates(siteid=variables.siteID, memberID=variables.useMID, subscriptionUID=variables.lawyerFinderUID, frequencyShortName=local.indys_BuilderFrequency, isRenewal=false)>		
				<cfset local.qryRateDetailsForLRSMembership = application.objCustomPageUtils.sub_GetEligibleRates(siteid=variables.siteID, memberID=variables.useMID, subscriptionUID=variables.LRSMembershipUID, frequencyShortName=local.indys_BuilderFrequency, isRenewal=false)>
 
				<cfif local.qryRateDetailsForLawyerFinder.recordCount neq 0>
					<cfset local.childStruct4 = structNew()>
					<cfset local.childStruct4.uid = Ucase(variables.lawyerFinderUID)  />
					<cfset local.childStruct4.rateUID = Ucase(local.qryRateDetailsForlawyerFinder.rateUID) />		
					<cfset ArrayAppend(local.childStruct.children, local.childStruct4)>
				</cfif>
				<cfif local.qryRateDetailsForLRSMembership.recordCount neq 0>
					<cfset local.childStruct4 = structNew()>
					<cfset local.childStruct4.uid = Ucase(variables.LRSMembershipUID)  />
					<cfset local.childStruct4.rateUID = Ucase(local.qryRateDetailsForLRSMembership.rateUID) />		
					<cfset ArrayAppend(local.childStruct.children, local.childStruct4)>
				</cfif>
			</cfif>	 

			<cfset ArrayAppend(local.subStruct3.children, local.childStruct)>

			<cfset local.subReturn3 = local.objSubReg.autoSubscribe(event=event, memberID=arguments.event.getTrimValue('memberID'), subStruct=local.subStruct3, newAsBilled=false)>
			
			<cfif local.subReturn3.success>
				<cfset local.subscriberIDlist = listAppend(local.subscriberIDlist,local.subReturn3.rootSubscriberID)>
			</cfif>
		</cfif>

		<cfscript>
			local.arrEmailTo = [];
			variables.ORGEmail.to = replace(variables.ORGEmail.to,",",";","all");
			local.toEmailArr = listToArray(variables.ORGEmail.to,';');
			for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
				local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
			}
		</cfscript>

		<cfset local.showErrorMsg = false>
		<cfif local.subReturn.success eq false 
			OR (structKeyExists(local, "subReturn2") AND local.subReturn2.success eq false)
			OR (structKeyExists(local, "subReturn3") AND local.subReturn3.success eq false)>
			<!--- email association ----------------------------------------------------------------------------------------- --->

			<cfsavecontent variable="local.mailContent">
				<cfoutput>
					#local.pageCSS#
					The system was unable to create the subscriptions for this application and #event.getValue('fname','')# #event.getValue('lname','')# was not sent an email confirmation.<br />
					<hr />
					#replace(replace(local.invoice, "*", "", "all"), "highlightRow", "", "all")#
				</cfoutput>
			</cfsavecontent>

			<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
				emailfrom={ name="", email=variables.ORGEmail.from},
				emailto=local.arrEmailTo,
				emailreplyto=variables.ORGEmail.from,
				emailsubject=variables.ORGEmail.SUBJECT,
				emailtitle=arguments.event.getTrimValue('mc_siteinfo.sitename') & " - " & variables.formNameDisplay,
				emailhtmlcontent=local.mailContent,
				siteID=arguments.event.getValue('mc_siteinfo.siteID'),
				memberID=arguments.event.getValue('mc_siteinfo.sysMemberID'),
				messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
				sendingSiteResourceID=this.siteResourceID
			)>
		
			<cfset local.showErrorMsg = true>
			<cfset local.errCode = 3>

			<!--- We may want an exception to see why the subscription was not created --->
		<cfelse>

			<!--- come back with invoices created, need to pay first one --->
			<!--- find the first invoice for the subscription and pay it --->
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryInvoice" result="local.qryInvoiceResult">
				set nocount on;

				declare @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('mc_siteInfo.orgID')#">;

				select s.rootSubscriberID, nv.invoiceID, nv.invoiceProfileID, sum(it.cache_invoiceAmountAfterAdjustment) as totalAmount, dueNow= case when nv.dateDue < getdate() then 1 else 0 end
				from dbo.sub_subscribers s
				inner join dbo.sub_subscriptions subs
					on subs.subscriptionID = s.subscriptionID
					and s.rootSubscriberID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.subscriberIDlist#" list="true">)
				inner join dbo.sub_types t
					on t.typeID = subs.typeID
					and t.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('mc_siteInfo.siteID')#">
				inner join dbo.tr_applications ta on ta.orgID = @orgID and ta.itemID = s.subscriberID
					and ta.applicationTypeID = 17
					and ta.itemType = 'Dues'
				inner join dbo.tr_invoiceTransactions it on it.orgID = @orgID and it.transactionID = ta.transactionID
				inner join dbo.tr_invoices nv on nv.orgID = @orgID and nv.invoiceID = it.invoiceID
				group by s.rootSubscriberID, nv.invoiceID, nv.invoiceProfileID, nv.dateDue
				order by nv.dateDue
			</cfquery>

			<cfquery dbtype="query" name="local.qryInvoiceDueNow">
				select invoiceID, invoiceProfileID, totalAmount as amount
				from [local].qryInvoice
				where dueNow=1
			</cfquery>

			<cfset local.totalAmount = arraySum(listToArray(valueList(local.qryInvoice.totalAmount)))>
			<cfset local.totalAmountDueNow = arraySum(listToArray(valueList(local.qryInvoiceDueNow.amount)))>	

			<cfif arguments.event.getValue('p_#variables.profile_1._profileID#_mppid',0)>
				<cfset local.memberPayProfileID = arguments.event.getValue('p_#variables.profile_1._profileID#_mppid',0)>
			<cfelse>
				<cfset local.memberPayProfileID = 0 >
			</cfif>			

			<cfset local.arrInvoicePaths = arrayNew(1)>	

			<cfif local.totalAmountDueNow gt 0 and event.getValue('payMeth','CC') eq "CC">
				<!--- -------------------------------------------------- --->
				<!--- Save card on file to subscription and all invoices --->
				<!--- -------------------------------------------------- --->
				<cfif local.totalAmount gt 0 and local.memberPayProfileID>
					<cfloop query="local.qryInvoice">
						<cfset application.objCustomPageUtils.acct_associateCardOnFile(invoiceIDList=valueList(local.qryInvoice.invoiceID), MPProfileID=variables.profile_1._profileID, mppID=local.memberPayProfileID)>
						<cfset application.objCustomPageUtils.sub_associateCardOnFile(subscriberID=local.qryInvoice.rootSubscriberID, MPProfileID=variables.profile_1._profileID, mppID=local.memberPayProfileID)>											
					</cfloop>
				</cfif>		

				<!--- ---------------------- --->
				<!--- Payment and accounting --->
				<!--- ---------------------- --->
				<cfset local.strAccTemp = { totalPaymentAmount=local.totalAmountDueNow, 
											assignedTomemberID=arguments.event.getTrimValue('memberID'), 
											recordedBymemberID=arguments.event.getTrimValue('memberID'), 
											rc=arguments.event.getCollection() } >
				<cfif local.strAccTemp.totalPaymentAmount gt 0 and event.getValue('payMeth','CC') eq "CC" >
					<cfset local.strAccTemp.payment = { detail=variables.formNameDisplay, 
														amount=local.strAccTemp.totalPaymentAmount, 
														profileID=variables.profile_1._profileID, 
														profileCode=variables.profile_1._profileCode }>
					<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>

					<cfset local.objAccounting.invoicePool = local.qryInvoiceDueNow >
					<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>
					<cfif val(local.strACCResponse.paymentResponse.transactionID)>
						<cfset application.objCustomPageUtils.sub_checkActivationsByMember(orgID=arguments.event.getValue('mc_siteInfo.orgID'), memberID=arguments.event.getTrimValue('memberID'), subscriberID=0, bypassQueue=0)>
					</cfif>
				<cfelse>
					<cfset local.strACCResponse.accResponseMessage = "<p><b>Check Payment selected.</b></p>">
				</cfif>
				<cfset local.returnstruct.strACCResponse = local.strACCResponse>
			<cfelseif local.totalAmount gt 0 and event.getValue('payMeth','CC') eq "Check">
				<cfset local.strACCResponse.accResponseMessage = "<p><b>Check Payment selected.</b></p>">					
			</cfif>	

			<!--- Checks And CCs should generate and invoice --->
			<cfset local.objInvoice = CreateObject("component","model.admin.transactions.invoice")>
			<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
			<cfset local.arrInvoice = arrayNew(1)>
			<cfloop query="local.qryInvoiceDueNow">
				<cfset local.strInvoiceForEmail= structNew()>
				<cfset local.strInvoice = local.objInvoice.generateInvoice(siteID=variables.siteID, invoiceID=local.qryInvoiceDueNow.invoiceID, tmpFolder=local.strFolder.folderPath, encryptFile=true, namedForBundle=false)>
				<cfset local.strInvoiceForEmail.displayName = local.strInvoice.displayName>
				<cfset local.strInvoiceForEmail.invoicePath = local.strInvoice.invoicePath>
				<cfset arrayAppend(local.arrInvoice,local.strInvoiceForEmail)>
			</cfloop>				

			<!--- email submitter (no error shown to user) --->			
			<cfset local.mailAttach = arrayNew(1)>
			<cfset local.i = 1>
			<cfloop array="#local.arrInvoice#" index="local.thisInvoice">
				<cfif FileExists("#local.thisInvoice.invoicePath#")>
					<cfset local.mailAttach[local.i] = structNew()>
					<cfset local.mailAttach[local.i]["file"] = local.thisInvoice.displayName>
					<cfset local.mailAttach[local.i]["folderpath"] = local.strFolder.folderPath>
					<cfset local.i = local.i + 1>
				</cfif>
			</cfloop>	

			<cfsavecontent variable="local.mailContent">
				<cfoutput>
					#local.pageCSS#
					<p>Thank you! Please print this page - it is your confirmation.</p><hr/>
					#replace(replace(local.invoice, "*", "", "all"), "highlightRow", "", "all")#
				</cfoutput>
			</cfsavecontent>

			<cfscript>
				local.responseStruct = application.objEmailWrapper.sendMailESQ(
					emailfrom={ name="", email=variables.memberEmail.from },
					emailto=[{ name="", email=variables.memberEmail.to }],
					emailreplyto= variables.ORGEmail.to,
					emailsubject= variables.memberEmail.SUBJECT,
					emailtitle=arguments.event.getTrimValue('mc_siteinfo.sitename') & " - " & variables.formNameDisplay,
					emailhtmlcontent=local.mailContent,
					emailAttachments=local.mailAttach,
					siteID=arguments.event.getTrimValue('mc_siteinfo.siteID'),
					memberID=arguments.event.getTrimValue('memberID'),
					messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
					sendingSiteResourceID=this.siteResourceID
				);
				local.emailSentToUser = local.responseStruct.success;
			</cfscript>

			<!--- email staff (no error shown to user) --->
			<cfset local.invoice = replaceNoCase(local.invoice,'<!-- @accResponseMessage@ -->',local.strACCResponse.accResponseMessage)>

			<cfsavecontent variable="local.mailContent">
				<cfoutput>
					<cfif NOT local.emailSentToUser>
						<p><b>The member was NOT sent an e-mail confirmation of this submission.</b></p>
					</cfif>
					<cfif NOT local.recordUpdated>
						<p><b>The member's record was NOT updated in Control Panel with any changes made on this application.</b></p>
					</cfif>
					#local.pageCSS#						
					#replace(replace(local.invoice, "*", "", "all"), "highlightRow", "", "all")#
					<br>
					<!--- <i class="highlightRow">The information marked with "*" denotes the user attempted to update that information.</i> --->
				</cfoutput>
			</cfsavecontent>

			<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
				emailfrom={ name="", email=variables.ORGEmail.from},
				emailto=local.arrEmailTo,
				emailreplyto=variables.ORGEmail.from,
				emailsubject=variables.ORGEmail.SUBJECT,
				emailtitle=arguments.event.getTrimValue('mc_siteinfo.sitename') & " - " & variables.formNameDisplay,
				emailhtmlcontent=local.mailContent,
				siteID=arguments.event.getValue('mc_siteinfo.siteID'),
				memberID=arguments.event.getValue('mc_siteinfo.sysMemberID'),
				messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
				sendingSiteResourceID=this.siteResourceID
			)>

			<!--- relocate to message page --->
			<cfset session.invoice = replaceNoCase(replaceNoCase(replaceNoCase(replace(replace(local.invoice, "*", "", "all"), "highlightRow", "", "all"),"html>","div>","ALL"),"body>","div>","ALL"),"head>","div>","ALL")>

		</cfif>
		<cfif local.showErrorMsg>
			<cfoutput>
				There was an error processing your application.  Please contact your association for assistance.
			</cfoutput>
		</cfif>
		
		<cflocation url="#variables.baselink#&fa=complete" addtoken="false">
	</cffunction>

	<cffunction name="getSubscriptionRates" hint="I fetch subscription rates with rate schedule UID" returntype="query">
		<cfargument name="subSubscriptionUID" required="true" type="string">
		<cfargument name="rateScheduleUID" required="true" type="string">
		<cfargument name="rateUID" required="true" type="string">
		<cfargument name="memberID" required="true" type="numeric">

		<cfset var local = structNew()>
		<cfset local.qualifySubRateRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SubscriptionRate", functionName="qualify")>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qGetSubscriptionRates">
			SET NOCOUNT ON;

			declare @FID int, @scheduleID int, @currentDate datetime, @subscriptionID int;
			set @currentDate = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#now()#">;
			set @FID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qualifySubRateRFID#">;

			select @scheduleID = scheduleID, @subscriptionID = subscriptionID
			from sub_subscriptions
			where uid = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.subSubscriptionUID#">;

			select newRate.rateID, newRate.uid, newRate.rateName, newRate.termEndDate,
				rs.scheduleName, rf.rateAmt, f.frequencyShortName, rf.rfid, f.frequencyName
			from dbo.sub_subscriptions subs
			inner join dbo.sub_rateSchedules as rs on rs.scheduleID = subs.scheduleID
				and rs.status = 'A'
				and subs.subscriptionID = @subscriptionID
			inner join dbo.sub_rates as newRate on newRate.scheduleID = rs.scheduleID 
				and newRate.status = 'A' 
				and newRate.isRenewalRate = 0
				and @currentDate between newRate.rateAFStartDate and dateadd(day, datediff(day, 0, newRate.rateAFEndDate)+1, 0)
			inner join dbo.sub_rateFrequencies rf on rf.rateID = newRate.rateID 
				and rf.status = 'A'
			inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID
				and f.status = 'A' 
				and f.frequencyShortName = 'F'
			INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints srfrp ON srfrp.siteResourceID = newRate.siteResourceID
				AND srfrp.functionID = @FID
			INNER JOIN dbo.cache_perms_groupPrintsRightPrints gprp on srfrp.rightPrintID = gprp.rightPrintID 
				and srfrp.siteID = gprp.siteID
			inner join dbo.ams_members m on m.groupPrintID = gprp.groupPrintID
				and newRate.uid = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.rateUID#">
				and rs.UID = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.rateScheduleUID#">
			order by newRate.rateName, newRate.rateORder;
		</cfquery>

		<cfreturn local.qGetSubscriptionRates>
	</cffunction>

	<cffunction name="getMembershipRates" output="false" returntype="query">
		<cfargument name="subscriptionUID" required="true">
		<cfargument name="rateUID" required="true">
		<cfargument name="memberID" required="true">
		
		<cfset var local = structNew()>
		<cfset local.qualifySubRateRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SubscriptionRate", functionName="qualify")>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.membershipRates">
			SET NOCOUNT ON;

			declare @FID int, @scheduleID int, @currentDate datetime, @subscriptionID int;
			set @currentDate = getDate();
			set @FID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qualifySubRateRFID#">;

			select @scheduleID = scheduleID, @subscriptionID = subscriptionID
			from sub_subscriptions
			where uid = '#arguments.subscriptionUID#';

			select newRate.rateID, newRate.uid,  newRate.rateName, newRate.termEndDate,
				rs.scheduleName,rf.rateAmt, f.frequencyShortName,f.frequencyName,rf.rfid,f.uid as freqUID
			from sub_subscriptions subs
			inner join dbo.sub_rateSchedules as rs
				on rs.scheduleID = subs.scheduleID
				and rs.status = 'A'
				and subs.subscriptionID = @subscriptionID
			inner join dbo.sub_rates as newRate 
				on newRate.scheduleID = rs.scheduleID 
				and newRate.status = 'A' 
				and newRate.isRenewalRate = 0
				and @currentDate between newRate.rateAFStartDate and dateadd(day, datediff(day, 0, newRate.rateAFEndDate)+1, 0)
			inner join dbo.sub_rateFrequencies rf on rf.rateID = newRate.rateID 
				and rf.status = 'A'
			inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID
				and f.status = 'A'
			INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints srfrp 
				ON srfrp.siteResourceID = newRate.siteResourceID
				AND srfrp.functionID = @FID
			INNER JOIN dbo.cache_perms_groupPrintsRightPrints gprp on srfrp.rightPrintID = gprp.rightPrintID and srfrp.siteID = gprp.siteID
			inner join ams_members m
				on m.groupPrintID = gprp.groupPrintID
				and m.memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
			WHERE rf.rfid in ( <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rateUID#" list="true">)
			order by newRate.rateORder;
		</cfquery>	

		<cfreturn local.membershipRates>
	</cffunction>

</cfcomponent>
