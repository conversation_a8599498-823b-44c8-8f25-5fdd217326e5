<cfoutput>
<form name="frm_convertSWLToSWOD" id="frm_convertSWLToSWOD" method="post" action="#local.formLink#" class="p-3" onsubmit="return showLoadingBtn();">
	<p class="mb-3"><strong>#local.seminarName#</strong></p>
	
	<div class="alert pl-2 align-content-center alert-info mb-3" role="alert">
		<span class="font-size-lg d-40 mr-2 text-center"><i class="fa-regular fa-exclamation-circle"></i></span>
		<span>
			<strong>Confirmation needed</strong><br/>
			Are you sure you want to convert this webinar to OnDemand?
		</span>
	</div>
	
	<div class="custom-control custom-switch my-2">
		<input type="checkbox" name="incWebinarSettings" id="incWebinarSettings" value="1" class="custom-control-input" checked="checked">
		<label for="incWebinarSettings" class="custom-control-label">Copy over all Webinar settings to OnDemand</label>
		<small class="form-text text-muted">Settings include custom fields, pre-test, post-test, evaluation, and certificate.</small>
	</div>
	
	<cfif local.hasCreditApprovals>
		<div class="custom-control custom-switch my-2">
			<input type="checkbox" name="incCreditApprovals" id="incCreditApprovals" value="1" class="custom-control-input">
			<label for="incCreditApprovals" class="custom-control-label">Copy over credit approval numbers to OnDemand</label>
		</div>
	</cfif>
	
	<!--- hidden submit triggered from parent --->
	<button type="submit" class="d-none"></button>
</form>
</cfoutput>

<script type="text/javascript">
	function showLoadingBtn() {
		top.$('#btnMCModalSave').prop('disabled',true).html('<i class="fa-light fa-circle-notch fa-spin"></i> Converting...');
		return true;
	}
</script>
