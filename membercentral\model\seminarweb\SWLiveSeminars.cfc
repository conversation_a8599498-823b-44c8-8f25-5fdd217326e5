<cfcomponent>

	<cffunction name="getSeminars" access="public" returntype="query" output="no">
		<cfargument name="start" type="date" required="yes">
		<cfargument name="end" type="date" required="yes">

		<cfset var local = StructNew()>
		
		<cfstoredproc procedure="swl_getSeminars" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.start#">
			<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.end# 23:59:59">
			<cfprocresult name="local.qrySeminars" resultset="1">
		</cfstoredproc>
		
		<cfreturn local.qrySeminars>
	</cffunction>

	<cffunction name="getCreditAuthoritiesForCatalog" access="public" returntype="query" output="no">
		<cfargument name="catalogorgCode" type="string" required="yes">

		<cfset var local = StructNew()>
		
		<cfstoredproc procedure="swl_getCreditAuthoritiesForCatalog" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.catalogorgCode#" null="No">
			<cfprocresult name="local.qryAuthorities" resultset="1">
		</cfstoredproc>
		
		<cfreturn local.qryAuthorities>
	</cffunction>

	<cffunction name="getCreditsAvailableForCatalogByAuthority" access="public" returntype="query" output="no">
		<cfargument name="catalogorgCode" type="string" required="true">
		<cfargument name="authorityIDList" type="string" required="true">

		<cfset var qryCreditsAvailable = "">

		<cfstoredproc procedure="swl_getCreditsAvailableForCatalogByAuthority" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.catalogorgCode#">
			<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.authorityIDList#">
			<cfprocresult name="qryCreditsAvailable" resultset="1">
		</cfstoredproc>
		
		<cfreturn qryCreditsAvailable>
	</cffunction>

	<cffunction name="addParsedTimeZoneToSeminars" access="public" returntype="query" output="no">
		<cfargument name="qrySeminars" type="query" required="yes">
		<cfargument name="catalogOrgCode" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.qrySeminars = arguments.qrySeminars>
		
		<!--- Add timezone specific display to query --->
		<cfset QueryAddColumn(local.qrySeminars,"showUSD","bit",ArrayNew(1))>
		<cfset QueryAddColumn(local.qrySeminars,"dspStartDate","date",ArrayNew(1))>
		<cfset QueryAddColumn(local.qrySeminars,"dspEndDate","date",ArrayNew(1))>
		<cfset QueryAddColumn(local.qrySeminars,"dspTZ","varchar",ArrayNew(1))>
		<cfset QueryAddColumn(local.qrySeminars,"dspTZAbbr","varchar",ArrayNew(1))>
		<cfset QueryAddColumn(local.qrySeminars,"dspTZStr","varchar",ArrayNew(1))>

		<cfif local.qrySeminars.recordcount gt 0>
			<cfset local.qryAssociation = CreateObject("component","SWParticipants").getAssociationDetails(arguments.catalogOrgCode).qryAssociation>
			<cfset local.qryTZ = CreateObject("component","model.system.platform.tsTimeZone").getTimeZones()>
			<cfloop query="local.qrySeminars">
				<cfif NOT isDefined("local.qrySeminars.format") or (isDefined("local.qrySeminars.format") and local.qrySeminars.format neq "SWB")>
					<cfset local.parsedTime = parseTimesFromWDDX(local.qrySeminars.wddxTimeZones,local.qryAssociation.wddxTimeZones,local.qrySeminars.dateStart,local.qrySeminars.dateEnd)>
					
					<cfquery name="local.qryTZ_selected" dbtype="query">
						SELECT timeZoneAbbr FROM [local].qryTZ WHERE timeZone = '#local.parsedTime.TimeZone#'
					</cfquery>

					<cfset QuerySetCell(local.qrySeminars,"dspStartDate",local.parsedTime.StartDate,local.qrySeminars.currentrow)>
					<cfset QuerySetCell(local.qrySeminars,"dspEndDate",local.parsedTime.EndDate,local.qrySeminars.currentrow)>
					<cfset QuerySetCell(local.qrySeminars,"dspTZ",local.parsedTime.TimeZone,local.qrySeminars.currentrow)>
					<cfset QuerySetCell(local.qrySeminars,"dspTZAbbr",local.qryTZ_selected.timeZoneAbbr,local.qrySeminars.currentrow)>
					<cfset QuerySetCell(local.qrySeminars,"dspTZStr",local.parsedTime.TimeZoneCompare,local.qrySeminars.currentrow)>
					<cfset QuerySetCell(local.qrySeminars,"showUSD",local.qryAssociation.showUSD,local.qrySeminars.currentrow)>
				</cfif>
			</cfloop>
		</cfif>

		<cfreturn local.qrySeminars>
	</cffunction>	
	
	<cffunction name="parseTimesFromWDDX" access="public" returntype="struct" output="no">
		<cfargument name="seminarWDDXTimeZones" type="any" required="yes">
		<cfargument name="orgWDDXTimeZones" type="any" required="yes">
		<cfargument name="ifErrStartTime" type="date" required="yes">
		<cfargument name="ifErrEndTime" type="date" required="yes">

		<cfset var local = StructNew()>
		<cfset local.returnStruct = Structnew()>
		
		<cftry>
			<!--- get timezones from wddx --->
			<cfwddx action="wddx2cfml" input="#arguments.seminarWDDXTimeZones#" output="local.strSeminarZones">
			<cfwddx action="wddx2cfml" input="#arguments.orgWDDXTimeZones#" output="local.strOrgZones">
			
			<!--- master time zones --->
			<cfset local.strMasterZones = CreateObject("component","SWCommon").getMasterTimeZones()>

			<!--- construct struct --->
			<cfset local.returnStruct.StartDate = local.strSeminarZones.start[local.strOrgZones.default]>
			<cfset local.returnStruct.EndDate = local.strSeminarZones.end[local.strOrgZones.default]>
			<cfset local.returnStruct.TimeZone = local.strMasterZones[local.strOrgZones.default].long>
			<cfif ArrayLen(local.strOrgZones.supporting) and (ArrayLen(local.strOrgZones.supporting) neq 1 or local.strOrgZones.supporting[1] neq local.strOrgZones.default)>
				<cfsavecontent variable="local.returnStruct.TimeZoneCompare">
					(
					<cfloop from="1" to="#ArrayLen(local.strOrgZones.supporting)#" index="local.thisEl">
						<cfoutput>#TimeFormat(local.strSeminarZones.start[local.strOrgZones.supporting[local.thisEl]],"h:mm")# #local.strMasterZones[local.strOrgZones.supporting[local.thisEl]].short#<cfif local.thisEl lt arraylen(local.strOrgZones.supporting)> / </cfif></cfoutput>
					</cfloop>
					)
				</cfsavecontent>
				<cfset local.returnStruct.TimeZoneCompare = replace(replace(replace(local.returnStruct.TimeZoneCompare,chr(9),"","ALL"),chr(13),"","ALL"),chr(10),"","ALL")>
			<cfelse>
				<cfset local.returnStruct.TimeZoneCompare = "">
			</cfif>

		<cfcatch type="Any">
			<cf_exception cfcatch="#cfcatch#" objectToDump="#local#">
			<cfset local.returnStruct.StartDate = arguments.ifErrStartTime>
			<cfset local.returnStruct.EndDate = arguments.ifErrEndTime>
			<cfset local.returnStruct.TimeZone = "Central">
			<cfset local.returnStruct.TimeZoneCompare = "">
		</cfcatch>
		</cftry>	
			
		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="getSeminarForCatalog" access="public" returntype="struct" output="no">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="catalogOrgCode" type="string" required="yes">
		<cfargument name="billingState" type="string" required="yes">
		<cfargument name="billingZip" type="string" required="yes">
		<cfargument name="depoMemberDataID" type="numeric" required="yes">
		<cfargument name="memberID" type="numeric" required="yes">

		<cfset var local = structnew()>

		<cfstoredproc procedure="swl_getSeminarForCatalog" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#" null="No">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.catalogOrgCode#" null="No">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.billingState#" null="No">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.billingZip#" null="No">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.depoMemberDataID#" null="No">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#" null="No">
			<cfprocresult name="local.qrySeminar" resultset="1">
			<cfprocresult name="local.qrySeminarPrices" resultset="2">
			<cfprocresult name="local.qrySeminarPricesBuyNow" resultset="3">
			<cfprocresult name="local.qryLearningObjectives" resultset="4">
			<cfprocresult name="local.qrySponsors" resultset="5">
			<cfprocresult name="local.qryLinkedCategories" resultset="6">
			<cfprocresult name="local.qryOptInParticipants" resultset="7">
		</cfstoredproc>
		
		<!--- Add timezone specific display to query --->
		<cfset local.qrySeminar = addParsedTimeZoneToSeminars(local.qrySeminar,arguments.catalogOrgCode)>

		<cfreturn local>
	</cffunction>
	
	<cffunction name="getSeminarBySeminarID" access="public" returntype="query" output="no">
		<cfargument name="seminarID" type="numeric" required="yes">

		<cfset var local = StructNew()>
		
		<cfstoredproc procedure="swl_getSeminarBySeminarID" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.SeminarID#">
			<cfprocresult name="local.qrySeminar" resultset="1">
		</cfstoredproc>
		
		<cfreturn local.qrySeminar>
	</cffunction>

	<cffunction name="getEnrollmentByEnrollmentID" access="public" returntype="query" output="no">
		<cfargument name="enrollmentID" type="numeric" required="yes">

		<cfset var qryEnrollment = "">
		
		<cfstoredproc procedure="swl_getEnrollmentByEnrollmentID" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.enrollmentID#">
			<cfprocresult name="qryEnrollment" resultset="1">
		</cfstoredproc>
		
		<cfreturn qryEnrollment>
	</cffunction>

	<cffunction name="registerUser" access="public" returntype="numeric" output="no">
		<cfargument name="depomemberdataID" type="numeric" required="yes">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="transactionID" type="numeric" required="yes">
		<cfargument name="signUpOrgCode" type="string" required="yes">
		<cfargument name="strCreditData" type="struct" required="yes">
		<cfargument name="performedBy" type="numeric" required="yes">
		<cfargument name="sendEmailConnectionInstructions" type="boolean" required="yes">
		<cfargument name="emailOverride" type="string" required="no" default="">
		<cfargument name="MCMemberID" type="numeric" required="no">

		<cfset var local = structnew()>

		<cfset local.qrySeminar = getSeminarBySeminarID(seminarID=arguments.seminarID)>

		<cfstoredproc procedure="swl_addEnrollment" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.depomemberdataID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.signUpOrgCode#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
			<cfif arguments.transactionID gt 0>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.transactionID#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
			</cfif>
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.MCMemberID#">
			<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.enrollmentID">
		</cfstoredproc>

		<cfif local.qrySeminar.offerCredit EQ 1 AND structCount(arguments.strCreditData)>
			<cfset local.objCredit = CreateObject("component","SWCredits")>
			<cfloop collection="#arguments.strCreditData.structCreditData#" item="local.key">
				<cfset local.objCredit.addEnrollmentCredit(enrollmentID=local.enrollmentID, seminarCreditID=local.key, idnumber=arguments.strCreditData.structCreditData[local.key])>
			</cfloop>
		</cfif>

		<!--- register at provider and update enrollment with user id --->
		<cfset local.strAdd = CreateObject("component","model.admin.seminarWeb.seminarWebSWL").addSWLProviderRegistrantID(seminarID=arguments.seminarID, registrantID=local.enrollmentID)>

		<cfset local.sendEmailConfirmation = false>
		<cfset local.sendReplayEmailConfirmation = false>

		<!--- seminar is open --->
		<cfif local.qrySeminar.isOpen>
			<cfset local.sendEmailConfirmation = arguments.sendEmailConnectionInstructions AND local.strAdd.success>
		<cfelse>
			<!--- closed seminar and offer replays --->
			<cfquery name="local.qrySendReplayConfirmation" dbtype="query">
				SELECT seminarID
				FROM [local].qrySeminar
				WHERE isOpen = 0
				AND offerReplay = 1
				AND isUploadedReplay = 1
				AND canResellSeminar = 1
				AND replayExpirationDate >= <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#now()#">
				AND allowRegistrants = 1
			</cfquery>
			<cfset local.sendReplayEmailConfirmation = local.qrySendReplayConfirmation.recordCount IS 1>
		</cfif>

		<cfif local.sendEmailConfirmation>
			<cfset CreateObject("component","SWLiveEmails").sendConfirmation(seminarID=arguments.seminarID, enrollmentID=local.enrollmentID, performedBy=arguments.performedBy, 
				outgoingType="registerInstructions", withMaterials=false, orgcode=arguments.signUpOrgCode)>
		<cfelseif local.sendReplayEmailConfirmation>
			<cfset CreateObject("component","SWLiveEmails").sendReplayConfirmation(seminarID=arguments.seminarID, enrollmentID=local.enrollmentID, performedBy=arguments.performedBy,
				outgoingType="replayInstructions", orgcode=arguments.signUpOrgCode, emailOverride=arguments.emailOverride, isRegistrationConfirmation=1)>
		</cfif>

		<cfreturn local.enrollmentID>
	</cffunction>

	<cffunction name="logAction" access="public" returntype="void" output="no">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="typeName" type="string" required="yes">
		<cfargument name="performedBy" type="numeric" required="yes">
		<cfargument name="contact" type="string" required="yes">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		<cfargument name="authorID" type="numeric" required="yes">
		<cfargument name="pending" type="numeric" required="no" default="0">

		<cfstoredproc procedure="swl_logAction" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#" null="No">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.typeName#" null="No">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.performedBy#" null="No">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.contact#" null="No">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.enrollmentID#" null="No">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.pending#" null="No">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.authorID#" null="No">
		</cfstoredproc>
	</cffunction>

	<cffunction name="getSeminarProgress" access="public" returntype="struct" output="no">
		<cfargument name="enrollmentID" type="numeric" required="yes">

		<cfset var local = StructNew()>

		<cfstoredproc procedure="swl_getSeminarProgress" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.enrollmentID#" null="No">
			<cfprocresult name="local.qrySeminarSettings" resultset="1">
			<cfprocresult name="local.qrySeminarProgress" resultset="2">
		</cfstoredproc>

		<cfreturn local>
	</cffunction>

	<cffunction name="hasNotMetExamAndSurveyDates" access="public" returntype="boolean" output="no">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		<cfargument name="authorityID" type="numeric" required="yes">

		<cfset var local = StructNew()>
		<cfset local.objSWCredit = CreateObject("component","model.seminarweb.SWCredits")>
		
		<cfset local.qryAuthority = local.objSWCredit.getAuthority(arguments.authorityID)>
		<cfset local.daysToCompleteEvaluation = val(local.qryAuthority.SWLdaysToCompleteEvaluation)>
		<cfset local.daysToCompleteExam = val(local.qryAuthority.SWLdaysToCompleteExam)>

		
		<cfstoredproc procedure="swl_hasNotMetDaysToComplete" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.enrollmentID#" null="No">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.daysToCompleteEvaluation#" null="No">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.daysToCompleteExam#" null="No">
			<cfprocresult name="local.qryResults" resultset="1">
		</cfstoredproc>
		
		<cfreturn local.qryResults.recordCount>
	</cffunction>

	<cffunction name="hasNotMetDateRequirements" access="public" returntype="boolean" output="no">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		<!--- determines if any authority daterequirements fails --->
		<cfset var local = StructNew()>
		<cfset local.objSWCredit = CreateObject("component","model.seminarweb.SWCredits")>
		<cfset local.hasNotMetExamAndSurveyDatesResult = 0>
		
		<!--- was credit selected/earned? --->
		<cfset local.qrySelectedCredits = local.objSWCredit.getCreditsByEnrollmentID(arguments.enrollmentID)>
		<cfloop query="local.qrySelectedCredits">		
			<cfif local.qrySelectedCredits.authorityID neq "">
				<cfset local.hasNotMetExamAndSurveyDatesResult = local.hasNotMetExamAndSurveyDatesResult + hasNotMetExamAndSurveyDates(arguments.enrollmentID,local.qrySelectedCredits.authorityID)>
			</cfif>
		</cfloop>		
		<cfreturn local.hasNotMetExamAndSurveyDatesResult>
	</cffunction>		

	<cffunction name="checkCompletion" access="public" returntype="struct" output="no">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		<cfargument name="passFail" type="string" required="yes">		
	
		<cfset var local = StructNew()>

		<cfstoredproc procedure="swl_completeWebinar" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.enrollmentID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="1">
			<cfprocresult name="local.qryCompleteResult" resultset="1">
		</cfstoredproc>

		<!--- Autogenerate certificate here.  Remove from joinOnline. --->
		<cfif local.qryCompleteResult.passed is 1 and local.qryCompleteResult.allowAutoSendOfCertificate is 1>
			<cfset generateCertificateAndEmail(enrollmentID=arguments.enrollmentID) />
		</cfif>

		<cfreturn local>
	</cffunction>

	<cffunction name="verifySWLCode" access="public" returntype="query" output="no">
		<cfargument name="swlCode" type="string" required="true">

		<cfset var local = StructNew()>
		
		<cfstoredproc procedure="swl_verifySWLCode" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.swlCode#">
			<cfprocresult name="local.qryCodeInfo" resultset="1">
		</cfstoredproc>
		
		<cfreturn local.qryCodeInfo>
	</cffunction>

	<cffunction name="showExamPlayer" access="public" returntype="void" output="true">
		<cfargument name="uniqueCode" type="string" required="true">
		<cfargument name="Event" type="any" required="true">
		
		<cfset var local = structNew()>
		<cfset local.semDataFromCode = verifySWLCode(swlCode=arguments.uniqueCode)>
		<cfset local.semData = getSeminarBySeminarID(seminarID=local.semDataFromCode.seminarID)>
		<cfset local.semDetail = getSeminarProgress(enrollmentID=local.semDataFromCode.enrollmentID)>	
		<cfset local.redirectURL = "/?pg=semwebCatalog&panel=joinLive&joinsubmitted=1&uniqueCode=#arguments.uniqueCode#">
		<cflocation url="#local.redirectURL#" addtoken="false">

	</cffunction>

	<!--- ----------------- --->
	<!--- private functions --->
	<!--- ----------------- --->

	<cffunction name="generateCertificateAndEmail" access="private">
		<cfargument name="enrollmentID" type="numeric" required="true">
	
		<cfset var local = structNew()>
		<cfset local.objCertificate = CreateObject("component","model.seminarweb.SWCertificates")>	
		<cfset local.objSeminarsSWL = CreateObject("component","model.seminarweb.SWLiveSeminars")>		
		
		<cfset local.strSeminar = local.objSeminarsSWL.getEnrollmentByEnrollmentID(enrollmentID=arguments.enrollmentID)>
		<cfset local.strSeminarProgress = local.objSeminarsSWL.getSeminarProgress(enrollmentID=arguments.enrollmentID)>

		<!--- Ticket 8413596 Only autosend certificates if the authority requires completed evaluation otherwise manual send certs --->-
		<cfif isValid("regex",local.strSeminar.Email,application.regEx.email) AND local.strSeminarProgress.qrySeminarProgress.allowAutoSendOfCertificate is 1>
			<cfset local.objCertificate.sendCertificateByEmail(enrollmentID=arguments.enrollmentID, performedBy=local.strSeminar.depoMemberDataID, 
				outgoingType="autoCertificate", emailToUse=local.strSeminar.Email)>
		</cfif>
	</cffunction>

	<cffunction name="getStructuredSWLData" access="public" output="false" returntype="struct">
		<cfargument name="strSeminar" type="struct" required="true">
		<cfargument name="strSeminarHost" type="struct" required="true">

		<cfset var local = structNew()>
		
		<cfset local.seminarDetailURL = "#application.objSiteInfo.getSiteInfo(arguments.strSeminarHost.sitecode).scheme#://#arguments.strSeminarHost.mainhostname#/#arguments.strSeminarHost.mainurl#&panel=showLive&seminarid=#arguments.strSeminar.qrySeminar.seminarID#">
		
		<cfset local.qryAuthors = CreateObject("component","SWAuthors").getAuthorsBySeminarID(seminarID=arguments.strSeminar.qrySeminar.seminarID, authorType='Speaker')>
		<cfset local.qryLinkedCategories = CreateObject("component","SWCategories").getCategoriesBySeminarID(seminarID=arguments.strSeminar.qrySeminar.seminarID)>

		<cfset local.performersArr = []>
		<cfloop query="local.qryAuthors">
			<cfset local.authorFullName = "#local.qryAuthors.prefix# #local.qryAuthors.firstname# #local.qryAuthors.middlename# #local.qryAuthors.lastname#">
			<cfif len(local.qryAuthors.suffix)>
				<cfset local.authorFullName &= ", #local.qryAuthors.suffix#">
			</cfif>
			<cfset ArrayAppend(local.performersArr, { "@type": "Person", "name": local.authorFullName })>
		</cfloop>

		<cfset local.linkedCategoriesArr = []>
		<cfloop query="local.qryLinkedCategories">
			<cfset ArrayAppend(local.linkedCategoriesArr, local.qryLinkedCategories.categoryName)>
		</cfloop>
		
		<cfset local.qryAssociation = CreateObject("component","SWParticipants").getAssociationDetails(arguments.strSeminarHost.sitecode).qryAssociation>
		<cfset local.parsedTime = parseTimesFromWDDX(arguments.strSeminar.qrySeminar.wddxTimeZones,local.qryAssociation.wddxTimeZones,arguments.strSeminar.qrySeminar.dateStart,arguments.strSeminar.qrySeminar.dateEnd)>
		<cfset local.startDate = local.parsedTime.StartDate>
		<cfset local.endDate = local.parsedTime.EndDate>
		<cfset local.TZ = local.parsedTime.TimeZone>

		<cfset local.objTSTZ = CreateObject("component","model.system.platform.tsTimeZone")>
		<cfset local.qryTZ = local.objTSTZ.getTimeZones()>
		<cfquery name="local.qryTZ_selected" dbtype="query">
			select timeZoneCode
			from [local].qryTZ
			where timeZone = '#local.TZ#'
		</cfquery>
		<cfset local.startDate = local.objTSTZ.convertTimeZone(dateToConvert=local.StartDate, fromTimeZone=local.qryTZ_selected.timeZoneCode, toTimeZone="UTC")>
		<cfset local.endDate = local.objTSTZ.convertTimeZone(dateToConvert=local.EndDate, fromTimeZone=local.qryTZ_selected.timeZoneCode, toTimeZone="UTC")>

		<cfset local.duration = "PT#dateDiff("n",arguments.strSeminar.qrySeminar.dspStartDate,arguments.strSeminar.qrySeminar.dspendDate)#M">
		
		<cfset local.strSeminarData = {
				"@context": "http://schema.org",
				"@type": "Event",
				"name": arguments.strSeminar.qrySeminar.seminarName,
				"eventAttendanceMode": "https://schema.org/OnlineEventAttendanceMode",
				"eventStatus": "https://schema.org/EventScheduled",
				"url": local.seminarDetailURL,
				"description": arguments.strSeminar.qrySeminar.SeminarDesc,
				"location": {
					"@type": "VirtualLocation",
					"url": local.seminarDetailURL
				},
				"identifier": "SWL-#arguments.strSeminar.qrySeminar.seminarID#",
				"about": local.linkedCategoriesArr,
				"performer": local.performersArr,
				"startDate": local.startDate,
				"endDate": local.endDate,
				"duration": "PT#dateDiff("n",arguments.strSeminar.qrySeminar.dspStartDate,arguments.strSeminar.qrySeminar.dspendDate)#M",
				"organizer": {
					"@type": "Organization",
					"name": arguments.strSeminarHost.sitename,
					"url": "#application.objSiteInfo.getSiteInfo(arguments.strSeminarHost.sitecode).scheme#://#arguments.strSeminarHost.mainhostname#"
				}
			}>
		
		<cfif arguments.strSeminar.qrySeminarPrices.recordcount>
			<cfset local.regEndDate = DateTimeFormat(arguments.strSeminar.qrySeminar.dateStart,"ISO8601")>
			<cfif arguments.strSeminar.qrySeminar.dateStart gt now()>
				<cfset local.regStartDate = DateTimeFormat(now(),"ISO8601")>
			<cfelse>
				<cfset local.regStartDate = DateTimeFormat(arguments.strSeminar.qrySeminar.dateStart,"ISO8601")>
			</cfif>

			<cfset local.strSeminarData['offers'] = arrayNew(1)>
			<cfset local.thisOffer = structNew()>
			<cfloop query="arguments.strSeminar.qrySeminarPrices">
				<cfset local.thisOffer = {
						"@type": "Offer",
						"name": arguments.strSeminar.qrySeminarPrices.description,
						"price": arguments.strSeminar.qrySeminarPrices.price,
						"priceCurrency": "USD",
						"availability": "https://schema.org/InStock",
						"validFrom": local.regStartDate,
						"validThrough": local.regEndDate,
						"url": local.seminarDetailURL
					}>
				<cfset local.strSeminarData['offers'].append(local.thisOffer)>
			</cfloop>
		</cfif>

		<cfreturn local.strSeminarData>
	</cffunction>

	<cffunction name="getSWLReplayVideoLinkFromSeminarID" access="public" output="false" returnType="struct">
		<cfargument name="seminarID" type="string" required="yes">

		<cfset var local = StructNew()>
		<cfset local.data = { "success":true, "replayVideoLink":"","expireDate":"","replayAvailability":"","offerReplay":"" }>
		<cfset local.s3bucket = "seminarweb">
		<cfset local.s3requesttype = "vhost">
		<cfset local.s3region = "us-east-1">
		<cfif application.objPlatform.isRequestSecure()>
			<cfset local.s3protocol = "https">
		<cfelse>
			<cfset local.s3protocol = "http">
		</cfif>
		
		<cfset local.s3expire = 30>

		<cfquery name="local.qryParticipantDetails" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT s.seminarID, p.participantID, p.orgcode as participantOrgCode,swl.replayExpirationDate,swl.replayAvailability,swl.offerReplay
			FROM dbo.tblSeminars as s
			INNER JOIN dbo.tblSeminarsSWLive as swl on swl.seminarID = s.seminarID
			INNER JOIN dbo.tblParticipants as p on p.participantID = s.participantID
			WHERE s.seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
			AND s.isDeleted = 0;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif local.qryParticipantDetails.recordCount>
			<cfset local.objectKey = lcase("swlreplay/#local.qryParticipantDetails.participantOrgCode#/#local.qryParticipantDetails.participantID#/#local.qryParticipantDetails.seminarID#.mp4")>
			<cfif application.objS3.s3FileExists(bucket=local.s3bucket, objectKey=local.objectKey, requestType=local.s3requesttype, region=local.s3region)>
				<cfset local.displayName = "#local.qryParticipantDetails.seminarID#.mp4">
				<cfset local.arrAmzHeaders = arrayNew(1)>
				<cfset local.tmpStr = { key="response-content-disposition", value="inline; filename=""#local.displayName#""; filename*=UTF-8''#urlEncodedFormat(local.displayName)#" }>
				
				<cfset arrayAppend(local.arrAmzHeaders,local.tmpStr)>
				<cfset local.data.replayVideoLink = application.objS3.s3Url(bucket=local.s3bucket, objectKey=local.objectKey, requestType=local.s3requesttype, expireInMinutes=local.s3expire, canonicalizedAmzHeaders=local.arrAmzHeaders, region=local.s3region, protocol=local.s3protocol)>
				<cfset local.data.expireDate = local.qryParticipantDetails.replayExpirationDate>
				<cfset local.data.replayAvailability = local.qryParticipantDetails.replayAvailability>
				<cfset local.data.offerReplay = local.qryParticipantDetails.offerReplay>
			</cfif>
		</cfif>

		<cfif NOT len(local.data.replayVideoLink)>
			<cfset local.data.success = false>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="updateEnrollmentLastReplay" access="public" output="false" returnType="void">
		<cfargument name="enrollmentID" type="string" required="yes">

		<cfset var qryUpdateEnrollmentLastReplay = "">

		<cfquery name="qryUpdateEnrollmentLastReplay" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			UPDATE dbo.tblEnrollmentsSWLive
			SET numReplays = numReplays + 1,
				lastReplay = GETDATE()
			WHERE enrollmentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.enrollmentID#">
		</cfquery>
	</cffunction>
	
	<cffunction name="getMaterialsDocument" access="public" output="false" returntype="query">
		<cfargument name="seminarID" type="numeric" required="true">
		<cfargument name="enrollmentID" type="numeric"  required="false" default="0">
		<cfargument name="userType" type="string"  required="false" default="speaker">
		<cfargument name="sitecode" type="string" required="true">
		<cfargument name="documentID" type="numeric" required="false" default="0">
		
		<cfset local = structNew()>
		
		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryDocuments">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			<cfif arguments.userType NEQ 'speaker'>
				DECLARE @siteCode varchar(10), @participantID INT, @siteID int;

				SET @siteCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.sitecode#">;
				SELECT @siteID = siteID from membercentral.dbo.sites where siteCode = @siteCode;
				SELECT @participantID = seminarWeb.dbo.fn_getParticipantIDFromOrgcode(@siteCode);
				
				SELECT sd.seminarDocumentID, sd.documentID, dl.docTitle, dv.fileName, dv.fileExt
				FROM seminarWeb.dbo.tblEnrollments e
				INNER JOIN seminarWeb.dbo.tblEnrollmentsSWLive eswl ON e.enrollmentID = eswl.enrollmentID
				INNER JOIN seminarWeb.dbo.tblSeminars s ON s.seminarID = e.seminarID AND s.isDeleted = 0
				INNER JOIN seminarWeb.dbo.tblSeminarsSWLive as swl on swl.seminarID = s.seminarID
				INNER JOIN seminarWeb.dbo.tblParticipants p ON e.participantID = p.participantID
				INNER JOIN seminarWeb.dbo.tblUsers u ON e.userID = u.userID
				INNER JOIN seminarWeb.dbo.tblSeminarsAndDocuments as sd ON sd.seminarID = s.seminarID
				<cfif val(arguments.documentID)>
					AND sd.documentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.documentID#">
				</cfif>
				INNER JOIN trialsmith.dbo.depomemberdata AS depo ON depo.depomemberdataID = u.depoMemberDataID			
				INNER JOIN membercentral.dbo.cms_documents as d ON sd.documentID = d.documentID
				INNER JOIN membercentral.dbo.cms_documentLanguages as dl ON d.documentID = dl.documentID
				INNER JOIN membercentral.dbo.cms_documentVersions as dv ON dl.documentLanguageID = dv.documentLanguageID AND dv.isActive = 1
				WHERE 1=1
				<cfif val(arguments.enrollmentID)>
					AND e.enrollmentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.enrollmentID#">
				</cfif>
				AND e.isActive = 1
				AND (s.participantID = @participantID OR e.participantID = @participantID)
				AND (depo.adminflag2 is null or depo.adminflag2 <> 'Y')
				ORDER BY sd.seminarDocumentID;
			<cfelse>			
				SELECT sd.seminarDocumentID, sd.documentID, dl.docTitle, dv.fileName, dv.fileExt
				FROM seminarWeb.dbo.tblSeminars s
				INNER JOIN seminarWeb.dbo.tblSeminarsSWLive as swl on swl.seminarID = s.seminarID
				INNER JOIN seminarWeb.dbo.tblSeminarsAndDocuments as sd ON sd.seminarID = s.seminarID
				<cfif val(arguments.documentID)>
					AND sd.documentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.documentID#">
				</cfif>		
				INNER JOIN membercentral.dbo.cms_documents as d ON sd.documentID = d.documentID
				INNER JOIN membercentral.dbo.cms_documentLanguages as dl ON d.documentID = dl.documentID
				INNER JOIN membercentral.dbo.cms_documentVersions as dv ON dl.documentLanguageID = dv.documentLanguageID AND dv.isActive = 1
				WHERE 1=1
				AND s.seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
				AND s.isDeleted = 0
				ORDER BY sd.seminarDocumentID;
			</cfif>

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qryDocuments>
	</cffunction>

</cfcomponent>