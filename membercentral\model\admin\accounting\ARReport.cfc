<cfcomponent extends="model.admin.admin" output="no">
	<cfset variables.defaultEvent = 'controller'>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
		var local = structNew();

		// set rights into event
		local.tmpRights = buildRightAssignments(siteResourceID=this.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteid'));
		arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;

		// links
		this.link.showReport = buildCurrentLink(arguments.event,"showReport");

		local.methodToRun = this[arguments.event.getValue('mca_ta')];
		return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>		
	
	<cffunction name="showReport" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.data = "">

		<cfset arguments.event.paramValue('f_startdate','#month(dateadd("m",-1,now()))#/1/#year(dateadd("m",-1,now()))#')>
		<cfset arguments.event.paramValue('f_enddate',dateformat(dateadd("d",-1,"#month(now())#/1/#year(now())#"),"m/d/yyyy"))>
		<cfset arguments.event.paramValue('f_diffOnly',0)>
		
		<cfset arguments.event.paramValue('reportAction','')>

		<cfif arguments.event.getValue('reportAction') eq "csv">
			<cfset csvReport(event=arguments.event)>
		<cfelseif arguments.event.getValue('reportAction') eq "csvByGL">
			<cfreturn csvReportByGL(event=arguments.event)>
		<cfelseif arguments.event.getValue('reportAction') eq "pdf">
			<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
			<cfset local.reportFileName = "AccountsReceivable.pdf">
			<cfset local.reportData = screenReport(event=arguments.event)>

			<cfsavecontent variable="local.data">
				<cfoutput>
				<html>
				<head>
					<style type="text/css">
						<cfinclude template="/assets/admin/css/pdfstylesheet.css">
					</style>
				</head>
				<body>
					#local.reportData#
				</body>
				</html>
				</cfoutput>
			</cfsavecontent>

			<cfdocument filename="#local.strFolder.folderPath#/#local.reportFileName#" pagetype="letter" margintop="0.5" marginbottom="0.5" marginright="0.5" format="PDF" marginleft="0.5" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
				<cfoutput>
				<cfdocumentsection>
					#local.data#
				</cfdocumentsection>
				</cfoutput>
			</cfdocument>

			<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
			<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>
			<cfif not local.docResult>
				<cflocation url="#this.link.showReport#" addtoken="false">
			</cfif>
		<cfelse>
			<cfsavecontent variable="local.js">
				<cfoutput>
				<script language="javascript">
					function runreport() { 
						document.forms['frmReport'].reportAction.value = 'run';	
						return true;
					}
					function csvreport() { 
						document.forms['frmReport'].reportAction.value = 'csv';	
						return true;
					}
					function csvReportByGL(gl,dt,glCell) {
						$('##'+glCell).html('<i class="fa-solid fa-circle-notch fa-spin"></i>');
						$('##divRunArea').load('#this.link.showReport#&limitToGL=' + gl + '&asOFDate=' + dt + '&reportAction=csvByGL&glCell=' + glCell + '&mode=stream');
					}
					function downloadreport(u,glCell) {
						window.setTimeout(function() { $('##'+glCell).html('<i class="fa-light fa-file-csv"></i>'); },2000);
						self.location.href='/tsdd/' + u;
					}
					function runpdfreport() { 
						document.forms['frmReport'].reportAction.value = 'pdf';	
						return true;
					}
					$(function() {
						mca_setupDatePickerRangeFields('f_startdate','f_enddate');
						mca_setupCalendarIcons('frmReport');
					});
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#application.objCommon.minText(local.js)#">

			<cfset local.data = screenReport(event=arguments.event)>
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

	<cffunction name="screenReport" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfif listFindNoCase("run,pdf",arguments.event.getValue('reportAction'))>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="tr_report_ar">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(arguments.event.getValue('mc_siteinfo.orgid'))#">
				<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.event.getValue('f_startdate')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.event.getValue('f_enddate')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('f_diffOnly',0)#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="true">
				<cfprocresult name="local.qryGLAccounts" resultset="1">
			</cfstoredproc>
		</cfif>

		<!--- to download GL detail for starting AR, we need to pass in 1 day earlier than the selected start date for the calculations to add up --->
		<cfset local.StartDate1DayEarlier = dateformat(dateAdd('d',-1,arguments.event.getValue('f_startdate')),"m/d/yyyy")>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<h4>Accounts Receivable Report</h4>
			<div>
				If you wish to book Accounts Receivable, then you must run the Batch Summary Report for general ledger entries 
				combined with the Accounts Receivable Report for the exact same Date Range. You will then have a summary of cash and payment 
				activity (Batch Report) and sales and billed items that are not yet paid (Accounts Receivable).
				<br/><br/>
				<b>Considerations:</b><br/>
				This report considers invoices that are pending, closed, or delinquent having a BILLED DATE on or before the chosen date. <br/>
				It does not consider open invoices, invoices having a BILLED DATE after the chosen date, or batches having a DEPOSIT DATE after the chosen date.<br/>
				Transactions appearing in open batches are not finalized and may alter data in this report.
			</div>

			<div class="row my-3">
				<div class="col-md-12">
					<div class="card card-box mb-1">
						<div class="card-header py-1 bg-light">
							<div class="card-header--title font-weight-bold font-size-md">
								Report Filters
							</div>
						</div>
						<div class="card-body pb-3">
							<div class="col-md-8">
								<cfif arguments.event.getValue('reportAction') eq 'pdf'>
									<table class="table">
										<tbody>
											<tr>
												<td>Date Range:</td>
												<td>#arguments.event.getValue('f_startdate')# to #arguments.event.getValue('f_enddate')#</td>
											</tr>
											<tr>
												<td></td>
												<td>
													<input type="checkbox"<cfif arguments.event.getValue('f_diffOnly') is 1> checked</cfif>>
													Only show accounts with changes in AR
												</td>
											</tr>
										</tbody>
									</table>
								<cfelse>
									<form name="frmReport" id="frmReport" method="post" action="#this.link.showReport#">
										<input type="hidden" name="reportAction" id="reportAction" value="#arguments.event.getValue('reportAction')#">
										
										<div class="form-group row">
											<label for="f_startdate" class="col-sm-3 col-form-label">Date Range</label>
											<div class="col-sm-9">
												<div class="row">
													<div class="col-sm-6">
														<div class="input-group input-group-sm">
															<input type="text" name="f_startdate" id="f_startdate" value="#arguments.event.getValue('f_startdate')#" class="form-control form-control-sm dateControl" placeholder="Date Range from">
															<div class="input-group-append">
																<span class="input-group-text cursor-pointer calendar-button" data-target="f_startdate"><i class="fa-solid fa-calendar"></i></span>
															</div>
														</div>
													</div>
													<div class="col-sm-6">
														<div class="input-group input-group-sm">
															<input type="text" name="f_enddate" id="f_enddate" value="#arguments.event.getValue('f_enddate')#" class="form-control form-control-sm dateControl" placeholder="Date Range To">
															<div class="input-group-append">
																<span class="input-group-text cursor-pointer calendar-button" data-target="f_enddate"><i class="fa-solid fa-calendar"></i></span>
															</div>
														</div>
													</div>
												</div>
											</div>
										</div>
										<div class="form-group row">
											<div class="col-sm-10 offset-sm-3">
												<label class="form-check-label">
													<input type="checkbox" name="f_diffOnly" id="f_diffOnly" value="1"<cfif arguments.event.getValue('f_diffOnly') is 1> checked</cfif>>
													Only show accounts with changes in AR
												</label>
											</div>
										</div>
										<div class="form-group row d-print-none">
											<div class="col-sm-10 offset-sm-3 mt-2">
												<button type="submit" class="btn btn-sm btn-primary" onclick="return runreport();">
													<span class="btn-wrapper--icon">
														<i class="fa-light fa-browser"></i>
													</span>
													<span class="btn-wrapper--label">Run Report</span>
												</button>
												<button type="submit" class="btn btn-sm btn-secondary" onclick="return csvreport();">
													<span class="btn-wrapper--icon">
														<i class="fa-light fa-file-csv"></i>
													</span>
													<span class="btn-wrapper--label">CSV</span>
												</button>
												<button type="submit" class="btn btn-sm btn-secondary" onclick="return runpdfreport();">
													<span class="btn-wrapper--icon">
														<i class="fa-light fa-file-pdf"></i>
													</span>
													<span class="btn-wrapper--label">PDF</span>
												</button>
											</div>
										</div>
									</form>
								</cfif>
							</div>
						</div>
					</div>
				</div>
			</div>
			</cfoutput>
			
			<cfif listFindNoCase("run,pdf",arguments.event.getValue('reportAction'))>
				<!--- any warnings --->
				<cfif local.qryGLAccounts.openInvoices gt 0>
					<cfoutput>
						<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning alert-dismissible fade show" role="alert">
							<span class="font-size-lg d-block d-40 mr-2 text-center">
								<i class="fa-solid fa-triangle-exclamation"></i>
							</span>
							<span>
								There <cfif local.qryGLAccounts.openInvoices eq 1>is 1 open invoice<cfelse>are #local.qryGLAccounts.openInvoices# open invoices</cfif> with BILLED DATE before #dateformat(arguments.event.getValue('f_enddate'),"m/d/yyyy")# that do not appear on this report.
							</span>
							<button type="button" class="close d-print-none" data-dismiss="alert" aria-label="Close">
								<span aria-hidden="true">&times;</span>
							</button>
						</div>
					</cfoutput>
				</cfif>

				<cfif local.qryGLAccounts.recordcount is 0>
					<cfoutput><div class="text-dark">No Accounts Receivable activity to report.</div></cfoutput>
				<cfelse>
					<cfoutput>
					<div class="card card-box">
						<div class="card-header bg-light d-print-none">
							<div class="card-header--title font-weight-bold font-size-md">Summary</div>
						</div>
						<div class="card-body p-2">
							<table class="table table-sm">
								<thead>
									<tr>
										<th class="text-left">Revenue Account</th>
										<th class="text-right" nowrap>Starting AR</th>
										<th class="text-right" nowrap>Ending AR</th>
										<th class="text-right">Difference</th>
									</tr>
								</thead>
								<tbody>
									<cfloop query="local.qryGLAccounts">
										<tr>
											<td <cfif local.qryGLAccounts.GLAccountID is 0>class="font-weight-bold"</cfif>>
												#local.qryGLAccounts.thePathExpanded#
												<div class="text-dim small">#local.qryGLAccounts.accountCode#&nbsp;</div>
												<cfif local.qryGLAccounts.GLAccountID is 0 and arguments.event.getValue('f_diffOnly',0) is 1>
													<br/>
													<i>* NOTE: The report totals include only those accounts with changes in AR and may not represent the overall AR balances in this period.</i>
												</cfif>
											</td>
											<td class="text-right<cfif local.qryGLAccounts.GLAccountID is 0> font-weight-bold</cfif>">
												#dollarformat(local.qryGLAccounts.ARStart)#
												<cfif arguments.event.getValue('reportAction') EQ 'run' AND local.qryGLAccounts.ARStart NEQ 0>
													<a href="##" id="ARStartGL_#local.qryGLAccounts.GLAccountID#" class="btn btn-md text-primary p-0 ml-1" onclick="csvReportByGL(#local.qryGLAccounts.GLAccountID#,'#local.StartDate1DayEarlier#','ARStartGL_#local.qryGLAccounts.GLAccountID#');" title="Download CSV">
														<i class="fa-light fa-file-csv"></i>
													</a>
												</cfif>
											</td>
											<td class="text-right<cfif local.qryGLAccounts.GLAccountID is 0> font-weight-bold</cfif>">
												#dollarformat(local.qryGLAccounts.AREnd)#
												<cfif arguments.event.getValue('reportAction') EQ 'run' AND local.qryGLAccounts.AREnd NEQ 0>
													<a href="##" id="AREndGL_#local.qryGLAccounts.GLAccountID#" class="btn btn-md text-primary p-0 ml-1" onclick="csvReportByGL(#local.qryGLAccounts.GLAccountID#,'#arguments.event.getValue('f_enddate')#','AREndGL_#local.qryGLAccounts.GLAccountID#');" title="Download CSV">
														<i class="fa-light fa-file-csv"></i>
													</a>
												</cfif>
											</td>
											<td class="text-right<cfif local.qryGLAccounts.GLAccountID is 0> font-weight-bold</cfif>">#dollarformat(local.qryGLAccounts.ARDifference)#</td>
										</tr>
									</cfloop>
								</tbody>
							</table>
						</div>
					</div>
					<cfif arguments.event.getValue('reportAction') EQ 'run'>
						<div id="divRunArea"></div>
					</cfif>
					</cfoutput>
				</cfif>	
				<cfoutput>
				<div class="text-center text-dark"><i>Report generated: #dateformat(now(),"dddd, mmmm d, yyyy")# at #timeformat(now(),"h:mm tt")# Central</i></div>
				</cfoutput>
			</cfif>			
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="csvReport" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.reportFileName = "AccountsReceivable.csv">

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="tr_report_ar">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(arguments.event.getValue('mc_siteinfo.orgid'))#">
			<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.event.getValue('f_startdate')#">
			<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.event.getValue('f_enddate')#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('f_diffOnly',0)#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.strFolder.folderPathUNC#\#local.reportFileName#">
		</cfstoredproc>

		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
		<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>
		<cfif not local.docResult>
			<cflocation url="#this.link.showReport#" addtoken="no">
		</cfif>	
	</cffunction>

	<cffunction name="csvReportByGL" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.limitToGL = val(arguments.event.getValue('limitToGL',0))>
		<cfset local.asOFDate = arguments.event.getValue('asOFDate')>
		<cfif local.limitToGL gt 0>
			<cfset local.strGLAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=local.limitToGL, orgID=arguments.event.getValue('mc_siteinfo.orgid'))>
		<cfelse>
			<cfset local.strGLAccount.qryAccount.accountName = "AllGLAccounts">
		</cfif>
		
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.cleanFileName = left(ReReplace(local.strGLAccount.qryAccount.accountName,'[^A-Za-z0-9]','','ALL'),30)>
		<cfset local.reportFileName = "AccountsReceivable_#local.cleanFileName#_#dateformat(local.asOFDate,"YYYYMMDD")#.csv">

		<cfsetting requesttimeout="300">

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="tr_report_arDetail">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgid')#">
			<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#local.asOFDate#">
			<cfif local.limitToGL gt 0>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.limitToGL#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
			</cfif>
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.strFolder.folderPathUNC#\#local.reportFileName#">
		</cfstoredproc>

		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
		<cfset local.stDownloadURL = application.objDocDownload.createDownloadURL(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script type="text/javascript">downloadreport('#local.stDownloadURL#','#arguments.event.getValue('glCell','')#');</script>
			</cfoutput>	
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
</cfcomponent>